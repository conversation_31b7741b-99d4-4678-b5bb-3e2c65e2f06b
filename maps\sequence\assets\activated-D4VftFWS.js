import{i as ce,u as fe,r as F,v as ge,z as he,X as me,Y as ye,j as N,o as L,l as u,m as O,t as q,F as ve,k as pe,n as be,B as Tt,Z as we,$ as xe,_ as Ce,S as ke,T as Ee}from"./style-CyfHChcX.js";import{u as Be}from"./activation-C4HdN75D.js";function _e(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var G={},ot,It;function Ae(){return It||(It=1,ot=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),ot}var st={},K={},Mt;function J(){if(Mt)return K;Mt=1;let r;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return K.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17},K.getSymbolTotalCodewords=function(n){return o[n]},K.getBCHDigit=function(s){let n=0;for(;s!==0;)n++,s>>>=1;return n},K.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');r=n},K.isKanjiModeEnabled=function(){return typeof r<"u"},K.toSJIS=function(n){return r(n)},K}var it={},St;function Rt(){return St||(St=1,function(r){r.L={bit:1},r.M={bit:0},r.Q={bit:3},r.H={bit:2};function o(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"l":case"low":return r.L;case"m":case"medium":return r.M;case"q":case"quartile":return r.Q;case"h":case"high":return r.H;default:throw new Error("Unknown EC Level: "+s)}}r.isValid=function(n){return n&&typeof n.bit<"u"&&n.bit>=0&&n.bit<4},r.from=function(n,t){if(r.isValid(n))return n;try{return o(n)}catch{return t}}}(it)),it}var at,Nt;function Re(){if(Nt)return at;Nt=1;function r(){this.buffer=[],this.length=0}return r.prototype={get:function(o){const s=Math.floor(o/8);return(this.buffer[s]>>>7-o%8&1)===1},put:function(o,s){for(let n=0;n<s;n++)this.putBit((o>>>s-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const s=Math.floor(this.length/8);this.buffer.length<=s&&this.buffer.push(0),o&&(this.buffer[s]|=128>>>this.length%8),this.length++}},at=r,at}var lt,Lt;function Pe(){if(Lt)return lt;Lt=1;function r(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return r.prototype.set=function(o,s,n,t){const e=o*this.size+s;this.data[e]=n,t&&(this.reservedBit[e]=!0)},r.prototype.get=function(o,s){return this.data[o*this.size+s]},r.prototype.xor=function(o,s,n){this.data[o*this.size+s]^=n},r.prototype.isReserved=function(o,s){return this.reservedBit[o*this.size+s]},lt=r,lt}var ut={},Dt;function Te(){return Dt||(Dt=1,function(r){const o=J().getSymbolSize;r.getRowColCoords=function(n){if(n===1)return[];const t=Math.floor(n/7)+2,e=o(n),i=e===145?26:Math.ceil((e-13)/(2*t-2))*2,l=[e-7];for(let a=1;a<t-1;a++)l[a]=l[a-1]-i;return l.push(6),l.reverse()},r.getPositions=function(n){const t=[],e=r.getRowColCoords(n),i=e.length;for(let l=0;l<i;l++)for(let a=0;a<i;a++)l===0&&a===0||l===0&&a===i-1||l===i-1&&a===0||t.push([e[l],e[a]]);return t}}(ut)),ut}var dt={},Ft;function Ie(){if(Ft)return dt;Ft=1;const r=J().getSymbolSize,o=7;return dt.getPositions=function(n){const t=r(n);return[[0,0],[t-o,0],[0,t-o]]},dt}var ct={},qt;function Me(){return qt||(qt=1,function(r){r.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};r.isValid=function(t){return t!=null&&t!==""&&!isNaN(t)&&t>=0&&t<=7},r.from=function(t){return r.isValid(t)?parseInt(t,10):void 0},r.getPenaltyN1=function(t){const e=t.size;let i=0,l=0,a=0,c=null,h=null;for(let E=0;E<e;E++){l=a=0,c=h=null;for(let v=0;v<e;v++){let f=t.get(E,v);f===c?l++:(l>=5&&(i+=o.N1+(l-5)),c=f,l=1),f=t.get(v,E),f===h?a++:(a>=5&&(i+=o.N1+(a-5)),h=f,a=1)}l>=5&&(i+=o.N1+(l-5)),a>=5&&(i+=o.N1+(a-5))}return i},r.getPenaltyN2=function(t){const e=t.size;let i=0;for(let l=0;l<e-1;l++)for(let a=0;a<e-1;a++){const c=t.get(l,a)+t.get(l,a+1)+t.get(l+1,a)+t.get(l+1,a+1);(c===4||c===0)&&i++}return i*o.N2},r.getPenaltyN3=function(t){const e=t.size;let i=0,l=0,a=0;for(let c=0;c<e;c++){l=a=0;for(let h=0;h<e;h++)l=l<<1&2047|t.get(c,h),h>=10&&(l===1488||l===93)&&i++,a=a<<1&2047|t.get(h,c),h>=10&&(a===1488||a===93)&&i++}return i*o.N3},r.getPenaltyN4=function(t){let e=0;const i=t.data.length;for(let a=0;a<i;a++)e+=t.data[a];return Math.abs(Math.ceil(e*100/i/5)-10)*o.N4};function s(n,t,e){switch(n){case r.Patterns.PATTERN000:return(t+e)%2===0;case r.Patterns.PATTERN001:return t%2===0;case r.Patterns.PATTERN010:return e%3===0;case r.Patterns.PATTERN011:return(t+e)%3===0;case r.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(e/3))%2===0;case r.Patterns.PATTERN101:return t*e%2+t*e%3===0;case r.Patterns.PATTERN110:return(t*e%2+t*e%3)%2===0;case r.Patterns.PATTERN111:return(t*e%3+(t+e)%2)%2===0;default:throw new Error("bad maskPattern:"+n)}}r.applyMask=function(t,e){const i=e.size;for(let l=0;l<i;l++)for(let a=0;a<i;a++)e.isReserved(a,l)||e.xor(a,l,s(t,a,l))},r.getBestMask=function(t,e){const i=Object.keys(r.Patterns).length;let l=0,a=1/0;for(let c=0;c<i;c++){e(c),r.applyMask(c,t);const h=r.getPenaltyN1(t)+r.getPenaltyN2(t)+r.getPenaltyN3(t)+r.getPenaltyN4(t);r.applyMask(c,t),h<a&&(a=h,l=c)}return l}}(ct)),ct}var et={},Ut;function se(){if(Ut)return et;Ut=1;const r=Rt(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return et.getBlocksCount=function(t,e){switch(e){case r.L:return o[(t-1)*4+0];case r.M:return o[(t-1)*4+1];case r.Q:return o[(t-1)*4+2];case r.H:return o[(t-1)*4+3];default:return}},et.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return s[(t-1)*4+0];case r.M:return s[(t-1)*4+1];case r.Q:return s[(t-1)*4+2];case r.H:return s[(t-1)*4+3];default:return}},et}var ft={},X={},zt;function Se(){if(zt)return X;zt=1;const r=new Uint8Array(512),o=new Uint8Array(256);return function(){let n=1;for(let t=0;t<255;t++)r[t]=n,o[n]=t,n<<=1,n&256&&(n^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),X.log=function(n){if(n<1)throw new Error("log("+n+")");return o[n]},X.exp=function(n){return r[n]},X.mul=function(n,t){return n===0||t===0?0:r[o[n]+o[t]]},X}var jt;function Ne(){return jt||(jt=1,function(r){const o=Se();r.mul=function(n,t){const e=new Uint8Array(n.length+t.length-1);for(let i=0;i<n.length;i++)for(let l=0;l<t.length;l++)e[i+l]^=o.mul(n[i],t[l]);return e},r.mod=function(n,t){let e=new Uint8Array(n);for(;e.length-t.length>=0;){const i=e[0];for(let a=0;a<t.length;a++)e[a]^=o.mul(t[a],i);let l=0;for(;l<e.length&&e[l]===0;)l++;e=e.slice(l)}return e},r.generateECPolynomial=function(n){let t=new Uint8Array([1]);for(let e=0;e<n;e++)t=r.mul(t,new Uint8Array([1,o.exp(e)]));return t}}(ft)),ft}var gt,Vt;function Le(){if(Vt)return gt;Vt=1;const r=Ne();function o(s){this.genPoly=void 0,this.degree=s,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(n){this.degree=n,this.genPoly=r.generateECPolynomial(this.degree)},o.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(n.length+this.degree);t.set(n);const e=r.mod(t,this.genPoly),i=this.degree-e.length;if(i>0){const l=new Uint8Array(this.degree);return l.set(e,i),l}return e},gt=o,gt}var ht={},mt={},yt={},Ot;function ie(){return Ot||(Ot=1,yt.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),yt}var z={},Kt;function ae(){if(Kt)return z;Kt=1;const r="[0-9]+",o="[A-Z $%*+\\-./:]+";let s="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";s=s.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+s+`)(?:.|[\r
]))+`;z.KANJI=new RegExp(s,"g"),z.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),z.BYTE=new RegExp(n,"g"),z.NUMERIC=new RegExp(r,"g"),z.ALPHANUMERIC=new RegExp(o,"g");const t=new RegExp("^"+s+"$"),e=new RegExp("^"+r+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return z.testKanji=function(a){return t.test(a)},z.testNumeric=function(a){return e.test(a)},z.testAlphanumeric=function(a){return i.test(a)},z}var Ht;function Y(){return Ht||(Ht=1,function(r){const o=ie(),s=ae();r.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},r.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},r.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},r.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},r.MIXED={bit:-1},r.getCharCountIndicator=function(e,i){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!o.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?e.ccBits[0]:i<27?e.ccBits[1]:e.ccBits[2]},r.getBestModeForData=function(e){return s.testNumeric(e)?r.NUMERIC:s.testAlphanumeric(e)?r.ALPHANUMERIC:s.testKanji(e)?r.KANJI:r.BYTE},r.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},r.isValid=function(e){return e&&e.bit&&e.ccBits};function n(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return r.NUMERIC;case"alphanumeric":return r.ALPHANUMERIC;case"kanji":return r.KANJI;case"byte":return r.BYTE;default:throw new Error("Unknown mode: "+t)}}r.from=function(e,i){if(r.isValid(e))return e;try{return n(e)}catch{return i}}}(mt)),mt}var Jt;function De(){return Jt||(Jt=1,function(r){const o=J(),s=se(),n=Rt(),t=Y(),e=ie(),i=7973,l=o.getBCHDigit(i);function a(v,f,R){for(let I=1;I<=40;I++)if(f<=r.getCapacity(I,R,v))return I}function c(v,f){return t.getCharCountIndicator(v,f)+4}function h(v,f){let R=0;return v.forEach(function(I){const S=c(I.mode,f);R+=S+I.getBitsLength()}),R}function E(v,f){for(let R=1;R<=40;R++)if(h(v,R)<=r.getCapacity(R,f,t.MIXED))return R}r.from=function(f,R){return e.isValid(f)?parseInt(f,10):R},r.getCapacity=function(f,R,I){if(!e.isValid(f))throw new Error("Invalid QR Code version");typeof I>"u"&&(I=t.BYTE);const S=o.getSymbolTotalCodewords(f),_=s.getTotalCodewordsCount(f,R),P=(S-_)*8;if(I===t.MIXED)return P;const B=P-c(I,f);switch(I){case t.NUMERIC:return Math.floor(B/10*3);case t.ALPHANUMERIC:return Math.floor(B/11*2);case t.KANJI:return Math.floor(B/13);case t.BYTE:default:return Math.floor(B/8)}},r.getBestVersionForData=function(f,R){let I;const S=n.from(R,n.M);if(Array.isArray(f)){if(f.length>1)return E(f,S);if(f.length===0)return 1;I=f[0]}else I=f;return a(I.mode,I.getLength(),S)},r.getEncodedBits=function(f){if(!e.isValid(f)||f<7)throw new Error("Invalid QR Code version");let R=f<<12;for(;o.getBCHDigit(R)-l>=0;)R^=i<<o.getBCHDigit(R)-l;return f<<12|R}}(ht)),ht}var vt={},Yt;function Fe(){if(Yt)return vt;Yt=1;const r=J(),o=1335,s=21522,n=r.getBCHDigit(o);return vt.getEncodedBits=function(e,i){const l=e.bit<<3|i;let a=l<<10;for(;r.getBCHDigit(a)-n>=0;)a^=o<<r.getBCHDigit(a)-n;return(l<<10|a)^s},vt}var pt={},bt,$t;function qe(){if($t)return bt;$t=1;const r=Y();function o(s){this.mode=r.NUMERIC,this.data=s.toString()}return o.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(n){let t,e,i;for(t=0;t+3<=this.data.length;t+=3)e=this.data.substr(t,3),i=parseInt(e,10),n.put(i,10);const l=this.data.length-t;l>0&&(e=this.data.substr(t),i=parseInt(e,10),n.put(i,l*3+1))},bt=o,bt}var wt,Gt;function Ue(){if(Gt)return wt;Gt=1;const r=Y(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(n){this.mode=r.ALPHANUMERIC,this.data=n}return s.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let i=o.indexOf(this.data[e])*45;i+=o.indexOf(this.data[e+1]),t.put(i,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},wt=s,wt}var xt,Qt;function ze(){if(Qt)return xt;Qt=1;const r=Y();function o(s){this.mode=r.BYTE,typeof s=="string"?this.data=new TextEncoder().encode(s):this.data=new Uint8Array(s)}return o.getBitsLength=function(n){return n*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(s){for(let n=0,t=this.data.length;n<t;n++)s.put(this.data[n],8)},xt=o,xt}var Ct,Xt;function je(){if(Xt)return Ct;Xt=1;const r=Y(),o=J();function s(n){this.mode=r.KANJI,this.data=n}return s.getBitsLength=function(t){return t*13},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(n){let t;for(t=0;t<this.data.length;t++){let e=o.toSJIS(this.data[t]);if(e>=33088&&e<=40956)e-=33088;else if(e>=57408&&e<=60351)e-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);e=(e>>>8&255)*192+(e&255),n.put(e,13)}},Ct=s,Ct}var kt={exports:{}},Zt;function Ve(){return Zt||(Zt=1,function(r){var o={single_source_shortest_paths:function(s,n,t){var e={},i={};i[n]=0;var l=o.PriorityQueue.make();l.push(n,0);for(var a,c,h,E,v,f,R,I,S;!l.empty();){a=l.pop(),c=a.value,E=a.cost,v=s[c]||{};for(h in v)v.hasOwnProperty(h)&&(f=v[h],R=E+f,I=i[h],S=typeof i[h]>"u",(S||I>R)&&(i[h]=R,l.push(h,R),e[h]=c))}if(typeof t<"u"&&typeof i[t]>"u"){var _=["Could not find a path from ",n," to ",t,"."].join("");throw new Error(_)}return e},extract_shortest_path_from_predecessor_list:function(s,n){for(var t=[],e=n;e;)t.push(e),s[e],e=s[e];return t.reverse(),t},find_path:function(s,n,t){var e=o.single_source_shortest_paths(s,n,t);return o.extract_shortest_path_from_predecessor_list(e,t)},PriorityQueue:{make:function(s){var n=o.PriorityQueue,t={},e;s=s||{};for(e in n)n.hasOwnProperty(e)&&(t[e]=n[e]);return t.queue=[],t.sorter=s.sorter||n.default_sorter,t},default_sorter:function(s,n){return s.cost-n.cost},push:function(s,n){var t={value:s,cost:n};this.queue.push(t),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};r.exports=o}(kt)),kt.exports}var Wt;function Oe(){return Wt||(Wt=1,function(r){const o=Y(),s=qe(),n=Ue(),t=ze(),e=je(),i=ae(),l=J(),a=Ve();function c(_){return unescape(encodeURIComponent(_)).length}function h(_,P,B){const p=[];let D;for(;(D=_.exec(B))!==null;)p.push({data:D[0],index:D.index,mode:P,length:D[0].length});return p}function E(_){const P=h(i.NUMERIC,o.NUMERIC,_),B=h(i.ALPHANUMERIC,o.ALPHANUMERIC,_);let p,D;return l.isKanjiModeEnabled()?(p=h(i.BYTE,o.BYTE,_),D=h(i.KANJI,o.KANJI,_)):(p=h(i.BYTE_KANJI,o.BYTE,_),D=[]),P.concat(B,p,D).sort(function(C,k){return C.index-k.index}).map(function(C){return{data:C.data,mode:C.mode,length:C.length}})}function v(_,P){switch(P){case o.NUMERIC:return s.getBitsLength(_);case o.ALPHANUMERIC:return n.getBitsLength(_);case o.KANJI:return e.getBitsLength(_);case o.BYTE:return t.getBitsLength(_)}}function f(_){return _.reduce(function(P,B){const p=P.length-1>=0?P[P.length-1]:null;return p&&p.mode===B.mode?(P[P.length-1].data+=B.data,P):(P.push(B),P)},[])}function R(_){const P=[];for(let B=0;B<_.length;B++){const p=_[B];switch(p.mode){case o.NUMERIC:P.push([p,{data:p.data,mode:o.ALPHANUMERIC,length:p.length},{data:p.data,mode:o.BYTE,length:p.length}]);break;case o.ALPHANUMERIC:P.push([p,{data:p.data,mode:o.BYTE,length:p.length}]);break;case o.KANJI:P.push([p,{data:p.data,mode:o.BYTE,length:c(p.data)}]);break;case o.BYTE:P.push([{data:p.data,mode:o.BYTE,length:c(p.data)}])}}return P}function I(_,P){const B={},p={start:{}};let D=["start"];for(let y=0;y<_.length;y++){const C=_[y],k=[];for(let m=0;m<C.length;m++){const A=C[m],x=""+y+m;k.push(x),B[x]={node:A,lastCount:0},p[x]={};for(let b=0;b<D.length;b++){const g=D[b];B[g]&&B[g].node.mode===A.mode?(p[g][x]=v(B[g].lastCount+A.length,A.mode)-v(B[g].lastCount,A.mode),B[g].lastCount+=A.length):(B[g]&&(B[g].lastCount=A.length),p[g][x]=v(A.length,A.mode)+4+o.getCharCountIndicator(A.mode,P))}}D=k}for(let y=0;y<D.length;y++)p[D[y]].end=0;return{map:p,table:B}}function S(_,P){let B;const p=o.getBestModeForData(_);if(B=o.from(P,p),B!==o.BYTE&&B.bit<p.bit)throw new Error('"'+_+'" cannot be encoded with mode '+o.toString(B)+`.
 Suggested mode is: `+o.toString(p));switch(B===o.KANJI&&!l.isKanjiModeEnabled()&&(B=o.BYTE),B){case o.NUMERIC:return new s(_);case o.ALPHANUMERIC:return new n(_);case o.KANJI:return new e(_);case o.BYTE:return new t(_)}}r.fromArray=function(P){return P.reduce(function(B,p){return typeof p=="string"?B.push(S(p,null)):p.data&&B.push(S(p.data,p.mode)),B},[])},r.fromString=function(P,B){const p=E(P,l.isKanjiModeEnabled()),D=R(p),y=I(D,B),C=a.find_path(y.map,"start","end"),k=[];for(let m=1;m<C.length-1;m++)k.push(y.table[C[m]].node);return r.fromArray(f(k))},r.rawSplit=function(P){return r.fromArray(E(P,l.isKanjiModeEnabled()))}}(pt)),pt}var te;function Ke(){if(te)return st;te=1;const r=J(),o=Rt(),s=Re(),n=Pe(),t=Te(),e=Ie(),i=Me(),l=se(),a=Le(),c=De(),h=Fe(),E=Y(),v=Oe();function f(y,C){const k=y.size,m=e.getPositions(C);for(let A=0;A<m.length;A++){const x=m[A][0],b=m[A][1];for(let g=-1;g<=7;g++)if(!(x+g<=-1||k<=x+g))for(let T=-1;T<=7;T++)b+T<=-1||k<=b+T||(g>=0&&g<=6&&(T===0||T===6)||T>=0&&T<=6&&(g===0||g===6)||g>=2&&g<=4&&T>=2&&T<=4?y.set(x+g,b+T,!0,!0):y.set(x+g,b+T,!1,!0))}}function R(y){const C=y.size;for(let k=8;k<C-8;k++){const m=k%2===0;y.set(k,6,m,!0),y.set(6,k,m,!0)}}function I(y,C){const k=t.getPositions(C);for(let m=0;m<k.length;m++){const A=k[m][0],x=k[m][1];for(let b=-2;b<=2;b++)for(let g=-2;g<=2;g++)b===-2||b===2||g===-2||g===2||b===0&&g===0?y.set(A+b,x+g,!0,!0):y.set(A+b,x+g,!1,!0)}}function S(y,C){const k=y.size,m=c.getEncodedBits(C);let A,x,b;for(let g=0;g<18;g++)A=Math.floor(g/3),x=g%3+k-8-3,b=(m>>g&1)===1,y.set(A,x,b,!0),y.set(x,A,b,!0)}function _(y,C,k){const m=y.size,A=h.getEncodedBits(C,k);let x,b;for(x=0;x<15;x++)b=(A>>x&1)===1,x<6?y.set(x,8,b,!0):x<8?y.set(x+1,8,b,!0):y.set(m-15+x,8,b,!0),x<8?y.set(8,m-x-1,b,!0):x<9?y.set(8,15-x-1+1,b,!0):y.set(8,15-x-1,b,!0);y.set(m-8,8,1,!0)}function P(y,C){const k=y.size;let m=-1,A=k-1,x=7,b=0;for(let g=k-1;g>0;g-=2)for(g===6&&g--;;){for(let T=0;T<2;T++)if(!y.isReserved(A,g-T)){let U=!1;b<C.length&&(U=(C[b]>>>x&1)===1),y.set(A,g-T,U),x--,x===-1&&(b++,x=7)}if(A+=m,A<0||k<=A){A-=m,m=-m;break}}}function B(y,C,k){const m=new s;k.forEach(function(T){m.put(T.mode.bit,4),m.put(T.getLength(),E.getCharCountIndicator(T.mode,y)),T.write(m)});const A=r.getSymbolTotalCodewords(y),x=l.getTotalCodewordsCount(y,C),b=(A-x)*8;for(m.getLengthInBits()+4<=b&&m.put(0,4);m.getLengthInBits()%8!==0;)m.putBit(0);const g=(b-m.getLengthInBits())/8;for(let T=0;T<g;T++)m.put(T%2?17:236,8);return p(m,y,C)}function p(y,C,k){const m=r.getSymbolTotalCodewords(C),A=l.getTotalCodewordsCount(C,k),x=m-A,b=l.getBlocksCount(C,k),g=m%b,T=b-g,U=Math.floor(m/b),H=Math.floor(x/b),Z=H+1,W=U-H,w=new a(W);let d=0;const M=new Array(b),Q=new Array(b);let tt=0;const de=new Uint8Array(y.buffer);for(let $=0;$<b;$++){const rt=$<T?H:Z;M[$]=de.slice(d,d+rt),Q[$]=w.encode(M[$]),d+=rt,tt=Math.max(tt,rt)}const nt=new Uint8Array(m);let Pt=0,j,V;for(j=0;j<tt;j++)for(V=0;V<b;V++)j<M[V].length&&(nt[Pt++]=M[V][j]);for(j=0;j<W;j++)for(V=0;V<b;V++)nt[Pt++]=Q[V][j];return nt}function D(y,C,k,m){let A;if(Array.isArray(y))A=v.fromArray(y);else if(typeof y=="string"){let U=C;if(!U){const H=v.rawSplit(y);U=c.getBestVersionForData(H,k)}A=v.fromString(y,U||40)}else throw new Error("Invalid data");const x=c.getBestVersionForData(A,k);if(!x)throw new Error("The amount of data is too big to be stored in a QR Code");if(!C)C=x;else if(C<x)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+x+`.
`);const b=B(C,k,A),g=r.getSymbolSize(C),T=new n(g);return f(T,C),R(T),I(T,C),_(T,k,0),C>=7&&S(T,C),P(T,b),isNaN(m)&&(m=i.getBestMask(T,_.bind(null,T,k))),i.applyMask(m,T),_(T,k,m),{modules:T,version:C,errorCorrectionLevel:k,maskPattern:m,segments:A}}return st.create=function(C,k){if(typeof C>"u"||C==="")throw new Error("No input text");let m=o.M,A,x;return typeof k<"u"&&(m=o.from(k.errorCorrectionLevel,o.M),A=c.from(k.version),x=i.from(k.maskPattern),k.toSJISFunc&&r.setToSJISFunction(k.toSJISFunc)),D(C,A,m,x)},st}var Et={},Bt={},ee;function le(){return ee||(ee=1,function(r){function o(s){if(typeof s=="number"&&(s=s.toString()),typeof s!="string")throw new Error("Color should be defined as hex string");let n=s.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+s);(n.length===3||n.length===4)&&(n=Array.prototype.concat.apply([],n.map(function(e){return[e,e]}))),n.length===6&&n.push("F","F");const t=parseInt(n.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:t&255,hex:"#"+n.slice(0,6).join("")}}r.getOptions=function(n){n||(n={}),n.color||(n.color={});const t=typeof n.margin>"u"||n.margin===null||n.margin<0?4:n.margin,e=n.width&&n.width>=21?n.width:void 0,i=n.scale||4;return{width:e,scale:e?4:i,margin:t,color:{dark:o(n.color.dark||"#000000ff"),light:o(n.color.light||"#ffffffff")},type:n.type,rendererOpts:n.rendererOpts||{}}},r.getScale=function(n,t){return t.width&&t.width>=n+t.margin*2?t.width/(n+t.margin*2):t.scale},r.getImageWidth=function(n,t){const e=r.getScale(n,t);return Math.floor((n+t.margin*2)*e)},r.qrToImageData=function(n,t,e){const i=t.modules.size,l=t.modules.data,a=r.getScale(i,e),c=Math.floor((i+e.margin*2)*a),h=e.margin*a,E=[e.color.light,e.color.dark];for(let v=0;v<c;v++)for(let f=0;f<c;f++){let R=(v*c+f)*4,I=e.color.light;if(v>=h&&f>=h&&v<c-h&&f<c-h){const S=Math.floor((v-h)/a),_=Math.floor((f-h)/a);I=E[l[S*i+_]?1:0]}n[R++]=I.r,n[R++]=I.g,n[R++]=I.b,n[R]=I.a}}}(Bt)),Bt}var ne;function He(){return ne||(ne=1,function(r){const o=le();function s(t,e,i){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=i,e.width=i,e.style.height=i+"px",e.style.width=i+"px"}function n(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}r.render=function(e,i,l){let a=l,c=i;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),i||(c=n()),a=o.getOptions(a);const h=o.getImageWidth(e.modules.size,a),E=c.getContext("2d"),v=E.createImageData(h,h);return o.qrToImageData(v.data,e,a),s(E,c,h),E.putImageData(v,0,0),c},r.renderToDataURL=function(e,i,l){let a=l;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),a||(a={});const c=r.render(e,i,a),h=a.type||"image/png",E=a.rendererOpts||{};return c.toDataURL(h,E.quality)}}(Et)),Et}var _t={},re;function Je(){if(re)return _t;re=1;const r=le();function o(t,e){const i=t.a/255,l=e+'="'+t.hex+'"';return i<1?l+" "+e+'-opacity="'+i.toFixed(2).slice(1)+'"':l}function s(t,e,i){let l=t+e;return typeof i<"u"&&(l+=" "+i),l}function n(t,e,i){let l="",a=0,c=!1,h=0;for(let E=0;E<t.length;E++){const v=Math.floor(E%e),f=Math.floor(E/e);!v&&!c&&(c=!0),t[E]?(h++,E>0&&v>0&&t[E-1]||(l+=c?s("M",v+i,.5+f+i):s("m",a,0),a=0,c=!1),v+1<e&&t[E+1]||(l+=s("h",h),h=0)):a++}return l}return _t.render=function(e,i,l){const a=r.getOptions(i),c=e.modules.size,h=e.modules.data,E=c+a.margin*2,v=a.color.light.a?"<path "+o(a.color.light,"fill")+' d="M0 0h'+E+"v"+E+'H0z"/>':"",f="<path "+o(a.color.dark,"stroke")+' d="'+n(h,c,a.margin)+'"/>',R='viewBox="0 0 '+E+" "+E+'"',S='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+R+' shape-rendering="crispEdges">'+v+f+`</svg>
`;return typeof l=="function"&&l(null,S),S},_t}var oe;function Ye(){if(oe)return G;oe=1;const r=Ae(),o=Ke(),s=He(),n=Je();function t(e,i,l,a,c){const h=[].slice.call(arguments,1),E=h.length,v=typeof h[E-1]=="function";if(!v&&!r())throw new Error("Callback required as last argument");if(v){if(E<2)throw new Error("Too few arguments provided");E===2?(c=l,l=i,i=a=void 0):E===3&&(i.getContext&&typeof c>"u"?(c=a,a=void 0):(c=a,a=l,l=i,i=void 0))}else{if(E<1)throw new Error("Too few arguments provided");return E===1?(l=i,i=a=void 0):E===2&&!i.getContext&&(a=l,l=i,i=void 0),new Promise(function(f,R){try{const I=o.create(l,a);f(e(I,i,a))}catch(I){R(I)}})}try{const f=o.create(l,a);c(null,e(f,i,a))}catch(f){c(f)}}return G.create=o.create,G.toCanvas=t.bind(null,s.render),G.toDataURL=t.bind(null,s.renderToDataURL),G.toString=t.bind(null,function(e,i,l){return n.render(e,l)}),G}var $e=Ye();const Ge=_e($e),Qe={class:"h-screen w-full flex flex-col overflow-x-hidden bg-gray-50 dark:bg-gray-900"},Xe={class:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4"},Ze={class:"max-w-4xl mx-auto flex justify-between items-center"},We={class:"flex items-center space-x-4"},tn={key:0,class:"flex items-center space-x-2"},en={key:1,class:"flex items-center space-x-4"},nn={class:"text-sm"},rn={class:"ml-1 font-medium text-gray-900 dark:text-white"},on={class:"text-sm"},sn={class:"ml-1 font-medium text-gray-900 dark:text-white"},an={key:2,class:"text-sm text-gray-500 dark:text-gray-400"},ln={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},un={class:"max-w-4xl mx-auto"},dn={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},cn={class:"ml-11"},fn={class:"flex justify-between items-center mb-4"},gn={key:0,class:"text-center py-8"},hn={key:1,class:"space-y-4"},mn=["onClick"],yn={class:"flex justify-between items-center"},vn={class:"font-medium text-gray-900 dark:text-white"},pn={class:"text-sm text-gray-500 dark:text-gray-400"},bn={class:"text-right"},wn={class:"text-lg font-semibold text-gray-900 dark:text-white"},xn={class:"text-xs text-gray-500 dark:text-gray-400"},Cn={key:2,class:"text-center py-8"},kn={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},En={class:"ml-11"},Bn={class:"text-center mb-4"},_n=["disabled"],An={key:0,class:"text-center"},Rn={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},Pn={class:"ml-11"},Tn={key:0,class:"text-center py-8"},In={key:1,class:"space-y-4"},Mn={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600"},Sn={key:0,class:"text-center"},Nn={key:0,class:"text-sm text-green-600 dark:text-green-400 font-medium"},Ln={key:1,class:"text-sm text-red-600 dark:text-red-400 font-medium"},Dn={key:1,class:"text-center"},Fn={key:2,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},qn={key:3,class:"text-center py-8"},Un={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},zn={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},jn={class:"flex justify-between items-center mb-4"},Vn={class:"space-y-4"},On={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},Kn=["disabled"],Hn={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},Jn=["disabled"],Yn={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},$n={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},Gn={class:"flex justify-between items-center mb-4"},Qn={class:"text-center space-y-4"},Xn={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Zn={class:"font-medium text-gray-900 dark:text-white mb-2"},Wn={class:"text-2xl font-bold text-green-600 dark:text-green-400"},tr={class:"text-sm text-gray-500 dark:text-gray-400"},er={class:"flex justify-center"},nr={key:0,class:"flex items-center space-x-2"},rr={key:1,class:"bg-white p-4 rounded-lg"},or=["src"],sr={class:"text-sm text-gray-600 dark:text-gray-300 space-y-2"},ir={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg"},ar={class:"flex items-center space-x-2"},lr={class:"text-sm text-blue-600 dark:text-blue-400"},ur={class:"flex space-x-2"},dr=["disabled"],At=300,cr=ce({__name:"Activated",setup(r){const o=fe(),s=Be(),n=F(""),t=F(""),e=F(!1),i=F(""),l=F(null),a=F(!1),c=F([]),h=F(null),E=F(!1),v=F(!1),f=F(!1),R=F(null),I=F(""),S=F(!1),_=F(!1),P=F(!1),B=F(null),p=F(0);ge(()=>{o.initTheme();const d=new URLSearchParams(window.location.search).get("deviceCode");d?n.value=d:i.value="未找到设备码参数",C(),y()}),he(()=>{b()});const D=async()=>{if(!h.value){i.value="请先选择一个许可证";return}try{e.value=!0,i.value="";const w=await s.generateActivateCode(n.value,h.value.orderId);w&&(t.value=w)}catch(w){i.value=w instanceof Error?w.message:"生成激活码失败",console.error("Failed to generate activate code:",w)}finally{e.value=!1}},y=async()=>{try{v.value=!0;const w=await me();w.success?c.value=w.data||[]:console.error("Failed to load licenses:",w.error)}catch(w){console.error("Failed to load licenses:",w)}finally{v.value=!1}},C=async()=>{try{a.value=!0;const w=await ye();w.success&&w.data?l.value=w.data:console.error("Failed to load user info:",w.error)}catch(w){console.error("Failed to load user info:",w)}finally{a.value=!1}},k=w=>{h.value=w},m=async w=>{try{f.value=!0;const M=await we({licenseType:w,payMethod:"wechat",payPrice:w===1?9900:19900});M.success?(R.value=M.data,E.value=!1,S.value=!0,await A(M.data.wxCodeUrl),x(M.data.orderId)):console.error("Failed to create order:",M.error)}catch(d){console.error("Failed to create order:",d)}finally{f.value=!1}},A=async w=>{try{_.value=!0;const d=await Ge.toDataURL(w,{width:256,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});I.value=d}catch(d){console.error("Failed to generate QR code:",d)}finally{_.value=!1}},x=w=>{if(P.value)return;P.value=!0,p.value=0;const d=async()=>{try{p.value++,console.log(`Polling order status, attempt ${p.value}`),(await xe({orderId:w})).success?(console.log("Order payment successful!"),b(),S.value=!1,await y(),alert("支付成功! 您现在可以使用许可证生成激活码了！")):p.value>=At?(console.log("Max polling count reached, stopping polling"),b(),alert("支付超时，请重新创建订单。")):B.value=setTimeout(d,1e3)}catch(M){console.error("Error polling order status:",M),p.value>=At?(b(),alert("检查订单状态失败，请手动刷新页面。")):B.value=setTimeout(d,1e3)}};d()},b=()=>{B.value&&(clearTimeout(B.value),B.value=null),P.value=!1,p.value=0},g=F(""),T=async()=>{try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(t.value),g.value="复制成功!",console.log("Activate code copied to clipboard");else{const w=document.createElement("textarea");w.value=t.value,w.style.position="fixed",w.style.left="-999999px",w.style.top="-999999px",document.body.appendChild(w),w.focus(),w.select();const d=document.execCommand("copy");document.body.removeChild(w),d?(g.value="复制成功!",console.log("Activate code copied to clipboard")):(g.value="复制失败，请手动复制",console.error("Failed to copy activate code"))}setTimeout(()=>{g.value=""},3e3)}catch(w){g.value="复制失败，请手动复制",console.error("Failed to copy activate code:",w)}},U=()=>{const w=document.querySelector(".activate-code-text");if(w){const d=document.createRange();d.selectNodeContents(w);const M=window.getSelection();M&&(M.removeAllRanges(),M.addRange(d),g.value="文本已选中，请按 Ctrl+C 复制",setTimeout(()=>{g.value=""},3e3))}},H=w=>w===1?"一年有效期":"永久有效期",Z=w=>(w/100).toFixed(0)+"元",W=w=>new Date(w).toLocaleDateString("zh-CN");return(w,d)=>(L(),N("div",Qe,[u("div",Xe,[u("div",Ze,[u("div",We,[d[10]||(d[10]=u("div",{class:"flex items-center space-x-2"},[u("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})]),u("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"用户信息")],-1)),a.value?(L(),N("div",tn,d[7]||(d[7]=[u("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1),u("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"加载中...",-1)]))):l.value?(L(),N("div",en,[u("div",nn,[d[8]||(d[8]=u("span",{class:"text-gray-500 dark:text-gray-400"},"ID:",-1)),u("span",rn,q(l.value.uniqueId),1)]),u("div",on,[d[9]||(d[9]=u("span",{class:"text-gray-500 dark:text-gray-400"},"昵称:",-1)),u("span",sn,q(l.value.username),1)])])):(L(),N("div",an," 未获取到用户信息 "))]),u("div",{class:"flex items-center space-x-2"},[u("button",{onClick:C,class:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200"}," 刷新 ")])])]),u("div",ln,[u("div",un,[d[22]||(d[22]=u("div",{class:"text-center mb-8"},[u("h1",{class:"text-3xl font-bold mb-4 text-gray-900 dark:text-white"}," XCodeMap 激活码生成 ")],-1)),u("div",dn,[d[14]||(d[14]=u("div",{class:"flex items-center mb-4"},[u("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 1 "),u("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第一步 选择许可证 ")],-1)),u("div",cn,[u("div",fn,[d[11]||(d[11]=u("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," 请选择一个可用的许可证来生成激活码 ",-1)),u("button",{onClick:d[0]||(d[0]=M=>E.value=!0),class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200"}," 购买许可证 ")]),v.value?(L(),N("div",gn,d[12]||(d[12]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在加载许可证...",-1)]))):c.value.length>0?(L(),N("div",hn,[(L(!0),N(ve,null,pe(c.value,M=>{var Q;return L(),N("div",{key:M.id,onClick:tt=>k(M),class:be(["p-4 border rounded-lg cursor-pointer transition-colors duration-200",((Q=h.value)==null?void 0:Q.id)===M.id?"border-blue-500 bg-blue-50 dark:bg-blue-900":"border-gray-300 dark:border-gray-600 hover:border-blue-300"])},[u("div",yn,[u("div",null,[u("h3",vn,q(H(M.licenseType)),1),u("p",pn," 过期时间: "+q(W(M.activationExpiredTime)),1)]),u("div",bn,[u("p",wn,q(Z(M.payPrice)),1),u("p",xn," 订单号: "+q(M.orderId),1)])])],10,mn)}),128))])):(L(),N("div",Cn,d[13]||(d[13]=[u("p",{class:"text-gray-500 dark:text-gray-400"},"暂无可用许可证",-1)])))])]),u("div",kn,[d[16]||(d[16]=u("div",{class:"flex items-center mb-4"},[u("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 2 "),u("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第二步 生成激活码 ")],-1)),u("div",En,[u("div",Bn,[u("button",{onClick:D,disabled:e.value||!h.value,class:"px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-md transition-colors duration-200"},q(e.value?"正在生成激活码...":"生成激活码"),9,_n)]),h.value?O("",!0):(L(),N("div",An,d[15]||(d[15]=[u("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请先选择许可证 ",-1)])))])]),u("div",Rn,[d[21]||(d[21]=u("div",{class:"flex items-center mb-4"},[u("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 3 "),u("div",null,[u("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第三步 获取激活码 "),u("p",{class:"text-sm text-gray-500 dark:text-gray-400 mt-1"}," 请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ")])],-1)),u("div",Pn,[e.value?(L(),N("div",Tn,d[17]||(d[17]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在生成激活码...",-1)]))):t.value&&!e.value?(L(),N("div",In,[u("div",Mn,[u("code",{class:"activate-code-text text-sm font-mono break-all text-gray-900 dark:text-gray-100 font-medium select-all cursor-pointer",onClick:T,title:"点击复制激活码"},q(t.value),1)]),u("div",{class:"flex justify-center space-x-4"},[u("button",{onClick:T,class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 复制激活码 "),u("button",{onClick:U,class:"px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 选择文本 ")]),g.value?(L(),N("div",Sn,[g.value==="复制成功!"?(L(),N("span",Nn," ✓ "+q(g.value),1)):(L(),N("span",Ln," ✗ "+q(g.value),1))])):O("",!0),g.value==="复制失败，请手动复制"?(L(),N("div",Dn,d[18]||(d[18]=[u("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 您可以点击上方的激活码文本进行手动复制 ",-1)]))):O("",!0),d[19]||(d[19]=u("div",{class:"text-center"},[u("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 激活码已生成，请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ")],-1))])):i.value?(L(),N("div",Fn,q(i.value),1)):(L(),N("div",qn,d[20]||(d[20]=[u("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请先完成前两步操作 ",-1)])))])])])]),E.value?(L(),N("div",Un,[u("div",zn,[u("div",jn,[d[24]||(d[24]=u("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 购买许可证 ",-1)),u("button",{onClick:d[1]||(d[1]=M=>E.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},d[23]||(d[23]=[u("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",Vn,[u("div",On,[d[25]||(d[25]=Tt('<div class="flex justify-between items-center" data-v-42db0dc5><div data-v-42db0dc5><h4 class="font-medium text-gray-900 dark:text-white" data-v-42db0dc5>一年有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-42db0dc5>适合短期使用</p></div><div class="text-right" data-v-42db0dc5><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-42db0dc5>99元</p></div></div>',1)),u("button",{onClick:d[2]||(d[2]=M=>m(1)),disabled:f.value,class:"w-full mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},q(f.value?"创建订单中...":"立即购买"),9,Kn)]),u("div",Hn,[d[26]||(d[26]=Tt('<div class="flex justify-between items-center" data-v-42db0dc5><div data-v-42db0dc5><h4 class="font-medium text-gray-900 dark:text-white" data-v-42db0dc5>永久有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-42db0dc5>一次购买，永久使用</p></div><div class="text-right" data-v-42db0dc5><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-42db0dc5>199元</p></div></div>',1)),u("button",{onClick:d[3]||(d[3]=M=>m(2)),disabled:f.value,class:"w-full mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},q(f.value?"创建订单中...":"立即购买"),9,Jn)])])])])):O("",!0),S.value?(L(),N("div",Yn,[u("div",$n,[u("div",Gn,[d[28]||(d[28]=u("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 微信支付 ",-1)),u("button",{onClick:d[4]||(d[4]=M=>S.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},d[27]||(d[27]=[u("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",Qn,[R.value?(L(),N("div",Xn,[u("h4",Zn,q(R.value.licenseType===1?"一年有效期许可证":"永久许可证"),1),u("p",Wn,q(Z(R.value.payPrice)),1),u("p",tr," 订单号: "+q(R.value.orderId),1)])):O("",!0),u("div",er,[_.value?(L(),N("div",nr,d[29]||(d[29]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("span",{class:"text-sm text-gray-600 dark:text-gray-300"},"生成二维码中...",-1)]))):I.value?(L(),N("div",rr,[u("img",{src:I.value,alt:"微信支付二维码",class:"w-64 h-64"},null,8,or)])):O("",!0)]),u("div",sr,[d[32]||(d[32]=u("p",null,"请使用微信扫描二维码完成支付",-1)),d[33]||(d[33]=u("p",null,"支付成功后，您可以使用许可证生成激活码",-1)),P.value?(L(),N("div",ir,[u("div",ar,[d[30]||(d[30]=u("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1)),u("span",lr," 正在检查支付状态... ("+q(p.value)+"/"+q(At)+") ",1)]),d[31]||(d[31]=u("p",{class:"text-xs text-blue-500 dark:text-blue-300 mt-1"}," 系统正在自动检查支付状态，请稍候 ",-1))])):O("",!0)]),u("div",ur,[u("button",{onClick:d[5]||(d[5]=M=>S.value=!1),disabled:P.value,class:"flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},q(P.value?"检查中...":"关闭"),9,dr),P.value?O("",!0):(L(),N("button",{key:0,onClick:d[6]||(d[6]=()=>{S.value=!1,y()}),class:"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"}," 刷新许可证 "))])])])])):O("",!0)]))}}),fr=Ce(cr,[["__scopeId","data-v-42db0dc5"]]),ue=ke(fr);ue.use(Ee());ue.mount("#app");
