<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Same Function List</title>
    <link href="../assets/element-ui-2.15.14/index.min.css" rel="stylesheet">
    <link href="../assets/xcodemap/theme.css" rel="stylesheet">
    <script src="../assets/xcodemap/common.js"></script>
    <script src="../assets/xcodemap/api.js"></script>
</head>
<body>
<div id="app">
    <el-container>
        <el-header height="40px" class="status-bar">
            <div class="status-message">{{ statusMessage }}</div>
        </el-header>
        <div class="left-container">
            <el-scrollbar view-style="height: 100%;" :native="false">
                <div class="func-notes">
                    <el-input
                        placeholder="输入关键字展开匹配节点"
                        v-model="filterText"
                        class="filter-input">
                    </el-input>
                    <el-tree
                        :data="functionDetailList.treeData.rootNodes"
                        :highlight-current="true"
                        :indent="0"
                        :props="defaultProps"
                        class="function-call-detail-list-tree"
                        @node-click="clickFuncTreeElement"
                        node-key="nodeKey"
                        :render-content="functionDetailList.renderContent"
                        ref="functionDetailListRef">
                    </el-tree>
                </div>
            </el-scrollbar>
        </div>
    </el-container>
</div>

<script src="../assets/axios-1.6.3/axios.min.js"></script>
<script src="../assets/vue-2.5.22/vue.min.js"></script>
<script src="../assets/element-ui-2.15.14/index.min.js"></script>

<script>
    var vue = new Vue({
        el: '#app',
        data: function () {
            return {
                filterText: '',
                project: NewProject(),
                functionDetailList: NewLocatorTree(),
                statusMessage: '加载中。。',
                defaultProps: {
                    children: 'children',
                    label: 'label',
                    nodeKey: 'nodeKey',
                    isLeaf: 'isLeaf'
                }
            }
        },
        created: async function () {
            this.project.doInitProjectInfo();
            this.functionDetailList.treeData = {
                rootNodes: []
            }
            // 设置 Vue 实例引用，以便 NewLocatorTree 可以使用 $set
            this.functionDetailList.vue = this;
            this.functionDetailList.$refs = this.$refs;
            this.functionDetailList.$refsName = 'functionDetailListRef';
            
            try {
                const currFuncCallDetail = await getFunctionCallDetail({
                    processId: this.project.processId,
                    threadId: this.project.threadId,
                    callId: this.project.currCallId
                });
                // Search for callIds
                const searchParams = {
                    processId: this.project.processId,
                    threadId: this.project.threadId,
                    rootFunc: this.project.rootFunc,
                    currCallId: this.project.currCallId,
                    namedSelector: {
                        type: this.project.searchType,
                        name: this.project.searchName, 
                        id: this.project.searchId,
                        scope: this.project.searchScope,
                    }
                };
                
                const callIdsResponse = await searchCallIds(searchParams);
                let codePosition = '';
                let scopeText = '';
                if (this.project.searchScope === 'usage') {
                    codePosition = currFuncCallDetail.data.usageCodePosition;
                    scopeText = '用法';
                } else if (this.project.searchScope === 'definition') {
                    codePosition = currFuncCallDetail.data.definitionCodePosition;
                    scopeText = '定义';
                }
                const baseStatueMessage = `在 ${scopeText}位置 ${codePosition} 处找到函数 ${currFuncCallDetail.data.funcName} 的 ${callIdsResponse.data.funcCallIds.length} 个调用`;
                console.log("callIdsResponse", searchParams, callIdsResponse);
                // Update status to show number of calls found
                this.statusMessage = baseStatueMessage + "。开始加载...";
                
                // Get function call details for each callId
                for (let i = 0; i < callIdsResponse.data.funcCallIds.length; i++) {
                    const callId = callIdsResponse.data.funcCallIds[i];
                    const detailParams = {
                        processId: this.project.processId,
                        threadId: this.project.threadId,
                        callId: callId
                    };
                    
                    // Update status to show current call loading progress
                    this.statusMessage = baseStatueMessage + `。正在加载第 ${i + 1}/${callIdsResponse.data.funcCallIds.length} 个调用...`;
                    
                    const functionDetail = await getFunctionCallDetail(detailParams);
                    // Add the function detail to the tree
                    if (functionDetail.data.headNode && functionDetail.data.treeData && functionDetail.data.treeData.rootNodes) {
                        /* const detailPromises = functionDetail.data.treeData.rootNodes.map(async node => {
                            await this.fetchAndSetObjectDetail(node, this.project, 2);
                        });

                        await Promise.all(detailPromises); */
                        
                        //check and init the children of headNode
                        if (!functionDetail.data.headNode.children) {
                            functionDetail.data.headNode.children = [];
                        }
                        const funcSelfNode =  {
                            label: "跳转源码",
                            labelKey: "url",
                            labelValue: "./findFunc?callId=" + callId,
                            children: [],
                            alwaysShow: true,
                            leaf: true
                        }

                        // Add rootNodes to headNode's children
                        functionDetail.data.headNode.children.push(funcSelfNode);
                        functionDetail.data.headNode.children.push(...functionDetail.data.treeData.rootNodes);
                        // Add the headNode to the tree
                        this.functionDetailList.treeData.rootNodes.push(functionDetail.data.headNode);
                    }
                }
                
                this.statusMessage = baseStatueMessage + "。加载完成。";
            } catch (error) {
                console.error('Error fetching function details:', error);
                this.statusMessage = '加载失败: ' + error.message;
            }
        },
        mounted: function() {
            // 在组件挂载后，确保树组件已经渲染完成
            this.$nextTick(() => {
                // 初始化时折叠所有节点
                this.functionDetailList.collapseAllNodes();
            });
        },
        watch: {
            filterText(val) {
                this.functionDetailList.setFilterText(val);
            }
        },
        methods: {
            /**
             * 通用的函数：基于node获取objectDetail并进行设置
             * @param {Object} node - 要处理的节点
             * @param {Object} project - 项目信息对象
             * @param {number} depth - 深度，默认为4
             * @returns {Promise} 返回Promise对象
             */
            async fetchAndSetObjectDetail(node, project, depth = 4) {
                if (!node.dataId || !node.nodeKey || !node.nodeKey.startsWith("object/")) {
                    return;
                }
                
                const detailParams = {
                    processId: project.processId,
                    threadId: project.threadId,
                    callId: project.currCallId,
                    labelKey: node.labelKey,
                    maxVersion: node.maxVersion,
                    uniqId: node.dataId,
                    depth: depth
                };
                
                try {
                    const objectDetail = await getObjectDetail(detailParams);
                    if (objectDetail.data && objectDetail.data.rootNodes && objectDetail.data.rootNodes.length > 0) {
                        const detailNode = objectDetail.data.rootNodes[0];
                        // Use Vue.set to ensure reactivity
                        this.$set(node, 'labelKey', detailNode.labelKey);
                        this.$set(node, 'labelValue', detailNode.labelValue);
                        this.$set(node, 'label', detailNode.label);
                        this.$set(node, 'maxVersion', detailNode.maxVersion);
                        this.$set(node, 'children', detailNode.children);
                        this.$set(node, 'leaf', detailNode.leaf);
                    }
                } catch (error) {
                    console.error('Error fetching object detail:', error);
                }
            },
            
            clickFuncTreeElement(data) {
                if (data.labelKey === "url") {
                    const url = data.labelValue;
                    window.open(url, '_blank');
                    return;
                }
                if (data.nodeKey && data.nodeKey.startsWith("object/") && data.dataId) {
                    if (data["dataLoaded"] === false) {
                         this.fetchAndSetObjectDetail(data, this.project, 1);
                    }
                }
            }
        }
    });
</script>

<style>
    html, body {
        margin: 0px;
        width: 600px;
        height: 100vh;
        overflow: hidden;
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    #app {
        height: 100vh;
        background-color: var(--bg-color);
    }

    .el-container {
        height: 100vh;
        background-color: var(--bg-color);
    }

    .status-bar {
        display: flex;
        align-items: center;
        padding: 0 20px;
    }

    .status-message {
        font-size: 14px;
    }

    .el-scrollbar {
        height: calc(100vh - 40px);
        background-color: var(--bg-color);
    }

    .el-scrollbar__bar.is-vertical {
        width: 10px;
        right: 0;
        background-color: var(--undercaret-bg-color);
    }

    .el-scrollbar__bar.is-horizontal {
        height: 10px;
        bottom: 0;
        background-color: var(--undercaret-bg-color);
    }

    .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
        white-space: nowrap;
        background-color: var(--bg-color);
    }

    .el-scrollbar__thumb {
        background-color: var(--text-color);
        border-radius: 5px;
        opacity: 0.6;
    }

    .el-scrollbar__thumb:hover {
        background-color: var(--text-color);
        opacity: 0.8;
    }

    .el-tree-node {
        position: relative;
    }

    .el-tree-node::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        left: -3px;
        top: -26px;
        border-width: 1px;
        border-left: 1px dashed #52627c;
    }

    .el-tree-node:last-child::before {
        height: 38px;
    }

    .el-tree-node::after {
        content: '';
        width: 14px;
        height: 10px;
        position: absolute;
        left: -3px;
        top: 12px;
        border-width: 1px;
        border-top: 1px dashed #52627c;
    }

    .el-tree-node__label {
        white-space: normal;
        word-break: break-all;
    }

    .el-tree-node__children {
        padding-left: 10px;
    }

    .el-tree-node__content {
        height: inherit;
        padding-bottom: 5px;
    }

    .el-tree-node__content>.el-tree-node__expand-icon {
        padding: 1px;
    }

    .func-notes {
        padding-left: 12px;
        word-wrap: break-word;
        white-space: pre-wrap;
    }

    .filter-input {
        margin-bottom: 2px;
    }

    .filter-input .el-input__inner {
        height: 28px;
    }

    .left-aside {
        display: block;
        margin-right: 5px;
        justify-content: flex-end;
    }

    .left-container {
        position: relative;
        height: calc(100vh - 40px);
        background-color: var(--bg-color);
    }
</style>
</html> 