var gn=Object.defineProperty;var mn=(r,e,t)=>e in r?gn(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var N=(r,e,t)=>mn(r,typeof e!="symbol"?e+"":e,t);import{d as ot,r as M,u as _t,c as Ue,g as bn,a as xn,b as vn,e as Pt,f as wn,p as kn,s as yn,h as Zt,i as Ur,w as wt,j as T,o as k,F as me,k as Ce,l as d,m as O,n as C,q as f,t as q,_ as Br,v as Fr,x as Tn,y as _n,z as En,A as zt,B as Se,C as Ae,D as Sn,E as Cn,G as An,H as Rn,I as Dn,J as Mn,K as $t,L as Ln,M as In,N as Nn,O as On,P as Pn,Q as zn,R as $n,S as Un}from"./style-D6mjsjaf.js";const Bn=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href,Kt=ot("chatStatus",()=>{const r=M("idle"),e=M("");return{status:r,statusMessage:e,setStatus:s=>{r.value=s},setStatusMessage:s=>{e.value=s}}}),Hr=ot("modelStatus",()=>{const r=M(null),e=M(null),t=_t(),n=Ue(()=>!r.value||!e.value?null:e.value.modelDescList.find(l=>l.uuid===r.value)),s=Ue(()=>{var l;return((l=e.value)==null?void 0:l.modelDescList)||[]});return{currentModelUuid:r,modelConfig:e,currentModel:n,availableModels:s,getModelConfigData:async()=>{var l;try{const h=await bn();if(h.success&&h.data)e.value=h.data,h.data.defaultModelId?h.data.modelDescList.find(p=>p.uuid===h.data.defaultModelId)?r.value=h.data.defaultModelId:r.value=((l=h.data.modelDescList[0])==null?void 0:l.uuid)||null:!r.value&&h.data.modelDescList.length>0&&(r.value=h.data.modelDescList[0].uuid);else throw new Error(h.error||"Failed to get model config")}catch(h){console.error("Failed to get model config:",h),t.setError(h instanceof Error?h.message:"Failed to get model config","GET_MODEL_CONFIG_ERROR")}},setCurrentModel:l=>{var i;((i=e.value)==null?void 0:i.modelDescList.find(p=>p.uuid===l))&&(r.value=l)}}}),ue="EMPTY_PLACE_HOLDER",Gr=ot("database",()=>{const r=M([{id:ue,name:"待选择数据源(取消行号X图标)",recordState:"idle",dataId:null,active:!1,serverSelected:!1}]),e=M(ue),t=Ue(()=>r.value.find(b=>b.id===e.value)||null),n=b=>{r.value.push(b)},s=b=>{e.value=b},a=async(b,g)=>{const _=r.value.find(v=>v.id===b);if(_)try{await xn({executionId:b,cmd:"change",state:g}),_.recordState="preparing"}catch(v){console.error("Failed to change record state:",v)}};return{databases:r,currentDatabase:t,currentDatabaseId:e,addDatabase:n,setCurrentDatabase:s,changeState:a,queryState:b=>{var g;return(g=r.value.find(_=>_.id===b))==null?void 0:g.recordState},startRecord:b=>{const g=r.value.find(_=>_.id===b);g&&g.recordState==="idle"&&a(b,"start")},endRecord:b=>{const g=r.value.find(_=>_.id===b);g&&g.recordState==="recording"&&a(b,"stop")},restartRecord:b=>{const g=r.value.find(_=>_.id===b);g&&g.recordState==="paused"&&(a(b,"start"),Wr().createNewChat())},getDatabase:async()=>{try{const b=await vn();r.value=[r.value[0]],b.forEach(g=>{n(g)})}catch(b){console.error("Failed to fetch process data:",b)}}}}),Qt=ot("inputBox",()=>{const r=M(""),e=_t(),t=Kt(),n=Gr(),s=()=>!n.currentDatabase||!n.currentDatabase.dataId||t.status==="waiting"||n.currentDatabaseId===ue;return{message:r,appendText:i=>{if(s())return e.setError("当前状态不可添加文本到输入框"),!1;const p=r.value&&!r.value.endsWith(" ");return r.value+=(p?" ":"")+i,window.dispatchEvent(new CustomEvent("textarea-content-changed")),!0},setText:i=>{r.value=i,window.dispatchEvent(new CustomEvent("textarea-content-changed"))},clearText:()=>{r.value="",window.dispatchEvent(new CustomEvent("textarea-content-changed"))},getText:()=>r.value,isDisabled:s}}),Fn="SystemStatus",Hn="AddToChat",Wr=ot("chat",()=>{const r=M([]),e=M(null),t=M(!1);let n=null;const s=_t(),a=Kt(),c=Hr(),l=Qt(),h=Ue(()=>r.value.find(S=>S.id===e.value)),i=async()=>{var S;try{e.value&&await Pt(e.value);const A={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await wn(A.id,((S=c.currentModel)==null?void 0:S.uuid)??""))return r.value=[A],e.value=A.id,t.value||p(),A;throw new Error("Failed to create chat channel")}catch(A){throw console.error("Failed to create chat channel:",A),s.setError(A instanceof Error?A.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),e.value=null,r.value=[],A}},p=()=>{n&&clearInterval(n),t.value=!0,n=window.setInterval(async()=>{if(t.value)try{const S=await kn();if(S&&S.length>0)for(const A of S){if(A.messageId===Hn){l.appendText(A.content);continue}if(A.messageId===Fn){a.setStatus("waiting"),a.setStatusMessage(A.content);continue}const B=r.value.find(D=>D.id===A.chatId);if(B){a.setStatus("waiting");const D=B.messages[B.messages.length-1];D&&D.role===A.role?D.content+=A.content:B.messages.push(A),B.updatedAt=Date.now()}}else a.setStatus("sending"),a.setStatusMessage("正在思考中...")}catch(S){console.error("Failed to poll messages:",S),s.setError(S instanceof Error?S.message:"Failed to poll messages","POLL_ERROR")}},1e3)},b=()=>{n&&(clearInterval(n),n=null),t.value=!1};return{chats:r,currentChatId:e,currentChat:h,createNewChat:i,sendMessage:async S=>{e.value||await i();const A=h.value;if(!A)return;if(!c.currentModel){s.setError("请先选择模型再发送消息","SEND_MESSAGE_ERROR");return}a.setStatus("waiting");const B={messageId:crypto.randomUUID(),content:S,role:"user",timestamp:Date.now(),chatId:e.value,modelId:c.currentModel.uuid};try{if(!e.value)return;if(await yn(B))A.messages.push(B),A.updatedAt=Date.now();else throw new Error("会话可能已失效，请重新创建会话")}catch(D){throw console.error("Failed to send message:",D),s.setError(D instanceof Error?D.message:"会话可能已失效，请重新创建会话","SEND_MESSAGE_ERROR"),D}},removeChat:async S=>{try{if(await Pt(S))r.value=r.value.filter(B=>B.id!==S),e.value===S&&(e.value=null);else throw new Error("Failed to remove chat channel")}catch(A){throw console.error("Failed to remove chat:",A),s.setError(A instanceof Error?A.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),A}},startPolling:p,stopPolling:b,cleanup:()=>{b(),e.value&&Pt(e.value).catch(console.error)}}}),Gn={class:"space-y-0.5"},Wn=["onClick"],qn={key:0,class:"p-0.5 rounded flex-shrink-0"},jn=["onClick"],Vn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Yn=["onClick"],Xn={key:0,class:"p-0.5 rounded flex-shrink-0"},Zn=["onClick"],Kn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Qn=["onClick"],Jn=["onClick"],es=Zt({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(r,{emit:e}){const t=r,n=Ur(),s=Qt(),a=M(t.nodes.map(b=>({...b,isExpanded:t.defaultExpanded,children:b.children?b.children.map(g=>({...g,isExpanded:t.defaultExpanded,children:g.children?g.children.map(_=>({..._,isExpanded:t.defaultExpanded})):[]})):[]})));wt(()=>t.nodes,b=>{a.value=b.map(g=>({...g,isExpanded:t.defaultExpanded,children:g.children?g.children.map(_=>({..._,isExpanded:t.defaultExpanded,children:_.children?_.children.map(v=>({...v,isExpanded:t.defaultExpanded})):[]})):[]}))},{deep:!0});const c=e,l=b=>{c("nodeClick",b)},h=b=>{b.isExpanded=!b.isExpanded},i=(b,g)=>{b.children&&b.children.length>0?h(b):l(b)},p=(b,g)=>{g.stopPropagation(),b.chatText&&s.setText(b.chatText)};return(b,g)=>(k(),T("div",Gn,[(k(!0),T(me,null,Ce(a.value,_=>(k(),T("div",{key:_.nodeKey,class:"tree-node"},[d("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:v=>i(_)},[_.children&&_.children.length>0?(k(),T("div",qn,[(k(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":_.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},g[0]||(g[0]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):O("",!0),d("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},q(_.label),3),_.chatText?(k(),T("button",{key:1,onClick:v=>p(_,v),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},g[1]||(g[1]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,jn)):O("",!0)],10,Wn),_.children&&_.children.length>0&&_.isExpanded?(k(),T("div",Vn,[(k(!0),T(me,null,Ce(_.children,v=>(k(),T("div",{key:v.nodeKey,class:"tree-node"},[d("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:S=>i(v)},[v.children&&v.children.length>0?(k(),T("div",Xn,[(k(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":v.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},g[2]||(g[2]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):O("",!0),d("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},q(v.label),3),v.chatText?(k(),T("button",{key:1,onClick:S=>p(v,S),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},g[3]||(g[3]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,Zn)):O("",!0)],10,Yn),v.children&&v.children.length>0&&v.isExpanded?(k(),T("div",Kn,[(k(!0),T(me,null,Ce(v.children,S=>(k(),T("div",{key:S.nodeKey,class:"pl-3"},[d("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:A=>i(S)},[d("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},q(S.label),3),S.chatText?(k(),T("button",{key:0,onClick:A=>p(S,A),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},g[4]||(g[4]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,Jn)):O("",!0)],10,Qn)]))),128))])):O("",!0)]))),128))])):O("",!0)]))),128))]))}}),ts=Br(es,[["__scopeId","data-v-4bd5633e"]]),rs=Zt({__name:"TypeWriter",props:{text:{},speed:{}},emits:["complete"],setup(r,{emit:e}){const t=r,n=e,s=M(""),a=M(0),c=M(!1),l=()=>{s.value="",a.value=0,c.value=!0;const h=setInterval(()=>{a.value<t.text.length?(s.value+=t.text[a.value],a.value++):(clearInterval(h),c.value=!1,n("complete"))},t.speed||50)};return wt(()=>t.text,()=>{l()}),Fr(()=>{l()}),(h,i)=>(k(),T("span",null,q(s.value),1))}});function Jt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var De=Jt();function qr(r){De=r}var at={exec:()=>null};function I(r,e=""){let t=typeof r=="string"?r:r.source;const n={replace:(s,a)=>{let c=typeof a=="string"?a:a.source;return c=c.replace(J.caret,"$1"),t=t.replace(s,c),n},getRegex:()=>new RegExp(t,e)};return n}var J={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:r=>new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}#`),htmlBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}<(?:[a-z].*>|!--)`,"i")},ns=/^(?:[ \t]*(?:\n|$))+/,ss=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,as=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,lt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,os=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,er=/(?:[*+-]|\d{1,9}[.)])/,jr=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Vr=I(jr).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),ls=I(jr).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),tr=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,is=/^[^\n]+/,rr=/(?!\s*\])(?:\\.|[^\[\]\\])+/,cs=I(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",rr).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),us=I(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,er).getRegex(),Et="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",nr=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,ds=I("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",nr).replace("tag",Et).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Yr=I(tr).replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex(),ps=I(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Yr).getRegex(),sr={blockquote:ps,code:ss,def:cs,fences:as,heading:os,hr:lt,html:ds,lheading:Vr,list:us,newline:ns,paragraph:Yr,table:at,text:is},Er=I("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex(),hs={...sr,lheading:ls,table:Er,paragraph:I(tr).replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Er).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex()},fs={...sr,html:I(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",nr).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:at,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:I(tr).replace("hr",lt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Vr).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},gs=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ms=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Xr=/^( {2,}|\\)\n(?!\s*$)/,bs=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,St=/[\p{P}\p{S}]/u,ar=/[\s\p{P}\p{S}]/u,Zr=/[^\s\p{P}\p{S}]/u,xs=I(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,ar).getRegex(),Kr=/(?!~)[\p{P}\p{S}]/u,vs=/(?!~)[\s\p{P}\p{S}]/u,ws=/(?:[^\s\p{P}\p{S}]|~)/u,ks=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Qr=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,ys=I(Qr,"u").replace(/punct/g,St).getRegex(),Ts=I(Qr,"u").replace(/punct/g,Kr).getRegex(),Jr="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",_s=I(Jr,"gu").replace(/notPunctSpace/g,Zr).replace(/punctSpace/g,ar).replace(/punct/g,St).getRegex(),Es=I(Jr,"gu").replace(/notPunctSpace/g,ws).replace(/punctSpace/g,vs).replace(/punct/g,Kr).getRegex(),Ss=I("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Zr).replace(/punctSpace/g,ar).replace(/punct/g,St).getRegex(),Cs=I(/\\(punct)/,"gu").replace(/punct/g,St).getRegex(),As=I(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Rs=I(nr).replace("(?:-->|$)","-->").getRegex(),Ds=I("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Rs).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),kt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ms=I(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",kt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),en=I(/^!?\[(label)\]\[(ref)\]/).replace("label",kt).replace("ref",rr).getRegex(),tn=I(/^!?\[(ref)\](?:\[\])?/).replace("ref",rr).getRegex(),Ls=I("reflink|nolink(?!\\()","g").replace("reflink",en).replace("nolink",tn).getRegex(),or={_backpedal:at,anyPunctuation:Cs,autolink:As,blockSkip:ks,br:Xr,code:ms,del:at,emStrongLDelim:ys,emStrongRDelimAst:_s,emStrongRDelimUnd:Ss,escape:gs,link:Ms,nolink:tn,punctuation:xs,reflink:en,reflinkSearch:Ls,tag:Ds,text:bs,url:at},Is={...or,link:I(/^!?\[(label)\]\((.*?)\)/).replace("label",kt).getRegex(),reflink:I(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",kt).getRegex()},qt={...or,emStrongRDelimAst:Es,emStrongLDelim:Ts,url:I(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ns={...qt,br:I(Xr).replace("{2,}","*").getRegex(),text:I(qt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},gt={normal:sr,gfm:hs,pedantic:fs},Qe={normal:or,gfm:qt,breaks:Ns,pedantic:Is},Os={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Sr=r=>Os[r];function ce(r,e){if(e){if(J.escapeTest.test(r))return r.replace(J.escapeReplace,Sr)}else if(J.escapeTestNoEncode.test(r))return r.replace(J.escapeReplaceNoEncode,Sr);return r}function Cr(r){try{r=encodeURI(r).replace(J.percentDecode,"%")}catch{return null}return r}function Ar(r,e){var a;const t=r.replace(J.findPipe,(c,l,h)=>{let i=!1,p=l;for(;--p>=0&&h[p]==="\\";)i=!i;return i?"|":" |"}),n=t.split(J.splitPipe);let s=0;if(n[0].trim()||n.shift(),n.length>0&&!((a=n.at(-1))!=null&&a.trim())&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(J.slashPipe,"|");return n}function Je(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n&&r.charAt(n-s-1)===e;)s++;return r.slice(0,n-s)}function Ps(r,e){if(r.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<r.length;n++)if(r[n]==="\\")n++;else if(r[n]===e[0])t++;else if(r[n]===e[1]&&(t--,t<0))return n;return t>0?-2:-1}function Rr(r,e,t,n,s){const a=e.href,c=e.title||null,l=r[1].replace(s.other.outputLinkReplace,"$1");n.state.inLink=!0;const h={type:r[0].charAt(0)==="!"?"image":"link",raw:t,href:a,title:c,text:l,tokens:n.inlineTokens(l)};return n.state.inLink=!1,h}function zs(r,e,t){const n=r.match(t.other.indentCodeCompensation);if(n===null)return e;const s=n[1];return e.split(`
`).map(a=>{const c=a.match(t.other.beginningSpace);if(c===null)return a;const[l]=c;return l.length>=s.length?a.slice(s.length):a}).join(`
`)}var yt=class{constructor(r){N(this,"options");N(this,"rules");N(this,"lexer");this.options=r||De}space(r){const e=this.rules.block.newline.exec(r);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(r){const e=this.rules.block.code.exec(r);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:Je(t,`
`)}}}fences(r){const e=this.rules.block.fences.exec(r);if(e){const t=e[0],n=zs(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(r){const e=this.rules.block.heading.exec(r);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const n=Je(t,"#");(this.options.pedantic||!n||this.rules.other.endingSpaceChar.test(n))&&(t=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(r){const e=this.rules.block.hr.exec(r);if(e)return{type:"hr",raw:Je(e[0],`
`)}}blockquote(r){const e=this.rules.block.blockquote.exec(r);if(e){let t=Je(e[0],`
`).split(`
`),n="",s="";const a=[];for(;t.length>0;){let c=!1;const l=[];let h;for(h=0;h<t.length;h++)if(this.rules.other.blockquoteStart.test(t[h]))l.push(t[h]),c=!0;else if(!c)l.push(t[h]);else break;t=t.slice(h);const i=l.join(`
`),p=i.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${i}`:i,s=s?`${s}
${p}`:p;const b=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(p,a,!0),this.lexer.state.top=b,t.length===0)break;const g=a.at(-1);if((g==null?void 0:g.type)==="code")break;if((g==null?void 0:g.type)==="blockquote"){const _=g,v=_.raw+`
`+t.join(`
`),S=this.blockquote(v);a[a.length-1]=S,n=n.substring(0,n.length-_.raw.length)+S.raw,s=s.substring(0,s.length-_.text.length)+S.text;break}else if((g==null?void 0:g.type)==="list"){const _=g,v=_.raw+`
`+t.join(`
`),S=this.list(v);a[a.length-1]=S,n=n.substring(0,n.length-g.raw.length)+S.raw,s=s.substring(0,s.length-_.raw.length)+S.raw,t=v.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:n,tokens:a,text:s}}}list(r){let e=this.rules.block.list.exec(r);if(e){let t=e[1].trim();const n=t.length>1,s={type:"list",raw:"",ordered:n,start:n?+t.slice(0,-1):"",loose:!1,items:[]};t=n?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=n?t:"[*+-]");const a=this.rules.other.listItemRegex(t);let c=!1;for(;r;){let h=!1,i="",p="";if(!(e=a.exec(r))||this.rules.block.hr.test(r))break;i=e[0],r=r.substring(i.length);let b=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,B=>" ".repeat(3*B.length)),g=r.split(`
`,1)[0],_=!b.trim(),v=0;if(this.options.pedantic?(v=2,p=b.trimStart()):_?v=e[1].length+1:(v=e[2].search(this.rules.other.nonSpaceChar),v=v>4?1:v,p=b.slice(v),v+=e[1].length),_&&this.rules.other.blankLine.test(g)&&(i+=g+`
`,r=r.substring(g.length+1),h=!0),!h){const B=this.rules.other.nextBulletRegex(v),D=this.rules.other.hrRegex(v),j=this.rules.other.fencesBeginRegex(v),P=this.rules.other.headingBeginRegex(v),se=this.rules.other.htmlBeginRegex(v);for(;r;){const ne=r.split(`
`,1)[0];let le;if(g=ne,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),le=g):le=g.replace(this.rules.other.tabCharGlobal,"    "),j.test(g)||P.test(g)||se.test(g)||B.test(g)||D.test(g))break;if(le.search(this.rules.other.nonSpaceChar)>=v||!g.trim())p+=`
`+le.slice(v);else{if(_||b.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||j.test(b)||P.test(b)||D.test(b))break;p+=`
`+g}!_&&!g.trim()&&(_=!0),i+=ne+`
`,r=r.substring(ne.length+1),b=le.slice(v)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(i)&&(c=!0));let S=null,A;this.options.gfm&&(S=this.rules.other.listIsTask.exec(p),S&&(A=S[0]!=="[ ] ",p=p.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:i,task:!!S,checked:A,loose:!1,text:p,tokens:[]}),s.raw+=i}const l=s.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let h=0;h<s.items.length;h++)if(this.lexer.state.top=!1,s.items[h].tokens=this.lexer.blockTokens(s.items[h].text,[]),!s.loose){const i=s.items[h].tokens.filter(b=>b.type==="space"),p=i.length>0&&i.some(b=>this.rules.other.anyLine.test(b.raw));s.loose=p}if(s.loose)for(let h=0;h<s.items.length;h++)s.items[h].loose=!0;return s}}html(r){const e=this.rules.block.html.exec(r);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(r){const e=this.rules.block.def.exec(r);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:n,title:s}}}table(r){var c;const e=this.rules.block.table.exec(r);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=Ar(e[1]),n=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===n.length){for(const l of n)this.rules.other.tableAlignRight.test(l)?a.align.push("right"):this.rules.other.tableAlignCenter.test(l)?a.align.push("center"):this.rules.other.tableAlignLeft.test(l)?a.align.push("left"):a.align.push(null);for(let l=0;l<t.length;l++)a.header.push({text:t[l],tokens:this.lexer.inline(t[l]),header:!0,align:a.align[l]});for(const l of s)a.rows.push(Ar(l,a.header.length).map((h,i)=>({text:h,tokens:this.lexer.inline(h),header:!1,align:a.align[i]})));return a}}lheading(r){const e=this.rules.block.lheading.exec(r);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(r){const e=this.rules.block.paragraph.exec(r);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(r){const e=this.rules.block.text.exec(r);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(r){const e=this.rules.inline.escape.exec(r);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(r){const e=this.rules.inline.tag.exec(r);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(r){const e=this.rules.inline.link.exec(r);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const a=Je(t.slice(0,-1),"\\");if((t.length-a.length)%2===0)return}else{const a=Ps(e[2],"()");if(a===-2)return;if(a>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+a;e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let n=e[2],s="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(n);a&&(n=a[1],s=a[3])}else s=e[3]?e[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?n=n.slice(1):n=n.slice(1,-1)),Rr(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(r,e){let t;if((t=this.rules.inline.reflink.exec(r))||(t=this.rules.inline.nolink.exec(r))){const n=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[n.toLowerCase()];if(!s){const a=t[0].charAt(0);return{type:"text",raw:a,text:a}}return Rr(t,s,t[0],this.lexer,this.rules)}}emStrong(r,e,t=""){let n=this.rules.inline.emStrongLDelim.exec(r);if(!n||n[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(n[1]||n[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const a=[...n[0]].length-1;let c,l,h=a,i=0;const p=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,e=e.slice(-1*r.length+a);(n=p.exec(e))!=null;){if(c=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!c)continue;if(l=[...c].length,n[3]||n[4]){h+=l;continue}else if((n[5]||n[6])&&a%3&&!((a+l)%3)){i+=l;continue}if(h-=l,h>0)continue;l=Math.min(l,l+h+i);const b=[...n[0]][0].length,g=r.slice(0,a+n.index+b+l);if(Math.min(a,l)%2){const v=g.slice(1,-1);return{type:"em",raw:g,text:v,tokens:this.lexer.inlineTokens(v)}}const _=g.slice(2,-2);return{type:"strong",raw:g,text:_,tokens:this.lexer.inlineTokens(_)}}}}codespan(r){const e=this.rules.inline.code.exec(r);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return n&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(r){const e=this.rules.inline.br.exec(r);if(e)return{type:"br",raw:e[0]}}del(r){const e=this.rules.inline.del.exec(r);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(r){const e=this.rules.inline.autolink.exec(r);if(e){let t,n;return e[2]==="@"?(t=e[1],n="mailto:"+t):(t=e[1],n=t),{type:"link",raw:e[0],text:t,href:n,tokens:[{type:"text",raw:t,text:t}]}}}url(r){var t;let e;if(e=this.rules.inline.url.exec(r)){let n,s;if(e[2]==="@")n=e[0],s="mailto:"+n;else{let a;do a=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(a!==e[0]);n=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(r){const e=this.rules.inline.text.exec(r);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},be=class jt{constructor(e){N(this,"tokens");N(this,"options");N(this,"state");N(this,"tokenizer");N(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||De,this.options.tokenizer=this.options.tokenizer||new yt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:J,block:gt.normal,inline:Qe.normal};this.options.pedantic?(t.block=gt.pedantic,t.inline=Qe.pedantic):this.options.gfm&&(t.block=gt.gfm,this.options.breaks?t.inline=Qe.breaks:t.inline=Qe.gfm),this.tokenizer.rules=t}static get rules(){return{block:gt,inline:Qe}}static lex(e,t){return new jt(t).lex(e)}static lexInline(e,t){return new jt(t).inlineTokens(e)}lex(e){e=e.replace(J.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){var s,a,c;for(this.options.pedantic&&(e=e.replace(J.tabCharGlobal,"    ").replace(J.spaceLine,""));e;){let l;if((a=(s=this.options.extensions)==null?void 0:s.block)!=null&&a.some(i=>(l=i.call({lexer:this},e,t))?(e=e.substring(l.raw.length),t.push(l),!0):!1))continue;if(l=this.tokenizer.space(e)){e=e.substring(l.raw.length);const i=t.at(-1);l.raw.length===1&&i!==void 0?i.raw+=`
`:t.push(l);continue}if(l=this.tokenizer.code(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(l=this.tokenizer.fences(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.heading(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.hr(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.blockquote(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.list(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.html(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.def(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.raw,this.inlineQueue.at(-1).src=i.text):this.tokens.links[l.tag]||(this.tokens.links[l.tag]={href:l.href,title:l.title});continue}if(l=this.tokenizer.table(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.lheading(e)){e=e.substring(l.raw.length),t.push(l);continue}let h=e;if((c=this.options.extensions)!=null&&c.startBlock){let i=1/0;const p=e.slice(1);let b;this.options.extensions.startBlock.forEach(g=>{b=g.call({lexer:this},p),typeof b=="number"&&b>=0&&(i=Math.min(i,b))}),i<1/0&&i>=0&&(h=e.substring(0,i+1))}if(this.state.top&&(l=this.tokenizer.paragraph(h))){const i=t.at(-1);n&&(i==null?void 0:i.type)==="paragraph"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l),n=h.length!==e.length,e=e.substring(l.raw.length);continue}if(l=this.tokenizer.text(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}else throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var l,h,i;let n=e,s=null;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)p.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,c="";for(;e;){a||(c=""),a=!1;let p;if((h=(l=this.options.extensions)==null?void 0:l.inline)!=null&&h.some(g=>(p=g.call({lexer:this},e,t))?(e=e.substring(p.raw.length),t.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);const g=t.at(-1);p.type==="text"&&(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(p=this.tokenizer.emStrong(e,n,c)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),t.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),t.push(p);continue}let b=e;if((i=this.options.extensions)!=null&&i.startInline){let g=1/0;const _=e.slice(1);let v;this.options.extensions.startInline.forEach(S=>{v=S.call({lexer:this},_),typeof v=="number"&&v>=0&&(g=Math.min(g,v))}),g<1/0&&g>=0&&(b=e.substring(0,g+1))}if(p=this.tokenizer.inlineText(b)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(c=p.raw.slice(-1)),a=!0;const g=t.at(-1);(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(e){const g="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(g);break}else throw new Error(g)}}return t}},Tt=class{constructor(r){N(this,"options");N(this,"parser");this.options=r||De}space(r){return""}code({text:r,lang:e,escaped:t}){var a;const n=(a=(e||"").match(J.notSpaceStart))==null?void 0:a[0],s=r.replace(J.endingNewline,"")+`
`;return n?'<pre><code class="language-'+ce(n)+'">'+(t?s:ce(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:ce(s,!0))+`</code></pre>
`}blockquote({tokens:r}){return`<blockquote>
${this.parser.parse(r)}</blockquote>
`}html({text:r}){return r}heading({tokens:r,depth:e}){return`<h${e}>${this.parser.parseInline(r)}</h${e}>
`}hr(r){return`<hr>
`}list(r){const e=r.ordered,t=r.start;let n="";for(let c=0;c<r.items.length;c++){const l=r.items[c];n+=this.listitem(l)}const s=e?"ol":"ul",a=e&&t!==1?' start="'+t+'"':"";return"<"+s+a+`>
`+n+"</"+s+`>
`}listitem(r){var t;let e="";if(r.task){const n=this.checkbox({checked:!!r.checked});r.loose?((t=r.tokens[0])==null?void 0:t.type)==="paragraph"?(r.tokens[0].text=n+" "+r.tokens[0].text,r.tokens[0].tokens&&r.tokens[0].tokens.length>0&&r.tokens[0].tokens[0].type==="text"&&(r.tokens[0].tokens[0].text=n+" "+ce(r.tokens[0].tokens[0].text),r.tokens[0].tokens[0].escaped=!0)):r.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):e+=n+" "}return e+=this.parser.parse(r.tokens,!!r.loose),`<li>${e}</li>
`}checkbox({checked:r}){return"<input "+(r?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:r}){return`<p>${this.parser.parseInline(r)}</p>
`}table(r){let e="",t="";for(let s=0;s<r.header.length;s++)t+=this.tablecell(r.header[s]);e+=this.tablerow({text:t});let n="";for(let s=0;s<r.rows.length;s++){const a=r.rows[s];t="";for(let c=0;c<a.length;c++)t+=this.tablecell(a[c]);n+=this.tablerow({text:t})}return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+n+`</table>
`}tablerow({text:r}){return`<tr>
${r}</tr>
`}tablecell(r){const e=this.parser.parseInline(r.tokens),t=r.header?"th":"td";return(r.align?`<${t} align="${r.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:r}){return`<strong>${this.parser.parseInline(r)}</strong>`}em({tokens:r}){return`<em>${this.parser.parseInline(r)}</em>`}codespan({text:r}){return`<code>${ce(r,!0)}</code>`}br(r){return"<br>"}del({tokens:r}){return`<del>${this.parser.parseInline(r)}</del>`}link({href:r,title:e,tokens:t}){const n=this.parser.parseInline(t),s=Cr(r);if(s===null)return n;r=s;let a='<a href="'+r+'"';return e&&(a+=' title="'+ce(e)+'"'),a+=">"+n+"</a>",a}image({href:r,title:e,text:t,tokens:n}){n&&(t=this.parser.parseInline(n,this.parser.textRenderer));const s=Cr(r);if(s===null)return ce(t);r=s;let a=`<img src="${r}" alt="${t}"`;return e&&(a+=` title="${ce(e)}"`),a+=">",a}text(r){return"tokens"in r&&r.tokens?this.parser.parseInline(r.tokens):"escaped"in r&&r.escaped?r.text:ce(r.text)}},lr=class{strong({text:r}){return r}em({text:r}){return r}codespan({text:r}){return r}del({text:r}){return r}html({text:r}){return r}text({text:r}){return r}link({text:r}){return""+r}image({text:r}){return""+r}br(){return""}},xe=class Vt{constructor(e){N(this,"options");N(this,"renderer");N(this,"textRenderer");this.options=e||De,this.options.renderer=this.options.renderer||new Tt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new lr}static parse(e,t){return new Vt(t).parse(e)}static parseInline(e,t){return new Vt(t).parseInline(e)}parse(e,t=!0){var s,a;let n="";for(let c=0;c<e.length;c++){const l=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=l,p=this.options.extensions.renderers[i.type].call({parser:this},i);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){n+=p||"";continue}}const h=l;switch(h.type){case"space":{n+=this.renderer.space(h);continue}case"hr":{n+=this.renderer.hr(h);continue}case"heading":{n+=this.renderer.heading(h);continue}case"code":{n+=this.renderer.code(h);continue}case"table":{n+=this.renderer.table(h);continue}case"blockquote":{n+=this.renderer.blockquote(h);continue}case"list":{n+=this.renderer.list(h);continue}case"html":{n+=this.renderer.html(h);continue}case"paragraph":{n+=this.renderer.paragraph(h);continue}case"text":{let i=h,p=this.renderer.text(i);for(;c+1<e.length&&e[c+1].type==="text";)i=e[++c],p+=`
`+this.renderer.text(i);t?n+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):n+=p;continue}default:{const i='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}parseInline(e,t=this.renderer){var s,a;let n="";for(let c=0;c<e.length;c++){const l=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=this.options.extensions.renderers[l.type].call({parser:this},l);if(i!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(l.type)){n+=i||"";continue}}const h=l;switch(h.type){case"escape":{n+=t.text(h);break}case"html":{n+=t.html(h);break}case"link":{n+=t.link(h);break}case"image":{n+=t.image(h);break}case"strong":{n+=t.strong(h);break}case"em":{n+=t.em(h);break}case"codespan":{n+=t.codespan(h);break}case"br":{n+=t.br(h);break}case"del":{n+=t.del(h);break}case"text":{n+=t.text(h);break}default:{const i='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}},Wt,xt=(Wt=class{constructor(r){N(this,"options");N(this,"block");this.options=r||De}preprocess(r){return r}postprocess(r){return r}processAllTokens(r){return r}provideLexer(){return this.block?be.lex:be.lexInline}provideParser(){return this.block?xe.parse:xe.parseInline}},N(Wt,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Wt),$s=class{constructor(...r){N(this,"defaults",Jt());N(this,"options",this.setOptions);N(this,"parse",this.parseMarkdown(!0));N(this,"parseInline",this.parseMarkdown(!1));N(this,"Parser",xe);N(this,"Renderer",Tt);N(this,"TextRenderer",lr);N(this,"Lexer",be);N(this,"Tokenizer",yt);N(this,"Hooks",xt);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const a of r)switch(t=t.concat(e.call(this,a)),a.type){case"table":{const c=a;for(const l of c.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of c.rows)for(const h of l)t=t.concat(this.walkTokens(h.tokens,e));break}case"list":{const c=a;t=t.concat(this.walkTokens(c.items,e));break}default:{const c=a;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(l=>{const h=c[l].flat(1/0);t=t.concat(this.walkTokens(h,e))}):c.tokens&&(t=t.concat(this.walkTokens(c.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=e.renderers[s.name];a?e.renderers[s.name]=function(...c){let l=s.renderer.apply(this,c);return l===!1&&(l=a.apply(this,c)),l}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=e[s.level];a?a.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new Tt(this.defaults);for(const a in t.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const c=a,l=t.renderer[c],h=s[c];s[c]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=h.apply(s,i)),p||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new yt(this.defaults);for(const a in t.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const c=a,l=t.tokenizer[c],h=s[c];s[c]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=h.apply(s,i)),p}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new xt;for(const a in t.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const c=a,l=t.hooks[c],h=s[c];xt.passThroughHooks.has(a)?s[c]=i=>{if(this.defaults.async)return Promise.resolve(l.call(s,i)).then(b=>h.call(s,b));const p=l.call(s,i);return h.call(s,p)}:s[c]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=h.apply(s,i)),p}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,a=t.walkTokens;n.walkTokens=function(c){let l=[];return l.push(a.call(this,c)),s&&(l=l.concat(s.call(this,c))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return be.lex(r,e??this.defaults)}parser(r,e){return xe.parse(r,e??this.defaults)}parseMarkdown(r){return(t,n)=>{const s={...n},a={...this.defaults,...s},c=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&s.async===!1)return c(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return c(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return c(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=r);const l=a.hooks?a.hooks.provideLexer():r?be.lex:be.lexInline,h=a.hooks?a.hooks.provideParser():r?xe.parse:xe.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(i=>l(i,a)).then(i=>a.hooks?a.hooks.processAllTokens(i):i).then(i=>a.walkTokens?Promise.all(this.walkTokens(i,a.walkTokens)).then(()=>i):i).then(i=>h(i,a)).then(i=>a.hooks?a.hooks.postprocess(i):i).catch(c);try{a.hooks&&(t=a.hooks.preprocess(t));let i=l(t,a);a.hooks&&(i=a.hooks.processAllTokens(i)),a.walkTokens&&this.walkTokens(i,a.walkTokens);let p=h(i,a);return a.hooks&&(p=a.hooks.postprocess(p)),p}catch(i){return c(i)}}}onError(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+ce(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}}},Re=new $s;function L(r,e){return Re.parse(r,e)}L.options=L.setOptions=function(r){return Re.setOptions(r),L.defaults=Re.defaults,qr(L.defaults),L};L.getDefaults=Jt;L.defaults=De;L.use=function(...r){return Re.use(...r),L.defaults=Re.defaults,qr(L.defaults),L};L.walkTokens=function(r,e){return Re.walkTokens(r,e)};L.parseInline=Re.parseInline;L.Parser=xe;L.parser=xe.parse;L.Renderer=Tt;L.TextRenderer=lr;L.Lexer=be;L.lexer=be.lex;L.Tokenizer=yt;L.Hooks=xt;L.parse=L;L.options;L.setOptions;L.use;L.walkTokens;L.parseInline;xe.parse;be.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:rn,setPrototypeOf:Dr,isFrozen:Us,getPrototypeOf:Bs,getOwnPropertyDescriptor:Fs}=Object;let{freeze:ee,seal:ae,create:nn}=Object,{apply:Yt,construct:Xt}=typeof Reflect<"u"&&Reflect;ee||(ee=function(e){return e});ae||(ae=function(e){return e});Yt||(Yt=function(e,t,n){return e.apply(t,n)});Xt||(Xt=function(e,t){return new e(...t)});const mt=te(Array.prototype.forEach),Hs=te(Array.prototype.lastIndexOf),Mr=te(Array.prototype.pop),et=te(Array.prototype.push),Gs=te(Array.prototype.splice),vt=te(String.prototype.toLowerCase),Ut=te(String.prototype.toString),Lr=te(String.prototype.match),tt=te(String.prototype.replace),Ws=te(String.prototype.indexOf),qs=te(String.prototype.trim),oe=te(Object.prototype.hasOwnProperty),Q=te(RegExp.prototype.test),rt=js(TypeError);function te(r){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return Yt(r,e,n)}}function js(r){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Xt(r,t)}}function R(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:vt;Dr&&Dr(r,null);let n=e.length;for(;n--;){let s=e[n];if(typeof s=="string"){const a=t(s);a!==s&&(Us(e)||(e[n]=a),s=a)}r[s]=!0}return r}function Vs(r){for(let e=0;e<r.length;e++)oe(r,e)||(r[e]=null);return r}function ge(r){const e=nn(null);for(const[t,n]of rn(r))oe(r,t)&&(Array.isArray(n)?e[t]=Vs(n):n&&typeof n=="object"&&n.constructor===Object?e[t]=ge(n):e[t]=n);return e}function nt(r,e){for(;r!==null;){const n=Fs(r,e);if(n){if(n.get)return te(n.get);if(typeof n.value=="function")return te(n.value)}r=Bs(r)}function t(){return null}return t}const Ir=ee(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Bt=ee(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ft=ee(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ys=ee(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ht=ee(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Xs=ee(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Nr=ee(["#text"]),Or=ee(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Gt=ee(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Pr=ee(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),bt=ee(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Zs=ae(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Ks=ae(/<%[\w\W]*|[\w\W]*%>/gm),Qs=ae(/\$\{[\w\W]*/gm),Js=ae(/^data-[\-\w.\u00B7-\uFFFF]+$/),ea=ae(/^aria-[\-\w]+$/),sn=ae(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),ta=ae(/^(?:\w+script|data):/i),ra=ae(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),an=ae(/^html$/i),na=ae(/^[a-z][.\w]*(-[.\w]+)+$/i);var zr=Object.freeze({__proto__:null,ARIA_ATTR:ea,ATTR_WHITESPACE:ra,CUSTOM_ELEMENT:na,DATA_ATTR:Js,DOCTYPE_NAME:an,ERB_EXPR:Ks,IS_ALLOWED_URI:sn,IS_SCRIPT_OR_DATA:ta,MUSTACHE_EXPR:Zs,TMPLIT_EXPR:Qs});const st={element:1,text:3,progressingInstruction:7,comment:8,document:9},sa=function(){return typeof window>"u"?null:window},aa=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(n=t.getAttribute(s));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML(c){return c},createScriptURL(c){return c}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},$r=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function on(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:sa();const e=E=>on(E);if(e.version="3.2.6",e.removed=[],!r||!r.document||r.document.nodeType!==st.document||!r.Element)return e.isSupported=!1,e;let{document:t}=r;const n=t,s=n.currentScript,{DocumentFragment:a,HTMLTemplateElement:c,Node:l,Element:h,NodeFilter:i,NamedNodeMap:p=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:b,DOMParser:g,trustedTypes:_}=r,v=h.prototype,S=nt(v,"cloneNode"),A=nt(v,"remove"),B=nt(v,"nextSibling"),D=nt(v,"childNodes"),j=nt(v,"parentNode");if(typeof c=="function"){const E=t.createElement("template");E.content&&E.content.ownerDocument&&(t=E.content.ownerDocument)}let P,se="";const{implementation:ne,createNodeIterator:le,createDocumentFragment:ve,getElementsByTagName:we}=t,{importNode:Me}=n;let V=$r();e.isSupported=typeof rn=="function"&&typeof j=="function"&&ne&&ne.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Be,ERB_EXPR:Fe,TMPLIT_EXPR:He,DATA_ATTR:Ct,ARIA_ATTR:At,IS_SCRIPT_OR_DATA:Ge,ATTR_WHITESPACE:We,CUSTOM_ELEMENT:Rt}=zr;let{IS_ALLOWED_URI:it}=zr,H=null;const qe=R({},[...Ir,...Bt,...Ft,...Ht,...Nr]);let F=null;const je=R({},[...Or,...Gt,...Pr,...bt]);let $=Object.seal(nn(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ke=null,Ve=null,Le=!0,Ye=!0,ct=!1,ut=!0,ye=!1,Ie=!0,de=!1,Ne=!1,Xe=!1,x=!1,u=!1,Y=!1,U=!0,w=!1;const Te="user-content-";let _e=!0,Ee=!1,K={},Oe=null;const cr=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ur=null;const dr=R({},["audio","video","img","source","image","track"]);let Dt=null;const pr=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),dt="http://www.w3.org/1998/Math/MathML",pt="http://www.w3.org/2000/svg",pe="http://www.w3.org/1999/xhtml";let Pe=pe,Mt=!1,Lt=null;const ln=R({},[dt,pt,pe],Ut);let ht=R({},["mi","mo","mn","ms","mtext"]),ft=R({},["annotation-xml"]);const cn=R({},["title","style","font","a","script"]);let Ze=null;const un=["application/xhtml+xml","text/html"],dn="text/html";let W=null,ze=null;const pn=t.createElement("form"),hr=function(o){return o instanceof RegExp||o instanceof Function},It=function(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(ze&&ze===o)){if((!o||typeof o!="object")&&(o={}),o=ge(o),Ze=un.indexOf(o.PARSER_MEDIA_TYPE)===-1?dn:o.PARSER_MEDIA_TYPE,W=Ze==="application/xhtml+xml"?Ut:vt,H=oe(o,"ALLOWED_TAGS")?R({},o.ALLOWED_TAGS,W):qe,F=oe(o,"ALLOWED_ATTR")?R({},o.ALLOWED_ATTR,W):je,Lt=oe(o,"ALLOWED_NAMESPACES")?R({},o.ALLOWED_NAMESPACES,Ut):ln,Dt=oe(o,"ADD_URI_SAFE_ATTR")?R(ge(pr),o.ADD_URI_SAFE_ATTR,W):pr,ur=oe(o,"ADD_DATA_URI_TAGS")?R(ge(dr),o.ADD_DATA_URI_TAGS,W):dr,Oe=oe(o,"FORBID_CONTENTS")?R({},o.FORBID_CONTENTS,W):cr,ke=oe(o,"FORBID_TAGS")?R({},o.FORBID_TAGS,W):ge({}),Ve=oe(o,"FORBID_ATTR")?R({},o.FORBID_ATTR,W):ge({}),K=oe(o,"USE_PROFILES")?o.USE_PROFILES:!1,Le=o.ALLOW_ARIA_ATTR!==!1,Ye=o.ALLOW_DATA_ATTR!==!1,ct=o.ALLOW_UNKNOWN_PROTOCOLS||!1,ut=o.ALLOW_SELF_CLOSE_IN_ATTR!==!1,ye=o.SAFE_FOR_TEMPLATES||!1,Ie=o.SAFE_FOR_XML!==!1,de=o.WHOLE_DOCUMENT||!1,x=o.RETURN_DOM||!1,u=o.RETURN_DOM_FRAGMENT||!1,Y=o.RETURN_TRUSTED_TYPE||!1,Xe=o.FORCE_BODY||!1,U=o.SANITIZE_DOM!==!1,w=o.SANITIZE_NAMED_PROPS||!1,_e=o.KEEP_CONTENT!==!1,Ee=o.IN_PLACE||!1,it=o.ALLOWED_URI_REGEXP||sn,Pe=o.NAMESPACE||pe,ht=o.MATHML_TEXT_INTEGRATION_POINTS||ht,ft=o.HTML_INTEGRATION_POINTS||ft,$=o.CUSTOM_ELEMENT_HANDLING||{},o.CUSTOM_ELEMENT_HANDLING&&hr(o.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&($.tagNameCheck=o.CUSTOM_ELEMENT_HANDLING.tagNameCheck),o.CUSTOM_ELEMENT_HANDLING&&hr(o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&($.attributeNameCheck=o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),o.CUSTOM_ELEMENT_HANDLING&&typeof o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&($.allowCustomizedBuiltInElements=o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ye&&(Ye=!1),u&&(x=!0),K&&(H=R({},Nr),F=[],K.html===!0&&(R(H,Ir),R(F,Or)),K.svg===!0&&(R(H,Bt),R(F,Gt),R(F,bt)),K.svgFilters===!0&&(R(H,Ft),R(F,Gt),R(F,bt)),K.mathMl===!0&&(R(H,Ht),R(F,Pr),R(F,bt))),o.ADD_TAGS&&(H===qe&&(H=ge(H)),R(H,o.ADD_TAGS,W)),o.ADD_ATTR&&(F===je&&(F=ge(F)),R(F,o.ADD_ATTR,W)),o.ADD_URI_SAFE_ATTR&&R(Dt,o.ADD_URI_SAFE_ATTR,W),o.FORBID_CONTENTS&&(Oe===cr&&(Oe=ge(Oe)),R(Oe,o.FORBID_CONTENTS,W)),_e&&(H["#text"]=!0),de&&R(H,["html","head","body"]),H.table&&(R(H,["tbody"]),delete ke.tbody),o.TRUSTED_TYPES_POLICY){if(typeof o.TRUSTED_TYPES_POLICY.createHTML!="function")throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof o.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');P=o.TRUSTED_TYPES_POLICY,se=P.createHTML("")}else P===void 0&&(P=aa(_,s)),P!==null&&typeof se=="string"&&(se=P.createHTML(""));ee&&ee(o),ze=o}},fr=R({},[...Bt,...Ft,...Ys]),gr=R({},[...Ht,...Xs]),hn=function(o){let m=j(o);(!m||!m.tagName)&&(m={namespaceURI:Pe,tagName:"template"});const y=vt(o.tagName),z=vt(m.tagName);return Lt[o.namespaceURI]?o.namespaceURI===pt?m.namespaceURI===pe?y==="svg":m.namespaceURI===dt?y==="svg"&&(z==="annotation-xml"||ht[z]):!!fr[y]:o.namespaceURI===dt?m.namespaceURI===pe?y==="math":m.namespaceURI===pt?y==="math"&&ft[z]:!!gr[y]:o.namespaceURI===pe?m.namespaceURI===pt&&!ft[z]||m.namespaceURI===dt&&!ht[z]?!1:!gr[y]&&(cn[y]||!fr[y]):!!(Ze==="application/xhtml+xml"&&Lt[o.namespaceURI]):!1},ie=function(o){et(e.removed,{element:o});try{j(o).removeChild(o)}catch{A(o)}},$e=function(o,m){try{et(e.removed,{attribute:m.getAttributeNode(o),from:m})}catch{et(e.removed,{attribute:null,from:m})}if(m.removeAttribute(o),o==="is")if(x||u)try{ie(m)}catch{}else try{m.setAttribute(o,"")}catch{}},mr=function(o){let m=null,y=null;if(Xe)o="<remove></remove>"+o;else{const G=Lr(o,/^[\r\n\t ]+/);y=G&&G[0]}Ze==="application/xhtml+xml"&&Pe===pe&&(o='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+o+"</body></html>");const z=P?P.createHTML(o):o;if(Pe===pe)try{m=new g().parseFromString(z,Ze)}catch{}if(!m||!m.documentElement){m=ne.createDocument(Pe,"template",null);try{m.documentElement.innerHTML=Mt?se:z}catch{}}const X=m.body||m.documentElement;return o&&y&&X.insertBefore(t.createTextNode(y),X.childNodes[0]||null),Pe===pe?we.call(m,de?"html":"body")[0]:de?m.documentElement:X},br=function(o){return le.call(o.ownerDocument||o,o,i.SHOW_ELEMENT|i.SHOW_COMMENT|i.SHOW_TEXT|i.SHOW_PROCESSING_INSTRUCTION|i.SHOW_CDATA_SECTION,null)},Nt=function(o){return o instanceof b&&(typeof o.nodeName!="string"||typeof o.textContent!="string"||typeof o.removeChild!="function"||!(o.attributes instanceof p)||typeof o.removeAttribute!="function"||typeof o.setAttribute!="function"||typeof o.namespaceURI!="string"||typeof o.insertBefore!="function"||typeof o.hasChildNodes!="function")},xr=function(o){return typeof l=="function"&&o instanceof l};function he(E,o,m){mt(E,y=>{y.call(e,o,m,ze)})}const vr=function(o){let m=null;if(he(V.beforeSanitizeElements,o,null),Nt(o))return ie(o),!0;const y=W(o.nodeName);if(he(V.uponSanitizeElement,o,{tagName:y,allowedTags:H}),Ie&&o.hasChildNodes()&&!xr(o.firstElementChild)&&Q(/<[/\w!]/g,o.innerHTML)&&Q(/<[/\w!]/g,o.textContent)||o.nodeType===st.progressingInstruction||Ie&&o.nodeType===st.comment&&Q(/<[/\w]/g,o.data))return ie(o),!0;if(!H[y]||ke[y]){if(!ke[y]&&kr(y)&&($.tagNameCheck instanceof RegExp&&Q($.tagNameCheck,y)||$.tagNameCheck instanceof Function&&$.tagNameCheck(y)))return!1;if(_e&&!Oe[y]){const z=j(o)||o.parentNode,X=D(o)||o.childNodes;if(X&&z){const G=X.length;for(let re=G-1;re>=0;--re){const fe=S(X[re],!0);fe.__removalCount=(o.__removalCount||0)+1,z.insertBefore(fe,B(o))}}}return ie(o),!0}return o instanceof h&&!hn(o)||(y==="noscript"||y==="noembed"||y==="noframes")&&Q(/<\/no(script|embed|frames)/i,o.innerHTML)?(ie(o),!0):(ye&&o.nodeType===st.text&&(m=o.textContent,mt([Be,Fe,He],z=>{m=tt(m,z," ")}),o.textContent!==m&&(et(e.removed,{element:o.cloneNode()}),o.textContent=m)),he(V.afterSanitizeElements,o,null),!1)},wr=function(o,m,y){if(U&&(m==="id"||m==="name")&&(y in t||y in pn))return!1;if(!(Ye&&!Ve[m]&&Q(Ct,m))){if(!(Le&&Q(At,m))){if(!F[m]||Ve[m]){if(!(kr(o)&&($.tagNameCheck instanceof RegExp&&Q($.tagNameCheck,o)||$.tagNameCheck instanceof Function&&$.tagNameCheck(o))&&($.attributeNameCheck instanceof RegExp&&Q($.attributeNameCheck,m)||$.attributeNameCheck instanceof Function&&$.attributeNameCheck(m))||m==="is"&&$.allowCustomizedBuiltInElements&&($.tagNameCheck instanceof RegExp&&Q($.tagNameCheck,y)||$.tagNameCheck instanceof Function&&$.tagNameCheck(y))))return!1}else if(!Dt[m]){if(!Q(it,tt(y,We,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&o!=="script"&&Ws(y,"data:")===0&&ur[o])){if(!(ct&&!Q(Ge,tt(y,We,"")))){if(y)return!1}}}}}}return!0},kr=function(o){return o!=="annotation-xml"&&Lr(o,Rt)},yr=function(o){he(V.beforeSanitizeAttributes,o,null);const{attributes:m}=o;if(!m||Nt(o))return;const y={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let z=m.length;for(;z--;){const X=m[z],{name:G,namespaceURI:re,value:fe}=X,Ke=W(G),Ot=fe;let Z=G==="value"?Ot:qs(Ot);if(y.attrName=Ke,y.attrValue=Z,y.keepAttr=!0,y.forceKeepAttr=void 0,he(V.uponSanitizeAttribute,o,y),Z=y.attrValue,w&&(Ke==="id"||Ke==="name")&&($e(G,o),Z=Te+Z),Ie&&Q(/((--!?|])>)|<\/(style|title)/i,Z)){$e(G,o);continue}if(y.forceKeepAttr)continue;if(!y.keepAttr){$e(G,o);continue}if(!ut&&Q(/\/>/i,Z)){$e(G,o);continue}ye&&mt([Be,Fe,He],_r=>{Z=tt(Z,_r," ")});const Tr=W(o.nodeName);if(!wr(Tr,Ke,Z)){$e(G,o);continue}if(P&&typeof _=="object"&&typeof _.getAttributeType=="function"&&!re)switch(_.getAttributeType(Tr,Ke)){case"TrustedHTML":{Z=P.createHTML(Z);break}case"TrustedScriptURL":{Z=P.createScriptURL(Z);break}}if(Z!==Ot)try{re?o.setAttributeNS(re,G,Z):o.setAttribute(G,Z),Nt(o)?ie(o):Mr(e.removed)}catch{$e(G,o)}}he(V.afterSanitizeAttributes,o,null)},fn=function E(o){let m=null;const y=br(o);for(he(V.beforeSanitizeShadowDOM,o,null);m=y.nextNode();)he(V.uponSanitizeShadowNode,m,null),vr(m),yr(m),m.content instanceof a&&E(m.content);he(V.afterSanitizeShadowDOM,o,null)};return e.sanitize=function(E){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,y=null,z=null,X=null;if(Mt=!E,Mt&&(E="<!-->"),typeof E!="string"&&!xr(E))if(typeof E.toString=="function"){if(E=E.toString(),typeof E!="string")throw rt("dirty is not a string, aborting")}else throw rt("toString is not a function");if(!e.isSupported)return E;if(Ne||It(o),e.removed=[],typeof E=="string"&&(Ee=!1),Ee){if(E.nodeName){const fe=W(E.nodeName);if(!H[fe]||ke[fe])throw rt("root node is forbidden and cannot be sanitized in-place")}}else if(E instanceof l)m=mr("<!---->"),y=m.ownerDocument.importNode(E,!0),y.nodeType===st.element&&y.nodeName==="BODY"||y.nodeName==="HTML"?m=y:m.appendChild(y);else{if(!x&&!ye&&!de&&E.indexOf("<")===-1)return P&&Y?P.createHTML(E):E;if(m=mr(E),!m)return x?null:Y?se:""}m&&Xe&&ie(m.firstChild);const G=br(Ee?E:m);for(;z=G.nextNode();)vr(z),yr(z),z.content instanceof a&&fn(z.content);if(Ee)return E;if(x){if(u)for(X=ve.call(m.ownerDocument);m.firstChild;)X.appendChild(m.firstChild);else X=m;return(F.shadowroot||F.shadowrootmode)&&(X=Me.call(n,X,!0)),X}let re=de?m.outerHTML:m.innerHTML;return de&&H["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&Q(an,m.ownerDocument.doctype.name)&&(re="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+re),ye&&mt([Be,Fe,He],fe=>{re=tt(re,fe," ")}),P&&Y?P.createHTML(re):re},e.setConfig=function(){let E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};It(E),Ne=!0},e.clearConfig=function(){ze=null,Ne=!1},e.isValidAttribute=function(E,o,m){ze||It({});const y=W(E),z=W(o);return wr(y,z,m)},e.addHook=function(E,o){typeof o=="function"&&et(V[E],o)},e.removeHook=function(E,o){if(o!==void 0){const m=Hs(V[E],o);return m===-1?void 0:Gs(V[E],m,1)[0]}return Mr(V[E])},e.removeHooks=function(E){V[E]=[]},e.removeAllHooks=function(){V=$r()},e}var oa=on();const la={class:"flex-1"},ia={class:"flex items-center"},ca={class:"relative"},ua={key:1,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5 sticky top-[40px] z-10"},da={key:0,class:"flex items-center gap-1 text-gray-500 text-xs"},pa={key:1,class:"flex flex-col gap-1"},ha=["onClick"],fa={key:2,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5"},ga={class:"flex items-center gap-2"},ma={class:"flex-1 overflow-y-auto overflow-x-hidden"},ba={class:"px-0.5"},xa={class:"mb-1"},va={key:0},wa={key:1,class:"text-gray-500"},ka={class:"flex items-center justify-between mt-0.5"},ya={class:"flex items-center gap-1"},Ta={class:"flex flex-wrap gap-1"},_a=["onClick"],Ea={key:0,class:"flex items-start gap-1.5 max-w-full"},Sa={class:"flex-1 bg-[var(--bg-color)] rounded-lg p-2 shadow-sm break-words"},Ca={class:"text-[var(--text-color)] leading-tight text-[15px]"},Aa=["innerHTML"],Ra={key:1,class:"h-full flex items-center justify-center text-gray-500 text-[15px]"},Da={class:"border-t border-[var(--border-color)] bg-[var(--bg-color)] w-full input-bar-container"},Ma={class:"flex items-center justify-between"},La={class:"text-xs opacity-75"},Ia={class:"p-0.5 max-w-full"},Na=["placeholder","disabled"],Oa={class:"flex items-center justify-between"},Pa={class:"flex items-center gap-1"},za={class:"relative model-selector"},$a={class:"max-h-[60vh] overflow-y-auto"},Ua=["onClick"],Ba={class:"flex items-center gap-2"},Fa=["disabled"],Ha={class:"flex-1 overflow-y-auto p-4"},Ga={class:"flex-1 min-w-0"},Wa={class:"flex items-center gap-2"},qa={class:"font-medium text-gray-900 truncate"},ja={class:"text-xs text-gray-400"},Va={key:0,class:"text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full"},Ya={class:"text-xs text-gray-500 break-all"},Xa={class:"flex items-center gap-1"},Za=["onClick"],Ka=["onClick"],Qa={key:0,class:"text-center text-gray-500 py-8"},Ja={key:4,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},eo={class:"add-model-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 flex flex-col"},to={class:"p-4 space-y-4"},ro={key:0,class:"mt-4"},no={class:"flex items-center gap-2"},so={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},ao={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},oo={class:"p-4 border-t border-[var(--border-color)] flex gap-2"},lo=["disabled"],io={key:0,class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},co=Zt({__name:"Main",setup(r){const e=Wr(),t=Gr(),n=_t(),s=Kt(),a=Hr(),c=Ur(),l=Qt(),h=M(!1),i=M(!1),p=M(null),b=M(""),g=M({entryDisplayConfig:{skipJsCss:!0,excludedPathPatterns:[]}}),_=M(!1),v=M(""),S=M(!1),A=M(!1),B=M(!1),D=M({showName:"",baseUrl:"",modelName:"",apiKey:""}),j=M(null),P=M(!1);let se=null;const ne=M(!0),le=M(!0),ve=ue;let we=0;const Me=M(null),V=Ue(()=>!t.currentDatabase||t.currentDatabaseId===ue?"请选择数据源再对话":t.currentDatabase.dataId?"Type your message...":"请先录制程序数据然后再对话"),Be=Ue(()=>!t.currentDatabase||!t.currentDatabase.dataId||s.status==="waiting"),Fe=()=>{var x;return t.databases.length===1&&t.currentDatabaseId===ue?"当前无可用的数据源，请使用 Debug with XCodeMap 录制程序数据。":t.currentDatabaseId===ue?"请先选择数据源再对话":(x=t.currentDatabase)!=null&&x.dataId?"你好！我是你的代码排查助手 XCodeMap。请告诉我具体是哪一个函数调用（CallID_Number）让你感到困惑，或者提供相关的类名、函数名等信息，我将尽力为你分析和解释。":"请先录制程序数据然后再对话"},He=async()=>{var u;const x=l.getText();if(x.trim()){if(!((u=t.currentDatabase)!=null&&u.dataId)){n.setError("当前没有数据，不可聊天。请先选择数据源。或者使用 Debug with XCodeMap 创建新的数据源。");return}if(!a.currentModel){n.setError("请先选择模型再发送消息"),S.value=!0;return}l.clearText(),await e.sendMessage(x)}},Ct=()=>{h.value=!h.value},At=async x=>{var U;const u=t.currentDatabase,Y=!u||u.id!==x;if(we=0,t.setCurrentDatabase(x),h.value=!1,Y){if(!await $t(x,((U=t.currentDatabase)==null?void 0:U.dataId)||"")){n.setError("Failed to switch process data");return}e.createNewChat(),p.value=null,ne.value=!0,i.value=!0}},Ge=async()=>{var x;if((x=t.currentDatabase)!=null&&x.dataId)try{const u=await Ln({processId:t.currentDatabase.dataId,first:ne.value,filterText:b.value}),Y=JSON.stringify(u),U=JSON.stringify(p.value);Y!==U&&(console.log("Data has changed, updating treeData",Y),p.value=u),ne.value=!1}catch(u){console.error("Failed to fetch tree data:",u)}},We=async()=>{const x=[...t.databases],u=t.currentDatabase;await t.getDatabase();const Y=t.databases,U=t.currentDatabase,w=new Set(x.map(K=>K.id)),Te=Y.filter(K=>!w.has(K.id)),_e=x.length===0||x.length===1&&x[0].id===ve;if(Te.filter(K=>!K.id.toLowerCase().includes("local")&&!K.id.toLowerCase().includes("remote")).length>0&&!_e&&n.setError("发现了新的数据源"),u&&u.id!==ve&&!U){console.log("Current database is no longer available, resetting to default"),t.setCurrentDatabase(ve),await $t(ve,"")||n.setError("Failed to switch process data"),h.value=!0,p.value=null,ne.value=!0,i.value=!1,e.createNewChat(),we=0;return}if(u&&U&&u.id===U.id&&u.dataId!==U.dataId&&!await $t(U.id,U.dataId||"")){n.setError("Failed to switch process data");return}U&&U.id!==ve&&(U.serverSelected?we=0:(we++,we>=3&&(console.log("Database consistency check failed 3 times, resetting to default"),t.setCurrentDatabase(ve),h.value=!0,p.value=null,ne.value=!0,i.value=!1,e.createNewChat(),we=0)))};Tn(()=>{Ge()});const Rt=x=>{x.labelKey==="url"&&x.labelValue&&window.open(x.labelValue,"_blank"),console.log("Clicked tree node:",x)},it=x=>{const u=g.value.entryDisplayConfig.excludedPathPatterns.indexOf(x);u>-1&&(g.value.entryDisplayConfig.excludedPathPatterns.splice(u,1),F())},H=()=>{_.value=!0,zt(()=>{const x=document.querySelector(".input-new-tag input");x&&x.focus()})},qe=()=>{v.value&&(g.value.entryDisplayConfig.excludedPathPatterns.push(v.value),F()),_.value=!1,v.value=""},F=async()=>{try{await In(g.value)||n.setError("Failed to update filter configuration")}catch(x){console.error("Failed to update filter configuration:",x)}},je=x=>{const u=x.target;u.closest(".model-selector")||(S.value=!1),u.closest(".model-manager-modal")||(A.value=!1)},$=async()=>{S.value=!1;try{await a.getModelConfigData(),A.value=!0}catch(x){console.error("Failed to open model manager:",x),n.setError("Failed to open model manager")}},ke=()=>{A.value=!1},Ve=()=>{B.value=!0,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""}},Le=()=>{B.value=!1,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""},j.value=null},Ye=async()=>{const x={showName:D.value.showName.trim(),baseUrl:D.value.baseUrl.trim(),modelName:D.value.modelName.trim(),apiKey:D.value.apiKey.trim()};if(!x.showName||!x.baseUrl||!x.modelName||!x.apiKey){n.setError("请填写所有必填字段");return}P.value=!0,j.value=null;const u={uuid:crypto.randomUUID(),showName:x.showName,baseUrl:x.baseUrl,modelName:x.modelName,apiKey:x.apiKey},Y=await Nn(u);if(!Y.success){j.value={success:!1,message:Y.error||"模型连通性测试失败"},P.value=!1;return}const U=await On(u);U.success?(Le(),await a.getModelConfigData()):j.value={success:!1,message:U.error||"添加模型失败"},P.value=!1},ct=async x=>{const u=await Pn(x);u.success?await a.getModelConfigData():n.setError(u.error||"删除模型失败")},ut=async x=>{const u=await zn(x);u.success?await a.getModelConfigData():n.setError(u.error||"设置默认模型失败")};Fr(()=>{c.initTheme(),We(),Ge(),_n().then(x=>{x.success&&x.data&&(g.value.entryDisplayConfig=x.data.entryDisplayConfig)}),a.getModelConfigData(),document.addEventListener("click",je),window.addEventListener("textarea-content-changed",Ne),se=window.setInterval(()=>{We(),Ge()},1e3)}),En(()=>{se!==null&&(clearInterval(se),se=null),document.removeEventListener("click",je),window.removeEventListener("textarea-content-changed",Ne)});const ye=(x,u)=>u!=="tool"?x.replace(/`?CallID_(\d+)`?/g,"[CallID_$1](./findFunc?callId=$1)"):x,Ie=x=>{L.setOptions({breaks:!0,gfm:!0,pedantic:!0});const u=L.parse(x);return oa.sanitize(u,{ALLOWED_TAGS:["p","br","strong","em","code","pre","blockquote","ul","ol","li","h1","h2","h3","h4","h5","h6","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title","class"]})},de=x=>{const u=x.target;u.style.height="auto",u.style.height=Math.min(u.scrollHeight,200)+"px"},Ne=()=>{zt(()=>{const x=document.querySelector(".input");x&&(x.style.height="auto",x.style.height=Math.min(x.scrollHeight,200)+"px")})},Xe=()=>{zt(()=>{Me.value&&(Me.value.scrollTop=Me.value.scrollHeight)})};return wt(()=>{var x;return(x=e.currentChat)==null?void 0:x.messages},()=>{var x;Xe(),(x=e.currentChat)!=null&&x.messages&&e.currentChat.messages.length>0&&(i.value=!1)},{deep:!0}),wt(()=>l.message,()=>{l.message.trim()&&(i.value=!1)}),(x,u)=>{var Y,U;return k(),T("div",{class:C(["h-screen w-full flex flex-col overflow-x-hidden",(f(c).theme==="dark","bg-[var(--bg-color)]")])},[f(n).error?(k(),T("div",{key:0,class:C(["fixed top-0 left-0 right-0 px-4 py-3 z-[9999] flex items-center justify-between",f(c).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[d("span",la,q(f(n).error.message),1),d("button",{onClick:u[0]||(u[0]=w=>f(n).clearError()),class:C(["ml-4 p-1 rounded-full hover:bg-opacity-20",f(c).theme==="dark"?"hover:bg-red-100":"hover:bg-red-200"])},u[18]||(u[18]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),2)],2)):O("",!0),d("div",{class:C(["border-b py-0.5 px-0.5 flex items-center justify-between sticky top-0 z-20 bg-[var(--bg-color)]",(f(c).theme==="dark","border-[var(--border-color)]")])},[d("div",ia,[d("div",ca,[d("button",{onClick:Ct,class:C(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,q(f(t).currentDatabase?f(t).currentDatabase.name:"待选择数据源(取消行号X图标)"),1),(k(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":h.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[19]||(u[19]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2)])]),d("button",{onClick:u[1]||(u[1]=()=>{f(e).createNewChat(),le.value=!0}),class:C(["p-1 rounded-full",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"]),title:"新建聊天"},u[20]||(u[20]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)],2),h.value||f(t).currentDatabaseId===f(ue)?(k(),T("div",ua,[f(t).databases.length===1?(k(),T("div",da,u[21]||(u[21]=[d("span",null,"当前无可用的数据源，请使用",-1),d("img",{src:Bn,alt:"XCodeMap Logo",class:"w-4 h-4"},null,-1),d("span",null,"Debug with XCodeMap 录制程序数据。",-1)]))):(k(),T("div",pa,[(k(!0),T(me,null,Ce(f(t).databases,w=>(k(),T("button",{key:w.id,onClick:Te=>At(w.id),class:C(["w-full px-2 py-0.5 rounded-lg text-xs font-medium text-left border-2 transition-all duration-150 focus:outline-none",[w.id===f(t).currentDatabaseId?f(c).theme==="dark"?"button-selected-dark":"button-selected-light":f(c).theme==="dark"?"button-hover-dark":"button-hover-light"]])},q(w.name),11,ha))),128))]))])):O("",!0),f(t).currentDatabase&&f(t).currentDatabase.active?(k(),T("div",fa,[d("div",ga,[d("span",{class:C(["px-1.5 py-0.5 text-xs rounded-full opacity-75",{"bg-green-50 text-green-600":f(t).currentDatabase.recordState==="recording","bg-gray-50 text-gray-500":f(t).currentDatabase.recordState==="idle","bg-yellow-50 text-yellow-600":f(t).currentDatabase.recordState==="paused"}])},q(f(t).currentDatabase.recordState),3),f(t).currentDatabase.recordState==="idle"?(k(),T("button",{key:0,onClick:u[2]||(u[2]=w=>f(t).startRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-green-50 text-green-600 hover:bg-green-200 hover:text-green-800 border border-green-200 hover:border-green-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"开始录制"}," 开始录制 ")):O("",!0),f(t).currentDatabase.recordState==="recording"?(k(),T("button",{key:1,onClick:u[3]||(u[3]=w=>f(t).endRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-red-50 text-red-600 hover:bg-red-200 hover:text-red-800 border border-red-200 hover:border-red-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"结束录制"}," 结束录制 ")):O("",!0),f(t).currentDatabase.recordState==="paused"?(k(),T("button",{key:2,onClick:u[4]||(u[4]=w=>f(t).restartRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-yellow-50 text-yellow-600 hover:bg-yellow-200 hover:text-yellow-800 border border-yellow-200 hover:border-yellow-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"重新录制"}," 重新录制 ")):O("",!0)])])):O("",!0),d("div",ma,[f(t).currentDatabaseId!==f(ue)?(k(),T("div",{key:0,class:C([(f(c).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]"),"border-b border-t"])},[d("div",ba,[d("div",{onClick:u[5]||(u[5]=w=>i.value=!i.value),class:C(["flex items-center cursor-pointer rounded-lg px-1 py-0.5 border-b border-[var(--border-color)] bg-[var(--undercaret-bg-color)] transition-colors duration-150",(f(c).theme==="dark","hover:bg-[var(--header-hover-bg-color)]")])},[(k(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-90":i.value,"text-[var(--text-color)]":!0}]),viewBox:"0 0 20 20",fill:"currentColor"},u[22]||(u[22]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),u[23]||(u[23]=d("span",{class:"text-xs font-medium px-1 text-[var(--text-color)]"},"请求与线程入口",-1))],2)]),i.value?(k(),T("div",{key:0,class:C(["p-1 bg-[var(--undercaret-bg-color)] overflow-y-auto",(f(c).theme==="dark","border-t border-[var(--border-color)]")])},[d("div",xa,[Se(d("input",{"onUpdate:modelValue":u[6]||(u[6]=w=>b.value=w),type:"text",placeholder:"搜索网络请求...",class:C(["w-full px-2 py-0.5 rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400":"bg-white border-gray-300 text-gray-700"])},null,2),[[Ae,b.value]])]),p.value?(k(),T("div",va,[Sn(ts,{nodes:p.value.rootNodes,onNodeClick:Rt},null,8,["nodes"])])):(k(),T("div",wa,"Loading tree data...")),d("div",ka,[d("div",ya,[d("span",{class:C(["text-xs opacity-50",f(c).theme==="dark"?"text-gray-400":"text-gray-600"])},"排除请求：",2),d("label",{class:C(["flex items-center gap-1 text-xs cursor-pointer opacity-50 hover:opacity-100 transition-opacity",f(c).theme==="dark"?"text-gray-500":"text-gray-400"])},[Se(d("input",{type:"checkbox","onUpdate:modelValue":u[7]||(u[7]=w=>g.value.entryDisplayConfig.skipJsCss=w),onChange:F,class:C(["rounded focus:ring-0 focus:ring-offset-0 h-2.5 w-2.5 cursor-pointer",f(c).theme==="dark"?"bg-gray-800 border-gray-600 text-gray-500":"border-gray-300 text-gray-400"])},null,34),[[Cn,g.value.entryDisplayConfig.skipJsCss]]),u[24]||(u[24]=d("span",{class:"text-[11px]"},"忽略css/js",-1))],2),d("div",Ta,[(k(!0),T(me,null,Ce(g.value.entryDisplayConfig.excludedPathPatterns,w=>(k(),T("div",{key:w,class:C(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"])},[d("span",null,q(w),1),d("button",{onClick:Te=>it(w),class:C(f(c).theme==="dark"?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700")},u[25]||(u[25]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,_a)],2))),128)),_.value?Se((k(),T("input",{key:0,"onUpdate:modelValue":u[8]||(u[8]=w=>v.value=w),class:C(["input-new-tag px-1 py-0 border rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200":"border-gray-300 text-gray-700"]),placeholder:"输入排除表达式，* 匹配一级路径，** 匹配多级路径",onKeyup:An(qe,["enter"]),onBlur:qe},null,34)),[[Ae,v.value]]):(k(),T("button",{key:1,onClick:H,class:C(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-blue-400":"bg-gray-100 hover:bg-gray-200 text-blue-700"])},u[26]||(u[26]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),d("span",null,"New Filter",-1)]),2))])]),d("button",{onClick:u[9]||(u[9]=w=>i.value=!1),class:C(["rounded-full flex items-center",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"]),title:"收起网络请求"},u[27]||(u[27]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),2)])],2)):O("",!0)],2)):O("",!0),d("div",{ref_key:"messageContainerRef",ref:Me,class:"p-0.5 space-y-0.5 w-full border-b border-[var(--border-color)]"},[f(e).currentChat?(k(),T(me,{key:0},[f(e).currentChat.messages.length===0?(k(),T("div",Ea,[d("div",Sa,[d("p",Ca,[(k(),Rn(rs,{key:(Y=f(e).currentChat)==null?void 0:Y.id,text:Fe(),speed:20,onComplete:u[10]||(u[10]=w=>le.value=!1)},null,8,["text"]))])])])):O("",!0),(k(!0),T(me,null,Ce(f(e).currentChat.messages,w=>(k(),T("div",{key:w.messageId,class:C(["flex items-start gap-1.5 max-w-full",{"justify-end":w.role==="user"}])},[d("div",{class:C(["flex-1 rounded-lg p-2 shadow-sm break-words min-w-0",w.role==="user"?(f(c).theme==="dark","bg-[var(--undercaret-bg-color)]"):(f(c).theme==="dark","bg-[var(--bg-color)]")])},[d("div",{class:C([w.role==="user"?(f(c).theme==="dark","text-[var(--text-color)]"):(f(c).theme==="dark","text-[var(--text-color)]"),"leading-tight text-[15px] markdown-content break-words whitespace-pre-wrap"]),innerHTML:Ie(ye(w.content,w.role))},null,10,Aa)],2)],2))),128))],64)):(k(),T("div",Ra," 请选择数据源后再对话 "))],512)]),d("div",Da,[f(s).status==="waiting"&&f(t).currentDatabase&&f(t).currentDatabaseId!==f(ue)?(k(),T("div",{key:0,class:C(["border-t border-b py-1.5 px-3",(f(c).theme==="dark","bg-[var(--undercaret-bg-color)] border-[var(--border-color)] text-[var(--text-color)]")])},[d("div",Ma,[d("span",La,q(f(s).statusMessage||"未知状态..."),1)])],2)):O("",!0),d("div",Ia,[Se(d("textarea",{"onUpdate:modelValue":u[11]||(u[11]=w=>f(l).message=w),class:"input w-full resize-none mb-0.5",rows:"1",placeholder:V.value,disabled:Be.value,onInput:de,style:{"min-height":"28px","max-height":"200px","overflow-y":"auto"}},null,40,Na),[[Ae,f(l).message]]),d("div",Oa,[d("div",Pa,[d("span",{class:C(["text-xs",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])},"模型",2),d("div",za,[d("button",{onClick:u[12]||(u[12]=w=>S.value=!S.value),class:C(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,q(((U=f(a).currentModel)==null?void 0:U.showName)||"请选择模型"),1),(k(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":S.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[28]||(u[28]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2),S.value?(k(),T("div",{key:0,class:C(["absolute bottom-full left-0 mb-2 w-80 border rounded-lg shadow-lg z-50",f(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[d("div",{class:C(["p-2 border-b",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("span",{class:C(["text-sm font-medium",f(c).theme==="dark"?"text-gray-200":"text-gray-700"])},"选择模型",2)],2),d("div",$a,[(k(!0),T(me,null,Ce(f(a).availableModels,w=>(k(),T("button",{key:w.uuid,onClick:()=>{f(a).setCurrentModel(w.uuid),S.value=!1},class:C(["w-full px-3 py-2 text-left text-sm transition-all duration-150 border-2 focus:outline-none",{"button-selected-dark":f(a).currentModelUuid===w.uuid&&f(c).theme==="dark","button-selected-light":f(a).currentModelUuid===w.uuid&&f(c).theme==="light","button-hover-dark":f(c).theme==="dark"&&f(a).currentModelUuid!==w.uuid,"button-hover-light":f(c).theme==="light"&&f(a).currentModelUuid!==w.uuid}])},[d("div",Ba,[d("span",null,q(w.showName),1),d("span",{class:C(["text-xs opacity-75",[f(a).currentModelUuid===w.uuid?f(c).theme==="dark"?"text-gray-300":"text-blue-500":f(c).theme==="dark"?"text-gray-500":"text-gray-400"]])},"("+q(w.modelName)+")",3)])],10,Ua))),128)),f(a).availableModels.length===0?(k(),T("div",{key:0,class:C(["px-3 py-2 text-sm",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])}," 暂无可用模型 ",2)):O("",!0)]),d("div",{class:C(["border-t",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("button",{onClick:$,class:C(["w-full px-3 py-2 text-left text-sm transition-colors",f(c).theme==="dark"?"text-blue-400 hover:bg-gray-700":"text-blue-600 hover:bg-blue-50"])}," 管理模型 ",2)],2)],2)):O("",!0)])]),d("button",{onClick:He,class:C(["p-0.5 rounded-full transition-colors",[f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400 hover:text-gray-200":"hover:bg-gray-100 text-gray-600",{"opacity-50 cursor-not-allowed":!f(l).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting"}]]),disabled:!f(l).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting",title:"发送消息"},u[29]||(u[29]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),10,Fa)])])]),A.value?(k(),T("div",{key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:ke},[d("div",{class:"model-manager-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 max-h-[80vh] flex flex-col",onClick:u[13]||(u[13]=Mn(()=>{},["stop"]))},[d("div",{class:"p-2 border-b border-[var(--border-color)] flex items-center justify-between"},[u[31]||(u[31]=d("h3",{class:"text-base font-medium text-[var(--text-color)]"},"模型管理",-1)),d("button",{onClick:ke,class:"text-gray-400 hover:text-gray-600"},u[30]||(u[30]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",Ha,[(k(!0),T(me,null,Ce(f(a).availableModels,w=>{var Te,_e;return k(),T("div",{key:w.uuid,class:"flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-1"},[d("div",Ga,[d("div",Wa,[d("span",qa,q(w.showName),1),d("span",ja,"("+q(w.modelName)+")",1),((Te=f(a).modelConfig)==null?void 0:Te.defaultModelId)===w.uuid?(k(),T("span",Va," 默认 ")):O("",!0)]),d("div",Ya,q(w.baseUrl),1)]),d("div",Xa,[((_e=f(a).modelConfig)==null?void 0:_e.defaultModelId)!==w.uuid?(k(),T("button",{key:0,onClick:Ee=>ut(w),class:"flex-shrink-0 text-blue-500 hover:text-blue-700",title:"设为默认"},u[32]||(u[32]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]),8,Za)):O("",!0),d("button",{onClick:Ee=>ct(w),class:"flex-shrink-0 text-red-500 hover:text-red-700",title:"删除模型"},u[33]||(u[33]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ka)])])}),128)),f(a).availableModels.length===0?(k(),T("div",Qa," 暂无已添加的模型 ")):O("",!0)]),d("div",{class:"p-4 border-t border-[var(--border-color)]"},[d("button",{onClick:Ve,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"},u[34]||(u[34]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Dn(" 添加新模型 ")]))])])])):O("",!0),B.value?(k(),T("div",Ja,[d("div",eo,[d("div",{class:"p-4 border-b border-[var(--border-color)] flex items-center justify-between"},[u[36]||(u[36]=d("h3",{class:"text-lg font-medium text-[var(--text-color)]"},"添加新模型",-1)),d("button",{onClick:Le,class:"text-gray-400 hover:text-gray-600"},u[35]||(u[35]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",to,[d("div",null,[u[37]||(u[37]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型显示名称",-1)),Se(d("input",{"onUpdate:modelValue":u[14]||(u[14]=w=>D.value.showName=w),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入模型显示名称"},null,512),[[Ae,D.value.showName]]),u[38]||(u[38]=d("p",{class:"mt-1 text-xs text-gray-500"},"模型需要兼容OpenAI的API，需要支持 FunctionCall。",-1))]),d("div",null,[u[39]||(u[39]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Base URL",-1)),Se(d("input",{"onUpdate:modelValue":u[15]||(u[15]=w=>D.value.baseUrl=w),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: https://api.openai.com/v1"},null,512),[[Ae,D.value.baseUrl]]),u[40]||(u[40]=d("p",{class:"mt-1 text-xs text-gray-500"},"例如: https://dashscope.aliyuncs.com/compatible-mode/v1 或者 https://api.deepseek.com/v1 ",-1))]),d("div",null,[u[41]||(u[41]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型名称",-1)),Se(d("input",{"onUpdate:modelValue":u[16]||(u[16]=w=>D.value.modelName=w),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: gpt-3.5-turbo"},null,512),[[Ae,D.value.modelName]]),u[42]||(u[42]=d("p",{class:"mt-1 text-xs text-gray-500"},"国内推荐阿里云qwen-max-latest 或者 deepseek-chat，国外推荐 Claude Sonnet 系列",-1))]),d("div",null,[u[43]||(u[43]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"API Key",-1)),Se(d("input",{"onUpdate:modelValue":u[17]||(u[17]=w=>D.value.apiKey=w),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入API Key"},null,512),[[Ae,D.value.apiKey]])]),j.value?(k(),T("div",ro,[d("div",{class:C(["p-3 rounded-lg",{"bg-green-50 text-green-700":j.value.success,"bg-red-50 text-red-700":!j.value.success}])},[d("div",no,[j.value.success?(k(),T("svg",so,u[44]||(u[44]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(k(),T("svg",ao,u[45]||(u[45]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 00-1.414-1.414L10 10l-1.293-1.293z","clip-rule":"evenodd"},null,-1)]))),d("span",null,q(j.value.message),1)])],2)])):O("",!0)]),d("div",oo,[d("button",{onClick:Le,class:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),d("button",{onClick:Ye,class:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",disabled:P.value},[P.value?(k(),T("svg",io,u[46]||(u[46]=[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):O("",!0),d("span",null,q(P.value?"测试中...":"保存"),1)],8,lo)])])])):O("",!0)],2)}}}),uo=Br(co,[["__scopeId","data-v-444fda9d"]]),ir=$n(uo);ir.use(Un());ir.mount("#app");window.$vm=ir._instance;
