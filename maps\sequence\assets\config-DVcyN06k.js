import{h as E,u as O,i as $,r as v,w as _,M as q,v as H,y as N,j as a,o as s,m as x,l as e,q as i,n as u,t as C,F as P,k as M,A as p,B,S as h,L as D,_ as R,Q as j,R as Q}from"./style-D0LgP_vx.js";const X={class:"block sm:inline"},G={class:"flex-1 overflow-y-auto overflow-x-hidden p-2"},J={class:"max-w-4xl mx-auto space-y-1.5"},K={class:"flex items-center gap-1.5 mb-1.5"},W={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},Y={key:1,class:"flex flex-wrap gap-1"},Z={class:"flex items-center gap-1.5 mb-1.5"},ee={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},oe={class:"flex flex-col gap-1.5"},te=["onClick"],re={class:"flex flex-col gap-1.5"},le={key:1,class:"flex gap-1 mt-1"},ae={class:"flex items-center gap-1.5 mb-1.5"},se={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},ne={class:"flex flex-col gap-1.5"},ie=["onClick"],de={key:0,class:"flex flex-col gap-1 w-full"},ue={class:"flex gap-1"},ce={class:"flex items-center gap-1.5 mb-1.5"},ve={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},xe={class:"flex gap-3"},pe={class:"flex items-center gap-1.5 cursor-pointer"},ge={class:"flex items-center gap-1.5 cursor-pointer"},be={class:"flex items-center gap-1.5 cursor-pointer"},fe={class:"flex gap-3"},me={class:"flex items-center gap-1.5 cursor-pointer"},we={class:"flex items-center gap-1.5 cursor-pointer"},he=E({__name:"Config",setup(ye){const g=O(),d=$(),F=v(!0),t=v({startOnDebug:!1,recordMode:"smart",enableAutoDetect:!0,autoDetectedPackages:[],includedPackagePrefixes:[],includedParentClasses:[]}),b=v({0:!1,1:!1,2:!1,3:!1}),w=v(!1),f=v(""),k=v(!1),m=v(""),y=r=>{b.value[r]=!b.value[r]},L=r=>{t.value.includedPackagePrefixes&&(t.value.includedPackagePrefixes.splice(r,1),n())},I=r=>{t.value.includedParentClasses&&(t.value.includedParentClasses.splice(r,1),n())};_(t,()=>{F.value||n()},{deep:!0});const n=async()=>{try{const r=await q(t.value);if(!r.success){console.error("Failed to update filter configuration:",r.error),g.setError(r.error||"Failed to update filter configuration");return}}catch(r){console.error("Failed to update filter configuration:",r),g.setError("Failed to update filter configuration")}},S=()=>{w.value=!0,D(()=>{const r=document.querySelector(".input-new-package-prefix input");r&&r.focus()})},A=()=>{f.value&&(t.value.includedPackagePrefixes||(t.value.includedPackagePrefixes=[]),t.value.includedPackagePrefixes.push(f.value),n()),w.value=!1,f.value=""},T=()=>{k.value=!0,D(()=>{const r=document.querySelector(".input-new-parent-class input");r&&r.focus()})},U=()=>{m.value&&(t.value.includedParentClasses||(t.value.includedParentClasses=[]),t.value.includedParentClasses.push({type:m.value}),n()),k.value=!1,m.value=""};return H(async()=>{d.initTheme();try{const r=await N();r.success&&r.data?t.value=r.data:g.setError(r.error||"Failed to get initial configuration")}catch(r){console.error("Failed to get initial configuration:",r),g.setError("Failed to get initial configuration")}}),(r,o)=>{var V;return s(),a("div",{class:u(["h-screen min-w-full w-screen flex flex-col overflow-x-hidden",(i(d).theme==="dark","bg-[var(--bg-color)]")])},[(V=i(g))!=null&&V.error?(s(),a("div",{key:0,class:u(["fixed top-2 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300",i(d).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[e("span",X,C(i(g).error.message),1)],2)):x("",!0),e("div",{class:u(["border-b py-2 px-4 shadow-sm",(i(d).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]")])},o[13]||(o[13]=[e("div",{class:"flex items-center"},[e("span",{class:"text-lg font-semibold text-[var(--text-color)]"},"录制配置")],-1)]),2),e("div",G,[e("div",J,[e("div",{class:u(["rounded p-3 shadow-sm",(i(d).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",K,[o[15]||(o[15]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制项目包",-1)),e("button",{onClick:o[0]||(o[0]=l=>y("1")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[14]||(o[14]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),b.value[1]?(s(),a("p",W," XCodeMap 默认检测并录制项目包。如果要录制依赖包，请在后面手工添加。如果发现项目包遗漏，也可在后面添加。 ")):x("",!0),t.value.enableAutoDetect?(s(),a("div",Y,[(s(!0),a(P,null,M(t.value.autoDetectedPackages,(l,c)=>(s(),a("div",{key:c,class:"px-1.5 py-0.5 rounded-full text-xs font-medium bg-[var(--bg-color)] text-[var(--text-color)]"},C(l),1))),128))])):x("",!0)],2),e("div",{class:u(["rounded p-3 shadow-sm",(i(d).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Z,[o[17]||(o[17]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制依赖包",-1)),e("button",{onClick:o[1]||(o[1]=l=>y("2")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[16]||(o[16]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),b.value[2]?(s(),a("p",ee," 配置包的前缀即可。例如配置 org.spring，将录制 spring 模块的调用数据。注意，如果包范围内的类处于循环或者递归中，则只采集部分调用。 ")):x("",!0),e("div",oe,[(s(!0),a(P,null,M(t.value.includedPackagePrefixes||[],(l,c)=>(s(),a("div",{key:c,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,C(l),1),e("button",{onClick:()=>{L(c)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},o[18]||(o[18]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,te)]))),128)),e("div",re,[w.value?p((s(),a("input",{key:0,"onUpdate:modelValue":o[2]||(o[2]=l=>f.value=l),class:"input-new-package-prefix px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写包前缀，例如 org.spring"},null,512)),[[B,f.value]]):x("",!0),w.value?(s(),a("div",le,[e("button",{onClick:A,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:o[3]||(o[3]=()=>{w.value=!1,f.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])):(s(),a("button",{key:2,onClick:S,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},o[19]||(o[19]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加包前缀",-1)])))])])],2),e("div",{class:u(["rounded p-3 shadow-sm",(i(d).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",ae,[o[21]||(o[21]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制重点类或接口",-1)),e("button",{onClick:o[4]||(o[4]=l=>y("3")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[20]||(o[20]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),b.value[3]?(s(),a("p",se," 配置类（接口）的完整名称。例如配置 org.springframework.beans.factory.BeanFactory，将录制该类（接口）本身及其子类的调用。 这些类即使处于循环或者递归中，也会录制每个函数调用。 ")):x("",!0),e("div",ne,[(s(!0),a(P,null,M(t.value.includedParentClasses,(l,c)=>(s(),a("div",{key:c,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,C(l.type),1),e("button",{onClick:()=>{I(c)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},o[22]||(o[22]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,ie)]))),128)),k.value?(s(),a("div",de,[p(e("input",{"onUpdate:modelValue":o[5]||(o[5]=l=>m.value=l),class:"input-new-parent-class px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写类完整名称，如 org.springframework.beans.factory.BeanFactory"},null,512),[[B,m.value]]),e("div",ue,[e("button",{onClick:U,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:o[6]||(o[6]=()=>{k.value=!1,m.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("button",{key:1,onClick:T,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},o[23]||(o[23]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加类",-1)])))])],2),e("div",{class:u(["rounded p-3 shadow-sm",(i(d).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",ce,[o[25]||(o[25]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制模式",-1)),e("button",{onClick:o[7]||(o[7]=l=>y("0")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[24]||(o[24]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),b.value[0]?(s(),a("p",ve," 智能模式，针对 SpringBoot 或者 Tomcat，只录制 HTTP/DB 等请求。 全录模式，默认录制所有链路。手工模式，默认不录制，需手工开启录制。 ")):x("",!0),e("div",xe,[e("label",pe,[p(e("input",{type:"radio","onUpdate:modelValue":o[8]||(o[8]=l=>t.value.recordMode=l),value:"smart",onChange:n,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.recordMode]]),o[26]||(o[26]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"智能模式",-1))]),e("label",ge,[p(e("input",{type:"radio","onUpdate:modelValue":o[9]||(o[9]=l=>t.value.recordMode=l),value:"all",onChange:n,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.recordMode]]),o[27]||(o[27]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"全录模式",-1))]),e("label",be,[p(e("input",{type:"radio","onUpdate:modelValue":o[10]||(o[10]=l=>t.value.recordMode=l),value:"manual",onChange:n,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.recordMode]]),o[28]||(o[28]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"手工模式",-1))])])],2),e("div",{class:u(["rounded p-3 shadow-sm",(i(d).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[o[31]||(o[31]=e("h3",{class:"text-sm font-semibold mb-1.5 text-[var(--text-color)]"},"默认随着 Debug 启动（测试功能）",-1)),e("div",fe,[e("label",me,[p(e("input",{type:"radio","onUpdate:modelValue":o[11]||(o[11]=l=>t.value.startOnDebug=l),value:!0,onChange:n,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.startOnDebug]]),o[29]||(o[29]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"是",-1))]),e("label",we,[p(e("input",{type:"radio","onUpdate:modelValue":o[12]||(o[12]=l=>t.value.startOnDebug=l),value:!1,onChange:n,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.startOnDebug]]),o[30]||(o[30]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"否",-1))])])],2)])])],2)}}}),ke=R(he,[["__scopeId","data-v-8941cee0"]]),z=j(ke);z.use(Q());z.mount("#app");window.$vm=z._instance;
