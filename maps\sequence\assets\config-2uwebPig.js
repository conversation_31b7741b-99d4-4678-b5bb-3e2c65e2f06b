import{h as U,u as E,i as O,r as x,w as $,M as q,v as H,y as N,j as i,o as u,m as c,l as e,q as t,n as r,t as C,F as P,k as M,A as p,B as V,S as h,L as B,_ as R,Q as j,R as Q}from"./style-DfXeGTYE.js";const X={class:"block sm:inline"},G={class:"flex items-center"},J={class:"flex-1 overflow-y-auto overflow-x-hidden p-2"},K={class:"max-w-4xl mx-auto space-y-1.5"},W={class:"flex items-center gap-1.5 mb-1.5"},Y={key:1,class:"flex flex-wrap gap-1"},Z={class:"flex items-center gap-1.5 mb-1.5"},ee={class:"flex flex-col gap-1.5"},te=["onClick"],ae={class:"flex flex-col gap-1.5"},re={key:1,class:"flex gap-1 mt-1"},se={class:"flex items-center gap-1.5 mb-1.5"},le={class:"flex flex-col gap-1.5"},oe=["onClick"],ne={key:0,class:"flex flex-col gap-1 w-full"},ie={class:"flex gap-1"},ue={class:"flex items-center gap-1.5 mb-1.5"},de={class:"flex gap-3"},ge={class:"flex items-center gap-1.5 cursor-pointer"},xe={class:"flex items-center gap-1.5 cursor-pointer"},ce={class:"flex items-center gap-1.5 cursor-pointer"},pe={class:"flex gap-3"},fe={class:"flex items-center gap-1.5 cursor-pointer"},ve={class:"flex items-center gap-1.5 cursor-pointer"},ye=U({__name:"Config",setup(me){const f=E(),a=O(),D=x(!0),l=x({startOnDebug:!1,recordMode:"smart",enableAutoDetect:!0,autoDetectedPackages:[],includedPackagePrefixes:[],includedParentClasses:[]}),v=x({0:!1,1:!1,2:!1,3:!1}),m=x(!1),y=x(""),w=x(!1),b=x(""),k=o=>{v.value[o]=!v.value[o]},F=o=>{l.value.includedPackagePrefixes&&(l.value.includedPackagePrefixes.splice(o,1),d())},L=o=>{l.value.includedParentClasses&&(l.value.includedParentClasses.splice(o,1),d())};$(l,()=>{D.value||d()},{deep:!0});const d=async()=>{try{const o=await q(l.value);if(!o.success){console.error("Failed to update filter configuration:",o.error),f.setError(o.error||"Failed to update filter configuration");return}}catch(o){console.error("Failed to update filter configuration:",o),f.setError("Failed to update filter configuration")}},I=()=>{m.value=!0,B(()=>{const o=document.querySelector(".input-new-package-prefix input");o&&o.focus()})},S=()=>{y.value&&(l.value.includedPackagePrefixes||(l.value.includedPackagePrefixes=[]),l.value.includedPackagePrefixes.push(y.value),d()),m.value=!1,y.value=""},A=()=>{w.value=!0,B(()=>{const o=document.querySelector(".input-new-parent-class input");o&&o.focus()})},T=()=>{b.value&&(l.value.includedParentClasses||(l.value.includedParentClasses=[]),l.value.includedParentClasses.push({type:b.value}),d()),w.value=!1,b.value=""};return H(async()=>{a.initTheme();try{const o=await N();o.success&&o.data?l.value=o.data:f.setError(o.error||"Failed to get initial configuration")}catch(o){console.error("Failed to get initial configuration:",o),f.setError("Failed to get initial configuration")}}),(o,s)=>{var z;return u(),i("div",{class:r(["h-screen min-w-full w-screen flex flex-col overflow-x-hidden",t(a).theme==="dark"?"bg-gray-900":"bg-gray-50"])},[(z=t(f))!=null&&z.error?(u(),i("div",{key:0,class:r(["fixed top-2 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300",t(a).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[e("span",X,C(t(f).error.message),1)],2)):c("",!0),e("div",{class:r(["border-b py-2 px-4 shadow-sm",t(a).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[e("div",G,[e("span",{class:r(["text-lg font-semibold",t(a).theme==="dark"?"text-gray-100":"text-gray-900"])},"录制配置",2)])],2),e("div",J,[e("div",K,[e("div",{class:r(["rounded p-3 shadow-sm",t(a).theme==="dark"?"bg-gray-800":"bg-white"])},[e("div",W,[e("h3",{class:r(["text-sm font-semibold",t(a).theme==="dark"?"text-gray-100":"text-gray-900"])},"录制项目包",2),e("button",{onClick:s[0]||(s[0]=n=>k("1")),class:r([t(a).theme==="dark"?"text-gray-500 hover:text-gray-400":"text-gray-400 hover:text-gray-600","transition-colors duration-200"])},s[13]||(s[13]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]),2)]),v.value[1]?(u(),i("p",{key:0,class:r(["text-xs mb-1.5 p-1.5 rounded",t(a).theme==="dark"?"text-gray-300 bg-gray-700":"text-gray-500 bg-gray-50"])}," XCodeMap 默认检测并录制项目包。如果要录制依赖包，请在后面手工添加。如果发现项目包遗漏，也可在后面添加。 ",2)):c("",!0),l.value.enableAutoDetect?(u(),i("div",Y,[(u(!0),i(P,null,M(l.value.autoDetectedPackages,(n,g)=>(u(),i("div",{key:g,class:r(["px-1.5 py-0.5 rounded-full text-xs font-medium",t(a).theme==="dark"?"bg-gray-700 text-gray-300":"bg-blue-50 text-blue-700"])},C(n),3))),128))])):c("",!0)],2),e("div",{class:r(["rounded p-3 shadow-sm",t(a).theme==="dark"?"bg-gray-800":"bg-white"])},[e("div",Z,[e("h3",{class:r(["text-sm font-semibold",t(a).theme==="dark"?"text-gray-100":"text-gray-900"])},"录制依赖包",2),e("button",{onClick:s[1]||(s[1]=n=>k("2")),class:r([t(a).theme==="dark"?"text-gray-500 hover:text-gray-400":"text-gray-400 hover:text-gray-600","transition-colors duration-200"])},s[14]||(s[14]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]),2)]),v.value[2]?(u(),i("p",{key:0,class:r(["text-xs mb-1.5 p-1.5 rounded",t(a).theme==="dark"?"text-gray-300 bg-gray-700":"text-gray-500 bg-gray-50"])}," 配置包的前缀即可。例如配置 org.spring，将录制 spring 模块的调用数据。注意，如果包范围内的类处于循环或者递归中，则只采集部分调用。 ",2)):c("",!0),e("div",ee,[(u(!0),i(P,null,M(l.value.includedPackagePrefixes||[],(n,g)=>(u(),i("div",{key:g,class:r(["inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit",t(a).theme==="dark"?"bg-gray-700 text-gray-300":"bg-blue-50 text-blue-700"])},[e("span",null,C(n),1),e("button",{onClick:()=>{F(g)},class:r(["inline-flex items-center p-0.5 rounded-full hover:bg-opacity-80 transition-colors",t(a).theme==="dark"?"text-gray-400 hover:text-gray-200":"text-blue-500 hover:text-blue-700"])},s[15]||(s[15]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,te)],2))),128)),e("div",ae,[m.value?p((u(),i("input",{key:0,"onUpdate:modelValue":s[2]||(s[2]=n=>y.value=n),class:r(["input-new-package-prefix px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full",t(a).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-100 focus:ring-gray-500 focus:border-transparent":"border-gray-300 focus:ring-blue-500 focus:border-transparent"]),placeholder:"填写包前缀，例如 org.spring"},null,2)),[[V,y.value]]):c("",!0),m.value?(u(),i("div",re,[e("button",{onClick:S,class:r(["px-2 py-0.5 rounded text-xs transition-colors",t(a).theme==="dark"?"bg-gray-600 hover:bg-gray-500 text-gray-100":"bg-blue-500 hover:bg-blue-600 text-white"])}," 保存 ",2),e("button",{onClick:s[3]||(s[3]=()=>{m.value=!1,y.value=""}),class:r(["px-2 py-0.5 rounded text-xs transition-colors",t(a).theme==="dark"?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"])}," 取消 ",2)])):(u(),i("button",{key:2,onClick:I,class:r(["inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors",t(a).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-gray-200":"bg-blue-50 hover:bg-blue-100 text-blue-700"])},s[16]||(s[16]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加包前缀",-1)]),2))])])],2),e("div",{class:r(["rounded p-3 shadow-sm",t(a).theme==="dark"?"bg-gray-800":"bg-white"])},[e("div",se,[e("h3",{class:r(["text-sm font-semibold",t(a).theme==="dark"?"text-gray-100":"text-gray-900"])},"录制重点类或接口",2),e("button",{onClick:s[4]||(s[4]=n=>k("3")),class:r([t(a).theme==="dark"?"text-gray-500 hover:text-gray-400":"text-gray-400 hover:text-gray-600","transition-colors duration-200"])},s[17]||(s[17]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]),2)]),v.value[3]?(u(),i("p",{key:0,class:r(["text-xs mb-1.5 p-1.5 rounded",t(a).theme==="dark"?"text-gray-300 bg-gray-700":"text-gray-500 bg-gray-50"])}," 配置类（接口）的完整名称。例如配置 org.springframework.beans.factory.BeanFactory，将录制该类（接口）本身及其子类的调用。 这些类即使处于循环或者递归中，也会录制每个函数调用。 ",2)):c("",!0),e("div",le,[(u(!0),i(P,null,M(l.value.includedParentClasses,(n,g)=>(u(),i("div",{key:g,class:r(["inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit",t(a).theme==="dark"?"bg-gray-700 text-gray-300":"bg-blue-50 text-blue-700"])},[e("span",null,C(n.type),1),e("button",{onClick:()=>{L(g)},class:r(t(a).theme==="dark"?"text-gray-400 hover:text-gray-200":"text-blue-500 hover:text-blue-700")},s[18]||(s[18]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,oe)],2))),128)),w.value?(u(),i("div",ne,[p(e("input",{"onUpdate:modelValue":s[5]||(s[5]=n=>b.value=n),class:r(["input-new-parent-class px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full",t(a).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-100 focus:ring-gray-500 focus:border-transparent":"border-gray-300 focus:ring-blue-500 focus:border-transparent"]),placeholder:"填写类完整名称，如 org.springframework.beans.factory.BeanFactory"},null,2),[[V,b.value]]),e("div",ie,[e("button",{onClick:T,class:r(t(a).theme==="dark"?"px-2 py-0.5 bg-gray-600 hover:bg-gray-500 text-gray-100 rounded text-xs":"px-2 py-0.5 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs")}," 保存 ",2),e("button",{onClick:s[6]||(s[6]=()=>{w.value=!1,b.value=""}),class:r(t(a).theme==="dark"?"px-2 py-0.5 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded text-xs":"px-2 py-0.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded text-xs")}," 取消 ",2)])])):(u(),i("button",{key:1,onClick:A,class:r(t(a).theme==="dark"?"inline-flex items-center gap-0.5 px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 rounded-full text-xs text-gray-300 hover:text-gray-200 w-fit":"inline-flex items-center gap-0.5 px-1.5 py-0.5 bg-blue-50 hover:bg-blue-100 rounded-full text-xs text-blue-700 w-fit")},s[19]||(s[19]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加类",-1)]),2))])],2),e("div",{class:r(["rounded p-3 shadow-sm",t(a).theme==="dark"?"bg-gray-800":"bg-white"])},[e("div",ue,[e("h3",{class:r(["text-sm font-semibold",t(a).theme==="dark"?"text-gray-100":"text-gray-900"])},"录制模式",2),e("button",{onClick:s[7]||(s[7]=n=>k("0")),class:r([t(a).theme==="dark"?"text-gray-500 hover:text-gray-400":"text-gray-400 hover:text-gray-600","transition-colors duration-200"])},s[20]||(s[20]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]),2)]),v.value[0]?(u(),i("p",{key:0,class:r(["text-xs mb-1.5 p-1.5 rounded",t(a).theme==="dark"?"text-gray-300 bg-gray-700":"text-gray-500 bg-gray-50"])}," 智能模式，针对 SpringBoot 或者 Tomcat，只录制 HTTP/DB 等请求。 全录模式，默认录制所有链路。手工模式，默认不录制，需手工开启录制。 ",2)):c("",!0),e("div",de,[e("label",ge,[p(e("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=n=>l.value.recordMode=n),value:"smart",onChange:d,class:r(t(a).theme==="dark"?"w-3.5 h-3.5 text-blue-400 focus:ring-blue-400 border-gray-600":"w-3.5 h-3.5 text-blue-600 focus:ring-blue-500 border-gray-300")},null,34),[[h,l.value.recordMode]]),e("span",{class:r(["text-xs font-medium",t(a).theme==="dark"?"text-gray-300":"text-gray-700"])},"智能模式",2)]),e("label",xe,[p(e("input",{type:"radio","onUpdate:modelValue":s[9]||(s[9]=n=>l.value.recordMode=n),value:"all",onChange:d,class:r(t(a).theme==="dark"?"w-3.5 h-3.5 text-blue-400 focus:ring-blue-400 border-gray-600":"w-3.5 h-3.5 text-blue-600 focus:ring-blue-500 border-gray-300")},null,34),[[h,l.value.recordMode]]),e("span",{class:r(["text-xs font-medium",t(a).theme==="dark"?"text-gray-300":"text-gray-700"])},"全录模式",2)]),e("label",ce,[p(e("input",{type:"radio","onUpdate:modelValue":s[10]||(s[10]=n=>l.value.recordMode=n),value:"manual",onChange:d,class:r(t(a).theme==="dark"?"w-3.5 h-3.5 text-blue-400 focus:ring-blue-400 border-gray-600":"w-3.5 h-3.5 text-blue-600 focus:ring-blue-500 border-gray-300")},null,34),[[h,l.value.recordMode]]),e("span",{class:r(["text-xs font-medium",t(a).theme==="dark"?"text-gray-300":"text-gray-700"])},"手工模式",2)])])],2),e("div",{class:r(["rounded p-3 shadow-sm",t(a).theme==="dark"?"bg-gray-800":"bg-white"])},[e("h3",{class:r(["text-sm font-semibold mb-1.5",t(a).theme==="dark"?"text-gray-100":"text-gray-900"])},"默认随着 Debug 启动（测试功能）",2),e("div",pe,[e("label",fe,[p(e("input",{type:"radio","onUpdate:modelValue":s[11]||(s[11]=n=>l.value.startOnDebug=n),value:!0,onChange:d,class:r(t(a).theme==="dark"?"text-blue-400 focus:ring-blue-400 border-gray-600":"text-blue-600 focus:ring-blue-500 border-gray-300")},null,34),[[h,l.value.startOnDebug]]),e("span",{class:r(["text-xs font-medium",t(a).theme==="dark"?"text-gray-300":"text-gray-700"])},"是",2)]),e("label",ve,[p(e("input",{type:"radio","onUpdate:modelValue":s[12]||(s[12]=n=>l.value.startOnDebug=n),value:!1,onChange:d,class:r(t(a).theme==="dark"?"text-blue-400 focus:ring-blue-400 border-gray-600":"text-blue-600 focus:ring-blue-500 border-gray-300")},null,34),[[h,l.value.startOnDebug]]),e("span",{class:r(["text-xs font-medium",t(a).theme==="dark"?"text-gray-300":"text-gray-700"])},"否",2)])])],2)])])],2)}}}),be=R(ye,[["__scopeId","data-v-46817caa"]]),_=j(be);_.use(Q());_.mount("#app");window.$vm=_._instance;
