var gn=Object.defineProperty;var mn=(r,e,t)=>e in r?gn(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var N=(r,e,t)=>mn(r,typeof e!="symbol"?e+"":e,t);import{d as nt,r as O,c as Be,g as bn,a as xn,b as vn,e as wn,f as Pt,h as kn,p as yn,s as Tn,i as Ur,u as Br,w as jt,j as T,o as _,F as Te,k as _e,l as u,m as I,n as C,q as f,t as X,_ as Fr,v as _n,x as En,y as Sn,z as Cn,A as zt,B as An,C as $t,D as ye,E as Ae,G as Rn,H as Dn,I as Mn,J as Ln,K as mt,L as In,M as Nn,N as On,O as Pn,P as zn,Q as $n,R as Un,S as Bn}from"./style-UMMLDSA_.js";import{u as Et}from"./error-C7MNaxXY.js";const Fn=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href,Kt=nt("chatStatus",()=>{const r=O("idle"),e=O("");return{status:r,statusMessage:e,setStatus:s=>{r.value=s},setStatusMessage:s=>{e.value=s}}}),Hr=nt("modelStatus",()=>{const r=O(null),e=O(null),t=Et(),n=Be(()=>!r.value||!e.value?null:e.value.modelDescList.find(l=>l.uuid===r.value)),s=Be(()=>{var l;return((l=e.value)==null?void 0:l.modelDescList)||[]});return{currentModelUuid:r,modelConfig:e,currentModel:n,availableModels:s,getModelConfigData:async()=>{var l;try{const g=await bn();if(g.success&&g.data)e.value=g.data,g.data.defaultModelId?g.data.modelDescList.find(p=>p.uuid===g.data.defaultModelId)?r.value=g.data.defaultModelId:r.value=((l=g.data.modelDescList[0])==null?void 0:l.uuid)||null:!r.value&&g.data.modelDescList.length>0&&(r.value=g.data.modelDescList[0].uuid);else throw new Error(g.error||"Failed to get model config")}catch(g){console.error("Failed to get model config:",g),t.setError(g instanceof Error?g.message:"Failed to get model config","GET_MODEL_CONFIG_ERROR")}},setCurrentModel:l=>{var i;((i=e.value)==null?void 0:i.modelDescList.find(p=>p.uuid===l))&&(r.value=l)}}}),ae="EMPTY_PLACE_HOLDER",Gr=nt("database",()=>{const r=O([{id:ae,name:"待选择程序数据(取消行号X图标)",recordState:"idle",dataId:null,active:!1,serverSelected:!1}]),e=O(ae),t=Be(()=>r.value.find(h=>h.id===e.value)||null),n=h=>{r.value.push(h)},s=h=>{e.value=h},a=async(h,w)=>{const v=r.value.find(y=>y.id===h);if(v)try{await xn({executionId:h,cmd:"change",state:w}),v.recordState="preparing"}catch(y){console.error("Failed to change record state:",y)}};return{databases:r,currentDatabase:t,currentDatabaseId:e,addDatabase:n,setCurrentDatabaseId:s,changeState:a,queryState:h=>{var w;return(w=r.value.find(v=>v.id===h))==null?void 0:w.recordState},startRecord:h=>{const w=r.value.find(v=>v.id===h);w&&w.recordState==="idle"&&a(h,"start")},endRecord:h=>{const w=r.value.find(v=>v.id===h);w&&w.recordState==="recording"&&a(h,"stop")},restartRecord:h=>{const w=r.value.find(v=>v.id===h);w&&w.recordState==="paused"&&(a(h,"start"),qr().createNewChat())},getDatabase:async()=>{try{const h=await vn();r.value=[r.value[0]],h.forEach(w=>{n(w)})}catch(h){console.error("Failed to fetch process data:",h)}},deleteDatabase:async h=>{try{if(await wn(h)){const v=r.value.findIndex(y=>y.id===h);return v>-1&&r.value.splice(v,1),e.value===h&&(e.value=ae),!0}return!1}catch(w){return console.error("Failed to delete database:",w),!1}}}}),Qt=nt("inputBox",()=>{const r=O(""),e=Et(),t=Kt(),n=Gr(),s=()=>!n.currentDatabase||!n.currentDatabase.dataId||t.status==="waiting"||n.currentDatabaseId===ae;return{message:r,appendText:i=>{if(s())return e.setError("当前状态不可添加文本到输入框"),!1;const p=r.value&&!r.value.endsWith(" ");return r.value+=(p?" ":"")+i,window.dispatchEvent(new CustomEvent("textarea-content-changed")),!0},setText:i=>{r.value=i,window.dispatchEvent(new CustomEvent("textarea-content-changed"))},clearText:()=>{r.value="",window.dispatchEvent(new CustomEvent("textarea-content-changed"))},getText:()=>r.value,isDisabled:s}}),Hn="SystemStatus",Gn="AddToChat",qr=nt("chat",()=>{const r=O([]),e=O(null),t=O(!1);let n=null;const s=Et(),a=Kt(),d=Hr(),l=Qt(),g=Be(()=>r.value.find(y=>y.id===e.value)),i=async()=>{var y;try{e.value&&await Pt(e.value);const A={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await kn(A.id,((y=d.currentModel)==null?void 0:y.uuid)??""))return r.value=[A],e.value=A.id,t.value||p(),A;throw new Error("Failed to create chat channel")}catch(A){throw console.error("Failed to create chat channel:",A),s.setError(A instanceof Error?A.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),e.value=null,r.value=[],A}},p=()=>{n&&clearInterval(n),t.value=!0,n=window.setInterval(async()=>{if(t.value)try{const y=await yn();if(y&&y.length>0)for(const A of y){if(A.messageId===Gn){l.appendText(A.content);continue}if(A.messageId===Hn){a.setStatus("waiting"),a.setStatusMessage(A.content);continue}const U=r.value.find(D=>D.id===A.chatId);if(U){a.setStatus("waiting");const D=U.messages[U.messages.length-1];D&&D.role===A.role?D.content+=A.content:U.messages.push(A),U.updatedAt=Date.now()}}else a.setStatus("sending"),a.setStatusMessage("正在思考中...")}catch(y){console.error("Failed to poll messages:",y),s.setError(y instanceof Error?y.message:"Failed to poll messages","POLL_ERROR")}},1e3)},E=()=>{n&&(clearInterval(n),n=null),t.value=!1};return{chats:r,currentChatId:e,currentChat:g,createNewChat:i,sendMessage:async y=>{e.value||await i();const A=g.value;if(!A)return;if(!d.currentModel){s.setError("请先选择模型再发送消息","SEND_MESSAGE_ERROR");return}a.setStatus("waiting");const U={messageId:crypto.randomUUID(),content:y,role:"user",timestamp:Date.now(),chatId:e.value,modelId:d.currentModel.uuid};try{if(!e.value)return;if(await Tn(U))A.messages.push(U),A.updatedAt=Date.now();else throw new Error("会话可能已失效，请重新创建会话")}catch(D){throw console.error("Failed to send message:",D),s.setError(D instanceof Error?D.message:"会话可能已失效，请重新创建会话","SEND_MESSAGE_ERROR"),D}},removeChat:async y=>{try{if(await Pt(y))r.value=r.value.filter(U=>U.id!==y),e.value===y&&(e.value=null);else throw new Error("Failed to remove chat channel")}catch(A){throw console.error("Failed to remove chat:",A),s.setError(A instanceof Error?A.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),A}},startPolling:p,stopPolling:E,cleanup:()=>{E(),e.value&&Pt(e.value).catch(console.error)}}}),qn={class:"space-y-0.5"},jn=["onClick"],Wn={key:0,class:"p-0.5 rounded flex-shrink-0"},Vn=["onClick"],Xn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Yn=["onClick"],Zn={key:0,class:"p-0.5 rounded flex-shrink-0"},Kn=["onClick"],Qn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Jn=["onClick"],es=["onClick"],ts=Ur({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(r,{emit:e}){const t=r,n=Br(),s=Qt(),a=O(t.nodes.map(E=>({...E,isExpanded:t.defaultExpanded,children:E.children?E.children.map(h=>({...h,isExpanded:t.defaultExpanded,children:h.children?h.children.map(w=>({...w,isExpanded:t.defaultExpanded})):[]})):[]})));jt(()=>t.nodes,E=>{a.value=E.map(h=>({...h,isExpanded:t.defaultExpanded,children:h.children?h.children.map(w=>({...w,isExpanded:t.defaultExpanded,children:w.children?w.children.map(v=>({...v,isExpanded:t.defaultExpanded})):[]})):[]}))},{deep:!0});const d=e,l=E=>{d("nodeClick",E)},g=E=>{E.isExpanded=!E.isExpanded},i=(E,h)=>{E.children&&E.children.length>0?g(E):l(E)},p=(E,h)=>{h.stopPropagation(),E.chatText&&s.setText(E.chatText)};return(E,h)=>(_(),T("div",qn,[(_(!0),T(Te,null,_e(a.value,w=>(_(),T("div",{key:w.nodeKey,class:"tree-node"},[u("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:v=>i(w)},[w.children&&w.children.length>0?(_(),T("div",Wn,[(_(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":w.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},h[0]||(h[0]=[u("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):I("",!0),u("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},X(w.label),3),w.chatText?(_(),T("button",{key:1,onClick:v=>p(w,v),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},h[1]||(h[1]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,Vn)):I("",!0)],10,jn),w.children&&w.children.length>0&&w.isExpanded?(_(),T("div",Xn,[(_(!0),T(Te,null,_e(w.children,v=>(_(),T("div",{key:v.nodeKey,class:"tree-node"},[u("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:y=>i(v)},[v.children&&v.children.length>0?(_(),T("div",Zn,[(_(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":v.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},h[2]||(h[2]=[u("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):I("",!0),u("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},X(v.label),3),v.chatText?(_(),T("button",{key:1,onClick:y=>p(v,y),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},h[3]||(h[3]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,Kn)):I("",!0)],10,Yn),v.children&&v.children.length>0&&v.isExpanded?(_(),T("div",Qn,[(_(!0),T(Te,null,_e(v.children,y=>(_(),T("div",{key:y.nodeKey,class:"pl-3"},[u("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:A=>i(y)},[u("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},X(y.label),3),y.chatText?(_(),T("button",{key:0,onClick:A=>p(y,A),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},h[4]||(h[4]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,es)):I("",!0)],10,Jn)]))),128))])):I("",!0)]))),128))])):I("",!0)]))),128))]))}}),rs=Fr(ts,[["__scopeId","data-v-4bd5633e"]]);function Jt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var De=Jt();function jr(r){De=r}var rt={exec:()=>null};function L(r,e=""){let t=typeof r=="string"?r:r.source;const n={replace:(s,a)=>{let d=typeof a=="string"?a:a.source;return d=d.replace(Q.caret,"$1"),t=t.replace(s,d),n},getRegex:()=>new RegExp(t,e)};return n}var Q={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:r=>new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}#`),htmlBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}<(?:[a-z].*>|!--)`,"i")},ns=/^(?:[ \t]*(?:\n|$))+/,ss=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,as=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,st=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,os=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,er=/(?:[*+-]|\d{1,9}[.)])/,Wr=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Vr=L(Wr).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),ls=L(Wr).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),tr=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,is=/^[^\n]+/,rr=/(?!\s*\])(?:\\.|[^\[\]\\])+/,cs=L(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",rr).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),us=L(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,er).getRegex(),St="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",nr=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,ds=L("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",nr).replace("tag",St).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Xr=L(tr).replace("hr",st).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex(),ps=L(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Xr).getRegex(),sr={blockquote:ps,code:ss,def:cs,fences:as,heading:os,hr:st,html:ds,lheading:Vr,list:us,newline:ns,paragraph:Xr,table:rt,text:is},Er=L("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",st).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex(),hs={...sr,lheading:ls,table:Er,paragraph:L(tr).replace("hr",st).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Er).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex()},fs={...sr,html:L(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",nr).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:rt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:L(tr).replace("hr",st).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Vr).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},gs=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ms=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Yr=/^( {2,}|\\)\n(?!\s*$)/,bs=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Ct=/[\p{P}\p{S}]/u,ar=/[\s\p{P}\p{S}]/u,Zr=/[^\s\p{P}\p{S}]/u,xs=L(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,ar).getRegex(),Kr=/(?!~)[\p{P}\p{S}]/u,vs=/(?!~)[\s\p{P}\p{S}]/u,ws=/(?:[^\s\p{P}\p{S}]|~)/u,ks=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Qr=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,ys=L(Qr,"u").replace(/punct/g,Ct).getRegex(),Ts=L(Qr,"u").replace(/punct/g,Kr).getRegex(),Jr="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",_s=L(Jr,"gu").replace(/notPunctSpace/g,Zr).replace(/punctSpace/g,ar).replace(/punct/g,Ct).getRegex(),Es=L(Jr,"gu").replace(/notPunctSpace/g,ws).replace(/punctSpace/g,vs).replace(/punct/g,Kr).getRegex(),Ss=L("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Zr).replace(/punctSpace/g,ar).replace(/punct/g,Ct).getRegex(),Cs=L(/\\(punct)/,"gu").replace(/punct/g,Ct).getRegex(),As=L(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Rs=L(nr).replace("(?:-->|$)","-->").getRegex(),Ds=L("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Rs).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),yt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ms=L(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",yt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),en=L(/^!?\[(label)\]\[(ref)\]/).replace("label",yt).replace("ref",rr).getRegex(),tn=L(/^!?\[(ref)\](?:\[\])?/).replace("ref",rr).getRegex(),Ls=L("reflink|nolink(?!\\()","g").replace("reflink",en).replace("nolink",tn).getRegex(),or={_backpedal:rt,anyPunctuation:Cs,autolink:As,blockSkip:ks,br:Yr,code:ms,del:rt,emStrongLDelim:ys,emStrongRDelimAst:_s,emStrongRDelimUnd:Ss,escape:gs,link:Ms,nolink:tn,punctuation:xs,reflink:en,reflinkSearch:Ls,tag:Ds,text:bs,url:rt},Is={...or,link:L(/^!?\[(label)\]\((.*?)\)/).replace("label",yt).getRegex(),reflink:L(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",yt).getRegex()},Wt={...or,emStrongRDelimAst:Es,emStrongLDelim:Ts,url:L(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ns={...Wt,br:L(Yr).replace("{2,}","*").getRegex(),text:L(Wt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},bt={normal:sr,gfm:hs,pedantic:fs},Ye={normal:or,gfm:Wt,breaks:Ns,pedantic:Is},Os={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Sr=r=>Os[r];function ce(r,e){if(e){if(Q.escapeTest.test(r))return r.replace(Q.escapeReplace,Sr)}else if(Q.escapeTestNoEncode.test(r))return r.replace(Q.escapeReplaceNoEncode,Sr);return r}function Cr(r){try{r=encodeURI(r).replace(Q.percentDecode,"%")}catch{return null}return r}function Ar(r,e){var a;const t=r.replace(Q.findPipe,(d,l,g)=>{let i=!1,p=l;for(;--p>=0&&g[p]==="\\";)i=!i;return i?"|":" |"}),n=t.split(Q.splitPipe);let s=0;if(n[0].trim()||n.shift(),n.length>0&&!((a=n.at(-1))!=null&&a.trim())&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(Q.slashPipe,"|");return n}function Ze(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n&&r.charAt(n-s-1)===e;)s++;return r.slice(0,n-s)}function Ps(r,e){if(r.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<r.length;n++)if(r[n]==="\\")n++;else if(r[n]===e[0])t++;else if(r[n]===e[1]&&(t--,t<0))return n;return t>0?-2:-1}function Rr(r,e,t,n,s){const a=e.href,d=e.title||null,l=r[1].replace(s.other.outputLinkReplace,"$1");n.state.inLink=!0;const g={type:r[0].charAt(0)==="!"?"image":"link",raw:t,href:a,title:d,text:l,tokens:n.inlineTokens(l)};return n.state.inLink=!1,g}function zs(r,e,t){const n=r.match(t.other.indentCodeCompensation);if(n===null)return e;const s=n[1];return e.split(`
`).map(a=>{const d=a.match(t.other.beginningSpace);if(d===null)return a;const[l]=d;return l.length>=s.length?a.slice(s.length):a}).join(`
`)}var Tt=class{constructor(r){N(this,"options");N(this,"rules");N(this,"lexer");this.options=r||De}space(r){const e=this.rules.block.newline.exec(r);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(r){const e=this.rules.block.code.exec(r);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:Ze(t,`
`)}}}fences(r){const e=this.rules.block.fences.exec(r);if(e){const t=e[0],n=zs(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(r){const e=this.rules.block.heading.exec(r);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const n=Ze(t,"#");(this.options.pedantic||!n||this.rules.other.endingSpaceChar.test(n))&&(t=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(r){const e=this.rules.block.hr.exec(r);if(e)return{type:"hr",raw:Ze(e[0],`
`)}}blockquote(r){const e=this.rules.block.blockquote.exec(r);if(e){let t=Ze(e[0],`
`).split(`
`),n="",s="";const a=[];for(;t.length>0;){let d=!1;const l=[];let g;for(g=0;g<t.length;g++)if(this.rules.other.blockquoteStart.test(t[g]))l.push(t[g]),d=!0;else if(!d)l.push(t[g]);else break;t=t.slice(g);const i=l.join(`
`),p=i.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${i}`:i,s=s?`${s}
${p}`:p;const E=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(p,a,!0),this.lexer.state.top=E,t.length===0)break;const h=a.at(-1);if((h==null?void 0:h.type)==="code")break;if((h==null?void 0:h.type)==="blockquote"){const w=h,v=w.raw+`
`+t.join(`
`),y=this.blockquote(v);a[a.length-1]=y,n=n.substring(0,n.length-w.raw.length)+y.raw,s=s.substring(0,s.length-w.text.length)+y.text;break}else if((h==null?void 0:h.type)==="list"){const w=h,v=w.raw+`
`+t.join(`
`),y=this.list(v);a[a.length-1]=y,n=n.substring(0,n.length-h.raw.length)+y.raw,s=s.substring(0,s.length-w.raw.length)+y.raw,t=v.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:n,tokens:a,text:s}}}list(r){let e=this.rules.block.list.exec(r);if(e){let t=e[1].trim();const n=t.length>1,s={type:"list",raw:"",ordered:n,start:n?+t.slice(0,-1):"",loose:!1,items:[]};t=n?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=n?t:"[*+-]");const a=this.rules.other.listItemRegex(t);let d=!1;for(;r;){let g=!1,i="",p="";if(!(e=a.exec(r))||this.rules.block.hr.test(r))break;i=e[0],r=r.substring(i.length);let E=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,U=>" ".repeat(3*U.length)),h=r.split(`
`,1)[0],w=!E.trim(),v=0;if(this.options.pedantic?(v=2,p=E.trimStart()):w?v=e[1].length+1:(v=e[2].search(this.rules.other.nonSpaceChar),v=v>4?1:v,p=E.slice(v),v+=e[1].length),w&&this.rules.other.blankLine.test(h)&&(i+=h+`
`,r=r.substring(h.length+1),g=!0),!g){const U=this.rules.other.nextBulletRegex(v),D=this.rules.other.hrRegex(v),W=this.rules.other.fencesBeginRegex(v),P=this.rules.other.headingBeginRegex(v),ne=this.rules.other.htmlBeginRegex(v);for(;r;){const te=r.split(`
`,1)[0];let ue;if(h=te,this.options.pedantic?(h=h.replace(this.rules.other.listReplaceNesting,"  "),ue=h):ue=h.replace(this.rules.other.tabCharGlobal,"    "),W.test(h)||P.test(h)||ne.test(h)||U.test(h)||D.test(h))break;if(ue.search(this.rules.other.nonSpaceChar)>=v||!h.trim())p+=`
`+ue.slice(v);else{if(w||E.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||W.test(E)||P.test(E)||D.test(E))break;p+=`
`+h}!w&&!h.trim()&&(w=!0),i+=te+`
`,r=r.substring(te.length+1),E=ue.slice(v)}}s.loose||(d?s.loose=!0:this.rules.other.doubleBlankLine.test(i)&&(d=!0));let y=null,A;this.options.gfm&&(y=this.rules.other.listIsTask.exec(p),y&&(A=y[0]!=="[ ] ",p=p.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:i,task:!!y,checked:A,loose:!1,text:p,tokens:[]}),s.raw+=i}const l=s.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let g=0;g<s.items.length;g++)if(this.lexer.state.top=!1,s.items[g].tokens=this.lexer.blockTokens(s.items[g].text,[]),!s.loose){const i=s.items[g].tokens.filter(E=>E.type==="space"),p=i.length>0&&i.some(E=>this.rules.other.anyLine.test(E.raw));s.loose=p}if(s.loose)for(let g=0;g<s.items.length;g++)s.items[g].loose=!0;return s}}html(r){const e=this.rules.block.html.exec(r);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(r){const e=this.rules.block.def.exec(r);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:n,title:s}}}table(r){var d;const e=this.rules.block.table.exec(r);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=Ar(e[1]),n=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(d=e[3])!=null&&d.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===n.length){for(const l of n)this.rules.other.tableAlignRight.test(l)?a.align.push("right"):this.rules.other.tableAlignCenter.test(l)?a.align.push("center"):this.rules.other.tableAlignLeft.test(l)?a.align.push("left"):a.align.push(null);for(let l=0;l<t.length;l++)a.header.push({text:t[l],tokens:this.lexer.inline(t[l]),header:!0,align:a.align[l]});for(const l of s)a.rows.push(Ar(l,a.header.length).map((g,i)=>({text:g,tokens:this.lexer.inline(g),header:!1,align:a.align[i]})));return a}}lheading(r){const e=this.rules.block.lheading.exec(r);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(r){const e=this.rules.block.paragraph.exec(r);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(r){const e=this.rules.block.text.exec(r);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(r){const e=this.rules.inline.escape.exec(r);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(r){const e=this.rules.inline.tag.exec(r);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(r){const e=this.rules.inline.link.exec(r);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const a=Ze(t.slice(0,-1),"\\");if((t.length-a.length)%2===0)return}else{const a=Ps(e[2],"()");if(a===-2)return;if(a>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+a;e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let n=e[2],s="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(n);a&&(n=a[1],s=a[3])}else s=e[3]?e[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?n=n.slice(1):n=n.slice(1,-1)),Rr(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(r,e){let t;if((t=this.rules.inline.reflink.exec(r))||(t=this.rules.inline.nolink.exec(r))){const n=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[n.toLowerCase()];if(!s){const a=t[0].charAt(0);return{type:"text",raw:a,text:a}}return Rr(t,s,t[0],this.lexer,this.rules)}}emStrong(r,e,t=""){let n=this.rules.inline.emStrongLDelim.exec(r);if(!n||n[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(n[1]||n[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const a=[...n[0]].length-1;let d,l,g=a,i=0;const p=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,e=e.slice(-1*r.length+a);(n=p.exec(e))!=null;){if(d=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!d)continue;if(l=[...d].length,n[3]||n[4]){g+=l;continue}else if((n[5]||n[6])&&a%3&&!((a+l)%3)){i+=l;continue}if(g-=l,g>0)continue;l=Math.min(l,l+g+i);const E=[...n[0]][0].length,h=r.slice(0,a+n.index+E+l);if(Math.min(a,l)%2){const v=h.slice(1,-1);return{type:"em",raw:h,text:v,tokens:this.lexer.inlineTokens(v)}}const w=h.slice(2,-2);return{type:"strong",raw:h,text:w,tokens:this.lexer.inlineTokens(w)}}}}codespan(r){const e=this.rules.inline.code.exec(r);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return n&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(r){const e=this.rules.inline.br.exec(r);if(e)return{type:"br",raw:e[0]}}del(r){const e=this.rules.inline.del.exec(r);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(r){const e=this.rules.inline.autolink.exec(r);if(e){let t,n;return e[2]==="@"?(t=e[1],n="mailto:"+t):(t=e[1],n=t),{type:"link",raw:e[0],text:t,href:n,tokens:[{type:"text",raw:t,text:t}]}}}url(r){var t;let e;if(e=this.rules.inline.url.exec(r)){let n,s;if(e[2]==="@")n=e[0],s="mailto:"+n;else{let a;do a=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(a!==e[0]);n=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(r){const e=this.rules.inline.text.exec(r);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},be=class Vt{constructor(e){N(this,"tokens");N(this,"options");N(this,"state");N(this,"tokenizer");N(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||De,this.options.tokenizer=this.options.tokenizer||new Tt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:Q,block:bt.normal,inline:Ye.normal};this.options.pedantic?(t.block=bt.pedantic,t.inline=Ye.pedantic):this.options.gfm&&(t.block=bt.gfm,this.options.breaks?t.inline=Ye.breaks:t.inline=Ye.gfm),this.tokenizer.rules=t}static get rules(){return{block:bt,inline:Ye}}static lex(e,t){return new Vt(t).lex(e)}static lexInline(e,t){return new Vt(t).inlineTokens(e)}lex(e){e=e.replace(Q.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){var s,a,d;for(this.options.pedantic&&(e=e.replace(Q.tabCharGlobal,"    ").replace(Q.spaceLine,""));e;){let l;if((a=(s=this.options.extensions)==null?void 0:s.block)!=null&&a.some(i=>(l=i.call({lexer:this},e,t))?(e=e.substring(l.raw.length),t.push(l),!0):!1))continue;if(l=this.tokenizer.space(e)){e=e.substring(l.raw.length);const i=t.at(-1);l.raw.length===1&&i!==void 0?i.raw+=`
`:t.push(l);continue}if(l=this.tokenizer.code(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(l=this.tokenizer.fences(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.heading(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.hr(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.blockquote(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.list(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.html(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.def(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.raw,this.inlineQueue.at(-1).src=i.text):this.tokens.links[l.tag]||(this.tokens.links[l.tag]={href:l.href,title:l.title});continue}if(l=this.tokenizer.table(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.lheading(e)){e=e.substring(l.raw.length),t.push(l);continue}let g=e;if((d=this.options.extensions)!=null&&d.startBlock){let i=1/0;const p=e.slice(1);let E;this.options.extensions.startBlock.forEach(h=>{E=h.call({lexer:this},p),typeof E=="number"&&E>=0&&(i=Math.min(i,E))}),i<1/0&&i>=0&&(g=e.substring(0,i+1))}if(this.state.top&&(l=this.tokenizer.paragraph(g))){const i=t.at(-1);n&&(i==null?void 0:i.type)==="paragraph"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l),n=g.length!==e.length,e=e.substring(l.raw.length);continue}if(l=this.tokenizer.text(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}else throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var l,g,i;let n=e,s=null;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)p.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,d="";for(;e;){a||(d=""),a=!1;let p;if((g=(l=this.options.extensions)==null?void 0:l.inline)!=null&&g.some(h=>(p=h.call({lexer:this},e,t))?(e=e.substring(p.raw.length),t.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);const h=t.at(-1);p.type==="text"&&(h==null?void 0:h.type)==="text"?(h.raw+=p.raw,h.text+=p.text):t.push(p);continue}if(p=this.tokenizer.emStrong(e,n,d)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),t.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),t.push(p);continue}let E=e;if((i=this.options.extensions)!=null&&i.startInline){let h=1/0;const w=e.slice(1);let v;this.options.extensions.startInline.forEach(y=>{v=y.call({lexer:this},w),typeof v=="number"&&v>=0&&(h=Math.min(h,v))}),h<1/0&&h>=0&&(E=e.substring(0,h+1))}if(p=this.tokenizer.inlineText(E)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(d=p.raw.slice(-1)),a=!0;const h=t.at(-1);(h==null?void 0:h.type)==="text"?(h.raw+=p.raw,h.text+=p.text):t.push(p);continue}if(e){const h="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(h);break}else throw new Error(h)}}return t}},_t=class{constructor(r){N(this,"options");N(this,"parser");this.options=r||De}space(r){return""}code({text:r,lang:e,escaped:t}){var a;const n=(a=(e||"").match(Q.notSpaceStart))==null?void 0:a[0],s=r.replace(Q.endingNewline,"")+`
`;return n?'<pre><code class="language-'+ce(n)+'">'+(t?s:ce(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:ce(s,!0))+`</code></pre>
`}blockquote({tokens:r}){return`<blockquote>
${this.parser.parse(r)}</blockquote>
`}html({text:r}){return r}heading({tokens:r,depth:e}){return`<h${e}>${this.parser.parseInline(r)}</h${e}>
`}hr(r){return`<hr>
`}list(r){const e=r.ordered,t=r.start;let n="";for(let d=0;d<r.items.length;d++){const l=r.items[d];n+=this.listitem(l)}const s=e?"ol":"ul",a=e&&t!==1?' start="'+t+'"':"";return"<"+s+a+`>
`+n+"</"+s+`>
`}listitem(r){var t;let e="";if(r.task){const n=this.checkbox({checked:!!r.checked});r.loose?((t=r.tokens[0])==null?void 0:t.type)==="paragraph"?(r.tokens[0].text=n+" "+r.tokens[0].text,r.tokens[0].tokens&&r.tokens[0].tokens.length>0&&r.tokens[0].tokens[0].type==="text"&&(r.tokens[0].tokens[0].text=n+" "+ce(r.tokens[0].tokens[0].text),r.tokens[0].tokens[0].escaped=!0)):r.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):e+=n+" "}return e+=this.parser.parse(r.tokens,!!r.loose),`<li>${e}</li>
`}checkbox({checked:r}){return"<input "+(r?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:r}){return`<p>${this.parser.parseInline(r)}</p>
`}table(r){let e="",t="";for(let s=0;s<r.header.length;s++)t+=this.tablecell(r.header[s]);e+=this.tablerow({text:t});let n="";for(let s=0;s<r.rows.length;s++){const a=r.rows[s];t="";for(let d=0;d<a.length;d++)t+=this.tablecell(a[d]);n+=this.tablerow({text:t})}return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+n+`</table>
`}tablerow({text:r}){return`<tr>
${r}</tr>
`}tablecell(r){const e=this.parser.parseInline(r.tokens),t=r.header?"th":"td";return(r.align?`<${t} align="${r.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:r}){return`<strong>${this.parser.parseInline(r)}</strong>`}em({tokens:r}){return`<em>${this.parser.parseInline(r)}</em>`}codespan({text:r}){return`<code>${ce(r,!0)}</code>`}br(r){return"<br>"}del({tokens:r}){return`<del>${this.parser.parseInline(r)}</del>`}link({href:r,title:e,tokens:t}){const n=this.parser.parseInline(t),s=Cr(r);if(s===null)return n;r=s;let a='<a href="'+r+'"';return e&&(a+=' title="'+ce(e)+'"'),a+=">"+n+"</a>",a}image({href:r,title:e,text:t,tokens:n}){n&&(t=this.parser.parseInline(n,this.parser.textRenderer));const s=Cr(r);if(s===null)return ce(t);r=s;let a=`<img src="${r}" alt="${t}"`;return e&&(a+=` title="${ce(e)}"`),a+=">",a}text(r){return"tokens"in r&&r.tokens?this.parser.parseInline(r.tokens):"escaped"in r&&r.escaped?r.text:ce(r.text)}},lr=class{strong({text:r}){return r}em({text:r}){return r}codespan({text:r}){return r}del({text:r}){return r}html({text:r}){return r}text({text:r}){return r}link({text:r}){return""+r}image({text:r}){return""+r}br(){return""}},xe=class Xt{constructor(e){N(this,"options");N(this,"renderer");N(this,"textRenderer");this.options=e||De,this.options.renderer=this.options.renderer||new _t,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new lr}static parse(e,t){return new Xt(t).parse(e)}static parseInline(e,t){return new Xt(t).parseInline(e)}parse(e,t=!0){var s,a;let n="";for(let d=0;d<e.length;d++){const l=e[d];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=l,p=this.options.extensions.renderers[i.type].call({parser:this},i);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){n+=p||"";continue}}const g=l;switch(g.type){case"space":{n+=this.renderer.space(g);continue}case"hr":{n+=this.renderer.hr(g);continue}case"heading":{n+=this.renderer.heading(g);continue}case"code":{n+=this.renderer.code(g);continue}case"table":{n+=this.renderer.table(g);continue}case"blockquote":{n+=this.renderer.blockquote(g);continue}case"list":{n+=this.renderer.list(g);continue}case"html":{n+=this.renderer.html(g);continue}case"paragraph":{n+=this.renderer.paragraph(g);continue}case"text":{let i=g,p=this.renderer.text(i);for(;d+1<e.length&&e[d+1].type==="text";)i=e[++d],p+=`
`+this.renderer.text(i);t?n+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):n+=p;continue}default:{const i='Token with "'+g.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}parseInline(e,t=this.renderer){var s,a;let n="";for(let d=0;d<e.length;d++){const l=e[d];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=this.options.extensions.renderers[l.type].call({parser:this},l);if(i!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(l.type)){n+=i||"";continue}}const g=l;switch(g.type){case"escape":{n+=t.text(g);break}case"html":{n+=t.html(g);break}case"link":{n+=t.link(g);break}case"image":{n+=t.image(g);break}case"strong":{n+=t.strong(g);break}case"em":{n+=t.em(g);break}case"codespan":{n+=t.codespan(g);break}case"br":{n+=t.br(g);break}case"del":{n+=t.del(g);break}case"text":{n+=t.text(g);break}default:{const i='Token with "'+g.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}},qt,wt=(qt=class{constructor(r){N(this,"options");N(this,"block");this.options=r||De}preprocess(r){return r}postprocess(r){return r}processAllTokens(r){return r}provideLexer(){return this.block?be.lex:be.lexInline}provideParser(){return this.block?xe.parse:xe.parseInline}},N(qt,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),qt),$s=class{constructor(...r){N(this,"defaults",Jt());N(this,"options",this.setOptions);N(this,"parse",this.parseMarkdown(!0));N(this,"parseInline",this.parseMarkdown(!1));N(this,"Parser",xe);N(this,"Renderer",_t);N(this,"TextRenderer",lr);N(this,"Lexer",be);N(this,"Tokenizer",Tt);N(this,"Hooks",wt);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const a of r)switch(t=t.concat(e.call(this,a)),a.type){case"table":{const d=a;for(const l of d.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of d.rows)for(const g of l)t=t.concat(this.walkTokens(g.tokens,e));break}case"list":{const d=a;t=t.concat(this.walkTokens(d.items,e));break}default:{const d=a;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[d.type]?this.defaults.extensions.childTokens[d.type].forEach(l=>{const g=d[l].flat(1/0);t=t.concat(this.walkTokens(g,e))}):d.tokens&&(t=t.concat(this.walkTokens(d.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=e.renderers[s.name];a?e.renderers[s.name]=function(...d){let l=s.renderer.apply(this,d);return l===!1&&(l=a.apply(this,d)),l}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=e[s.level];a?a.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new _t(this.defaults);for(const a in t.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const d=a,l=t.renderer[d],g=s[d];s[d]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=g.apply(s,i)),p||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new Tt(this.defaults);for(const a in t.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const d=a,l=t.tokenizer[d],g=s[d];s[d]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=g.apply(s,i)),p}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new wt;for(const a in t.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const d=a,l=t.hooks[d],g=s[d];wt.passThroughHooks.has(a)?s[d]=i=>{if(this.defaults.async)return Promise.resolve(l.call(s,i)).then(E=>g.call(s,E));const p=l.call(s,i);return g.call(s,p)}:s[d]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=g.apply(s,i)),p}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,a=t.walkTokens;n.walkTokens=function(d){let l=[];return l.push(a.call(this,d)),s&&(l=l.concat(s.call(this,d))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return be.lex(r,e??this.defaults)}parser(r,e){return xe.parse(r,e??this.defaults)}parseMarkdown(r){return(t,n)=>{const s={...n},a={...this.defaults,...s},d=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&s.async===!1)return d(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return d(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return d(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=r);const l=a.hooks?a.hooks.provideLexer():r?be.lex:be.lexInline,g=a.hooks?a.hooks.provideParser():r?xe.parse:xe.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(i=>l(i,a)).then(i=>a.hooks?a.hooks.processAllTokens(i):i).then(i=>a.walkTokens?Promise.all(this.walkTokens(i,a.walkTokens)).then(()=>i):i).then(i=>g(i,a)).then(i=>a.hooks?a.hooks.postprocess(i):i).catch(d);try{a.hooks&&(t=a.hooks.preprocess(t));let i=l(t,a);a.hooks&&(i=a.hooks.processAllTokens(i)),a.walkTokens&&this.walkTokens(i,a.walkTokens);let p=g(i,a);return a.hooks&&(p=a.hooks.postprocess(p)),p}catch(i){return d(i)}}}onError(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+ce(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}}},Re=new $s;function M(r,e){return Re.parse(r,e)}M.options=M.setOptions=function(r){return Re.setOptions(r),M.defaults=Re.defaults,jr(M.defaults),M};M.getDefaults=Jt;M.defaults=De;M.use=function(...r){return Re.use(...r),M.defaults=Re.defaults,jr(M.defaults),M};M.walkTokens=function(r,e){return Re.walkTokens(r,e)};M.parseInline=Re.parseInline;M.Parser=xe;M.parser=xe.parse;M.Renderer=_t;M.TextRenderer=lr;M.Lexer=be;M.lexer=be.lex;M.Tokenizer=Tt;M.Hooks=wt;M.parse=M;M.options;M.setOptions;M.use;M.walkTokens;M.parseInline;xe.parse;be.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:rn,setPrototypeOf:Dr,isFrozen:Us,getPrototypeOf:Bs,getOwnPropertyDescriptor:Fs}=Object;let{freeze:J,seal:oe,create:nn}=Object,{apply:Yt,construct:Zt}=typeof Reflect<"u"&&Reflect;J||(J=function(e){return e});oe||(oe=function(e){return e});Yt||(Yt=function(e,t,n){return e.apply(t,n)});Zt||(Zt=function(e,t){return new e(...t)});const xt=ee(Array.prototype.forEach),Hs=ee(Array.prototype.lastIndexOf),Mr=ee(Array.prototype.pop),Ke=ee(Array.prototype.push),Gs=ee(Array.prototype.splice),kt=ee(String.prototype.toLowerCase),Ut=ee(String.prototype.toString),Lr=ee(String.prototype.match),Qe=ee(String.prototype.replace),qs=ee(String.prototype.indexOf),js=ee(String.prototype.trim),le=ee(Object.prototype.hasOwnProperty),K=ee(RegExp.prototype.test),Je=Ws(TypeError);function ee(r){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return Yt(r,e,n)}}function Ws(r){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Zt(r,t)}}function R(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:kt;Dr&&Dr(r,null);let n=e.length;for(;n--;){let s=e[n];if(typeof s=="string"){const a=t(s);a!==s&&(Us(e)||(e[n]=a),s=a)}r[s]=!0}return r}function Vs(r){for(let e=0;e<r.length;e++)le(r,e)||(r[e]=null);return r}function me(r){const e=nn(null);for(const[t,n]of rn(r))le(r,t)&&(Array.isArray(n)?e[t]=Vs(n):n&&typeof n=="object"&&n.constructor===Object?e[t]=me(n):e[t]=n);return e}function et(r,e){for(;r!==null;){const n=Fs(r,e);if(n){if(n.get)return ee(n.get);if(typeof n.value=="function")return ee(n.value)}r=Bs(r)}function t(){return null}return t}const Ir=J(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Bt=J(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ft=J(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Xs=J(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ht=J(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Ys=J(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Nr=J(["#text"]),Or=J(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Gt=J(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Pr=J(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),vt=J(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Zs=oe(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Ks=oe(/<%[\w\W]*|[\w\W]*%>/gm),Qs=oe(/\$\{[\w\W]*/gm),Js=oe(/^data-[\-\w.\u00B7-\uFFFF]+$/),ea=oe(/^aria-[\-\w]+$/),sn=oe(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),ta=oe(/^(?:\w+script|data):/i),ra=oe(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),an=oe(/^html$/i),na=oe(/^[a-z][.\w]*(-[.\w]+)+$/i);var zr=Object.freeze({__proto__:null,ARIA_ATTR:ea,ATTR_WHITESPACE:ra,CUSTOM_ELEMENT:na,DATA_ATTR:Js,DOCTYPE_NAME:an,ERB_EXPR:Ks,IS_ALLOWED_URI:sn,IS_SCRIPT_OR_DATA:ta,MUSTACHE_EXPR:Zs,TMPLIT_EXPR:Qs});const tt={element:1,text:3,progressingInstruction:7,comment:8,document:9},sa=function(){return typeof window>"u"?null:window},aa=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(n=t.getAttribute(s));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML(d){return d},createScriptURL(d){return d}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},$r=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function on(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:sa();const e=S=>on(S);if(e.version="3.2.6",e.removed=[],!r||!r.document||r.document.nodeType!==tt.document||!r.Element)return e.isSupported=!1,e;let{document:t}=r;const n=t,s=n.currentScript,{DocumentFragment:a,HTMLTemplateElement:d,Node:l,Element:g,NodeFilter:i,NamedNodeMap:p=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:E,DOMParser:h,trustedTypes:w}=r,v=g.prototype,y=et(v,"cloneNode"),A=et(v,"remove"),U=et(v,"nextSibling"),D=et(v,"childNodes"),W=et(v,"parentNode");if(typeof d=="function"){const S=t.createElement("template");S.content&&S.content.ownerDocument&&(t=S.content.ownerDocument)}let P,ne="";const{implementation:te,createNodeIterator:ue,createDocumentFragment:ve,getElementsByTagName:de}=t,{importNode:Me}=n;let V=$r();e.isSupported=typeof rn=="function"&&typeof W=="function"&&te&&te.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Fe,ERB_EXPR:He,TMPLIT_EXPR:Ge,DATA_ATTR:At,ARIA_ATTR:qe,IS_SCRIPT_OR_DATA:at,ATTR_WHITESPACE:ot,CUSTOM_ELEMENT:Rt}=zr;let{IS_ALLOWED_URI:lt}=zr,B=null;const Le=R({},[...Ir,...Bt,...Ft,...Ht,...Nr]);let F=null;const it=R({},[...Or,...Gt,...Pr,...vt]);let z=Object.seal(nn(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ee=null,Se=null,ct=!0,je=!0,ut=!1,dt=!0,we=!1,Ie=!0,pe=!1,Ne=!1,We=!1,x=!1,c=!1,G=!1,b=!0,se=!1;const Oe="user-content-";let ke=!0,q=!1,Ce={},Pe=null;const cr=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ur=null;const dr=R({},["audio","video","img","source","image","track"]);let Dt=null;const pr=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),pt="http://www.w3.org/1998/Math/MathML",ht="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml";let ze=he,Mt=!1,Lt=null;const ln=R({},[pt,ht,he],Ut);let ft=R({},["mi","mo","mn","ms","mtext"]),gt=R({},["annotation-xml"]);const cn=R({},["title","style","font","a","script"]);let Ve=null;const un=["application/xhtml+xml","text/html"],dn="text/html";let j=null,$e=null;const pn=t.createElement("form"),hr=function(o){return o instanceof RegExp||o instanceof Function},It=function(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!($e&&$e===o)){if((!o||typeof o!="object")&&(o={}),o=me(o),Ve=un.indexOf(o.PARSER_MEDIA_TYPE)===-1?dn:o.PARSER_MEDIA_TYPE,j=Ve==="application/xhtml+xml"?Ut:kt,B=le(o,"ALLOWED_TAGS")?R({},o.ALLOWED_TAGS,j):Le,F=le(o,"ALLOWED_ATTR")?R({},o.ALLOWED_ATTR,j):it,Lt=le(o,"ALLOWED_NAMESPACES")?R({},o.ALLOWED_NAMESPACES,Ut):ln,Dt=le(o,"ADD_URI_SAFE_ATTR")?R(me(pr),o.ADD_URI_SAFE_ATTR,j):pr,ur=le(o,"ADD_DATA_URI_TAGS")?R(me(dr),o.ADD_DATA_URI_TAGS,j):dr,Pe=le(o,"FORBID_CONTENTS")?R({},o.FORBID_CONTENTS,j):cr,Ee=le(o,"FORBID_TAGS")?R({},o.FORBID_TAGS,j):me({}),Se=le(o,"FORBID_ATTR")?R({},o.FORBID_ATTR,j):me({}),Ce=le(o,"USE_PROFILES")?o.USE_PROFILES:!1,ct=o.ALLOW_ARIA_ATTR!==!1,je=o.ALLOW_DATA_ATTR!==!1,ut=o.ALLOW_UNKNOWN_PROTOCOLS||!1,dt=o.ALLOW_SELF_CLOSE_IN_ATTR!==!1,we=o.SAFE_FOR_TEMPLATES||!1,Ie=o.SAFE_FOR_XML!==!1,pe=o.WHOLE_DOCUMENT||!1,x=o.RETURN_DOM||!1,c=o.RETURN_DOM_FRAGMENT||!1,G=o.RETURN_TRUSTED_TYPE||!1,We=o.FORCE_BODY||!1,b=o.SANITIZE_DOM!==!1,se=o.SANITIZE_NAMED_PROPS||!1,ke=o.KEEP_CONTENT!==!1,q=o.IN_PLACE||!1,lt=o.ALLOWED_URI_REGEXP||sn,ze=o.NAMESPACE||he,ft=o.MATHML_TEXT_INTEGRATION_POINTS||ft,gt=o.HTML_INTEGRATION_POINTS||gt,z=o.CUSTOM_ELEMENT_HANDLING||{},o.CUSTOM_ELEMENT_HANDLING&&hr(o.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(z.tagNameCheck=o.CUSTOM_ELEMENT_HANDLING.tagNameCheck),o.CUSTOM_ELEMENT_HANDLING&&hr(o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(z.attributeNameCheck=o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),o.CUSTOM_ELEMENT_HANDLING&&typeof o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(z.allowCustomizedBuiltInElements=o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),we&&(je=!1),c&&(x=!0),Ce&&(B=R({},Nr),F=[],Ce.html===!0&&(R(B,Ir),R(F,Or)),Ce.svg===!0&&(R(B,Bt),R(F,Gt),R(F,vt)),Ce.svgFilters===!0&&(R(B,Ft),R(F,Gt),R(F,vt)),Ce.mathMl===!0&&(R(B,Ht),R(F,Pr),R(F,vt))),o.ADD_TAGS&&(B===Le&&(B=me(B)),R(B,o.ADD_TAGS,j)),o.ADD_ATTR&&(F===it&&(F=me(F)),R(F,o.ADD_ATTR,j)),o.ADD_URI_SAFE_ATTR&&R(Dt,o.ADD_URI_SAFE_ATTR,j),o.FORBID_CONTENTS&&(Pe===cr&&(Pe=me(Pe)),R(Pe,o.FORBID_CONTENTS,j)),ke&&(B["#text"]=!0),pe&&R(B,["html","head","body"]),B.table&&(R(B,["tbody"]),delete Ee.tbody),o.TRUSTED_TYPES_POLICY){if(typeof o.TRUSTED_TYPES_POLICY.createHTML!="function")throw Je('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof o.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Je('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');P=o.TRUSTED_TYPES_POLICY,ne=P.createHTML("")}else P===void 0&&(P=aa(w,s)),P!==null&&typeof ne=="string"&&(ne=P.createHTML(""));J&&J(o),$e=o}},fr=R({},[...Bt,...Ft,...Xs]),gr=R({},[...Ht,...Ys]),hn=function(o){let m=W(o);(!m||!m.tagName)&&(m={namespaceURI:ze,tagName:"template"});const k=kt(o.tagName),$=kt(m.tagName);return Lt[o.namespaceURI]?o.namespaceURI===ht?m.namespaceURI===he?k==="svg":m.namespaceURI===pt?k==="svg"&&($==="annotation-xml"||ft[$]):!!fr[k]:o.namespaceURI===pt?m.namespaceURI===he?k==="math":m.namespaceURI===ht?k==="math"&&gt[$]:!!gr[k]:o.namespaceURI===he?m.namespaceURI===ht&&!gt[$]||m.namespaceURI===pt&&!ft[$]?!1:!gr[k]&&(cn[k]||!fr[k]):!!(Ve==="application/xhtml+xml"&&Lt[o.namespaceURI]):!1},ie=function(o){Ke(e.removed,{element:o});try{W(o).removeChild(o)}catch{A(o)}},Ue=function(o,m){try{Ke(e.removed,{attribute:m.getAttributeNode(o),from:m})}catch{Ke(e.removed,{attribute:null,from:m})}if(m.removeAttribute(o),o==="is")if(x||c)try{ie(m)}catch{}else try{m.setAttribute(o,"")}catch{}},mr=function(o){let m=null,k=null;if(We)o="<remove></remove>"+o;else{const H=Lr(o,/^[\r\n\t ]+/);k=H&&H[0]}Ve==="application/xhtml+xml"&&ze===he&&(o='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+o+"</body></html>");const $=P?P.createHTML(o):o;if(ze===he)try{m=new h().parseFromString($,Ve)}catch{}if(!m||!m.documentElement){m=te.createDocument(ze,"template",null);try{m.documentElement.innerHTML=Mt?ne:$}catch{}}const Y=m.body||m.documentElement;return o&&k&&Y.insertBefore(t.createTextNode(k),Y.childNodes[0]||null),ze===he?de.call(m,pe?"html":"body")[0]:pe?m.documentElement:Y},br=function(o){return ue.call(o.ownerDocument||o,o,i.SHOW_ELEMENT|i.SHOW_COMMENT|i.SHOW_TEXT|i.SHOW_PROCESSING_INSTRUCTION|i.SHOW_CDATA_SECTION,null)},Nt=function(o){return o instanceof E&&(typeof o.nodeName!="string"||typeof o.textContent!="string"||typeof o.removeChild!="function"||!(o.attributes instanceof p)||typeof o.removeAttribute!="function"||typeof o.setAttribute!="function"||typeof o.namespaceURI!="string"||typeof o.insertBefore!="function"||typeof o.hasChildNodes!="function")},xr=function(o){return typeof l=="function"&&o instanceof l};function fe(S,o,m){xt(S,k=>{k.call(e,o,m,$e)})}const vr=function(o){let m=null;if(fe(V.beforeSanitizeElements,o,null),Nt(o))return ie(o),!0;const k=j(o.nodeName);if(fe(V.uponSanitizeElement,o,{tagName:k,allowedTags:B}),Ie&&o.hasChildNodes()&&!xr(o.firstElementChild)&&K(/<[/\w!]/g,o.innerHTML)&&K(/<[/\w!]/g,o.textContent)||o.nodeType===tt.progressingInstruction||Ie&&o.nodeType===tt.comment&&K(/<[/\w]/g,o.data))return ie(o),!0;if(!B[k]||Ee[k]){if(!Ee[k]&&kr(k)&&(z.tagNameCheck instanceof RegExp&&K(z.tagNameCheck,k)||z.tagNameCheck instanceof Function&&z.tagNameCheck(k)))return!1;if(ke&&!Pe[k]){const $=W(o)||o.parentNode,Y=D(o)||o.childNodes;if(Y&&$){const H=Y.length;for(let re=H-1;re>=0;--re){const ge=y(Y[re],!0);ge.__removalCount=(o.__removalCount||0)+1,$.insertBefore(ge,U(o))}}}return ie(o),!0}return o instanceof g&&!hn(o)||(k==="noscript"||k==="noembed"||k==="noframes")&&K(/<\/no(script|embed|frames)/i,o.innerHTML)?(ie(o),!0):(we&&o.nodeType===tt.text&&(m=o.textContent,xt([Fe,He,Ge],$=>{m=Qe(m,$," ")}),o.textContent!==m&&(Ke(e.removed,{element:o.cloneNode()}),o.textContent=m)),fe(V.afterSanitizeElements,o,null),!1)},wr=function(o,m,k){if(b&&(m==="id"||m==="name")&&(k in t||k in pn))return!1;if(!(je&&!Se[m]&&K(At,m))){if(!(ct&&K(qe,m))){if(!F[m]||Se[m]){if(!(kr(o)&&(z.tagNameCheck instanceof RegExp&&K(z.tagNameCheck,o)||z.tagNameCheck instanceof Function&&z.tagNameCheck(o))&&(z.attributeNameCheck instanceof RegExp&&K(z.attributeNameCheck,m)||z.attributeNameCheck instanceof Function&&z.attributeNameCheck(m))||m==="is"&&z.allowCustomizedBuiltInElements&&(z.tagNameCheck instanceof RegExp&&K(z.tagNameCheck,k)||z.tagNameCheck instanceof Function&&z.tagNameCheck(k))))return!1}else if(!Dt[m]){if(!K(lt,Qe(k,ot,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&o!=="script"&&qs(k,"data:")===0&&ur[o])){if(!(ut&&!K(at,Qe(k,ot,"")))){if(k)return!1}}}}}}return!0},kr=function(o){return o!=="annotation-xml"&&Lr(o,Rt)},yr=function(o){fe(V.beforeSanitizeAttributes,o,null);const{attributes:m}=o;if(!m||Nt(o))return;const k={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let $=m.length;for(;$--;){const Y=m[$],{name:H,namespaceURI:re,value:ge}=Y,Xe=j(H),Ot=ge;let Z=H==="value"?Ot:js(Ot);if(k.attrName=Xe,k.attrValue=Z,k.keepAttr=!0,k.forceKeepAttr=void 0,fe(V.uponSanitizeAttribute,o,k),Z=k.attrValue,se&&(Xe==="id"||Xe==="name")&&(Ue(H,o),Z=Oe+Z),Ie&&K(/((--!?|])>)|<\/(style|title)/i,Z)){Ue(H,o);continue}if(k.forceKeepAttr)continue;if(!k.keepAttr){Ue(H,o);continue}if(!dt&&K(/\/>/i,Z)){Ue(H,o);continue}we&&xt([Fe,He,Ge],_r=>{Z=Qe(Z,_r," ")});const Tr=j(o.nodeName);if(!wr(Tr,Xe,Z)){Ue(H,o);continue}if(P&&typeof w=="object"&&typeof w.getAttributeType=="function"&&!re)switch(w.getAttributeType(Tr,Xe)){case"TrustedHTML":{Z=P.createHTML(Z);break}case"TrustedScriptURL":{Z=P.createScriptURL(Z);break}}if(Z!==Ot)try{re?o.setAttributeNS(re,H,Z):o.setAttribute(H,Z),Nt(o)?ie(o):Mr(e.removed)}catch{Ue(H,o)}}fe(V.afterSanitizeAttributes,o,null)},fn=function S(o){let m=null;const k=br(o);for(fe(V.beforeSanitizeShadowDOM,o,null);m=k.nextNode();)fe(V.uponSanitizeShadowNode,m,null),vr(m),yr(m),m.content instanceof a&&S(m.content);fe(V.afterSanitizeShadowDOM,o,null)};return e.sanitize=function(S){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,k=null,$=null,Y=null;if(Mt=!S,Mt&&(S="<!-->"),typeof S!="string"&&!xr(S))if(typeof S.toString=="function"){if(S=S.toString(),typeof S!="string")throw Je("dirty is not a string, aborting")}else throw Je("toString is not a function");if(!e.isSupported)return S;if(Ne||It(o),e.removed=[],typeof S=="string"&&(q=!1),q){if(S.nodeName){const ge=j(S.nodeName);if(!B[ge]||Ee[ge])throw Je("root node is forbidden and cannot be sanitized in-place")}}else if(S instanceof l)m=mr("<!---->"),k=m.ownerDocument.importNode(S,!0),k.nodeType===tt.element&&k.nodeName==="BODY"||k.nodeName==="HTML"?m=k:m.appendChild(k);else{if(!x&&!we&&!pe&&S.indexOf("<")===-1)return P&&G?P.createHTML(S):S;if(m=mr(S),!m)return x?null:G?ne:""}m&&We&&ie(m.firstChild);const H=br(q?S:m);for(;$=H.nextNode();)vr($),yr($),$.content instanceof a&&fn($.content);if(q)return S;if(x){if(c)for(Y=ve.call(m.ownerDocument);m.firstChild;)Y.appendChild(m.firstChild);else Y=m;return(F.shadowroot||F.shadowrootmode)&&(Y=Me.call(n,Y,!0)),Y}let re=pe?m.outerHTML:m.innerHTML;return pe&&B["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&K(an,m.ownerDocument.doctype.name)&&(re="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+re),we&&xt([Fe,He,Ge],ge=>{re=Qe(re,ge," ")}),P&&G?P.createHTML(re):re},e.setConfig=function(){let S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};It(S),Ne=!0},e.clearConfig=function(){$e=null,Ne=!1},e.isValidAttribute=function(S,o,m){$e||It({});const k=j(S),$=j(o);return wr(k,$,m)},e.addHook=function(S,o){typeof o=="function"&&Ke(V[S],o)},e.removeHook=function(S,o){if(o!==void 0){const m=Hs(V[S],o);return m===-1?void 0:Gs(V[S],m,1)[0]}return Mr(V[S])},e.removeHooks=function(S){V[S]=[]},e.removeAllHooks=function(){V=$r()},e}var oa=on();const la={class:"flex-1"},ia={class:"flex items-center"},ca={class:"relative"},ua={key:1,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5 z-10"},da={class:"flex flex-col gap-1"},pa=["onClick"],ha=["onClick"],fa={key:0,class:"flex flex-col gap-2 text-gray-500 text-xs p-3"},ga={key:0,class:"flex items-center"},ma={key:2,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5"},ba={class:"flex items-center gap-2"},xa={class:"flex-1 overflow-y-auto overflow-x-hidden"},va={class:"px-0.5"},wa={class:"mb-1"},ka={key:0},ya={key:1,class:"text-gray-500"},Ta={class:"flex items-center justify-between mt-0.5"},_a={class:"flex items-center gap-1"},Ea={class:"flex flex-wrap gap-1"},Sa=["onClick"],Ca=["innerHTML"],Aa={key:1,class:"h-full flex items-center justify-center text-gray-500 text-[15px]"},Ra={class:"border-t border-[var(--border-color)] bg-[var(--bg-color)] w-full input-bar-container"},Da={class:"flex items-center justify-between relative z-10"},Ma={class:"flex items-center gap-2"},La={class:"text-sm font-medium"},Ia={class:"p-0.5 max-w-full"},Na=["placeholder","disabled"],Oa={class:"flex items-center justify-between"},Pa={class:"flex items-center gap-1"},za={class:"relative model-selector"},$a={class:"max-h-[60vh] overflow-y-auto"},Ua=["onClick"],Ba={class:"flex items-center gap-2"},Fa=["disabled"],Ha={class:"flex-1 overflow-y-auto p-4"},Ga={class:"flex-1 min-w-0"},qa={class:"flex items-center gap-2"},ja={class:"font-medium text-gray-900 truncate"},Wa={class:"text-xs text-gray-400"},Va={key:0,class:"text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full"},Xa={class:"text-xs text-gray-500 break-all"},Ya={class:"flex items-center gap-1"},Za=["onClick"],Ka=["onClick"],Qa={key:0,class:"text-center text-gray-500 py-8"},Ja={key:4,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},eo={class:"add-model-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 flex flex-col"},to={class:"p-4 space-y-4"},ro={key:0,class:"mt-4"},no={class:"flex items-center gap-2"},so={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},ao={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},oo={class:"p-4 border-t border-[var(--border-color)] flex gap-2"},lo=["disabled"],io={key:0,class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},co=Ur({__name:"Main",setup(r){const e=qr(),t=Gr(),n=Et(),s=Kt(),a=Hr(),d=Br(),l=Qt(),g=O(!1),i=O(!1),p=O(null),E=O(""),h=O({entryDisplayConfig:{skipJsCss:!0,excludedPathPatterns:[]}}),w=O(!1),v=O(""),y=O(!1),A=O(!1),U=O(!1),D=O({showName:"",baseUrl:"",modelName:"",apiKey:""}),W=O(null),P=O(!1);let ne=null;const te=O(!0),ue=O(!0),ve=ae;let de=0;const Me=O(null),V=Be(()=>!t.currentDatabase||t.currentDatabaseId===ae?"请选择程序数据再对话":t.currentDatabase.dataId?"AI 能力正在测试中，敬请期待！":"请先录制程序数据然后再对话"),Fe=Be(()=>!t.currentDatabase||!t.currentDatabase.dataId||s.status==="waiting"),He=async()=>{var c;const x=l.getText();if(x.trim()){if(!((c=t.currentDatabase)!=null&&c.dataId)){n.setError("当前没有数据，不可聊天。请先选择程序数据。或者使用 Debug with XCodeMap 创建新的程序数据。");return}if(!a.currentModel){n.setError("请先选择模型再发送消息"),y.value=!0;return}l.clearText(),await e.sendMessage(x)}},Ge=()=>{g.value=!g.value},At=async x=>{var b;const c=t.currentDatabase,G=!c||c.id!==x;if(de=0,console.log("handleDatabaseSelect",x),t.setCurrentDatabaseId(x),g.value=!1,G){if(!await mt(x,((b=t.currentDatabase)==null?void 0:b.dataId)||"")){n.setError("Failed to switch process data");return}e.createNewChat(),p.value=null,te.value=!0,i.value=!0}},qe=async()=>{var x;if((x=t.currentDatabase)!=null&&x.dataId)try{const c=await In({processId:t.currentDatabase.dataId,first:te.value,filterText:E.value}),G=JSON.stringify(c),b=JSON.stringify(p.value);G!==b&&(console.log("Data has changed, updating treeData",G),p.value=c),te.value=!1}catch(c){console.error("Failed to fetch tree data:",c)}},at=async()=>{const x=[...t.databases],c=t.currentDatabase;await t.getDatabase();const G=t.databases,b=t.currentDatabase,se=new Set(x.map(q=>q.id)),Oe=G.filter(q=>!se.has(q.id));x.length===0||x.length===1&&x[0].id;const ke=Oe.filter(q=>!q.id.toLowerCase().includes("local")&&!q.id.toLowerCase().includes("remote"));if(ke.length>0){const q=ke[0];if(t.setCurrentDatabaseId(q.id),!await mt(q.id,q.dataId||"")){n.setError("Failed to switch to new process data");return}e.createNewChat(),p.value=null,te.value=!0,i.value=!0,g.value=!1,de=0;return}if(c&&c.id!==ve&&!b){console.log("Current database is no longer available, resetting to default"),t.setCurrentDatabaseId(ve),await mt(ve,"")||n.setError("Failed to switch process data"),g.value=!0,p.value=null,te.value=!0,i.value=!1,e.createNewChat(),de=0;return}if(c&&b&&c.id===b.id&&c.dataId!==b.dataId&&!await mt(b.id,b.dataId||"")){n.setError("Failed to switch process data");return}b&&b.id!==ve&&(b.serverSelected?de=0:(de++,de>=3&&(console.log("Database consistency check failed 3 times, resetting to default"),t.setCurrentDatabaseId(ve),g.value=!0,p.value=null,te.value=!0,i.value=!1,e.createNewChat(),de=0)))};_n(()=>{qe()});const ot=x=>{x.labelKey==="url"&&x.labelValue&&window.open(x.labelValue,"_blank"),console.log("Clicked tree node:",x)},Rt=x=>{const c=h.value.entryDisplayConfig.excludedPathPatterns.indexOf(x);c>-1&&(h.value.entryDisplayConfig.excludedPathPatterns.splice(c,1),Le())},lt=()=>{w.value=!0,zt(()=>{const x=document.querySelector(".input-new-tag input");x&&x.focus()})},B=()=>{v.value&&(h.value.entryDisplayConfig.excludedPathPatterns.push(v.value),Le()),w.value=!1,v.value=""},Le=async()=>{try{await Nn(h.value)||n.setError("Failed to update filter configuration")}catch(x){console.error("Failed to update filter configuration:",x)}},F=x=>{const c=x.target;c.closest(".model-selector")||(y.value=!1),c.closest(".model-manager-modal")||(A.value=!1)},it=async()=>{y.value=!1;try{await a.getModelConfigData(),A.value=!0}catch(x){console.error("Failed to open model manager:",x),n.setError("Failed to open model manager")}},z=()=>{A.value=!1},Ee=()=>{U.value=!0,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""}},Se=()=>{U.value=!1,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""},W.value=null},ct=async()=>{const x={showName:D.value.showName.trim(),baseUrl:D.value.baseUrl.trim(),modelName:D.value.modelName.trim(),apiKey:D.value.apiKey.trim()};if(!x.showName||!x.baseUrl||!x.modelName||!x.apiKey){n.setError("请填写所有必填字段");return}P.value=!0,W.value=null;const c={uuid:crypto.randomUUID(),showName:x.showName,baseUrl:x.baseUrl,modelName:x.modelName,apiKey:x.apiKey},G=await On(c);if(!G.success){W.value={success:!1,message:G.error||"模型连通性测试失败"},P.value=!1;return}const b=await Pn(c);b.success?(Se(),await a.getModelConfigData()):W.value={success:!1,message:b.error||"添加模型失败"},P.value=!1},je=async x=>{const c=await zn(x);c.success?await a.getModelConfigData():n.setError(c.error||"删除模型失败")},ut=async x=>{const c=await $n(x);c.success?await a.getModelConfigData():n.setError(c.error||"设置默认模型失败")},dt=async(x,c)=>{if(c.stopPropagation(),x.id===ae){n.setError("不能删除默认程序数据");return}if(x.active){n.setError("不能删除正在运行的程序数据");return}if(x.id===t.currentDatabaseId){n.setError("不能删除当前选中的程序数据");return}await t.deleteDatabase(x.id)||n.setError("删除程序数据失败")};En(()=>{d.initTheme(),at(),qe(),Sn().then(x=>{x.success&&x.data&&(h.value.entryDisplayConfig=x.data.entryDisplayConfig)}),a.getModelConfigData(),document.addEventListener("click",F),window.addEventListener("textarea-content-changed",Ne),ne=window.setInterval(()=>{at(),qe()},1e3)}),Cn(()=>{ne!==null&&(clearInterval(ne),ne=null),document.removeEventListener("click",F),window.removeEventListener("textarea-content-changed",Ne)});const we=(x,c)=>c!=="tool"?x.replace(/`?CallID_(\d+)`?/g,"[CallID_$1](./findFunc?callId=$1)"):x,Ie=x=>{M.setOptions({breaks:!0,gfm:!0,pedantic:!0});const c=M.parse(x);return oa.sanitize(c,{ALLOWED_TAGS:["p","br","strong","em","code","pre","blockquote","ul","ol","li","h1","h2","h3","h4","h5","h6","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title","class"]})},pe=x=>{const c=x.target;c.style.height="auto",c.style.height=Math.min(c.scrollHeight,200)+"px"},Ne=()=>{zt(()=>{const x=document.querySelector(".input");x&&(x.style.height="auto",x.style.height=Math.min(x.scrollHeight,200)+"px")})},We=()=>{zt(()=>{Me.value&&(Me.value.scrollTop=Me.value.scrollHeight)})};return jt(()=>{var x;return(x=e.currentChat)==null?void 0:x.messages},()=>{var x;We(),(x=e.currentChat)!=null&&x.messages&&e.currentChat.messages.length>0&&(i.value=!1)},{deep:!0}),jt(()=>l.message,()=>{l.message.trim()&&(i.value=!1)}),(x,c)=>{var G;return _(),T("div",{class:C(["min-h-screen w-full flex flex-col overflow-x-hidden",(f(d).theme==="dark","bg-[var(--bg-color)]")])},[f(n).error?(_(),T("div",{key:0,class:C(["fixed top-0 left-0 right-0 px-4 py-3 z-[9999] flex items-center justify-between",f(d).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[u("span",la,X(f(n).error.message),1),u("button",{onClick:c[0]||(c[0]=b=>f(n).clearError()),class:C(["ml-4 p-1 rounded-full hover:bg-opacity-20",f(d).theme==="dark"?"hover:bg-red-100":"hover:bg-red-200"])},c[17]||(c[17]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),2)],2)):I("",!0),u("div",{class:C(["border-b py-0.5 px-0.5 flex items-center justify-between sticky top-0 z-20 bg-[var(--bg-color)]",(f(d).theme==="dark","border-[var(--border-color)]")])},[u("div",ia,[u("div",ca,[u("button",{onClick:Ge,class:C(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(d).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[u("span",null,X(f(t).currentDatabase?f(t).currentDatabase.name:"待选择程序数据(取消行号X图标)"),1),(_(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":g.value}]),viewBox:"0 0 20 20",fill:"currentColor"},c[18]||(c[18]=[u("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2)])]),u("button",{onClick:c[1]||(c[1]=()=>{f(e).createNewChat(),ue.value=!0}),class:C(["p-1 rounded-full",f(d).theme==="dark"?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"]),title:"新建聊天"},c[19]||(c[19]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)],2),g.value||f(t).currentDatabaseId===f(ae)?(_(),T("div",ua,[u("div",da,[(_(!0),T(Te,null,_e(f(t).databases,b=>(_(),T("div",{key:b.id,class:C(["flex items-center justify-between w-full px-2 py-0.5 rounded-lg text-xs font-medium text-left border-2 transition-all duration-150 focus:outline-none cursor-pointer",[b.id===f(t).currentDatabaseId?f(d).theme==="dark"?"button-selected-dark":"button-selected-light":f(d).theme==="dark"?"button-hover-dark":"button-hover-light"]])},[u("div",{class:"flex-1",onClick:se=>At(b.id)},X(b.name),9,pa),b.id!==f(ae)&&!b.active&&b.id!==f(t).currentDatabaseId?(_(),T("button",{key:0,onClick:se=>dt(b,se),class:C(["ml-2 p-0.5 rounded-full transition-colors",f(d).theme==="dark"?"hover:bg-red-700 text-red-400 hover:text-red-200":"hover:bg-red-100 text-red-500 hover:text-red-700"]),title:"删除程序数据"},c[20]||(c[20]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)]),10,ha)):I("",!0)],2))),128))]),f(t).currentDatabaseId===f(ae)?(_(),T("div",fa,[f(t).databases.length===1?(_(),T("div",ga,c[21]||(c[21]=[u("span",null,[$t("当前无可用的程序数据，请使用 "),u("img",{src:Fn,alt:"XCodeMap Logo",class:"w-3 h-3 inline align-text-bottom"}),$t(" Debug with XCodeMap 录制程序数据。")],-1)]))):I("",!0),c[22]||(c[22]=An('<div class="text-xs opacity-75 leading-relaxed space-y-1" data-v-2173ce65><div class="text-xs opacity-75 mb-2" data-v-2173ce65>XCodeMap 无需断点，随时查看每个函数的输入输出与堆栈</div><div class="font-medium" data-v-2173ce65>使用步骤：</div><div class="space-y-1 pl-2" data-v-2173ce65><strong data-v-2173ce65> 录制步骤：</strong><div data-v-2173ce65>1. 点击 Debug with XCodeMap 按钮</div><div data-v-2173ce65>2. 开始录制</div><div data-v-2173ce65>3. 触发业务请求</div><div data-v-2173ce65>4. 关闭录制</div><strong data-v-2173ce65> 回放步骤：</strong><div data-v-2173ce65>1. 点击请求列表、或者点击行号栏 X 图标，以回放现场</div><div data-v-2173ce65>2. 鼠标悬停在函数名字上，查看每个函数的输入输出</div><div data-v-2173ce65>3. StepInto/StepOut/StepOver/StepBack 模拟程序的执行</div><div data-v-2173ce65>4. 单击（跳转调用处）/双击（跳转定义处）序列图上的函数名字可以与源码联动</div><div data-v-2173ce65>5. 点击序列图上的 +/- 可以展开/折叠函数，右上角的“浏览轨迹”按钮，可以修剪序列图</div><div data-v-2173ce65>6. “Services” 窗口中，关闭序列图，可以退出当前请求/线程的回放（此时仍可以看到行号栏 X 图标）</div><div data-v-2173ce65>7. 将数据源设置“待选择程序数据（取消行号 X 图标）”，可以退出当前程序数据的回放</div><strong data-v-2173ce65> 高级技巧：</strong><div data-v-2173ce65> 1. 在配置页面配置&quot;默认随着 Debug 启动&quot;，可以与 Jrebel 等热部署工具配合使用</div><div data-v-2173ce65> 2. XCodeMap 默认只录制项目包，如需录制其他包，请在配置页面配置</div><div data-v-2173ce65> 3. 不录制时，XCodeMap 对CPU、内存占用极低，可以长期使用</div><div data-v-2173ce65> 4. 尽量按需录制，单次录制时间不宜过长，避免内存占用过高</div><div data-v-2173ce65> 5. 更多功能查看配置页面，或者关注 B 站视频教程</div></div></div>',1))])):I("",!0)])):I("",!0),f(t).currentDatabase&&f(t).currentDatabase.active?(_(),T("div",ma,[u("div",ba,[u("span",{class:C(["px-1.5 py-0.5 text-xs rounded-full opacity-75",{"bg-green-50 text-green-600":f(t).currentDatabase.recordState==="recording","bg-gray-50 text-gray-500":f(t).currentDatabase.recordState==="idle","bg-yellow-50 text-yellow-600":f(t).currentDatabase.recordState==="paused"}])},X(f(t).currentDatabase.recordState),3),f(t).currentDatabase.recordState==="idle"?(_(),T("button",{key:0,onClick:c[2]||(c[2]=b=>f(t).startRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-green-50 text-green-600 hover:bg-green-200 hover:text-green-800 border border-green-200 hover:border-green-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"开始录制"}," 开始录制 ")):I("",!0),f(t).currentDatabase.recordState==="recording"?(_(),T("button",{key:1,onClick:c[3]||(c[3]=b=>f(t).endRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-red-50 text-red-600 hover:bg-red-200 hover:text-red-800 border border-red-200 hover:border-red-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"结束录制"}," 结束录制 ")):I("",!0),f(t).currentDatabase.recordState==="paused"?(_(),T("button",{key:2,onClick:c[4]||(c[4]=b=>f(t).restartRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-yellow-50 text-yellow-600 hover:bg-yellow-200 hover:text-yellow-800 border border-yellow-200 hover:border-yellow-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"重新录制"}," 重新录制 ")):I("",!0)])])):I("",!0),u("div",xa,[f(t).currentDatabaseId!==f(ae)?(_(),T("div",{key:0,class:C([(f(d).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]"),"border-b border-t"])},[u("div",va,[u("div",{onClick:c[5]||(c[5]=b=>i.value=!i.value),class:C(["flex items-center cursor-pointer rounded-lg px-1 py-0.5 border-b border-[var(--border-color)] bg-[var(--undercaret-bg-color)] transition-colors duration-150",(f(d).theme==="dark","hover:bg-[var(--header-hover-bg-color)]")])},[(_(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-90":i.value,"text-[var(--text-color)]":!0}]),viewBox:"0 0 20 20",fill:"currentColor"},c[23]||(c[23]=[u("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),c[24]||(c[24]=u("span",{class:"text-xs font-medium px-1 text-[var(--text-color)]"},"请求与线程入口",-1))],2)]),i.value?(_(),T("div",{key:0,class:C(["p-1 bg-[var(--undercaret-bg-color)] overflow-y-auto",(f(d).theme==="dark","border-t border-[var(--border-color)]")])},[u("div",wa,[ye(u("input",{"onUpdate:modelValue":c[6]||(c[6]=b=>E.value=b),type:"text",placeholder:"搜索网络请求...",class:C(["w-full px-2 py-0.5 rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(d).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400":"bg-white border-gray-300 text-gray-700"])},null,2),[[Ae,E.value]])]),p.value?(_(),T("div",ka,[Rn(rs,{nodes:p.value.rootNodes,onNodeClick:ot},null,8,["nodes"])])):(_(),T("div",ya,"Loading tree data...")),u("div",Ta,[u("div",_a,[u("span",{class:C(["text-xs opacity-50",f(d).theme==="dark"?"text-gray-400":"text-gray-600"])},"排除请求：",2),u("label",{class:C(["flex items-center gap-1 text-xs cursor-pointer opacity-50 hover:opacity-100 transition-opacity",f(d).theme==="dark"?"text-gray-500":"text-gray-400"])},[ye(u("input",{type:"checkbox","onUpdate:modelValue":c[7]||(c[7]=b=>h.value.entryDisplayConfig.skipJsCss=b),onChange:Le,class:C(["rounded focus:ring-0 focus:ring-offset-0 h-2.5 w-2.5 cursor-pointer",f(d).theme==="dark"?"bg-gray-800 border-gray-600 text-gray-500":"border-gray-300 text-gray-400"])},null,34),[[Dn,h.value.entryDisplayConfig.skipJsCss]]),c[25]||(c[25]=u("span",{class:"text-[11px]"},"忽略css/js",-1))],2),u("div",Ea,[(_(!0),T(Te,null,_e(h.value.entryDisplayConfig.excludedPathPatterns,b=>(_(),T("div",{key:b,class:C(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(d).theme==="dark"?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"])},[u("span",null,X(b),1),u("button",{onClick:se=>Rt(b),class:C(f(d).theme==="dark"?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700")},c[26]||(c[26]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,Sa)],2))),128)),w.value?ye((_(),T("input",{key:0,"onUpdate:modelValue":c[8]||(c[8]=b=>v.value=b),class:C(["input-new-tag px-1 py-0 border rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(d).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200":"border-gray-300 text-gray-700"]),placeholder:"输入排除表达式，* 匹配一级路径，** 匹配多级路径",onKeyup:Mn(B,["enter"]),onBlur:B},null,34)),[[Ae,v.value]]):(_(),T("button",{key:1,onClick:lt,class:C(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(d).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-blue-400":"bg-gray-100 hover:bg-gray-200 text-blue-700"])},c[27]||(c[27]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),u("span",null,"New Filter",-1)]),2))])]),u("button",{onClick:c[9]||(c[9]=b=>i.value=!1),class:C(["rounded-full flex items-center",f(d).theme==="dark"?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"]),title:"收起网络请求"},c[28]||(c[28]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),2)])],2)):I("",!0)],2)):I("",!0),u("div",{ref_key:"messageContainerRef",ref:Me,class:"p-0.5 space-y-0.5 w-full border-b border-[var(--border-color)]"},[f(e).currentChat?(_(!0),T(Te,{key:0},_e(f(e).currentChat.messages,b=>(_(),T("div",{key:b.messageId,class:C(["flex items-start gap-1.5 max-w-full",{"justify-end":b.role==="user"}])},[u("div",{class:C(["flex-1 rounded-lg p-2 shadow-sm break-words min-w-0",b.role==="user"?(f(d).theme==="dark","bg-[var(--undercaret-bg-color)]"):(f(d).theme==="dark","bg-[var(--bg-color)]")])},[u("div",{class:C([b.role==="user"?(f(d).theme==="dark","text-[var(--text-color)]"):(f(d).theme==="dark","text-[var(--text-color)]"),"leading-tight text-[15px] markdown-content break-words whitespace-pre-wrap"]),innerHTML:Ie(we(b.content,b.role))},null,10,Ca)],2)],2))),128)):(_(),T("div",Aa," 请选择程序数据后再对话 "))],512)]),u("div",Ra,[f(s).status==="waiting"&&f(t).currentDatabase&&f(t).currentDatabaseId!==f(ae)?(_(),T("div",{key:0,class:C(["border-t border-b py-2 px-3 relative overflow-hidden",f(d).theme==="dark"?"bg-gradient-to-r from-blue-900/30 to-purple-900/30 border-blue-500/50 text-blue-200":"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-300 text-blue-700"])},[c[30]||(c[30]=u("div",{class:"absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 animate-pulse"},null,-1)),u("div",Da,[u("div",Ma,[c[29]||(c[29]=u("div",{class:"flex space-x-1"},[u("div",{class:"w-1.5 h-1.5 bg-current rounded-full animate-bounce",style:{"animation-delay":"0ms"}}),u("div",{class:"w-1.5 h-1.5 bg-current rounded-full animate-bounce",style:{"animation-delay":"150ms"}}),u("div",{class:"w-1.5 h-1.5 bg-current rounded-full animate-bounce",style:{"animation-delay":"300ms"}})],-1)),u("span",La,X(f(s).statusMessage||"正在处理中..."),1)])])],2)):I("",!0),u("div",Ia,[ye(u("textarea",{"onUpdate:modelValue":c[10]||(c[10]=b=>f(l).message=b),class:"input w-full resize-none mb-0.5",rows:"1",placeholder:V.value,disabled:Fe.value,onInput:pe,style:{"min-height":"28px","max-height":"200px","overflow-y":"auto"}},null,40,Na),[[Ae,f(l).message]]),u("div",Oa,[u("div",Pa,[u("span",{class:C(["text-xs",f(d).theme==="dark"?"text-gray-400":"text-gray-500"])},"模型",2),u("div",za,[u("button",{onClick:c[11]||(c[11]=b=>y.value=!y.value),class:C(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(d).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[u("span",null,X(((G=f(a).currentModel)==null?void 0:G.showName)||"请选择模型"),1),(_(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":y.value}]),viewBox:"0 0 20 20",fill:"currentColor"},c[31]||(c[31]=[u("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2),y.value?(_(),T("div",{key:0,class:C(["absolute bottom-full left-0 mb-2 w-80 border rounded-lg shadow-lg z-50",f(d).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[u("div",{class:C(["p-2 border-b",f(d).theme==="dark"?"border-gray-700":"border-gray-200"])},[u("span",{class:C(["text-sm font-medium",f(d).theme==="dark"?"text-gray-200":"text-gray-700"])},"选择模型",2)],2),u("div",$a,[(_(!0),T(Te,null,_e(f(a).availableModels,b=>(_(),T("button",{key:b.uuid,onClick:()=>{f(a).setCurrentModel(b.uuid),y.value=!1},class:C(["w-full px-3 py-2 text-left text-sm transition-all duration-150 border-2 focus:outline-none",{"button-selected-dark":f(a).currentModelUuid===b.uuid&&f(d).theme==="dark","button-selected-light":f(a).currentModelUuid===b.uuid&&f(d).theme==="light","button-hover-dark":f(d).theme==="dark"&&f(a).currentModelUuid!==b.uuid,"button-hover-light":f(d).theme==="light"&&f(a).currentModelUuid!==b.uuid}])},[u("div",Ba,[u("span",null,X(b.showName),1),u("span",{class:C(["text-xs opacity-75",[f(a).currentModelUuid===b.uuid?f(d).theme==="dark"?"text-gray-300":"text-blue-500":f(d).theme==="dark"?"text-gray-500":"text-gray-400"]])},"("+X(b.modelName)+")",3)])],10,Ua))),128)),f(a).availableModels.length===0?(_(),T("div",{key:0,class:C(["px-3 py-2 text-sm",f(d).theme==="dark"?"text-gray-400":"text-gray-500"])}," 暂无可用模型 ",2)):I("",!0)]),u("div",{class:C(["border-t",f(d).theme==="dark"?"border-gray-700":"border-gray-200"])},[u("button",{onClick:it,class:C(["w-full px-3 py-2 text-left text-sm transition-colors",f(d).theme==="dark"?"text-blue-400 hover:bg-gray-700":"text-blue-600 hover:bg-blue-50"])}," 管理模型 ",2)],2)],2)):I("",!0)])]),u("button",{onClick:He,class:C(["p-0.5 rounded-full transition-colors",[f(d).theme==="dark"?"hover:bg-gray-700 text-gray-400 hover:text-gray-200":"hover:bg-gray-100 text-gray-600",{"opacity-50 cursor-not-allowed":!f(l).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting"}]]),disabled:!f(l).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting",title:"发送消息"},c[32]||(c[32]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[u("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),10,Fa)])])]),A.value?(_(),T("div",{key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:z},[u("div",{class:"model-manager-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 max-h-[80vh] flex flex-col",onClick:c[12]||(c[12]=Ln(()=>{},["stop"]))},[u("div",{class:"p-2 border-b border-[var(--border-color)] flex items-center justify-between"},[c[34]||(c[34]=u("h3",{class:"text-base font-medium text-[var(--text-color)]"},"模型管理",-1)),u("button",{onClick:z,class:"text-gray-400 hover:text-gray-600"},c[33]||(c[33]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",Ha,[(_(!0),T(Te,null,_e(f(a).availableModels,b=>{var se,Oe;return _(),T("div",{key:b.uuid,class:"flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-1"},[u("div",Ga,[u("div",qa,[u("span",ja,X(b.showName),1),u("span",Wa,"("+X(b.modelName)+")",1),((se=f(a).modelConfig)==null?void 0:se.defaultModelId)===b.uuid?(_(),T("span",Va," 默认 ")):I("",!0)]),u("div",Xa,X(b.baseUrl),1)]),u("div",Ya,[((Oe=f(a).modelConfig)==null?void 0:Oe.defaultModelId)!==b.uuid?(_(),T("button",{key:0,onClick:ke=>ut(b),class:"flex-shrink-0 text-blue-500 hover:text-blue-700",title:"设为默认"},c[35]||(c[35]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]),8,Za)):I("",!0),u("button",{onClick:ke=>je(b),class:"flex-shrink-0 text-red-500 hover:text-red-700",title:"删除模型"},c[36]||(c[36]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ka)])])}),128)),f(a).availableModels.length===0?(_(),T("div",Qa," 暂无已添加的模型 ")):I("",!0)]),u("div",{class:"p-4 border-t border-[var(--border-color)]"},[u("button",{onClick:Ee,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"},c[37]||(c[37]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),$t(" 添加新模型 ")]))])])])):I("",!0),U.value?(_(),T("div",Ja,[u("div",eo,[u("div",{class:"p-4 border-b border-[var(--border-color)] flex items-center justify-between"},[c[39]||(c[39]=u("h3",{class:"text-lg font-medium text-[var(--text-color)]"},"添加新模型",-1)),u("button",{onClick:Se,class:"text-gray-400 hover:text-gray-600"},c[38]||(c[38]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",to,[u("div",null,[c[40]||(c[40]=u("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型显示名称",-1)),ye(u("input",{"onUpdate:modelValue":c[13]||(c[13]=b=>D.value.showName=b),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入模型显示名称"},null,512),[[Ae,D.value.showName]]),c[41]||(c[41]=u("p",{class:"mt-1 text-xs text-gray-500"},"模型需要兼容OpenAI的API，需要支持 FunctionCall。",-1))]),u("div",null,[c[42]||(c[42]=u("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Base URL",-1)),ye(u("input",{"onUpdate:modelValue":c[14]||(c[14]=b=>D.value.baseUrl=b),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: https://api.openai.com/v1"},null,512),[[Ae,D.value.baseUrl]]),c[43]||(c[43]=u("p",{class:"mt-1 text-xs text-gray-500"},"例如: https://dashscope.aliyuncs.com/compatible-mode/v1 或者 https://api.deepseek.com/v1 ",-1))]),u("div",null,[c[44]||(c[44]=u("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型名称",-1)),ye(u("input",{"onUpdate:modelValue":c[15]||(c[15]=b=>D.value.modelName=b),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: gpt-3.5-turbo"},null,512),[[Ae,D.value.modelName]]),c[45]||(c[45]=u("p",{class:"mt-1 text-xs text-gray-500"},"国内推荐阿里云qwen-max-latest 或者 deepseek-chat，国外推荐 Claude Sonnet 系列",-1))]),u("div",null,[c[46]||(c[46]=u("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"API Key",-1)),ye(u("input",{"onUpdate:modelValue":c[16]||(c[16]=b=>D.value.apiKey=b),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入API Key"},null,512),[[Ae,D.value.apiKey]])]),W.value?(_(),T("div",ro,[u("div",{class:C(["p-3 rounded-lg",{"bg-green-50 text-green-700":W.value.success,"bg-red-50 text-red-700":!W.value.success}])},[u("div",no,[W.value.success?(_(),T("svg",so,c[47]||(c[47]=[u("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(_(),T("svg",ao,c[48]||(c[48]=[u("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 00-1.414-1.414L10 10l-1.293-1.293z","clip-rule":"evenodd"},null,-1)]))),u("span",null,X(W.value.message),1)])],2)])):I("",!0)]),u("div",oo,[u("button",{onClick:Se,class:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),u("button",{onClick:ct,class:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",disabled:P.value},[P.value?(_(),T("svg",io,c[49]||(c[49]=[u("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),u("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):I("",!0),u("span",null,X(P.value?"测试中...":"保存"),1)],8,lo)])])])):I("",!0)],2)}}}),uo=Fr(co,[["__scopeId","data-v-2173ce65"]]),ir=Un(uo);ir.use(Bn());ir.mount("#app");window.$vm=ir._instance;
