<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>XCodeMap List</title>
    <link href="../assets/element-ui-2.15.14/index.min.css" rel="stylesheet">
    <link href="../assets/xcodemap/theme.css" rel="stylesheet">
    <script src="../assets/xcodemap/common.js"></script>
    <script>
    </script>
</head>
<body>
<div id="app">
    <el-container>
        <el-main>
            <div class="left-container">
                <el-scrollbar>
                    <div class="record-status-tab">
                        <div v-for="(item, index) in recordStatusManager.data" :key="index" v-show="recordStatusManager.isRunning()">
                            <el-tag type="" >{{item.processName}}</el-tag>
                            <el-tag type="" >{{item.stateDesc}}</el-tag>
                            <el-button plain @click="recordStatusManager.stopRecorder(index)" v-show="item.state === 'start' ">结束录制</el-button>
                            <el-button plain @click="recordStatusManager.startRecorder(index)" v-show="item.state === 'stop' ">开始录制</el-button>
                        </div>
                        <div v-show="!recordStatusManager.isRunning()" class="record-status-empty-prompt">
                            暂无录制中的程序。请在程序启动处，点击
                            <span class="record-status-logo"><img src="../assets/xcodemap/icons/logo.svg"  /></span>
                            Debug with XCodeMap 的按钮。
                            遇到问题，欢迎到 <el-link href="https://github.com/xcodemap/community"> Github 提交 ISSUE</el-link>。
                        </div>
                    </div>
                    <el-divider></el-divider>
                    <el-input
                            placeholder="输入关键字过滤请求或者线程"
                            v-model="locatorTree.filterText" @input="locatorTree.setFilterText" v-show="locatorTree.hasData()" class="entry-list">
                    </el-input>
                    <el-tree
                            :accordion="true"
                            :data="locatorTree.treeData"
                            :highlight-current="true"
                            :indent="1"
                            :default-expanded-keys="locatorTree.defaultExpandedKeys"
                            :filter-node-method="filterNode"
                            :props="locatorTree.defaultProps"
                            @node-click="locatorTree.clickTreeNode"
                            class="filter-tree"
                            icon-class="none"
                            node-key="nodeKey"
                            ref="locatorTree" v-show="locatorTree.hasData()">
                        <span slot-scope="{ node, data }">
                            <i :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"
                               v-if="data.leaf===false"></i>
                            <i class="el-icon-document" v-else></i>
                            <span :id="data.nodeKey">{{ node.label}}</span>
                        </span>
                    </el-tree>
                    <div class="locator-tree-filter" v-show="locatorTree.hasData()">
                        <span>排除请求：</span>
                        <el-checkbox v-model="filterConfigManager.filterConfig.entryDisplayConfig.skipJsCss" @change="filterConfigManager.updateConfig()">忽略css/js</el-checkbox>
                        <el-tag
                                :key="tag"
                                v-for="tag in filterConfigManager.filterConfig.entryDisplayConfig.excludedPathPatterns"
                                closable
                                :disable-transitions="false"
                                @close="filterConfigManager.handleFilterClose(tag)">
                            {{tag}}
                        </el-tag>
                        <el-input
                                class="input-new-tag"
                                v-if="filterConfigManager.filterInputVisible"
                                v-model="filterConfigManager.filterInputValue"
                                ref="saveTagInput"
                                size="small"
                                @keyup.enter.native="filterConfigManager.handleFilterInputConfirm"
                                @blur="filterConfigManager.handleFilterInputConfirm"
                                placeholder="输入排除表达式，* 匹配一级路径，** 匹配多级路径"
                        >
                        </el-input>
                        <el-button v-else class="button-new-tag" size="small" @click="filterConfigManager.showFilterInput" >+ New Filter</el-button>
                    </div>
                    <el-divider></el-divider>
                    <div class="doc-list">
                        <strong>使用文档</strong>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第一步</el-tag> <el-link href="./help_step1.html?functionName=Learn-Step-1" >Debug with XCodeMap，启动程序并开始录制</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第二步</el-tag> <el-link href="./help_step2.html?functionName=Learn-Step-2" >点击 + 展开序列图，并用"浏览记录"和"忽略规则"来修剪序列图</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第三步</el-tag> <el-link href="./help_step3.html?functionName=Learn-Step-3" >点击高亮执行源码，与序列图联动</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第四步</el-tag> <el-link href="./help_step4.html?functionName=Learn-Step-4" >点击展开上下文数据，包括参数、返回值等</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第五步</el-tag> <el-link href="./help_step5.html?functionName=Learn-Step-5" >通过搜索和回溯，实现时光穿梭</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="info" size="mini">加餐</el-tag> <el-link href="./why.html?functionName=why-xcodemap" >为什么说 "重现" 是搞定 "屎山代码" 的良药？</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="info" size="mini">加餐</el-tag> <el-link href="./imp.html?functionName=重要提示" >如何避免 IDE 卡顿？ </el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="info" size="mini">加餐</el-tag> <el-link href="./contact.html?functionName=关注我们" >加微信，进一步交流 </el-link>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </el-main>
        <div class="x-handle" @mousedown="rightAside.mouseDown"></div>
        <el-aside :style="{ width: rightAside.asideWidth + 'px' }" class="right-aside">
            <div class="right-container">
                <el-scrollbar ref="scrollMenuRightRef">
                <div class="right-tab-content" v-show="rightAside.activeTabName === 'recordFilter'">
                    <div class="filter-button-wrap">
                        <div class="filter-button">
                            <el-button plain @click="filterConfigManager.updateConfig()">Save</el-button>
                        </div>
                        <div class="filter-button">
                            <el-button @click="filterConfigManager.resetConfig()" plain>Reset</el-button>
                        </div>
                    </div>
                    <div class="filter-detail-list">
                        <div class="included-filter-label">
                            录制项目包
                            <span @click="filterConfigManager.setNotesLabel('1')" class="filterBarMenuChat">
                                <i class="el-icon-chat-dot-round"></i>
                            </span>
                        </div>
                        <div class="included-filter-notes" v-show="filterConfigManager.notesLabel['1']">
                            XCodeMap 默认检测并录制项目包。如果要录制依赖包，请在后面手工添加。如果发现项目包遗漏，也可在后面添加。
                        </div>
                        <div v-show="filterConfigManager.filterConfig.enableAutoDetect" class="included-filter-body" v-for="(item, index) in filterConfigManager.filterConfig.autoDetectedPackages" :key="index">
                            <el-tag type="">{{filterConfigManager.filterConfig.autoDetectedPackages[index]}}</el-tag>
                        </div>

                        <div class="included-filter-label">
                            录制依赖包
                            <span @click="filterConfigManager.addPackagePrefix()" class="filterBarMenu filterBarMenuPlus">
                            <i class="el-icon-plus"></i>
                            </span>
                            <span @click="filterConfigManager.setNotesLabel('2')" class="filterBarMenu filterBarMenuChat">
                                <i class="el-icon-chat-dot-round"></i>
                            </span>
                        </div>
                        <div class="included-filter-notes" v-show="filterConfigManager.notesLabel['2']">
                            配置包的前缀即可。例如配置 org.spring，将录制 spring 模块的调用数据。注意，如果包范围内的类处于循环或者递归中，则只采集部分调用。
                        </div>

                        <div class="filter-input included-filter-body" v-for="(item, index) in filterConfigManager.filterConfig.includedPackagePrefixes" :key="index">
                            <el-input v-model="filterConfigManager.filterConfig.includedPackagePrefixes[index]" placeHolder=" 填写包前缀，例如 org.spring "> </el-input>
                            <span class="filterBarMenu filterBarMenuMinus" @click="filterConfigManager.deletePackagePrefix(index)">
                                <i class="el-icon-minus"></i>
                            </span>
                        </div>

                        <div class="included-filter-label">
                            录制重点类或接口
                            <span @click="filterConfigManager.addParentClass()" class="filterBarMenu filterBarMenuPlus">
                            <i class="el-icon-plus"></i>
                            </span>
                            <span @click="filterConfigManager.setNotesLabel('3')" class="filterBarMenuChat">
                                <i class="el-icon-chat-dot-round"></i>
                            </span>
                        </div>
                        <div class="included-filter-notes" v-show="filterConfigManager.notesLabel['3']">
                            配置类（接口）的完整名称。例如配置 org.springframework.beans.factory.BeanFactory，将录制该类（接口）本身及其子类的调用。
                            这些类即使处于循环或者递归中，也会录制每个函数调用。
                        </div>

                        <div class="filter-input included-filter-body" v-for="(item, index) in filterConfigManager.filterConfig.includedParentClasses" :key="index">
                            <el-input v-model="filterConfigManager.filterConfig.includedParentClasses[index].type" placeHolder="填写类完整名称，如 org.springframework.beans.factory.BeanFactory "> </el-input>
                            <span class="filterBarMenu filterBarMenuMinus" @click="filterConfigManager.deleteParentClass(index)">
                                <i class="el-icon-minus"></i>
                            </span>
                        </div>
                        <div class="included-filter-label">
                            录制模式
                            <span @click="filterConfigManager.setNotesLabel('0')" class="filterBarMenuChat">
                                <i class="el-icon-chat-dot-round"></i>
                            </span>
                        </div>
                        <div class="included-filter-notes" v-show="filterConfigManager.notesLabel['0']">
                            智能模式，针对 SpringBoot 或者 Tomcat，只录制 HTTP/DB 等请求。
                            全录模式，默认录制所有链路。手工模式，默认不录制，需手工开启录制。
                        </div>
                        <div class="included-filter-body">
                            <br/>
                            <el-radio-group v-model="filterConfigManager.filterConfig.recordMode">
                                <el-radio label="smart">智能模式</el-radio>
                                <el-radio label="all">全录模式</el-radio>
                                <el-radio label="manual">手工模式</el-radio>
                            </el-radio-group>
                            <br/>
                        </div>
                        <div class="included-filter-label">
                            默认随着 Debug 启动（测试功能）
                        </div>
                        <div class="included-filter-body">
                            <br/>
                            <el-radio-group v-model="filterConfigManager.filterConfig.startOnDebug">
                                <el-radio :label="true">是</el-radio>
                                <el-radio :label="false">否</el-radio>
                            </el-radio-group>
                            <br/>
                        </div>
                    </div>
                </div>
                </el-scrollbar>
            </div>
            <div class="custom-tab-right">
                <el-tabs tab-position="right" type="card" v-model="rightAside.activeTabName"
                         @tab-click="rightAside.handleTabClick">
                    <el-tab-pane label="录制配置" name="recordFilter">
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-aside>
    </el-container>

</div>
</body>

<script src="../assets/axios-1.6.3/axios.min.js"></script>
<!-- 导入 Mermaid 库 -->
<script src="../assets/mermaid-10.9.0/dist/mermaid.min.js"></script>
<script src="../assets/vue-2.5.22/vue.min.js"></script>
<script src="../assets/element-ui-2.15.14/index.min.js"></script>
<script>
    function NewRecordStatusManager() {
        const obj = {
            executionId: null,
            data: [],
        }
        obj.isRunning = function () {
            return obj.data !== null && obj.data.length > 0;
        }
        obj.setExecutionId = function (executionId) {
            obj.executionId = executionId;
        }
/*
        obj.eventFromConsole = function () {
            if (!obj.isRunning()) {
                return
            }
            axios.post("./eventFromConsole", {
                "cmd": "negotiate",
                "executionId" : obj.data.executionId
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                const errorCode = res.data.errorCode;
                console.log("./eventFromConsole", res.data)
                if (errorCode === '200') {
                    obj.data = res.data.data;
                    if (obj.data.state === "terminal") {
                        obj.data.stateDesc = "程序终止"
                    } else if (obj.data.state === "prepare") {
                        obj.data.stateDesc = "准备中..."
                    } else if (obj.data.state === "prepareStart") {
                        obj.data.stateDesc = "开始录制..."
                    } else if (obj.data.state === "start") {
                        obj.data.stateDesc = "正在录制..."
                    } else if (obj.data.state === "prepareStop") {
                        obj.data.stateDesc = "录制停止"
                    } else if (obj.data.state === "stop") {
                        obj.data.stateDesc = "录制中止"
                    }
                } else {
                    console.log("getRecordStatus failed", res.data)
                }
            }).catch(err => {
                console.log(err)
            })
        }
*/
        obj.getRecordStates = function () {
            axios.post("./recordStates", {
                "cmd": "negotiate",
                "executionId" : obj.executionId
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                const errorCode = res.data.errorCode;
                console.log("./recordStates", res.data)
                if (errorCode === '200') {
                    obj.data = res.data.data;
                    for (let i = 0; i < obj.data.length; i++) {
                        const serviceState = obj.data[i];
                        if (serviceState.processName === null) {
                            serviceState.processName = "Process";
                        }
                        if (serviceState.state === "terminal") {
                            serviceState.stateDesc = "程序终止"
                        } else if (serviceState.state === "prepare") {
                            serviceState.stateDesc = "准备中..."
                        } else if (serviceState.state === "prepareStart") {
                            serviceState.stateDesc = "变更中(将开始录制)"
                        } else if (serviceState.state === "start") {
                            serviceState.stateDesc = "正在录制..."
                        } else if (serviceState.state === "prepareStop") {
                            serviceState.stateDesc = "变更中（将结束录制）"
                        } else if (serviceState.state === "stop") {
                            serviceState.stateDesc = "录制中止"
                        }
                    }
                } else {
                    console.log("getRecordStatus failed", res.data)
                }
            }).catch(err => {
                console.log(err)
            })
        }
        obj.startRecorder = function (index) {
            const serviceState = obj.data[index]
            axios.post("./eventFromConsole", {
                "state": "start",
                "cmd": "change",
                "executionId" : serviceState.executionId
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                console.log("./eventFromConsole", "start", res.data)
            }).catch(err => {
                console.log(err)
            }).finally(()=> {
                //flush the config.
                obj.getRecordStates();
            })
        }
        obj.stopRecorder = function (index) {
            const serviceState = obj.data[index]
            axios.post("./eventFromConsole", {
                "state": "stop",
                "cmd": "change",
                "executionId" : serviceState.executionId
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                console.log("./eventFromConsole", "stop", res.data)
            }).catch(err => {
                console.log(err)
            }).finally(()=> {
                //flush the config.
                obj.getRecordStates();
            })
        }
        return obj;
    }

    function NewFilterConfigManager() {
        const obj = {
            notesLabel:{"0": false, "1": false, "2": false, "3": false},
            filterInputVisible: false,
            filterInputValue: null,
            filterConfig: {
                startOnDebug: false,
                recordMode: "smart",
                enableAutoDetect: true,
                autoDetectedPackages: [],
                includedPackagePrefixes: [],
                includedParentClasses: [],
                entryDisplayConfig: {
                    skipJsCss: false,
                }
            }
        }
        obj.handleFilterClose = function (filter) {
            const dynamicTags = obj.filterConfig.entryDisplayConfig.excludedPathPatterns;
            dynamicTags.splice(dynamicTags.indexOf(filter), 1);
            obj.updateConfig();
        },
        obj.showFilterInput = function () {
            obj.filterInputVisible = true;
            vue.$nextTick(_ => {
                vue.$refs.saveTagInput.$refs.input.focus();
            });
        },
        obj.handleFilterInputConfirm = function () {
            const dynamicTags = obj.filterConfig.entryDisplayConfig.excludedPathPatterns;
            let inputValue = obj.filterInputValue;
            if (inputValue) {
                dynamicTags.push(inputValue);
                obj.updateConfig();
            }
            obj.filterInputVisible = false;
            obj.filterInputValue = '';
        }
        obj.setNotesLabel= function (index) {
            console.log("start", index, obj.notesLabel)
            if (obj.notesLabel[index]) {
                obj.notesLabel[index] = false
            } else {
                obj.notesLabel[index] = true
            }
            console.log("end", index, obj.notesLabel)
        }
        obj.deletePackagePrefix = function (index) {
            obj.filterConfig.includedPackagePrefixes.splice(index, 1)
        }
        obj.addPackagePrefix = function () {
            openLink("metrics://" + "ADD_PACKAGE")
            if (obj.filterConfig.includedPackagePrefixes === null) {
                obj.filterConfig.includedPackagePrefixes = []
            }
            obj.filterConfig.includedPackagePrefixes.push("")
        }
        obj.deleteParentClass = function (index) {
            obj.filterConfig.includedParentClasses.splice(index, 1)
        }
        obj.addParentClass = function () {
            openLink("metrics://" + "ADD_CLASS")
            if (obj.filterConfig.includedParentClasses === null) {
                obj.filterConfig.includedParentClasses = []
            }
            obj.filterConfig.includedParentClasses.push({"type":""})
        }
        obj.getConfig = function (updateAll) {
            axios.post("./getConfig", {}, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                const errorCode = res.data.errorCode;
                console.log("./getConfig", res.data)
                if (errorCode === '200') {
                    if (updateAll) {
                        obj.filterConfig = res.data.data;
                    } else {
                        obj.filterConfig.autoDetectedPackages = res.data.data.autoDetectedPackages;
                    }
                } else {
                    console.log("getConfig failed", res.data)
                }
            }).catch(err => {
                console.log(err)
            })
        }
        obj.resetConfig = function () {
            obj.filterConfig.enableAutoDetect = true
            obj.filterConfig.includedParentClasses = []
            obj.filterConfig.includedPackagePrefixes = []
            obj.updateConfig()
        }
        obj.updateConfig = function () {
            axios.post("./updateConfig", obj.filterConfig, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                const errorCode = res.data.errorCode;
                console.log("./updateConfig", res.data)
                if (errorCode !== '200') {
                    vue.$message.error("updateConfig failed")
                    console.log("updateConfig failed", res.data)
                } else {
                    vue.$message({
                        message: 'Update Success',
                        type: 'success'
                    });
                }
            }).catch(err => {
                console.log(err)
            }).finally(()=> {
                //flush the config.
                obj.getConfig(true);
            })
        }
        return obj;
    }

</script>

<script>
    var vue = new Vue({
        el: '#app',
        data: function () {
            return {
                project: NewProject(),
/*
                docTree: NewLocatorTree(),
*/
                locatorTree: NewLocatorTree(),
                leftAside: NewAside("left", ""),
                rightAside: NewAside("right", "recordFilter"),
                recordStatusManager: NewRecordStatusManager(),
                filterConfigManager: NewFilterConfigManager()
            }
        },
        created() {
            console.log("this", this)
            this.project.doInitProjectInfo();
            this.recordStatusManager.setExecutionId(this.project.executionId)
            this.locatorTree.$refs = this.$refs;
            this.locatorTree.$refsName = "locatorTree";
/*
            this.docTree.initDocTree();
*/
            document.addEventListener("mouseup", this.leftAside.mouseUp);
            document.addEventListener("mouseup", this.rightAside.mouseUp);
        },
        destroyed() {
            document.removeEventListener("mouseup", this.leftAside.mouseUp);
            document.removeEventListener("mouseup", this.rightAside.mouseUp);
        },
        methods: {
            filterNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            }
        }
    })
    fixScrollClass();
    vue.locatorTree.doGetTreeData(true);
    vue.filterConfigManager.getConfig(true);
    vue.recordStatusManager.getRecordStates();
    /*vue.$watch("locator.filterText", (newValue, oldValue) => {
        console.log("locator.filterText changed from ", oldValue, " to ", newValue)
        vue.locatorTree.setFilterText(newValue)
    })*/
</script>

<script>
    function refreshData() {
        vue.locatorTree.doGetTreeData(false, function () {
            setTimeout(function () {
                vue.locatorTree.recoverFilterText();
            }, 100)
        });
        vue.filterConfigManager.getConfig(false);
        vue.recordStatusManager.getRecordStates();
    }

    function clearDebugStatus() {
        vue.project.debug = "disabled";
        console.log("clearDebugStatus", vue.project)
    }
</script>

<script>
    // 按键状态标记
    let isKeyPressed = false;
    // 注册按键按下事件监听器
    window.addEventListener('keydown', (event) => {
        //console.log("key down", event)
        // 检查按下的按键是否为你想要的按键（这里假设是 Ctrl 键）
        if (event.ctrlKey) {
            isKeyPressed = true;
        }
    });
    // 注册按键释放事件监听器
    window.addEventListener('keyup', (event) => {
        //console.log("key up", event)
        isKeyPressed = false;
    });
    // 添加键盘事件监听器
    //document.addEventListener('keydown', forwardOrBackward);
</script>


<style>
    body {
        margin: 0px;
    }
    .el-scrollbar {
        height: 100vh;
        width: 100%;
    }
    .el-scrollbar__bar.is-vertical {
        width: 10px;
    }
    .el-scrollbar__bar.is-horizontal {
        height: 10px;
    }
    /*.el-scrollbar__wrap {
        overflow-x: auto;
        height: calc(100% + 20px);
    }*/
    .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
        /*white-space: nowrap;*/
    }

    .el-divider--horizontal {
        margin: 5px 0;
    }

</style>

<style>
    .el-link span {
        margin-top: -3px;
    }
    .record-status-tab {
        margin-top: 5px;
    }
    .record-status-tab .el-tag {
        border-style: none;
    }
    .record-status-tab .el-button {
        height: 34px;
        padding: 10px 20px;
    }
    .locator-tree-filter {
        height: 100%;
    }
    .locator-tree-filter .el-tag {
        line-height: 24px;
        height: 26px;
    }
    .locator-tree-filter .el-button--small {
        padding: 6px 10px;
    }
    .doc-list-item {
        margin-top: 5px;
    }
    .el-link.el-link--default {
        font-style: oblique;
    }

</style>
<style>
    .controlBarMenu:hover {
        cursor: pointer;
    }
    .controlBarMenu {
        display: inline-block;
    }
    .controlBarMenuMinus {
        height: 32px;
        line-height: 32px;
        padding: 4px;
    }
    .controlBarMenuPlus {
        height: 24px;
        line-height: 22px;
        padding: 4px;
        border-width: 1px;
    }
</style>

<style>
    .filter-button-wrap {
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 95%;
        margin-left: 5px;
    }

    .filter-button {
        text-align: center;
        width: 49%;
    }

    .filter-button > .el-button {
        width: 100%;
    }

    .el-input.is-disabled .el-input__inner {
        cursor: auto;
    }

    .entry-list .el-input__inner {
        height: 28px;
    }

    .included-filter-body .el-tag {
        font-size: 14px;
        white-space: normal;
        word-break: break-all;
        height: 100%;
    }



    .var-exp-op {
        width: 220px;
        margin: 0px 2px;
    }

    .var-exp-name {

    }

    .var-op-value {

    }

    .filter-detail-list {
        width: 95%;
        margin-left: 5px;
    }

    .included-filter-label {
        height: 32px;
        padding: 0 10px;
        line-height: 30px;
        font-size: 12px;
        /* box-sizing: border-box; */
        /* white-space: nowrap; */
        margin-top: 10px;
    }

    .included-filter-body .el-input__inner {
        text-align: left;
        padding: 0 0px;
    }

    .included-filter-body {
        margin-top: 4px;
        display: flex;
    }

    .included-filter-notes {
        font-style: italic;
        font-size: small;
    }

    /* start filterBarMenu */
    .filterBarMenu:hover {
        cursor: pointer;
    }

    .filterBarMenu {
        display: inline-block;
    }

    .filterBarMenuMinus {
        height: 32px;
        line-height: 32px;
        padding: 4px;
    }
    .filterBarMenuPlus {
        height: 24px;
        line-height: 22px;
        padding: 4px;
        border-width: 1px;
    }
    .filterBarMenuChat {
        /*
        position: absolute;
        */
        /*
        right: 16px;
        */
        cursor: pointer;
    }
</style>


<style>
    .el-tabs {
        padding: 0 0px;
        margin-top: 24px;
    }

    .el-tabs__nav-wrap {
        margin-left: 0px;
    }

    .el-tabs__header {
        margin: 0 0 24px;
    }

    .el-tabs__content {
        padding: 0px;
    }

    .el-tabs__item.is-left, .el-tabs__item.is-right {
        width: 25px;
        height: 100px;
        padding: 0;
        margin: 0;
        writing-mode: vertical-lr; /* 将文字垂直显示，从左到右 */
        line-height: 25px;
    }

    .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
        text-align: center; /* 文字居中 */
    }

    .el-tabs--right.el-tabs--card .el-tabs__item.is-right {
        text-align: center; /* 文字居中 */
    }
    .el-tabs__item.is-active {
        opacity: 1;
    }
    .el-tabs__header {
        margin: 0;
    }
    .el-tabs--right .el-tabs__header.is-right {
        margin-left: 0px;
    }
</style>

<style>
    .el-aside, .el-main, .el-header {
        height: 100%;
    }

    .el-header {
        padding: 0 32px;
        display: flex;
        /*
        justify-content: center; !* Center content horizontally *!
        */
        align-items: center; /* Center content vertically */
        height: 30px;
        margin-bottom: 5px;
    }

    .el-main {
        padding: 0px;
    }


    .el-breadcrumb {
        /*
        margin-top: 15px;
        */
        margin-left: 0px;
        font-size: 16px;
    }



    .el-table {
        margin-top: 24px;
    }



    .el-header, .el-tab-pane {
        white-space: pre-line;
    }

    .el-menu-item > a, .el-menu-item > * > a, .el-menu-item > * > * > a, .el-menu-item > * > * > * > a {
        text-decoration: none; /* 去掉下划线 */
    }

    .el-button > a, .el-button > * > a, .el-button > * > * > a, .el-button > * > * > * > a {
        text-decoration: none;
    }


    .el-main {
        display: flex;
        flex-direction: row;
        padding-left: 0;
        padding-right: 0;
    }

    .codeCanvasDiv {
        height: 100%;
        width: 100%;
        overflow: auto;
    }

    .codeCanvasPre {
        width: 100%;
        overflow: auto;
    }

    .flowCanvasDiv {
        height: 100%;
        width: 550px;
        overflow: auto;
    }

    .left-aside {
        display: flex;
        margin-right: 5px;
    }

    .left-tab-content {
        #width: calc(100% - 25px);
        #margin-top: 20px;
    }

    .right-aside {
        display: flex;
        margin-left: 5px;
        justify-content: flex-end;
    }

    .right-tab-content {
        #width: calc(100% - 25px);
        margin-top: 20px;
        overflow-wrap: break-word;
        margin-left: 6px;
        margin-bottom: 10px;
    }

    .usageListSelectDiv {
        width: 270px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .focus-on-token {
        text-decoration: underline;
    }


    .x-handle {
        width: 3px;
        cursor: ew-resize;
        z-index: 10;
    }



    .custom-tab-left {
        position: fixed;
        width: 25px;
        height: 100px;
    }

    .custom-tab-right {
        position: fixed;
        width: 25px;
        height: 100px;
    }



    .left-container {
        position: relative;
        margin-left: 25px;
        width: 100%;
    }

    .right-container {
        position: relative;
        width: calc(100% - 25px);
        margin-right: 25px;
        height: 100vh;
    }

    .hide-icon-left {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: right;
        z-index: 2;
    }




    .zdiv {
        margin: 0;
        border: 1px solid rgb(187, 186, 186);
        background: rgb(255, 255, 255);
        z-index: 1000;
        position: absolute;
        list-style-type: none;
        padding: 5px;
        border-radius: 7px;
        font-size: 12px;
    }

    .zdiv li {
        margin: 0;
        padding: 5px;
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .zdiv li:hover {
        background: #e1e1e1;
        cursor: pointer;
    }

    .usageTraceDetail {
        margin-bottom: 2px;
    }

    .usageTraceDetail:hover {
        cursor: pointer;
    }

    .code-content-widget-detail-parent {
        max-width: 600px;
    }

    .code-content-widget-detail-header {
        font-size: larger;
    }

    .code-content-widget-detail-header::after {
        clear: both;
        content: '';
        display: table;
    }

    .code-content-widget-detail-header div:first-child {
        float: left;
    }

    .code-content-widget-detail-header div:nth-child(2) {
        float: right;
    }

    .code-content-widget-detail-header div i {
        margin-left: 5px;
        font-size: larger;
    }

    .code-content-widget-detail-header div i:hover {
        cursor: pointer;
    }

    .code-content-widget-detail-desc {
        /*padding-top: 10px;*/
        padding-bottom: 32px
    }

    .code-content-widget-button-parent {
        height: 18px;
    }

    .code-content-widget-button {
        height: 18px;
        opacity: 0.7;
        margin-top: 18px;
    }

    .code-content-widget-button:hover {
        cursor: pointer;
    }

    .editor-extra-class {
        margin-right: 100px;
    }

</style>

<style>
    .el-tree {
        margin-bottom: 20px;
    }
    .el-tree-node {
        position: relative;
        padding-left: 1px;
    }

    .el-tree-node__content {
        margin-top: 1px;
    }

    .el-tree-node__children {
        padding-left: 20px;
    }

    .el-tree-node::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        left: -3px;
        top: -26px;
        border-width: 1px;
        border-left: 1px dashed #52627c;
    }

    .el-tree-node:last-child::before {
        height: 38px;
    }

    .el-tree-node::after {
        content: '';
        width: 14px;
        height: 10px;
        position: absolute;
        left: -3px;
        top: 12px;
        border-width: 1px;
        border-top: 1px dashed #52627c;
    }

    /*.el-tree-node > .el-tree-node::after {
        border-top: none;
    }

    .el-tree-node > .el-tree-node::before {
        border-left: none;
    }*/


    .el-tree-node__expand-icon {
        font-size: 18px;
    }

    .el-tree-node__expand-icon > .is-leaf {
        #display: none;
    }
</style>

<script>
    console.log("xcodemap-idea-plugin-ready");
</script>
</html>