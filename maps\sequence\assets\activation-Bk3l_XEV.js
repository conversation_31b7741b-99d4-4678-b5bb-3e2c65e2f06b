import{d as o,r as i,a0 as s,a1 as c,a2 as n}from"./style-vtS1bOjc.js";const _=o("activation",()=>{const e=i({step:"idle",deviceCode:null,activateCode:null,expiredTimestamp:null,error:null});return{state:e,reset:()=>{e.value={step:"idle",deviceCode:null,activateCode:null,expiredTimestamp:null,error:null}},generateDeviceCode:async()=>{try{e.value.step="generating_device_code",e.value.error=null;const t=await s();if(t.success&&t.data)return e.value.deviceCode=t.data,e.value.step="device_code_generated",t.data;throw e.value.error=t.error||"Failed to generate device code",e.value.step="error",new Error(t.error||"Failed to generate device code")}catch(t){throw e.value.error=t instanceof Error?t.message:"Failed to generate device code",e.value.step="error",t}},generateActivateCode:async(t,r)=>{try{e.value.step="generating_activate_code",e.value.error=null;const a=await c(t,r);if(a.success&&a.data)return e.value.activateCode=a.data,e.value.step="activate_code_generated",a.data;throw e.value.error=a.error||"Failed to generate activate code",e.value.step="error",new Error(a.error||"Failed to generate activate code")}catch(a){throw e.value.error=a instanceof Error?a.message:"Failed to generate activate code",e.value.step="error",a}},finishActivationProcess:async t=>{try{e.value.step="finishing_activation",e.value.error=null;const r=await n(t);if(r.success&&r.data!==null&&r.data!==void 0)return e.value.step="activated",e.value.expiredTimestamp=r.data,console.log("Activation successful, expired timestamp:",r.data),r.data;throw e.value.error=r.error||"Failed to finish activation",e.value.step="error",new Error(r.error||"Failed to finish activation")}catch(r){throw e.value.error=r instanceof Error?r.message:"Failed to finish activation",e.value.step="error",r}},isActivated:()=>e.value.step==="activated",hasError:()=>e.value.step==="error",getError:()=>e.value.error,getCurrentStep:()=>e.value.step,getDeviceCode:()=>e.value.deviceCode,getActivateCode:()=>e.value.activateCode,getExpiredTimestamp:()=>e.value.expiredTimestamp,setDeviceCode:t=>{e.value.deviceCode=t},setStep:t=>{e.value.step=t}}});export{_ as u};
