function formatText(text) {
    // 替换换行符和空格
    return text.replace(/\n/g, '\\n');
}

// Initialize theme
const tempProject = GetTempProject();
const theme = tempProject.theme;
const inPopup = tempProject.inPopup;
if (theme === "light") {
    document.documentElement.setAttribute("data-theme", inPopup === "true" ? "light-popup" : "light");
} else {
    document.documentElement.setAttribute("data-theme", inPopup === "true" ? "dark-popup" : "dark"); 
}

function NewAside(position, defaultActiveTabName) {
    const obj = {
        position: position,
        activeTabName: defaultActiveTabName,
        lastActiveTabName: defaultActiveTabName,
        lastX: "",
        defaultWidth: "350",
        asideWidth: "350",
        minAsideWidth: "270",
        maxAsideWidth: "600"
    }
    obj.mouseDown = function (event) {
        document.addEventListener("mousemove", obj.mouseMove);
        obj.lastX = event.screenX;
    },
    obj.mouseMove = function (event) {
        if (obj.position === 'left') {
            obj.asideWidth -= (obj.lastX - event.screenX);
        } else {
            obj.asideWidth -= (event.screenX - obj.lastX);
        }
        if (obj.asideWidth < obj.minAsideWidth) {
            obj.asideWidth = obj.minAsideWidth;
        }
        if (obj.asideWidth > obj.maxAsideWidth) {
            obj.asideWidth = obj.maxAsideWidth;
        }
        obj.lastX = event.screenX;
    },
    obj.mouseUp = function () {
        obj.lastX = "";
        document.removeEventListener("mousemove", obj.mouseMove);
    },
    obj.changeTab = function (tabName) {
        obj.activeTabName = tabName;
        obj.handleTabClick();
    },
    obj.handleCloseAside = function () {
        obj.activeTabName = '';
        obj.asideWidth = 25;
    },
    obj.handleTabClick = function () {
        if (obj.lastActiveTabName === obj.activeTabName) {
            obj.handleCloseAside();
        } else {
            obj.asideWidth = obj.defaultWidth;
        }
        obj.lastActiveTabName = obj.activeTabName;
    }
    return obj;
}

function GetTempProject() {
    const tempProject = NewProject();
    tempProject.doInitProjectInfo();
    return tempProject;
}

function NewProject() {
    const obj = {
        processId: null,
        threadId: null,
        funcName: null,
        rootFunctionId: null,
        rootFunc: null,
        debug: null,
        currCallId: null,
        currObjectId: null,
        searchType: null,
        searchName: null,
        searchId: null,
        searchScope: null,
        executionId: null,
        theme: null,
        revertWheelX: null,
        osrEnable: null,
        platform: null,
        navStateType: null,
        inPopup: false,
    }
    obj.doInitProjectInfo = function () {
        obj.doInitProjectInfoFromSearchParas(window.location.search);
    }
    obj.doInitProjectInfoFromSearchParas = function (searchParasStr) {
        const urlParams = new URLSearchParams(searchParasStr);
        if (urlParams.has("functionName")) {
            obj.funcName = urlParams.get("functionName")
        }
        if (urlParams.has("graphNodeKey")) {
            const graphNodeKey = urlParams.get("graphNodeKey");
            const items = graphNodeKey.split("/");
            obj.processId = items[0];
            obj.threadId = items[1];
            if (items.length >= 3) {
                obj.rootFunctionId = items[2];
                obj.rootFunc = { type:"functionTree", name: funcName, id: obj.rootFunctionId}
            }
        }
        if (urlParams.has("processId")) {
            obj.processId = urlParams.get("processId")
        }
        if (urlParams.has("threadId")) {
            obj.threadId = urlParams.get("threadId")
        }
        if (urlParams.has("debug")) {
            obj.debug = urlParams.get("debug");
        }
        if (urlParams.has("currCallId")) {
            obj.currCallId = urlParams.get("currCallId");
            if (obj.currCallId.length === 0) {
                obj.currCallId = null
            }
        }
        if (urlParams.has("currObjectId")) {
            obj.currObjectId = urlParams.get("currObjectId");
            if (obj.currObjectId.length === 0) {
                obj.currObjectId = null
            }
        }
        if (urlParams.has("searchType")) {
            obj.searchType = urlParams.get("searchType");
        }
        if (urlParams.has("searchName")) {
            obj.searchName = urlParams.get("searchName");
        }
        if (urlParams.has("searchId")) {
            obj.searchId = urlParams.get("searchId");
        }
        if (urlParams.has("searchScope")) {
            obj.searchScope = urlParams.get("searchScope");
        }
        if (urlParams.has("executionId")) {
            obj.executionId = urlParams.get("executionId");
        }
        if (urlParams.has("theme")) {
            obj.theme = urlParams.get("theme")
        }
        if (urlParams.has("osrEnable")) {
            obj.osrEnable = urlParams.get("osrEnable")
        }
        if (urlParams.has("platform")) {
            obj.platform = urlParams.get("platform")
        }
        if (urlParams.has("revertWheelX")) {
            obj.revertWheelX = urlParams.get("revertWheelX")
        }
        if (urlParams.has("navStateType")) {
            obj.navStateType = urlParams.get("navStateType")
        }
        if (urlParams.has("inPopup")) {
            obj.inPopup = urlParams.get("inPopup")
        }
    }
    return obj;
}

function NewLocatorTree() {
    const obj = {
        currentFuncKey: "",
        unFoldAll: false,
        treeData: {
            "rootNodes": [],
            "expandedKeys": []
        },
        defaultExpandedKeys: [],
        filterText: "",
        defaultProps: {
            children: 'children',
            label: 'label',
            nodeKey: 'nodeKey',
            isLeaf: 'isLeaf'
        }
    }
    obj.hasData = function () {
        return obj.treeData !== null && obj.treeData.length > 0;
    }
    obj.getExpandedKeys = function () {
        const expandedKeys = []
        const locatorRef = obj.$refs[obj.$refsName];
        if (locatorRef === undefined || locatorRef == null) {
            return expandedKeys;
        }
        const nodesMap = locatorRef.store.nodesMap;
        for (const key in nodesMap) {
            if (nodesMap.hasOwnProperty(key)) {
                const node = nodesMap[key];
                if (node.expanded) {
                    expandedKeys.push(key);
                }
            }
        }
        return expandedKeys;
    }
    obj.recoverFilterText = function () {
        console.log("recoverFilterText:", obj.filterText)
        obj.setFilterText(obj.filterText)
    }
    obj.setFilterText = function(value) {
        obj.filterText = value;
        obj.handleFilterTextChange(value);
    },
    obj.handleFilterTextChange = function(value) {
        if (!value) {
            // 如果搜索框为空，折叠所有节点
            obj.collapseAllNodes();
        } else {
            // 如果有搜索内容，只展开匹配的节点
            obj.expandMatchingNodes(value);
        }
    },
    obj.expandAllNodes = function() {
        const tree = obj.$refs[obj.$refsName];
        if (tree) {
            // 展开所有节点
            obj.expandNodesRecursively(obj.treeData.rootNodes);
        }
    },
    obj.expandMatchingNodes = function(searchValue) {
        const tree = obj.$refs[obj.$refsName];
        if (tree) {
            // 先折叠所有节点
            obj.collapseAllNodes();
            // 然后展开匹配的节点
            obj.expandMatchingNodesRecursively(obj.treeData.rootNodes, searchValue);
        }
    },
    obj.expandNodesRecursively = function(nodes) {
        if (!nodes) return;
        nodes.forEach(node => {
            if (node.children && node.children.length > 0) {
                // 展开当前节点
                obj.$refs[obj.$refsName].store.nodesMap[node.nodeKey].expanded = true;
                // 递归展开子节点
                obj.expandNodesRecursively(node.children);
            }
        });
    },
    obj.collapseAllNodes = function() {
        const tree = obj.$refs[obj.$refsName];
        if (tree && tree.store && tree.store.nodesMap) {
            Object.values(tree.store.nodesMap).forEach(node => {
                if (node.childNodes && node.childNodes.length > 0) {
                    node.expanded = false;
                }
                // 清除高亮状态
                if (node.data) {
                    if (obj.vue && obj.vue.$set) {
                        obj.vue.$set(node.data, 'isHighlighted', false);
                    }
                }
            });
        }
        // 同时清除根节点的高亮状态
        obj.clearHighlightFromRootNodes(obj.treeData.rootNodes);
    },
    obj.clearHighlightFromRootNodes = function(nodes) {
        if (!nodes) return;
        nodes.forEach(node => {
            if (obj.vue && obj.vue.$set) {
                obj.vue.$set(node, 'isHighlighted', false);
            }
            if (node.children) {
                obj.clearHighlightFromRootNodes(node.children);
            }
        });
    },
    obj.expandMatchingNodesRecursively = function(nodes, searchValue) {
        if (!nodes) return;
        nodes.forEach(node => {
            const isMatch = obj.isNodeMatching(node, searchValue);
            const hasMatchingChild = obj.hasMatchingChild(node, searchValue);
            
            if (isMatch) {
                // 如果当前节点匹配，高亮当前节点，展开父节点
                obj.highlightNode(node);
                obj.expandParentNodes(node);
            } else if (hasMatchingChild) {
                // 如果当前节点有匹配的子节点，展开当前节点
                if (node.children && node.children.length > 0) {
                    obj.$refs[obj.$refsName].store.nodesMap[node.nodeKey].expanded = true;
                }
            }
            
            // 无论是否匹配，都递归处理子节点
            if (node.children) {
                obj.expandMatchingNodesRecursively(node.children, searchValue);
            }
        });
    },
    obj.isNodeMatching = function(node, searchValue) {
        if (!searchValue) return false;
        if (node.alwaysShow) return false; // 跳过 alwaysShow 的节点
        return node.label && node.label.toLowerCase().indexOf(searchValue.toLowerCase()) !== -1;
    },
    obj.hasMatchingChild = function(node, searchValue) {
        if (!node.children) return false;
        return node.children.some(child => {
            return obj.isNodeMatching(child, searchValue) || obj.hasMatchingChild(child, searchValue);
        });
    },
    obj.highlightNode = function(node) {
        // 高亮命中的节点
        if (node.nodeKey && obj.vue && obj.vue.$set) {
            obj.vue.$set(node, 'isHighlighted', true);
        }
    },
    obj.expandParentNodes = function(node) {
        // 展开当前节点的所有父节点
        const tree = obj.$refs[obj.$refsName];
        if (tree && tree.store && tree.store.nodesMap) {
            let currentNode = tree.store.nodesMap[node.nodeKey];
            while (currentNode && currentNode.parent) {
                currentNode.parent.expanded = true;
                currentNode = currentNode.parent;
            }
        }
    },
    obj.renderContent = function(h, { node, data, store }) {
        const isHighlighted = data.isHighlighted;
        return h('span', {
            class: {
                'el-tree-node__label': true,
                'highlighted': isHighlighted
            }
        }, node.label);
    },
    obj.unfoldAllNodes = function () {
        const nodesMap = obj.$refs[obj.$refsName].store.nodesMap;
        for (const key in nodesMap) {
            if (nodesMap.hasOwnProperty(key)) {
                const node = nodesMap[key];
                node.expanded = true;
            }
        }
        obj.unFoldAll = true
    },
    obj.foldAllNodes = function () {
        const nodesMap = obj.$refs[obj.$refsName].store.nodesMap;
        for (const key in nodesMap) {
            if (nodesMap.hasOwnProperty(key)) {
                const node = nodesMap[key];
                node.expanded = false;
            }
        }
        obj.unFoldAll = false
    },
    obj.doFilterText = function (value) {
        obj.setFilterText(value);
    },
    obj.doGetTreeData = function (first, callback) {
        // 获取目录结构
        axios.post("./tree", {
            processId: vue.project.processId,
            executionId: vue.project.executionId,
            "first": first,
            "filterText": obj.filterText
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            const errorCode = res.data.errorCode;
            console.log("../tree", res.data)
            if (errorCode === '200') {
                if (JSON.stringify(obj.treeData) !== JSON.stringify(res.data.data.rootNodes)) {
                    const previousExpandedKeys = obj.getExpandedKeys()
                    const serverExpandKeys = res.data.data.expandedKeys;
                    if (serverExpandKeys !== null && serverExpandKeys.length > 0) {
                        obj.defaultExpandedKeys = serverExpandKeys;
                    } else {
                        obj.defaultExpandedKeys = previousExpandedKeys;
                    }
                    obj.treeData = res.data.data.rootNodes;
                    if (callback !== undefined) {
                        callback();
                    }
                }
            } else {
                console.log("doGetLocatorTree failed", res.data)
            }
        }).catch(err => {
            console.log(err)
        })
    },
    /*obj.initDocTree = function () {
        obj.defaultExpandedKeys = ["/doc/index"]
        obj.treeData = [{
            label: '使用手册',
            leaf: false,
            nodeKey: "/doc/index",
            children: [
                {
                    label: '有问题，加微信交流',
                    labelKey: "url",
                    labelValue: "./contact.html?functionName=Doc-Contact",
                    leaf: true,
                    nodeKey: "/doc/2",
                },
                {
                    label: '【重要】IDE 卡顿解决办法',
                    labelKey: "url",
                    labelValue: "./imp.html?functionName=Prompt",
                    leaf: true,
                    nodeKey: "/doc/3",
                },
                {
                    label: '快速开始，走读代码【推荐JDK 17、21， 其次 JDK 8、11，其它非LTS版本可能有Bug】',
                    labelKey: "url",
                    labelValue: "./help.html?functionName=Doc-QuickStart",
                    leaf: true,
                    nodeKey: "/doc/0",
                },
                {
                    label: '常见问题，最佳实践',
                    labelKey: "url",
                    labelValue: "./faq.html?functionName=Doc-FAQ",
                    leaf: true,
                    nodeKey: "/doc/1",
                }
            ]
        }]
    }*/
    obj.clickTreeNode = function (data) {
        if (data.leaf === true) {
            if (data["labelKey"] === "url") {
                const url = data["labelValue"];
                openLink(url)
            } else {
                const nodeKey = data.nodeKey;
                let link = "./detail.html?graphNodeKey=" + nodeKey;
                const message = data.labelValue;
                if (message !== undefined && message !== null) {
                    link += "&functionName=" + encodeURIComponent(message);
                }
                openLink(link)
            }
            console.log("Click Tree Node", data)
        }
    },
    obj.selectTreeNode = function (fileNodeKey) {
        const pathArray = fileNodeKey.split(/\/+/).filter(Boolean);
        var realPath = '';
        for (let i = 0; i < pathArray.length; i++) {
            if (realPath === '') {
                realPath = pathArray[i];
            } else {
                realPath += '/' + pathArray[i];
            }
            if (obj.$refs.$refsName.store.nodesMap[realPath] === undefined) {
                continue;
            }
            obj.$refs.$refsName.store.nodesMap[realPath].expanded = true;
        }
        obj.$refs.$refsName.setCurrentKey(realPath);
        // 展开后，延迟一定时间才能找到要定位文件的id。
        setTimeout(function () {
            const node = document.getElementById(realPath); // 通过Id获取到对应的dom元素
            if (node !== null) {
                node.scrollIntoView({block: 'start'});
            }
        }, 300);
    }
    return obj;
}

function writeToClipboard(text) {
    navigator.clipboard.writeText(text) // 将文本写入剪切板
        .then(() => {
            vue.$message({
                message: '已复制到剪切板',
                type: 'success'
            });
        })
        .catch(err => {
            if (window.open !== undefined) {
                window.open("clipboard://" + text)
                vue.$message({
                    message: '已复制到剪切板',
                    type: 'success'
                });

            } else {
                vue.$message({
                    message: '复制到剪切板失败',
                    type: 'error'
                });
            }
            console.log("write clipboard error", err)
        });
}

function openLink(link) {
    let absoluteUrl = link;
    if (!absoluteUrl.includes("://")) {
        absoluteUrl = new URL(link, window.location.href).href
        if (!absoluteUrl.includes("theme=")) {
            if (!absoluteUrl.includes("?")) {
                absoluteUrl += "?theme=" + theme;
            } else {
                if (absoluteUrl.endsWith("?"))  {
                    absoluteUrl += "theme=" + theme;
                } else {
                    absoluteUrl += "&theme=" + theme;
                }
            }
        }
    }
    
    // 检查URL参数中是否包含 onlyHttp=true
    const urlParams = new URLSearchParams(window.location.search);
    const onlyHttp = urlParams.get("onlyHttp");
    
    // 如果 onlyHttp=true 且 absoluteUrl 不是以 http 开头，只打印日志不跳转
    if (onlyHttp === "true" && !absoluteUrl.startsWith("http")) {
        console.log("Only HTTP links allowed, skipping non-HTTP link:", absoluteUrl);
        return;
    }
    
    if (window.openTargetLink !== undefined) {
        console.log("Will open link by openTargetLink", absoluteUrl)
        window.openTargetLink(absoluteUrl)
    } else {
        console.log("Will open link by open", absoluteUrl)
        window.open(absoluteUrl)
    }
}

function fixScrollClass() {
    document.querySelectorAll(".el-scrollbar__wrap").forEach((childElement) => {
        if (!childElement.classList.contains("el-scrollbar__wrap--hidden-default")) {
            childElement.classList.add("el-scrollbar__wrap--hidden-default")
        }
        childElement.style.removeProperty('margin-bottom');
        childElement.style.removeProperty('margin-right');
    })
}