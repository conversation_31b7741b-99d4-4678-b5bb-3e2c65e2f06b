import{i as L,u as R,r as u,c as T,x as I,z as E,j as r,o as a,l as e,n as f,q as i,t as v,m as p,D as $,E as j,_ as M,R as F,S as q}from"./style-CvhRCPHz.js";import{u as z}from"./activation-9JjzkOvO.js";const B={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},V={class:"max-w-3xl mx-auto"},X={class:"text-center mb-8"},N={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},O={class:"ml-11"},W={key:0,class:"space-y-4"},G={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600"},H={class:"flex items-center justify-between"},J={class:"text-sm font-mono break-all flex-1 mr-3 text-gray-900 dark:text-gray-100 font-medium"},K={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},Q={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},Y={class:"ml-11"},Z={key:0,class:"space-y-4"},ee={key:0,class:"text-center"},te={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},oe={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},se={class:"ml-11"},re={key:0,class:"space-y-4"},ae={class:"space-y-4"},ie=["disabled"],ne=["disabled"],de={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},le={class:"space-y-4"},ce={key:0,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},ue={key:1,class:"bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg"},ge={key:0,class:"text-sm mt-2"},ve={key:2,class:"bg-blue-100 dark:bg-blue-900 border border-blue-400 text-blue-700 dark:text-blue-200 px-4 py-3 rounded-lg"},pe=L({__name:"Activating",setup(xe){const m=R(),o=z(),c=u(""),d=u(""),g=u(!1),l=u(null),b=u(!1),w=u(1),k=T(()=>o.getCurrentStep()==="idle"||o.getCurrentStep()==="generating_device_code"?1:o.getCurrentStep()==="device_code_generated"&&!g.value?2:g.value?3:1),x=()=>{w.value=k.value};I(()=>{m.initTheme(),C(),S(),x()}),E(()=>{});const C=()=>{const s=new URLSearchParams(window.location.search),t=s.get("deviceCode"),n=s.get("utype");t&&(l.value=t,console.log("Device code found in URL parameters:",t)),n&&console.log("Utype found in URL parameters:",n)},S=async()=>{try{if(!l.value){o.state.error="没有找到设备码",o.state.step="error";return}if(l.value){o.setDeviceCode(l.value),o.setStep("device_code_generated");const t=new URLSearchParams(window.location.search).get("utype");let n=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(l.value)}`;t&&(n+=`&utype=${encodeURIComponent(t)}`),d.value=n,g.value=!0,x(),y()}else{await o.generateDeviceCode();const s=o.getDeviceCode();if(s){const n=new URLSearchParams(window.location.search).get("utype");let h=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(s)}`;n&&(h+=`&utype=${encodeURIComponent(n)}`),d.value=h,g.value=!0,x(),y()}}}catch(s){console.error("Failed to start activation process:",s)}},y=()=>{},U=async()=>{try{await navigator.clipboard.writeText(d.value),b.value=!0,setTimeout(()=>{b.value=!1},2e3),console.log("Activation link copied to clipboard")}catch(s){console.error("Failed to copy activation link:",s);const t=`clipboard://${d.value}`;console.log("Using clipboard protocol:",t),window.open(t,"_blank")}},A=()=>{d.value&&window.open(d.value,"_blank")},P=async()=>{if(c.value.trim())try{if(l.value){const s=`clipboard://xcm_fin_ack://${c.value.trim()}`;console.log("Using clipboard protocol:",s),window.open(s,"_blank"),o.setStep("finishing_activation")}else{const s=await o.finishActivationProcess(c.value.trim());if(console.log("Activation completed successfully, expired timestamp:",s),s){const t=new Date(s);console.log("License expires at:",t.toLocaleString())}}}catch(s){console.error("Failed to finish activation:",s)}},D=()=>{switch(o.getCurrentStep()){case"idle":return"准备开始激活流程...";case"generating_device_code":return"正在生成设备码...";case"device_code_generated":return"新用户可免费激活一个月";case"finishing_activation":return"正在完成激活...";case"activated":return"激活成功！";case"error":return`激活失败: ${o.getError()}`;default:return"未知状态"}};return setInterval(()=>{x()},1e3),(s,t)=>(a(),r("div",{class:f(["h-screen w-full flex flex-col overflow-x-hidden",(i(m).theme==="dark","bg-[var(--bg-color)]")])},[e("div",B,[e("div",V,[e("div",X,[e("h1",{class:f(["text-3xl font-bold mb-4",i(m).theme==="dark"?"text-white":"text-gray-900"])}," 请激活 XCodeMap ",2),e("p",{class:f(["text-lg",i(m).theme==="dark"?"text-gray-300":"text-gray-600"])},v(D()),3)]),e("div",N,[t[3]||(t[3]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 1 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第一步 生成激活链接 ")],-1)),e("div",O,[d.value?(a(),r("div",W,[e("div",G,[e("div",H,[e("code",J,v(d.value),1)])]),t[1]||(t[1]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," ✓ 激活链接已生成完成 ",-1)),t[2]||(t[2]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," 此链接为本机器所专用，请勿混淆 ",-1))])):(a(),r("div",K," 正在生成您的专属激活链接... "))])]),e("div",Q,[t[7]||(t[7]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 2 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第二步 访问链接生成激活码 ")],-1)),e("div",Y,[d.value?(a(),r("div",Z,[t[5]||(t[5]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," 请点击下方按钮访问链接，获取您的专属激活码 ",-1)),e("div",{class:"flex space-x-4"},[e("button",{onClick:A,class:"px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 访问链接 "),e("button",{onClick:U,class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 复制链接 ")]),b.value?(a(),r("div",ee,t[4]||(t[4]=[e("p",{class:"text-sm text-green-600 dark:text-green-400 font-medium"}," ✓ 链接已复制到剪贴板 ",-1)]))):p("",!0),t[6]||(t[6]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 如果无法直接访问，请复制链接到能联网访问的浏览器打开 ",-1))])):(a(),r("div",te," 等待激活链接生成... "))])]),e("div",oe,[t[9]||(t[9]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 3 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第三步 输入激活码，完成激活 ")],-1)),e("div",se,[g.value?(a(),r("div",re,[t[8]||(t[8]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," 请输入从XCodeMap官网 https://xcodemap.tech 获得的激活码 ",-1)),e("div",ae,[$(e("textarea",{"onUpdate:modelValue":t[0]||(t[0]=n=>c.value=n),placeholder:"请输入激活码",rows:"4",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none",disabled:i(o).getCurrentStep()==="finishing_activation"},null,8,ie),[[j,c.value]]),e("button",{onClick:P,disabled:!c.value.trim()||i(o).getCurrentStep()==="finishing_activation",class:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-lg"},v(i(o).getCurrentStep()==="finishing_activation"?"激活中...":"完成激活"),9,ne)])])):(a(),r("div",de," 等待前两步完成后，将显示激活码输入框 "))])]),e("div",le,[i(o).hasError()?(a(),r("div",ce,v(i(o).getError()),1)):p("",!0),i(o).isActivated()?(a(),r("div",ue,[t[10]||(t[10]=e("div",{class:"font-medium"},"激活成功！您现在可以使用 XCodeMap 了。",-1)),i(o).getExpiredTimestamp()?(a(),r("div",ge," 许可证过期时间："+v(new Date(i(o).getExpiredTimestamp()).toLocaleString()),1)):p("",!0)])):p("",!0),l.value&&i(o).getCurrentStep()==="finishing_activation"?(a(),r("div",ve,t[11]||(t[11]=[e("div",{class:"font-medium"},"激活中...请等待成功通知。",-1)]))):p("",!0)])])])],2))}}),me=M(pe,[["__scopeId","data-v-416409a1"]]),_=F(me);_.use(q());_.mount("#app");
