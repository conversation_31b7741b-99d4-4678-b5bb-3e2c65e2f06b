var fr=Object.defineProperty;var gr=(n,e,t)=>e in n?fr(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var N=(n,e,t)=>gr(n,typeof e!="symbol"?e+"":e,t);import{d as Je,r as I,u as wt,c as Oe,g as mr,a as br,b as xr,e as It,f as kr,p as wr,s as vr,h as jt,i as Pn,w as zn,j as T,o as v,F as ge,k as we,l as d,m as F,n as _,q as h,t as j,_ as $n,v as Un,x as yr,y as Tr,z as _r,A as ke,B as Ee,C as Er,D as Sr,E as Ar,G as Cr,H as Rr,I as Dr,J as vn,K as Mr,L as Lr,M as Ir,N as Nr,O as Or,P as Pr,Q as zr,R as $r}from"./style-DfXeGTYE.js";const Ur=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href,qt=Je("chatStatus",()=>{const n=I("idle"),e=I("");return{status:n,statusMessage:e,setStatus:s=>{n.value=s},setStatusMessage:s=>{e.value=s}}}),Bn=Je("modelStatus",()=>{const n=I(null),e=I(null),t=wt(),r=Oe(()=>!n.value||!e.value?null:e.value.modelDescList.find(o=>o.uuid===n.value)),s=Oe(()=>{var o;return((o=e.value)==null?void 0:o.modelDescList)||[]});return{currentModelUuid:n,modelConfig:e,currentModel:r,availableModels:s,getModelConfigData:async()=>{try{const o=await mr();if(o.success&&o.data)e.value=o.data,!n.value&&o.data.modelDescList.length>0&&(n.value=o.data.modelDescList[0].uuid);else throw new Error(o.error||"Failed to get model config")}catch(o){console.error("Failed to get model config:",o),t.setError(o instanceof Error?o.message:"Failed to get model config","GET_MODEL_CONFIG_ERROR")}},setCurrentModel:o=>{var i;((i=e.value)==null?void 0:i.modelDescList.find(p=>p.uuid===o))&&(n.value=o)}}}),ce="EMPTY_PLACE_HOLDER",Fn=Je("database",()=>{const n=I([{id:ce,name:"请选择数据源",recordState:"idle",dataId:null,active:!1,serverSelected:!1}]),e=I(ce),t=Oe(()=>n.value.find(b=>b.id===e.value)||null),r=b=>{n.value.push(b)},s=b=>{e.value=b},a=async(b,g)=>{const A=n.value.find(E=>E.id===b);if(A)try{await br({executionId:b,cmd:"change",state:g}),A.recordState="preparing"}catch(E){console.error("Failed to change record state:",E)}};return{databases:n,currentDatabase:t,currentDatabaseId:e,addDatabase:r,setCurrentDatabase:s,changeState:a,queryState:b=>{var g;return(g=n.value.find(A=>A.id===b))==null?void 0:g.recordState},startRecord:b=>{const g=n.value.find(A=>A.id===b);g&&g.recordState==="idle"&&a(b,"start")},endRecord:b=>{const g=n.value.find(A=>A.id===b);g&&g.recordState==="recording"&&a(b,"stop")},restartRecord:b=>{const g=n.value.find(A=>A.id===b);g&&g.recordState==="paused"&&(a(b,"start"),Gn().createNewChat())},getDatabase:async()=>{try{const b=await xr();n.value=[n.value[0]],b.forEach(g=>{r(g)})}catch(b){console.error("Failed to fetch process data:",b)}}}}),Hn=Je("inputBox",()=>{const n=I(""),e=wt(),t=qt(),r=Fn(),s=()=>!r.currentDatabase||!r.currentDatabase.dataId||t.status==="waiting"||r.currentDatabaseId===ce;return{message:n,appendText:i=>{if(s())return e.setError("当前状态不可添加文本到输入框"),!1;const p=n.value&&!n.value.endsWith(" ");return n.value+=(p?" ":"")+i,!0},setText:i=>{n.value=i},clearText:()=>{n.value=""},getText:()=>n.value,isDisabled:s}}),Br="SystemStatus",Fr="AddToChat",Gn=Je("chat",()=>{const n=I([]),e=I(null),t=I(!1);let r=null;const s=wt(),a=qt(),c=Bn(),o=Hn(),f=Oe(()=>n.value.find(S=>S.id===e.value)),i=async()=>{var S;try{e.value&&await It(e.value);const C={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await kr(C.id,((S=c.currentModel)==null?void 0:S.uuid)??""))return n.value=[C],e.value=C.id,t.value||p(),C;throw new Error("Failed to create chat channel")}catch(C){throw console.error("Failed to create chat channel:",C),s.setError(C instanceof Error?C.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),e.value=null,n.value=[],C}},p=()=>{r&&clearInterval(r),t.value=!0,r=window.setInterval(async()=>{if(t.value)try{const S=await wr();if(S&&S.length>0){a.setStatus("waiting");for(const C of S){if(C.messageId===Br){a.setStatusMessage(C.content);continue}if(C.messageId===Fr){o.appendText(C.content);continue}const U=n.value.find(D=>D.id===C.chatId);if(U){const D=U.messages[U.messages.length-1];D&&D.role===C.role?D.content+=C.content:U.messages.push(C),U.updatedAt=Date.now()}}}else a.setStatus("sending")}catch(S){console.error("Failed to poll messages:",S),s.setError(S instanceof Error?S.message:"Failed to poll messages","POLL_ERROR")}},1e3)},b=()=>{r&&(clearInterval(r),r=null),t.value=!1};return{chats:n,currentChatId:e,currentChat:f,createNewChat:i,sendMessage:async S=>{e.value||await i();const C=f.value;if(!C)return;if(!c.currentModel){s.setError("请先选择模型再发送消息","SEND_MESSAGE_ERROR");return}a.setStatus("waiting");const U={messageId:crypto.randomUUID(),content:S,role:"user",timestamp:Date.now(),chatId:e.value,modelId:c.currentModel.uuid};try{if(!e.value)return;if(await vr(U))C.messages.push(U),C.updatedAt=Date.now();else throw new Error("会话可能已失效，请重新创建会话")}catch(D){throw console.error("Failed to send message:",D),s.setError(D instanceof Error?D.message:"会话可能已失效，请重新创建会话","SEND_MESSAGE_ERROR"),D}},removeChat:async S=>{try{if(await It(S))n.value=n.value.filter(U=>U.id!==S),e.value===S&&(e.value=null);else throw new Error("Failed to remove chat channel")}catch(C){throw console.error("Failed to remove chat:",C),s.setError(C instanceof Error?C.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),C}},startPolling:p,stopPolling:b,cleanup:()=>{b(),e.value&&It(e.value).catch(console.error)}}}),Hr={class:"space-y-0.5"},Gr=["onClick"],Wr=["onClick"],jr={key:0,class:"pl-3 mt-0.5 space-y-0.5"},qr=["onClick"],Vr=["onClick"],Yr={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Zr=["onClick"],Xr=jt({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(n,{emit:e}){const t=n,r=Pn(),s=I(t.nodes.map(f=>({...f,isExpanded:t.defaultExpanded,children:f.children?f.children.map(i=>({...i,isExpanded:t.defaultExpanded,children:i.children?i.children.map(p=>({...p,isExpanded:t.defaultExpanded})):[]})):[]})));zn(()=>t.nodes,f=>{s.value=f.map(i=>({...i,isExpanded:t.defaultExpanded,children:i.children?i.children.map(p=>({...p,isExpanded:t.defaultExpanded,children:p.children?p.children.map(b=>({...b,isExpanded:t.defaultExpanded})):[]})):[]}))},{deep:!0});const a=e,c=f=>{a("nodeClick",f)},o=f=>{f.isExpanded=!f.isExpanded};return(f,i)=>(v(),T("div",Hr,[(v(!0),T(ge,null,we(s.value,p=>(v(),T("div",{key:p.nodeKey,class:"tree-node"},[d("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",h(r).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[p.children&&p.children.length>0?(v(),T("button",{key:0,onClick:b=>o(p),class:_(["p-0.5 rounded",h(r).theme==="dark"?"hover:bg-gray-600":"hover:bg-gray-100"])},[(v(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":p.isExpanded,"text-gray-600 hover:text-gray-800":h(r).theme==="light","text-gray-400 hover:text-gray-200":h(r).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},i[0]||(i[0]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],10,Gr)):F("",!0),d("span",{class:_({"text-gray-700 hover:text-gray-900":h(r).theme==="light","text-gray-300 hover:text-gray-100":h(r).theme==="dark","cursor-pointer flex-grow":!0}),onClick:b=>c(p)},j(p.label),11,Wr)],2),p.children&&p.children.length>0&&p.isExpanded?(v(),T("div",jr,[(v(!0),T(ge,null,we(p.children,b=>(v(),T("div",{key:b.nodeKey,class:"tree-node"},[d("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",h(r).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[b.children&&b.children.length>0?(v(),T("button",{key:0,onClick:g=>o(b),class:_(["p-0.5 rounded",h(r).theme==="dark"?"hover:bg-gray-600":"hover:bg-gray-100"])},[(v(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":b.isExpanded,"text-gray-600 hover:text-gray-800":h(r).theme==="light","text-gray-400 hover:text-gray-200":h(r).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},i[1]||(i[1]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],10,qr)):F("",!0),d("span",{class:_({"text-gray-700 hover:text-gray-900":h(r).theme==="light","text-gray-300 hover:text-gray-100":h(r).theme==="dark","cursor-pointer flex-grow":!0}),onClick:g=>c(b)},j(b.label),11,Vr)],2),b.children&&b.children.length>0&&b.isExpanded?(v(),T("div",Yr,[(v(!0),T(ge,null,we(b.children,g=>(v(),T("div",{key:g.nodeKey,class:"pl-3"},[d("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",h(r).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[d("span",{class:_({"text-gray-700 hover:text-gray-900":h(r).theme==="light","text-gray-300 hover:text-gray-100":h(r).theme==="dark","cursor-pointer flex-grow":!0}),onClick:A=>c(g)},j(g.label),11,Zr)],2)]))),128))])):F("",!0)]))),128))])):F("",!0)]))),128))]))}}),Kr=$n(Xr,[["__scopeId","data-v-a8953f9c"]]),Qr=jt({__name:"TypeWriter",props:{text:{},speed:{}},emits:["complete"],setup(n,{emit:e}){const t=n,r=e,s=I(""),a=I(0),c=I(!1),o=()=>{s.value="",a.value=0,c.value=!0;const f=setInterval(()=>{a.value<t.text.length?(s.value+=t.text[a.value],a.value++):(clearInterval(f),c.value=!1,r("complete"))},t.speed||50)};return zn(()=>t.text,()=>{o()}),Un(()=>{o()}),(f,i)=>(v(),T("span",null,j(s.value),1))}});function Vt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var Ae=Vt();function Wn(n){Ae=n}var Qe={exec:()=>null};function L(n,e=""){let t=typeof n=="string"?n:n.source;const r={replace:(s,a)=>{let c=typeof a=="string"?a:a.source;return c=c.replace(Q.caret,"$1"),t=t.replace(s,c),r},getRegex:()=>new RegExp(t,e)};return r}var Q={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:n=>new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}#`),htmlBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}<(?:[a-z].*>|!--)`,"i")},Jr=/^(?:[ \t]*(?:\n|$))+/,es=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,ts=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,et=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,ns=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Yt=/(?:[*+-]|\d{1,9}[.)])/,jn=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,qn=L(jn).replace(/bull/g,Yt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),rs=L(jn).replace(/bull/g,Yt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Zt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,ss=/^[^\n]+/,Xt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,as=L(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Xt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ls=L(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Yt).getRegex(),vt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Kt=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,os=L("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Kt).replace("tag",vt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Vn=L(Zt).replace("hr",et).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",vt).getRegex(),is=L(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Vn).getRegex(),Qt={blockquote:is,code:es,def:as,fences:ts,heading:ns,hr:et,html:os,lheading:qn,list:ls,newline:Jr,paragraph:Vn,table:Qe,text:ss},yn=L("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",et).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",vt).getRegex(),cs={...Qt,lheading:rs,table:yn,paragraph:L(Zt).replace("hr",et).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",yn).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",vt).getRegex()},us={...Qt,html:L(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Kt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Qe,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:L(Zt).replace("hr",et).replace("heading",` *#{1,6} *[^
]`).replace("lheading",qn).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ds=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ps=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Yn=/^( {2,}|\\)\n(?!\s*$)/,hs=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,yt=/[\p{P}\p{S}]/u,Jt=/[\s\p{P}\p{S}]/u,Zn=/[^\s\p{P}\p{S}]/u,fs=L(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Jt).getRegex(),Xn=/(?!~)[\p{P}\p{S}]/u,gs=/(?!~)[\s\p{P}\p{S}]/u,ms=/(?:[^\s\p{P}\p{S}]|~)/u,bs=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Kn=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,xs=L(Kn,"u").replace(/punct/g,yt).getRegex(),ks=L(Kn,"u").replace(/punct/g,Xn).getRegex(),Qn="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",ws=L(Qn,"gu").replace(/notPunctSpace/g,Zn).replace(/punctSpace/g,Jt).replace(/punct/g,yt).getRegex(),vs=L(Qn,"gu").replace(/notPunctSpace/g,ms).replace(/punctSpace/g,gs).replace(/punct/g,Xn).getRegex(),ys=L("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Zn).replace(/punctSpace/g,Jt).replace(/punct/g,yt).getRegex(),Ts=L(/\\(punct)/,"gu").replace(/punct/g,yt).getRegex(),_s=L(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Es=L(Kt).replace("(?:-->|$)","-->").getRegex(),Ss=L("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Es).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),bt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,As=L(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",bt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Jn=L(/^!?\[(label)\]\[(ref)\]/).replace("label",bt).replace("ref",Xt).getRegex(),er=L(/^!?\[(ref)\](?:\[\])?/).replace("ref",Xt).getRegex(),Cs=L("reflink|nolink(?!\\()","g").replace("reflink",Jn).replace("nolink",er).getRegex(),en={_backpedal:Qe,anyPunctuation:Ts,autolink:_s,blockSkip:bs,br:Yn,code:ps,del:Qe,emStrongLDelim:xs,emStrongRDelimAst:ws,emStrongRDelimUnd:ys,escape:ds,link:As,nolink:er,punctuation:fs,reflink:Jn,reflinkSearch:Cs,tag:Ss,text:hs,url:Qe},Rs={...en,link:L(/^!?\[(label)\]\((.*?)\)/).replace("label",bt).getRegex(),reflink:L(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",bt).getRegex()},Bt={...en,emStrongRDelimAst:vs,emStrongLDelim:ks,url:L(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ds={...Bt,br:L(Yn).replace("{2,}","*").getRegex(),text:L(Bt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},pt={normal:Qt,gfm:cs,pedantic:us},je={normal:en,gfm:Bt,breaks:Ds,pedantic:Rs},Ms={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Tn=n=>Ms[n];function ie(n,e){if(e){if(Q.escapeTest.test(n))return n.replace(Q.escapeReplace,Tn)}else if(Q.escapeTestNoEncode.test(n))return n.replace(Q.escapeReplaceNoEncode,Tn);return n}function _n(n){try{n=encodeURI(n).replace(Q.percentDecode,"%")}catch{return null}return n}function En(n,e){var a;const t=n.replace(Q.findPipe,(c,o,f)=>{let i=!1,p=o;for(;--p>=0&&f[p]==="\\";)i=!i;return i?"|":" |"}),r=t.split(Q.splitPipe);let s=0;if(r[0].trim()||r.shift(),r.length>0&&!((a=r.at(-1))!=null&&a.trim())&&r.pop(),e)if(r.length>e)r.splice(e);else for(;r.length<e;)r.push("");for(;s<r.length;s++)r[s]=r[s].trim().replace(Q.slashPipe,"|");return r}function qe(n,e,t){const r=n.length;if(r===0)return"";let s=0;for(;s<r&&n.charAt(r-s-1)===e;)s++;return n.slice(0,r-s)}function Ls(n,e){if(n.indexOf(e[1])===-1)return-1;let t=0;for(let r=0;r<n.length;r++)if(n[r]==="\\")r++;else if(n[r]===e[0])t++;else if(n[r]===e[1]&&(t--,t<0))return r;return t>0?-2:-1}function Sn(n,e,t,r,s){const a=e.href,c=e.title||null,o=n[1].replace(s.other.outputLinkReplace,"$1");r.state.inLink=!0;const f={type:n[0].charAt(0)==="!"?"image":"link",raw:t,href:a,title:c,text:o,tokens:r.inlineTokens(o)};return r.state.inLink=!1,f}function Is(n,e,t){const r=n.match(t.other.indentCodeCompensation);if(r===null)return e;const s=r[1];return e.split(`
`).map(a=>{const c=a.match(t.other.beginningSpace);if(c===null)return a;const[o]=c;return o.length>=s.length?a.slice(s.length):a}).join(`
`)}var xt=class{constructor(n){N(this,"options");N(this,"rules");N(this,"lexer");this.options=n||Ae}space(n){const e=this.rules.block.newline.exec(n);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(n){const e=this.rules.block.code.exec(n);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:qe(t,`
`)}}}fences(n){const e=this.rules.block.fences.exec(n);if(e){const t=e[0],r=Is(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:r}}}heading(n){const e=this.rules.block.heading.exec(n);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const r=qe(t,"#");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(t=r.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(n){const e=this.rules.block.hr.exec(n);if(e)return{type:"hr",raw:qe(e[0],`
`)}}blockquote(n){const e=this.rules.block.blockquote.exec(n);if(e){let t=qe(e[0],`
`).split(`
`),r="",s="";const a=[];for(;t.length>0;){let c=!1;const o=[];let f;for(f=0;f<t.length;f++)if(this.rules.other.blockquoteStart.test(t[f]))o.push(t[f]),c=!0;else if(!c)o.push(t[f]);else break;t=t.slice(f);const i=o.join(`
`),p=i.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}
${i}`:i,s=s?`${s}
${p}`:p;const b=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(p,a,!0),this.lexer.state.top=b,t.length===0)break;const g=a.at(-1);if((g==null?void 0:g.type)==="code")break;if((g==null?void 0:g.type)==="blockquote"){const A=g,E=A.raw+`
`+t.join(`
`),S=this.blockquote(E);a[a.length-1]=S,r=r.substring(0,r.length-A.raw.length)+S.raw,s=s.substring(0,s.length-A.text.length)+S.text;break}else if((g==null?void 0:g.type)==="list"){const A=g,E=A.raw+`
`+t.join(`
`),S=this.list(E);a[a.length-1]=S,r=r.substring(0,r.length-g.raw.length)+S.raw,s=s.substring(0,s.length-A.raw.length)+S.raw,t=E.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:r,tokens:a,text:s}}}list(n){let e=this.rules.block.list.exec(n);if(e){let t=e[1].trim();const r=t.length>1,s={type:"list",raw:"",ordered:r,start:r?+t.slice(0,-1):"",loose:!1,items:[]};t=r?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=r?t:"[*+-]");const a=this.rules.other.listItemRegex(t);let c=!1;for(;n;){let f=!1,i="",p="";if(!(e=a.exec(n))||this.rules.block.hr.test(n))break;i=e[0],n=n.substring(i.length);let b=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,U=>" ".repeat(3*U.length)),g=n.split(`
`,1)[0],A=!b.trim(),E=0;if(this.options.pedantic?(E=2,p=b.trimStart()):A?E=e[1].length+1:(E=e[2].search(this.rules.other.nonSpaceChar),E=E>4?1:E,p=b.slice(E),E+=e[1].length),A&&this.rules.other.blankLine.test(g)&&(i+=g+`
`,n=n.substring(g.length+1),f=!0),!f){const U=this.rules.other.nextBulletRegex(E),D=this.rules.other.hrRegex(E),q=this.rules.other.fencesBeginRegex(E),O=this.rules.other.headingBeginRegex(E),ne=this.rules.other.htmlBeginRegex(E);for(;n;){const re=n.split(`
`,1)[0];let le;if(g=re,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),le=g):le=g.replace(this.rules.other.tabCharGlobal,"    "),q.test(g)||O.test(g)||ne.test(g)||U.test(g)||D.test(g))break;if(le.search(this.rules.other.nonSpaceChar)>=E||!g.trim())p+=`
`+le.slice(E);else{if(A||b.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||q.test(b)||O.test(b)||D.test(b))break;p+=`
`+g}!A&&!g.trim()&&(A=!0),i+=re+`
`,n=n.substring(re.length+1),b=le.slice(E)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(i)&&(c=!0));let S=null,C;this.options.gfm&&(S=this.rules.other.listIsTask.exec(p),S&&(C=S[0]!=="[ ] ",p=p.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:i,task:!!S,checked:C,loose:!1,text:p,tokens:[]}),s.raw+=i}const o=s.items.at(-1);if(o)o.raw=o.raw.trimEnd(),o.text=o.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let f=0;f<s.items.length;f++)if(this.lexer.state.top=!1,s.items[f].tokens=this.lexer.blockTokens(s.items[f].text,[]),!s.loose){const i=s.items[f].tokens.filter(b=>b.type==="space"),p=i.length>0&&i.some(b=>this.rules.other.anyLine.test(b.raw));s.loose=p}if(s.loose)for(let f=0;f<s.items.length;f++)s.items[f].loose=!0;return s}}html(n){const e=this.rules.block.html.exec(n);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(n){const e=this.rules.block.def.exec(n);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:r,title:s}}}table(n){var c;const e=this.rules.block.table.exec(n);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=En(e[1]),r=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===r.length){for(const o of r)this.rules.other.tableAlignRight.test(o)?a.align.push("right"):this.rules.other.tableAlignCenter.test(o)?a.align.push("center"):this.rules.other.tableAlignLeft.test(o)?a.align.push("left"):a.align.push(null);for(let o=0;o<t.length;o++)a.header.push({text:t[o],tokens:this.lexer.inline(t[o]),header:!0,align:a.align[o]});for(const o of s)a.rows.push(En(o,a.header.length).map((f,i)=>({text:f,tokens:this.lexer.inline(f),header:!1,align:a.align[i]})));return a}}lheading(n){const e=this.rules.block.lheading.exec(n);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(n){const e=this.rules.block.paragraph.exec(n);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(n){const e=this.rules.block.text.exec(n);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(n){const e=this.rules.inline.escape.exec(n);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(n){const e=this.rules.inline.tag.exec(n);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(n){const e=this.rules.inline.link.exec(n);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const a=qe(t.slice(0,-1),"\\");if((t.length-a.length)%2===0)return}else{const a=Ls(e[2],"()");if(a===-2)return;if(a>-1){const o=(e[0].indexOf("!")===0?5:4)+e[1].length+a;e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,o).trim(),e[3]=""}}let r=e[2],s="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(r);a&&(r=a[1],s=a[3])}else s=e[3]?e[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?r=r.slice(1):r=r.slice(1,-1)),Sn(e,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(n,e){let t;if((t=this.rules.inline.reflink.exec(n))||(t=this.rules.inline.nolink.exec(n))){const r=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[r.toLowerCase()];if(!s){const a=t[0].charAt(0);return{type:"text",raw:a,text:a}}return Sn(t,s,t[0],this.lexer,this.rules)}}emStrong(n,e,t=""){let r=this.rules.inline.emStrongLDelim.exec(n);if(!r||r[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(r[1]||r[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const a=[...r[0]].length-1;let c,o,f=a,i=0;const p=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,e=e.slice(-1*n.length+a);(r=p.exec(e))!=null;){if(c=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!c)continue;if(o=[...c].length,r[3]||r[4]){f+=o;continue}else if((r[5]||r[6])&&a%3&&!((a+o)%3)){i+=o;continue}if(f-=o,f>0)continue;o=Math.min(o,o+f+i);const b=[...r[0]][0].length,g=n.slice(0,a+r.index+b+o);if(Math.min(a,o)%2){const E=g.slice(1,-1);return{type:"em",raw:g,text:E,tokens:this.lexer.inlineTokens(E)}}const A=g.slice(2,-2);return{type:"strong",raw:g,text:A,tokens:this.lexer.inlineTokens(A)}}}}codespan(n){const e=this.rules.inline.code.exec(n);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const r=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return r&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(n){const e=this.rules.inline.br.exec(n);if(e)return{type:"br",raw:e[0]}}del(n){const e=this.rules.inline.del.exec(n);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(n){const e=this.rules.inline.autolink.exec(n);if(e){let t,r;return e[2]==="@"?(t=e[1],r="mailto:"+t):(t=e[1],r=t),{type:"link",raw:e[0],text:t,href:r,tokens:[{type:"text",raw:t,text:t}]}}}url(n){var t;let e;if(e=this.rules.inline.url.exec(n)){let r,s;if(e[2]==="@")r=e[0],s="mailto:"+r;else{let a;do a=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(a!==e[0]);r=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(n){const e=this.rules.inline.text.exec(n);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},me=class Ft{constructor(e){N(this,"tokens");N(this,"options");N(this,"state");N(this,"tokenizer");N(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Ae,this.options.tokenizer=this.options.tokenizer||new xt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:Q,block:pt.normal,inline:je.normal};this.options.pedantic?(t.block=pt.pedantic,t.inline=je.pedantic):this.options.gfm&&(t.block=pt.gfm,this.options.breaks?t.inline=je.breaks:t.inline=je.gfm),this.tokenizer.rules=t}static get rules(){return{block:pt,inline:je}}static lex(e,t){return new Ft(t).lex(e)}static lexInline(e,t){return new Ft(t).inlineTokens(e)}lex(e){e=e.replace(Q.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const r=this.inlineQueue[t];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],r=!1){var s,a,c;for(this.options.pedantic&&(e=e.replace(Q.tabCharGlobal,"    ").replace(Q.spaceLine,""));e;){let o;if((a=(s=this.options.extensions)==null?void 0:s.block)!=null&&a.some(i=>(o=i.call({lexer:this},e,t))?(e=e.substring(o.raw.length),t.push(o),!0):!1))continue;if(o=this.tokenizer.space(e)){e=e.substring(o.raw.length);const i=t.at(-1);o.raw.length===1&&i!==void 0?i.raw+=`
`:t.push(o);continue}if(o=this.tokenizer.code(e)){e=e.substring(o.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+o.raw,i.text+=`
`+o.text,this.inlineQueue.at(-1).src=i.text):t.push(o);continue}if(o=this.tokenizer.fences(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.heading(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.hr(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.blockquote(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.list(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.html(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.def(e)){e=e.substring(o.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+o.raw,i.text+=`
`+o.raw,this.inlineQueue.at(-1).src=i.text):this.tokens.links[o.tag]||(this.tokens.links[o.tag]={href:o.href,title:o.title});continue}if(o=this.tokenizer.table(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.lheading(e)){e=e.substring(o.raw.length),t.push(o);continue}let f=e;if((c=this.options.extensions)!=null&&c.startBlock){let i=1/0;const p=e.slice(1);let b;this.options.extensions.startBlock.forEach(g=>{b=g.call({lexer:this},p),typeof b=="number"&&b>=0&&(i=Math.min(i,b))}),i<1/0&&i>=0&&(f=e.substring(0,i+1))}if(this.state.top&&(o=this.tokenizer.paragraph(f))){const i=t.at(-1);r&&(i==null?void 0:i.type)==="paragraph"?(i.raw+=`
`+o.raw,i.text+=`
`+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(o),r=f.length!==e.length,e=e.substring(o.raw.length);continue}if(o=this.tokenizer.text(e)){e=e.substring(o.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="text"?(i.raw+=`
`+o.raw,i.text+=`
`+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(o);continue}if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}else throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var o,f,i;let r=e,s=null;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)p.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,s.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,c="";for(;e;){a||(c=""),a=!1;let p;if((f=(o=this.options.extensions)==null?void 0:o.inline)!=null&&f.some(g=>(p=g.call({lexer:this},e,t))?(e=e.substring(p.raw.length),t.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);const g=t.at(-1);p.type==="text"&&(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(p=this.tokenizer.emStrong(e,r,c)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),t.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),t.push(p);continue}let b=e;if((i=this.options.extensions)!=null&&i.startInline){let g=1/0;const A=e.slice(1);let E;this.options.extensions.startInline.forEach(S=>{E=S.call({lexer:this},A),typeof E=="number"&&E>=0&&(g=Math.min(g,E))}),g<1/0&&g>=0&&(b=e.substring(0,g+1))}if(p=this.tokenizer.inlineText(b)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(c=p.raw.slice(-1)),a=!0;const g=t.at(-1);(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(e){const g="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(g);break}else throw new Error(g)}}return t}},kt=class{constructor(n){N(this,"options");N(this,"parser");this.options=n||Ae}space(n){return""}code({text:n,lang:e,escaped:t}){var a;const r=(a=(e||"").match(Q.notSpaceStart))==null?void 0:a[0],s=n.replace(Q.endingNewline,"")+`
`;return r?'<pre><code class="language-'+ie(r)+'">'+(t?s:ie(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:ie(s,!0))+`</code></pre>
`}blockquote({tokens:n}){return`<blockquote>
${this.parser.parse(n)}</blockquote>
`}html({text:n}){return n}heading({tokens:n,depth:e}){return`<h${e}>${this.parser.parseInline(n)}</h${e}>
`}hr(n){return`<hr>
`}list(n){const e=n.ordered,t=n.start;let r="";for(let c=0;c<n.items.length;c++){const o=n.items[c];r+=this.listitem(o)}const s=e?"ol":"ul",a=e&&t!==1?' start="'+t+'"':"";return"<"+s+a+`>
`+r+"</"+s+`>
`}listitem(n){var t;let e="";if(n.task){const r=this.checkbox({checked:!!n.checked});n.loose?((t=n.tokens[0])==null?void 0:t.type)==="paragraph"?(n.tokens[0].text=r+" "+n.tokens[0].text,n.tokens[0].tokens&&n.tokens[0].tokens.length>0&&n.tokens[0].tokens[0].type==="text"&&(n.tokens[0].tokens[0].text=r+" "+ie(n.tokens[0].tokens[0].text),n.tokens[0].tokens[0].escaped=!0)):n.tokens.unshift({type:"text",raw:r+" ",text:r+" ",escaped:!0}):e+=r+" "}return e+=this.parser.parse(n.tokens,!!n.loose),`<li>${e}</li>
`}checkbox({checked:n}){return"<input "+(n?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:n}){return`<p>${this.parser.parseInline(n)}</p>
`}table(n){let e="",t="";for(let s=0;s<n.header.length;s++)t+=this.tablecell(n.header[s]);e+=this.tablerow({text:t});let r="";for(let s=0;s<n.rows.length;s++){const a=n.rows[s];t="";for(let c=0;c<a.length;c++)t+=this.tablecell(a[c]);r+=this.tablerow({text:t})}return r&&(r=`<tbody>${r}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+r+`</table>
`}tablerow({text:n}){return`<tr>
${n}</tr>
`}tablecell(n){const e=this.parser.parseInline(n.tokens),t=n.header?"th":"td";return(n.align?`<${t} align="${n.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:n}){return`<strong>${this.parser.parseInline(n)}</strong>`}em({tokens:n}){return`<em>${this.parser.parseInline(n)}</em>`}codespan({text:n}){return`<code>${ie(n,!0)}</code>`}br(n){return"<br>"}del({tokens:n}){return`<del>${this.parser.parseInline(n)}</del>`}link({href:n,title:e,tokens:t}){const r=this.parser.parseInline(t),s=_n(n);if(s===null)return r;n=s;let a='<a href="'+n+'"';return e&&(a+=' title="'+ie(e)+'"'),a+=">"+r+"</a>",a}image({href:n,title:e,text:t,tokens:r}){r&&(t=this.parser.parseInline(r,this.parser.textRenderer));const s=_n(n);if(s===null)return ie(t);n=s;let a=`<img src="${n}" alt="${t}"`;return e&&(a+=` title="${ie(e)}"`),a+=">",a}text(n){return"tokens"in n&&n.tokens?this.parser.parseInline(n.tokens):"escaped"in n&&n.escaped?n.text:ie(n.text)}},tn=class{strong({text:n}){return n}em({text:n}){return n}codespan({text:n}){return n}del({text:n}){return n}html({text:n}){return n}text({text:n}){return n}link({text:n}){return""+n}image({text:n}){return""+n}br(){return""}},be=class Ht{constructor(e){N(this,"options");N(this,"renderer");N(this,"textRenderer");this.options=e||Ae,this.options.renderer=this.options.renderer||new kt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new tn}static parse(e,t){return new Ht(t).parse(e)}static parseInline(e,t){return new Ht(t).parseInline(e)}parse(e,t=!0){var s,a;let r="";for(let c=0;c<e.length;c++){const o=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[o.type]){const i=o,p=this.options.extensions.renderers[i.type].call({parser:this},i);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){r+=p||"";continue}}const f=o;switch(f.type){case"space":{r+=this.renderer.space(f);continue}case"hr":{r+=this.renderer.hr(f);continue}case"heading":{r+=this.renderer.heading(f);continue}case"code":{r+=this.renderer.code(f);continue}case"table":{r+=this.renderer.table(f);continue}case"blockquote":{r+=this.renderer.blockquote(f);continue}case"list":{r+=this.renderer.list(f);continue}case"html":{r+=this.renderer.html(f);continue}case"paragraph":{r+=this.renderer.paragraph(f);continue}case"text":{let i=f,p=this.renderer.text(i);for(;c+1<e.length&&e[c+1].type==="text";)i=e[++c],p+=`
`+this.renderer.text(i);t?r+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):r+=p;continue}default:{const i='Token with "'+f.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return r}parseInline(e,t=this.renderer){var s,a;let r="";for(let c=0;c<e.length;c++){const o=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[o.type]){const i=this.options.extensions.renderers[o.type].call({parser:this},o);if(i!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type)){r+=i||"";continue}}const f=o;switch(f.type){case"escape":{r+=t.text(f);break}case"html":{r+=t.html(f);break}case"link":{r+=t.link(f);break}case"image":{r+=t.image(f);break}case"strong":{r+=t.strong(f);break}case"em":{r+=t.em(f);break}case"codespan":{r+=t.codespan(f);break}case"br":{r+=t.br(f);break}case"del":{r+=t.del(f);break}case"text":{r+=t.text(f);break}default:{const i='Token with "'+f.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return r}},Ut,gt=(Ut=class{constructor(n){N(this,"options");N(this,"block");this.options=n||Ae}preprocess(n){return n}postprocess(n){return n}processAllTokens(n){return n}provideLexer(){return this.block?me.lex:me.lexInline}provideParser(){return this.block?be.parse:be.parseInline}},N(Ut,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Ut),Ns=class{constructor(...n){N(this,"defaults",Vt());N(this,"options",this.setOptions);N(this,"parse",this.parseMarkdown(!0));N(this,"parseInline",this.parseMarkdown(!1));N(this,"Parser",be);N(this,"Renderer",kt);N(this,"TextRenderer",tn);N(this,"Lexer",me);N(this,"Tokenizer",xt);N(this,"Hooks",gt);this.use(...n)}walkTokens(n,e){var r,s;let t=[];for(const a of n)switch(t=t.concat(e.call(this,a)),a.type){case"table":{const c=a;for(const o of c.header)t=t.concat(this.walkTokens(o.tokens,e));for(const o of c.rows)for(const f of o)t=t.concat(this.walkTokens(f.tokens,e));break}case"list":{const c=a;t=t.concat(this.walkTokens(c.items,e));break}default:{const c=a;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(o=>{const f=c[o].flat(1/0);t=t.concat(this.walkTokens(f,e))}):c.tokens&&(t=t.concat(this.walkTokens(c.tokens,e)))}}return t}use(...n){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return n.forEach(t=>{const r={...t};if(r.async=this.defaults.async||r.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=e.renderers[s.name];a?e.renderers[s.name]=function(...c){let o=s.renderer.apply(this,c);return o===!1&&(o=a.apply(this,c)),o}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=e[s.level];a?a.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),r.extensions=e),t.renderer){const s=this.defaults.renderer||new kt(this.defaults);for(const a in t.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const c=a,o=t.renderer[c],f=s[c];s[c]=(...i)=>{let p=o.apply(s,i);return p===!1&&(p=f.apply(s,i)),p||""}}r.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new xt(this.defaults);for(const a in t.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const c=a,o=t.tokenizer[c],f=s[c];s[c]=(...i)=>{let p=o.apply(s,i);return p===!1&&(p=f.apply(s,i)),p}}r.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new gt;for(const a in t.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const c=a,o=t.hooks[c],f=s[c];gt.passThroughHooks.has(a)?s[c]=i=>{if(this.defaults.async)return Promise.resolve(o.call(s,i)).then(b=>f.call(s,b));const p=o.call(s,i);return f.call(s,p)}:s[c]=(...i)=>{let p=o.apply(s,i);return p===!1&&(p=f.apply(s,i)),p}}r.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,a=t.walkTokens;r.walkTokens=function(c){let o=[];return o.push(a.call(this,c)),s&&(o=o.concat(s.call(this,c))),o}}this.defaults={...this.defaults,...r}}),this}setOptions(n){return this.defaults={...this.defaults,...n},this}lexer(n,e){return me.lex(n,e??this.defaults)}parser(n,e){return be.parse(n,e??this.defaults)}parseMarkdown(n){return(t,r)=>{const s={...r},a={...this.defaults,...s},c=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&s.async===!1)return c(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return c(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return c(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=n);const o=a.hooks?a.hooks.provideLexer():n?me.lex:me.lexInline,f=a.hooks?a.hooks.provideParser():n?be.parse:be.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(i=>o(i,a)).then(i=>a.hooks?a.hooks.processAllTokens(i):i).then(i=>a.walkTokens?Promise.all(this.walkTokens(i,a.walkTokens)).then(()=>i):i).then(i=>f(i,a)).then(i=>a.hooks?a.hooks.postprocess(i):i).catch(c);try{a.hooks&&(t=a.hooks.preprocess(t));let i=o(t,a);a.hooks&&(i=a.hooks.processAllTokens(i)),a.walkTokens&&this.walkTokens(i,a.walkTokens);let p=f(i,a);return a.hooks&&(p=a.hooks.postprocess(p)),p}catch(i){return c(i)}}}onError(n,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,n){const r="<p>An error occurred:</p><pre>"+ie(t.message+"",!0)+"</pre>";return e?Promise.resolve(r):r}if(e)return Promise.reject(t);throw t}}},Se=new Ns;function M(n,e){return Se.parse(n,e)}M.options=M.setOptions=function(n){return Se.setOptions(n),M.defaults=Se.defaults,Wn(M.defaults),M};M.getDefaults=Vt;M.defaults=Ae;M.use=function(...n){return Se.use(...n),M.defaults=Se.defaults,Wn(M.defaults),M};M.walkTokens=function(n,e){return Se.walkTokens(n,e)};M.parseInline=Se.parseInline;M.Parser=be;M.parser=be.parse;M.Renderer=kt;M.TextRenderer=tn;M.Lexer=me;M.lexer=me.lex;M.Tokenizer=xt;M.Hooks=gt;M.parse=M;M.options;M.setOptions;M.use;M.walkTokens;M.parseInline;be.parse;me.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:tr,setPrototypeOf:An,isFrozen:Os,getPrototypeOf:Ps,getOwnPropertyDescriptor:zs}=Object;let{freeze:J,seal:se,create:nr}=Object,{apply:Gt,construct:Wt}=typeof Reflect<"u"&&Reflect;J||(J=function(e){return e});se||(se=function(e){return e});Gt||(Gt=function(e,t,r){return e.apply(t,r)});Wt||(Wt=function(e,t){return new e(...t)});const ht=ee(Array.prototype.forEach),$s=ee(Array.prototype.lastIndexOf),Cn=ee(Array.prototype.pop),Ve=ee(Array.prototype.push),Us=ee(Array.prototype.splice),mt=ee(String.prototype.toLowerCase),Nt=ee(String.prototype.toString),Rn=ee(String.prototype.match),Ye=ee(String.prototype.replace),Bs=ee(String.prototype.indexOf),Fs=ee(String.prototype.trim),ae=ee(Object.prototype.hasOwnProperty),K=ee(RegExp.prototype.test),Ze=Hs(TypeError);function ee(n){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];return Gt(n,e,r)}}function Hs(n){return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Wt(n,t)}}function R(n,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:mt;An&&An(n,null);let r=e.length;for(;r--;){let s=e[r];if(typeof s=="string"){const a=t(s);a!==s&&(Os(e)||(e[r]=a),s=a)}n[s]=!0}return n}function Gs(n){for(let e=0;e<n.length;e++)ae(n,e)||(n[e]=null);return n}function fe(n){const e=nr(null);for(const[t,r]of tr(n))ae(n,t)&&(Array.isArray(r)?e[t]=Gs(r):r&&typeof r=="object"&&r.constructor===Object?e[t]=fe(r):e[t]=r);return e}function Xe(n,e){for(;n!==null;){const r=zs(n,e);if(r){if(r.get)return ee(r.get);if(typeof r.value=="function")return ee(r.value)}n=Ps(n)}function t(){return null}return t}const Dn=J(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ot=J(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Pt=J(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ws=J(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),zt=J(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),js=J(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Mn=J(["#text"]),Ln=J(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),$t=J(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),In=J(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ft=J(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),qs=se(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Vs=se(/<%[\w\W]*|[\w\W]*%>/gm),Ys=se(/\$\{[\w\W]*/gm),Zs=se(/^data-[\-\w.\u00B7-\uFFFF]+$/),Xs=se(/^aria-[\-\w]+$/),rr=se(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ks=se(/^(?:\w+script|data):/i),Qs=se(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),sr=se(/^html$/i),Js=se(/^[a-z][.\w]*(-[.\w]+)+$/i);var Nn=Object.freeze({__proto__:null,ARIA_ATTR:Xs,ATTR_WHITESPACE:Qs,CUSTOM_ELEMENT:Js,DATA_ATTR:Zs,DOCTYPE_NAME:sr,ERB_EXPR:Vs,IS_ALLOWED_URI:rr,IS_SCRIPT_OR_DATA:Ks,MUSTACHE_EXPR:qs,TMPLIT_EXPR:Ys});const Ke={element:1,text:3,progressingInstruction:7,comment:8,document:9},ea=function(){return typeof window>"u"?null:window},ta=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let r=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(r=t.getAttribute(s));const a="dompurify"+(r?"#"+r:"");try{return e.createPolicy(a,{createHTML(c){return c},createScriptURL(c){return c}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},On=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function ar(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ea();const e=y=>ar(y);if(e.version="3.2.6",e.removed=[],!n||!n.document||n.document.nodeType!==Ke.document||!n.Element)return e.isSupported=!1,e;let{document:t}=n;const r=t,s=r.currentScript,{DocumentFragment:a,HTMLTemplateElement:c,Node:o,Element:f,NodeFilter:i,NamedNodeMap:p=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:b,DOMParser:g,trustedTypes:A}=n,E=f.prototype,S=Xe(E,"cloneNode"),C=Xe(E,"remove"),U=Xe(E,"nextSibling"),D=Xe(E,"childNodes"),q=Xe(E,"parentNode");if(typeof c=="function"){const y=t.createElement("template");y.content&&y.content.ownerDocument&&(t=y.content.ownerDocument)}let O,ne="";const{implementation:re,createNodeIterator:le,createDocumentFragment:Pe,getElementsByTagName:ve}=t,{importNode:Tt}=r;let V=On();e.isSupported=typeof tr=="function"&&typeof q=="function"&&re&&re.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:ze,ERB_EXPR:$e,TMPLIT_EXPR:Ue,DATA_ATTR:_t,ARIA_ATTR:Be,IS_SCRIPT_OR_DATA:tt,ATTR_WHITESPACE:nt,CUSTOM_ELEMENT:Et}=Nn;let{IS_ALLOWED_URI:rt}=Nn,B=null;const Ce=R({},[...Dn,...Ot,...Pt,...zt,...Mn]);let H=null;const st=R({},[...Ln,...$t,...In,...ft]);let P=Object.seal(nr(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ye=null,Te=null,at=!0,Fe=!0,lt=!1,ot=!0,xe=!1,k=!0,u=!1,Y=!1,$=!1,x=!1,ue=!1,Re=!1,_e=!0,rn=!1;const lr="user-content-";let St=!0,He=!1,De={},Me=null;const sn=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let an=null;const ln=R({},["audio","video","img","source","image","track"]);let At=null;const on=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),it="http://www.w3.org/1998/Math/MathML",ct="http://www.w3.org/2000/svg",de="http://www.w3.org/1999/xhtml";let Le=de,Ct=!1,Rt=null;const or=R({},[it,ct,de],Nt);let ut=R({},["mi","mo","mn","ms","mtext"]),dt=R({},["annotation-xml"]);const ir=R({},["title","style","font","a","script"]);let Ge=null;const cr=["application/xhtml+xml","text/html"],ur="text/html";let W=null,Ie=null;const dr=t.createElement("form"),cn=function(l){return l instanceof RegExp||l instanceof Function},Dt=function(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Ie&&Ie===l)){if((!l||typeof l!="object")&&(l={}),l=fe(l),Ge=cr.indexOf(l.PARSER_MEDIA_TYPE)===-1?ur:l.PARSER_MEDIA_TYPE,W=Ge==="application/xhtml+xml"?Nt:mt,B=ae(l,"ALLOWED_TAGS")?R({},l.ALLOWED_TAGS,W):Ce,H=ae(l,"ALLOWED_ATTR")?R({},l.ALLOWED_ATTR,W):st,Rt=ae(l,"ALLOWED_NAMESPACES")?R({},l.ALLOWED_NAMESPACES,Nt):or,At=ae(l,"ADD_URI_SAFE_ATTR")?R(fe(on),l.ADD_URI_SAFE_ATTR,W):on,an=ae(l,"ADD_DATA_URI_TAGS")?R(fe(ln),l.ADD_DATA_URI_TAGS,W):ln,Me=ae(l,"FORBID_CONTENTS")?R({},l.FORBID_CONTENTS,W):sn,ye=ae(l,"FORBID_TAGS")?R({},l.FORBID_TAGS,W):fe({}),Te=ae(l,"FORBID_ATTR")?R({},l.FORBID_ATTR,W):fe({}),De=ae(l,"USE_PROFILES")?l.USE_PROFILES:!1,at=l.ALLOW_ARIA_ATTR!==!1,Fe=l.ALLOW_DATA_ATTR!==!1,lt=l.ALLOW_UNKNOWN_PROTOCOLS||!1,ot=l.ALLOW_SELF_CLOSE_IN_ATTR!==!1,xe=l.SAFE_FOR_TEMPLATES||!1,k=l.SAFE_FOR_XML!==!1,u=l.WHOLE_DOCUMENT||!1,x=l.RETURN_DOM||!1,ue=l.RETURN_DOM_FRAGMENT||!1,Re=l.RETURN_TRUSTED_TYPE||!1,$=l.FORCE_BODY||!1,_e=l.SANITIZE_DOM!==!1,rn=l.SANITIZE_NAMED_PROPS||!1,St=l.KEEP_CONTENT!==!1,He=l.IN_PLACE||!1,rt=l.ALLOWED_URI_REGEXP||rr,Le=l.NAMESPACE||de,ut=l.MATHML_TEXT_INTEGRATION_POINTS||ut,dt=l.HTML_INTEGRATION_POINTS||dt,P=l.CUSTOM_ELEMENT_HANDLING||{},l.CUSTOM_ELEMENT_HANDLING&&cn(l.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(P.tagNameCheck=l.CUSTOM_ELEMENT_HANDLING.tagNameCheck),l.CUSTOM_ELEMENT_HANDLING&&cn(l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(P.attributeNameCheck=l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),l.CUSTOM_ELEMENT_HANDLING&&typeof l.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(P.allowCustomizedBuiltInElements=l.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),xe&&(Fe=!1),ue&&(x=!0),De&&(B=R({},Mn),H=[],De.html===!0&&(R(B,Dn),R(H,Ln)),De.svg===!0&&(R(B,Ot),R(H,$t),R(H,ft)),De.svgFilters===!0&&(R(B,Pt),R(H,$t),R(H,ft)),De.mathMl===!0&&(R(B,zt),R(H,In),R(H,ft))),l.ADD_TAGS&&(B===Ce&&(B=fe(B)),R(B,l.ADD_TAGS,W)),l.ADD_ATTR&&(H===st&&(H=fe(H)),R(H,l.ADD_ATTR,W)),l.ADD_URI_SAFE_ATTR&&R(At,l.ADD_URI_SAFE_ATTR,W),l.FORBID_CONTENTS&&(Me===sn&&(Me=fe(Me)),R(Me,l.FORBID_CONTENTS,W)),St&&(B["#text"]=!0),u&&R(B,["html","head","body"]),B.table&&(R(B,["tbody"]),delete ye.tbody),l.TRUSTED_TYPES_POLICY){if(typeof l.TRUSTED_TYPES_POLICY.createHTML!="function")throw Ze('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof l.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Ze('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');O=l.TRUSTED_TYPES_POLICY,ne=O.createHTML("")}else O===void 0&&(O=ta(A,s)),O!==null&&typeof ne=="string"&&(ne=O.createHTML(""));J&&J(l),Ie=l}},un=R({},[...Ot,...Pt,...Ws]),dn=R({},[...zt,...js]),pr=function(l){let m=q(l);(!m||!m.tagName)&&(m={namespaceURI:Le,tagName:"template"});const w=mt(l.tagName),z=mt(m.tagName);return Rt[l.namespaceURI]?l.namespaceURI===ct?m.namespaceURI===de?w==="svg":m.namespaceURI===it?w==="svg"&&(z==="annotation-xml"||ut[z]):!!un[w]:l.namespaceURI===it?m.namespaceURI===de?w==="math":m.namespaceURI===ct?w==="math"&&dt[z]:!!dn[w]:l.namespaceURI===de?m.namespaceURI===ct&&!dt[z]||m.namespaceURI===it&&!ut[z]?!1:!dn[w]&&(ir[w]||!un[w]):!!(Ge==="application/xhtml+xml"&&Rt[l.namespaceURI]):!1},oe=function(l){Ve(e.removed,{element:l});try{q(l).removeChild(l)}catch{C(l)}},Ne=function(l,m){try{Ve(e.removed,{attribute:m.getAttributeNode(l),from:m})}catch{Ve(e.removed,{attribute:null,from:m})}if(m.removeAttribute(l),l==="is")if(x||ue)try{oe(m)}catch{}else try{m.setAttribute(l,"")}catch{}},pn=function(l){let m=null,w=null;if($)l="<remove></remove>"+l;else{const G=Rn(l,/^[\r\n\t ]+/);w=G&&G[0]}Ge==="application/xhtml+xml"&&Le===de&&(l='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+l+"</body></html>");const z=O?O.createHTML(l):l;if(Le===de)try{m=new g().parseFromString(z,Ge)}catch{}if(!m||!m.documentElement){m=re.createDocument(Le,"template",null);try{m.documentElement.innerHTML=Ct?ne:z}catch{}}const Z=m.body||m.documentElement;return l&&w&&Z.insertBefore(t.createTextNode(w),Z.childNodes[0]||null),Le===de?ve.call(m,u?"html":"body")[0]:u?m.documentElement:Z},hn=function(l){return le.call(l.ownerDocument||l,l,i.SHOW_ELEMENT|i.SHOW_COMMENT|i.SHOW_TEXT|i.SHOW_PROCESSING_INSTRUCTION|i.SHOW_CDATA_SECTION,null)},Mt=function(l){return l instanceof b&&(typeof l.nodeName!="string"||typeof l.textContent!="string"||typeof l.removeChild!="function"||!(l.attributes instanceof p)||typeof l.removeAttribute!="function"||typeof l.setAttribute!="function"||typeof l.namespaceURI!="string"||typeof l.insertBefore!="function"||typeof l.hasChildNodes!="function")},fn=function(l){return typeof o=="function"&&l instanceof o};function pe(y,l,m){ht(y,w=>{w.call(e,l,m,Ie)})}const gn=function(l){let m=null;if(pe(V.beforeSanitizeElements,l,null),Mt(l))return oe(l),!0;const w=W(l.nodeName);if(pe(V.uponSanitizeElement,l,{tagName:w,allowedTags:B}),k&&l.hasChildNodes()&&!fn(l.firstElementChild)&&K(/<[/\w!]/g,l.innerHTML)&&K(/<[/\w!]/g,l.textContent)||l.nodeType===Ke.progressingInstruction||k&&l.nodeType===Ke.comment&&K(/<[/\w]/g,l.data))return oe(l),!0;if(!B[w]||ye[w]){if(!ye[w]&&bn(w)&&(P.tagNameCheck instanceof RegExp&&K(P.tagNameCheck,w)||P.tagNameCheck instanceof Function&&P.tagNameCheck(w)))return!1;if(St&&!Me[w]){const z=q(l)||l.parentNode,Z=D(l)||l.childNodes;if(Z&&z){const G=Z.length;for(let te=G-1;te>=0;--te){const he=S(Z[te],!0);he.__removalCount=(l.__removalCount||0)+1,z.insertBefore(he,U(l))}}}return oe(l),!0}return l instanceof f&&!pr(l)||(w==="noscript"||w==="noembed"||w==="noframes")&&K(/<\/no(script|embed|frames)/i,l.innerHTML)?(oe(l),!0):(xe&&l.nodeType===Ke.text&&(m=l.textContent,ht([ze,$e,Ue],z=>{m=Ye(m,z," ")}),l.textContent!==m&&(Ve(e.removed,{element:l.cloneNode()}),l.textContent=m)),pe(V.afterSanitizeElements,l,null),!1)},mn=function(l,m,w){if(_e&&(m==="id"||m==="name")&&(w in t||w in dr))return!1;if(!(Fe&&!Te[m]&&K(_t,m))){if(!(at&&K(Be,m))){if(!H[m]||Te[m]){if(!(bn(l)&&(P.tagNameCheck instanceof RegExp&&K(P.tagNameCheck,l)||P.tagNameCheck instanceof Function&&P.tagNameCheck(l))&&(P.attributeNameCheck instanceof RegExp&&K(P.attributeNameCheck,m)||P.attributeNameCheck instanceof Function&&P.attributeNameCheck(m))||m==="is"&&P.allowCustomizedBuiltInElements&&(P.tagNameCheck instanceof RegExp&&K(P.tagNameCheck,w)||P.tagNameCheck instanceof Function&&P.tagNameCheck(w))))return!1}else if(!At[m]){if(!K(rt,Ye(w,nt,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&l!=="script"&&Bs(w,"data:")===0&&an[l])){if(!(lt&&!K(tt,Ye(w,nt,"")))){if(w)return!1}}}}}}return!0},bn=function(l){return l!=="annotation-xml"&&Rn(l,Et)},xn=function(l){pe(V.beforeSanitizeAttributes,l,null);const{attributes:m}=l;if(!m||Mt(l))return;const w={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:H,forceKeepAttr:void 0};let z=m.length;for(;z--;){const Z=m[z],{name:G,namespaceURI:te,value:he}=Z,We=W(G),Lt=he;let X=G==="value"?Lt:Fs(Lt);if(w.attrName=We,w.attrValue=X,w.keepAttr=!0,w.forceKeepAttr=void 0,pe(V.uponSanitizeAttribute,l,w),X=w.attrValue,rn&&(We==="id"||We==="name")&&(Ne(G,l),X=lr+X),k&&K(/((--!?|])>)|<\/(style|title)/i,X)){Ne(G,l);continue}if(w.forceKeepAttr)continue;if(!w.keepAttr){Ne(G,l);continue}if(!ot&&K(/\/>/i,X)){Ne(G,l);continue}xe&&ht([ze,$e,Ue],wn=>{X=Ye(X,wn," ")});const kn=W(l.nodeName);if(!mn(kn,We,X)){Ne(G,l);continue}if(O&&typeof A=="object"&&typeof A.getAttributeType=="function"&&!te)switch(A.getAttributeType(kn,We)){case"TrustedHTML":{X=O.createHTML(X);break}case"TrustedScriptURL":{X=O.createScriptURL(X);break}}if(X!==Lt)try{te?l.setAttributeNS(te,G,X):l.setAttribute(G,X),Mt(l)?oe(l):Cn(e.removed)}catch{Ne(G,l)}}pe(V.afterSanitizeAttributes,l,null)},hr=function y(l){let m=null;const w=hn(l);for(pe(V.beforeSanitizeShadowDOM,l,null);m=w.nextNode();)pe(V.uponSanitizeShadowNode,m,null),gn(m),xn(m),m.content instanceof a&&y(m.content);pe(V.afterSanitizeShadowDOM,l,null)};return e.sanitize=function(y){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,w=null,z=null,Z=null;if(Ct=!y,Ct&&(y="<!-->"),typeof y!="string"&&!fn(y))if(typeof y.toString=="function"){if(y=y.toString(),typeof y!="string")throw Ze("dirty is not a string, aborting")}else throw Ze("toString is not a function");if(!e.isSupported)return y;if(Y||Dt(l),e.removed=[],typeof y=="string"&&(He=!1),He){if(y.nodeName){const he=W(y.nodeName);if(!B[he]||ye[he])throw Ze("root node is forbidden and cannot be sanitized in-place")}}else if(y instanceof o)m=pn("<!---->"),w=m.ownerDocument.importNode(y,!0),w.nodeType===Ke.element&&w.nodeName==="BODY"||w.nodeName==="HTML"?m=w:m.appendChild(w);else{if(!x&&!xe&&!u&&y.indexOf("<")===-1)return O&&Re?O.createHTML(y):y;if(m=pn(y),!m)return x?null:Re?ne:""}m&&$&&oe(m.firstChild);const G=hn(He?y:m);for(;z=G.nextNode();)gn(z),xn(z),z.content instanceof a&&hr(z.content);if(He)return y;if(x){if(ue)for(Z=Pe.call(m.ownerDocument);m.firstChild;)Z.appendChild(m.firstChild);else Z=m;return(H.shadowroot||H.shadowrootmode)&&(Z=Tt.call(r,Z,!0)),Z}let te=u?m.outerHTML:m.innerHTML;return u&&B["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&K(sr,m.ownerDocument.doctype.name)&&(te="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+te),xe&&ht([ze,$e,Ue],he=>{te=Ye(te,he," ")}),O&&Re?O.createHTML(te):te},e.setConfig=function(){let y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Dt(y),Y=!0},e.clearConfig=function(){Ie=null,Y=!1},e.isValidAttribute=function(y,l,m){Ie||Dt({});const w=W(y),z=W(l);return mn(w,z,m)},e.addHook=function(y,l){typeof l=="function"&&Ve(V[y],l)},e.removeHook=function(y,l){if(l!==void 0){const m=$s(V[y],l);return m===-1?void 0:Us(V[y],m,1)[0]}return Cn(V[y])},e.removeHooks=function(y){V[y]=[]},e.removeAllHooks=function(){V=On()},e}var na=ar();const ra={class:"flex-1"},sa={class:"flex items-center"},aa={class:"relative"},la={key:1,class:"bg-white border-b border-gray-200 py-0.5 px-0.5"},oa={key:0,class:"flex items-center gap-1 text-gray-500 text-xs"},ia={key:1,class:"flex flex-col gap-1"},ca=["onClick"],ua={key:2,class:"bg-white border-b border-gray-200 py-0.5 px-0.5"},da={class:"flex items-center justify-between"},pa={class:"flex gap-1"},ha={class:"px-0.5"},fa={class:"mb-1"},ga={key:0},ma={key:1,class:"text-gray-500"},ba={class:"flex items-center justify-between mt-0.5"},xa={class:"flex items-center gap-1"},ka={class:"flex flex-wrap gap-1"},wa=["onClick"],va={class:"flex-1 overflow-y-auto overflow-x-hidden p-0.5 space-y-0.5 w-full"},ya={key:0,class:"flex items-start gap-1.5 max-w-full"},Ta={class:"flex-1 bg-white rounded-lg p-2 shadow-sm break-words"},_a={class:"text-gray-800 leading-tight text-[15px]"},Ea=["innerHTML"],Sa={key:1,class:"h-full flex items-center justify-center text-gray-500 text-[15px]"},Aa={class:"flex items-center justify-between"},Ca={class:"text-sm"},Ra={class:"border-t border-gray-200 bg-white w-full"},Da={class:"p-0.5 max-w-full"},Ma=["placeholder","disabled"],La={class:"flex items-center justify-between"},Ia={class:"flex items-center gap-1"},Na={class:"relative model-selector"},Oa={class:"max-h-[60vh] overflow-y-auto"},Pa=["onClick"],za={class:"flex items-center gap-2"},$a=["disabled"],Ua={class:"flex-1 overflow-y-auto p-4"},Ba={class:"flex-1 min-w-0"},Fa={class:"flex items-center gap-2"},Ha={class:"font-medium text-gray-900 truncate"},Ga={class:"text-xs text-gray-400"},Wa={class:"text-xs text-gray-500 break-all"},ja=["onClick"],qa={key:0,class:"text-center text-gray-500 py-8"},Va={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ya={class:"add-model-modal bg-white rounded-lg shadow-lg w-96 flex flex-col"},Za={class:"p-4 space-y-4"},Xa={key:0,class:"mt-4"},Ka={class:"flex items-center gap-2"},Qa={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},Ja={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},el={class:"p-4 border-t border-gray-200 flex gap-2"},tl=["disabled"],nl={key:0,class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},rl=jt({__name:"Main",setup(n){const e=Gn(),t=Fn(),r=wt(),s=qt(),a=Bn(),c=Pn(),o=Hn(),f=I(!1),i=I(!1),p=I(null),b=I(""),g=I({entryDisplayConfig:{skipJsCss:!0,excludedPathPatterns:[]}}),A=I(!1),E=I(""),S=I(!1),C=I(!1),U=I(!1),D=I({showName:"",baseUrl:"",modelName:"",apiKey:""}),q=I(null),O=I(!1);let ne=null;const re=I(!0),le=I(!0),Pe=ce;let ve=0;const Tt=Oe(()=>!t.currentDatabase||t.currentDatabaseId===ce?"请选择数据源再对话":t.currentDatabase.dataId?"Type your message...":"请先录制程序数据然后再对话"),V=Oe(()=>!t.currentDatabase||!t.currentDatabase.dataId||s.status==="waiting"),ze=()=>{var k;return t.databases.length===1&&t.currentDatabaseId===ce?"当前无可用的数据源，请使用 Debug with XCodeMap 录制程序数据。":t.currentDatabaseId===ce?"请先选择数据源再对话":(k=t.currentDatabase)!=null&&k.dataId?"你好！我是你的代码排查助手 XCodeMap。请告诉我具体是哪一个函数调用（CallID_Number）让你感到困惑，或者提供相关的类名、函数名等信息，我将尽力为你分析和解释。":"请先录制程序数据然后再对话"},$e=async()=>{var u;const k=o.getText();if(k.trim()){if(!((u=t.currentDatabase)!=null&&u.dataId)){r.setError("当前没有数据，不可聊天。请先选择数据源。或者使用 Debug with XCodeMap 创建新的数据源。");return}if(!a.currentModel){r.setError("请先选择模型再发送消息"),S.value=!0;return}o.clearText(),await e.sendMessage(k)}},Ue=()=>{f.value=!f.value},_t=async k=>{var $;const u=t.currentDatabase,Y=!u||u.id!==k;if(ve=0,t.setCurrentDatabase(k),f.value=!1,Y){if(!await vn(k,(($=t.currentDatabase)==null?void 0:$.dataId)||"")){r.setError("Failed to switch process data");return}e.createNewChat(),p.value=null,re.value=!0,i.value=!0}},Be=async()=>{var k;if((k=t.currentDatabase)!=null&&k.dataId)try{const u=await Mr({processId:t.currentDatabase.dataId,first:re.value,filterText:b.value}),Y=JSON.stringify(u),$=JSON.stringify(p.value);Y!==$&&(console.log("Data has changed, updating treeData",Y),p.value=u),re.value=!1}catch(u){console.error("Failed to fetch tree data:",u)}},tt=async()=>{const k=[...t.databases],u=t.currentDatabase;await t.getDatabase();const Y=t.databases,$=t.currentDatabase,x=new Set(k.map(_e=>_e.id)),ue=Y.filter(_e=>!x.has(_e.id)),Re=k.length===0||k.length===1&&k[0].id===Pe;if(ue.length>0&&!Re&&r.setError("发现了新的数据源"),u&&$&&u.id===$.id&&u.dataId!==$.dataId&&!await vn($.id,$.dataId||"")){r.setError("Failed to switch process data");return}$&&$.id!==Pe&&($.serverSelected?ve=0:(ve++,ve>=3&&(console.log("Database consistency check failed 3 times, resetting to default"),t.setCurrentDatabase(Pe),f.value=!0,p.value=null,re.value=!0,i.value=!1,e.createNewChat(),ve=0)))};yr(()=>{Be()});const nt=k=>{k.labelKey==="url"&&k.labelValue&&window.open(k.labelValue,"_blank"),console.log("Clicked tree node:",k)},Et=k=>{const u=g.value.entryDisplayConfig.excludedPathPatterns.indexOf(k);u>-1&&(g.value.entryDisplayConfig.excludedPathPatterns.splice(u,1),Ce())},rt=()=>{A.value=!0,Lr(()=>{const k=document.querySelector(".input-new-tag input");k&&k.focus()})},B=()=>{E.value&&(g.value.entryDisplayConfig.excludedPathPatterns.push(E.value),Ce()),A.value=!1,E.value=""},Ce=async()=>{try{await Ir(g.value)||r.setError("Failed to update filter configuration")}catch(k){console.error("Failed to update filter configuration:",k)}},H=k=>{const u=k.target;u.closest(".model-selector")||(S.value=!1),u.closest(".model-manager-modal")||(C.value=!1)},st=async()=>{S.value=!1;try{await a.getModelConfigData(),C.value=!0}catch(k){console.error("Failed to open model manager:",k),r.setError("Failed to open model manager")}},P=()=>{C.value=!1},ye=()=>{U.value=!0,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""}},Te=()=>{U.value=!1,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""},q.value=null},at=async()=>{const k={showName:D.value.showName.trim(),baseUrl:D.value.baseUrl.trim(),modelName:D.value.modelName.trim(),apiKey:D.value.apiKey.trim()};if(!k.showName||!k.baseUrl||!k.modelName||!k.apiKey){r.setError("请填写所有必填字段");return}O.value=!0,q.value=null;const u={uuid:crypto.randomUUID(),showName:k.showName,baseUrl:k.baseUrl,modelName:k.modelName,apiKey:k.apiKey},Y=await Nr(u);if(!Y.success){q.value={success:!1,message:Y.error||"模型连通性测试失败"},O.value=!1;return}const $=await Or(u);$.success?(Te(),await a.getModelConfigData()):q.value={success:!1,message:$.error||"添加模型失败"},O.value=!1},Fe=async k=>{const u=await Pr(k);u.success?await a.getModelConfigData():r.setError(u.error||"删除模型失败")};Un(()=>{c.initTheme(),tt(),Be(),Tr().then(k=>{k.success&&k.data&&(g.value.entryDisplayConfig=k.data.entryDisplayConfig)}),a.getModelConfigData(),document.addEventListener("click",H),ne=window.setInterval(()=>{tt(),Be()},1e3)}),_r(()=>{ne!==null&&(clearInterval(ne),ne=null),document.removeEventListener("click",H),C.value=!1,U.value=!1});const lt=(k,u)=>u!=="tool"?k.replace(/`?CallID_(\d+)`?/g,"[CallID_$1](./findFunc?callId=$1)"):k,ot=k=>{M.setOptions({breaks:!0,gfm:!0,pedantic:!0});const u=M.parse(k);return na.sanitize(u,{ALLOWED_TAGS:["p","br","strong","em","code","pre","blockquote","ul","ol","li","h1","h2","h3","h4","h5","h6","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title","class"]})},xe=k=>{const u=k.target;u.style.height="auto",u.style.height=Math.min(u.scrollHeight,200)+"px"};return(k,u)=>{var Y,$;return v(),T("div",{class:_(["h-screen w-full flex flex-col overflow-x-hidden",h(c).theme==="dark"?"bg-[#2b2b2b]":"bg-[#ffffff]"])},[h(r).error?(v(),T("div",{key:0,class:_(["fixed top-0 left-0 right-0 px-4 py-3 z-[9999] flex items-center justify-between",h(c).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[d("span",ra,j(h(r).error.message),1),d("button",{onClick:u[0]||(u[0]=x=>h(r).clearError()),class:_(["ml-4 p-1 rounded-full hover:bg-opacity-20",h(c).theme==="dark"?"hover:bg-red-100":"hover:bg-red-200"])},u[18]||(u[18]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),2)],2)):F("",!0),d("div",{class:_(["border-b py-0.5 px-0.5 flex items-center justify-between",h(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[d("div",sa,[d("div",aa,[d("button",{onClick:Ue,class:_(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",h(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,j(h(t).currentDatabase?h(t).currentDatabase.name:"请选择数据源"),1),(v(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":f.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[19]||(u[19]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2)])]),d("button",{onClick:u[1]||(u[1]=()=>{h(e).createNewChat(),le.value=!0}),class:_(["p-1 rounded-full",h(c).theme==="dark"?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"]),title:"新建聊天"},u[20]||(u[20]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)],2),f.value||h(t).currentDatabaseId===h(ce)?(v(),T("div",la,[h(t).databases.length===1?(v(),T("div",oa,u[21]||(u[21]=[d("span",null,"当前无可用的数据源，请使用",-1),d("img",{src:Ur,alt:"XCodeMap Logo",class:"w-4 h-4"},null,-1),d("span",null,"Debug with XCodeMap 录制程序数据。",-1)]))):(v(),T("div",ia,[(v(!0),T(ge,null,we(h(t).databases,x=>(v(),T("button",{key:x.id,onClick:ue=>_t(x.id),class:_(["w-full px-2 py-0.5 rounded-lg text-xs font-medium text-left border-2 transition-all duration-150 focus:outline-none",[x.id===h(t).currentDatabaseId?h(c).theme==="dark"?"button-selected-dark":"button-selected-light":h(c).theme==="dark"?"button-hover-dark":"button-hover-light"]])},j(x.name),11,ca))),128))]))])):F("",!0),h(t).currentDatabase&&h(t).currentDatabase.active?(v(),T("div",ua,[d("div",da,[d("span",{class:_(["px-1.5 py-0.5 text-xs rounded-full",{"bg-green-100 text-green-800":h(t).currentDatabase.recordState==="recording","bg-gray-100 text-gray-800":h(t).currentDatabase.recordState==="idle","bg-yellow-100 text-yellow-800":h(t).currentDatabase.recordState==="paused"}])},j(h(t).currentDatabase.recordState),3),d("div",pa,[h(t).currentDatabase.recordState==="idle"?(v(),T("button",{key:0,onClick:u[2]||(u[2]=x=>h(t).startRecord(h(t).currentDatabase.id)),class:"p-1 hover:bg-gray-100 rounded-full",title:"开始录制"},u[22]||(u[22]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z","clip-rule":"evenodd"})],-1)]))):F("",!0),h(t).currentDatabase.recordState==="recording"?(v(),T("button",{key:1,onClick:u[3]||(u[3]=x=>h(t).endRecord(h(t).currentDatabase.id)),class:"p-1 hover:bg-gray-100 rounded-full",title:"结束录制"},u[23]||(u[23]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z","clip-rule":"evenodd"})],-1)]))):F("",!0),h(t).currentDatabase.recordState==="paused"?(v(),T("button",{key:2,onClick:u[4]||(u[4]=x=>h(t).restartRecord(h(t).currentDatabase.id)),class:"p-1 hover:bg-gray-100 rounded-full",title:"重新录制"},u[24]||(u[24]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"})],-1)]))):F("",!0)])])])):F("",!0),h(t).currentDatabaseId!==h(ce)?(v(),T("div",{key:3,class:_([h(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200","border-b"])},[d("div",ha,[d("div",{onClick:u[5]||(u[5]=x=>i.value=!i.value),class:_(["flex items-center cursor-pointer rounded-lg px-1 py-0.5",h(c).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-100"])},[(v(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-3 w-3 transition-transform duration-200",{"transform rotate-90":i.value,"text-gray-400 hover:text-gray-300":h(c).theme==="dark","text-gray-600 hover:text-gray-800":h(c).theme==="light"}]),viewBox:"0 0 20 20",fill:"currentColor"},u[25]||(u[25]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),d("span",{class:_(["text-xs font-medium px-1",h(c).theme==="dark"?"text-gray-200":"text-gray-900"])},"请求与线程入口",2)],2)]),i.value?(v(),T("div",{key:0,class:_(["p-1",h(c).theme==="dark"?"border-t border-gray-700":"border-t border-gray-200"])},[d("div",fa,[ke(d("input",{"onUpdate:modelValue":u[6]||(u[6]=x=>b.value=x),type:"text",placeholder:"搜索网络请求...",class:_(["w-full px-2 py-0.5 rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",h(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400":"bg-white border-gray-300 text-gray-700"])},null,2),[[Ee,b.value]])]),p.value?(v(),T("div",ga,[Er(Kr,{nodes:p.value.rootNodes,onNodeClick:nt},null,8,["nodes"])])):(v(),T("div",ma,"Loading tree data...")),d("div",ba,[d("div",xa,[d("span",{class:_(["text-xs opacity-50",h(c).theme==="dark"?"text-gray-400":"text-gray-600"])},"排除请求：",2),d("label",{class:_(["flex items-center gap-1 text-xs cursor-pointer opacity-50 hover:opacity-100 transition-opacity",h(c).theme==="dark"?"text-gray-500":"text-gray-400"])},[ke(d("input",{type:"checkbox","onUpdate:modelValue":u[7]||(u[7]=x=>g.value.entryDisplayConfig.skipJsCss=x),onChange:Ce,class:_(["rounded focus:ring-0 focus:ring-offset-0 h-2.5 w-2.5 cursor-pointer",h(c).theme==="dark"?"bg-gray-800 border-gray-600 text-gray-500":"border-gray-300 text-gray-400"])},null,34),[[Sr,g.value.entryDisplayConfig.skipJsCss]]),u[26]||(u[26]=d("span",{class:"text-[11px]"},"忽略css/js",-1))],2),d("div",ka,[(v(!0),T(ge,null,we(g.value.entryDisplayConfig.excludedPathPatterns,x=>(v(),T("div",{key:x,class:_(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",h(c).theme==="dark"?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"])},[d("span",null,j(x),1),d("button",{onClick:ue=>Et(x),class:_(h(c).theme==="dark"?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700")},u[27]||(u[27]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,wa)],2))),128)),A.value?ke((v(),T("input",{key:0,"onUpdate:modelValue":u[8]||(u[8]=x=>E.value=x),class:_(["input-new-tag px-1 py-0 border rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",h(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200":"border-gray-300 text-gray-700"]),placeholder:"输入排除表达式，* 匹配一级路径，** 匹配多级路径",onKeyup:Ar(B,["enter"]),onBlur:B},null,34)),[[Ee,E.value]]):(v(),T("button",{key:1,onClick:rt,class:_(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",h(c).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-blue-400":"bg-gray-100 hover:bg-gray-200 text-blue-700"])},u[28]||(u[28]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),d("span",null,"New Filter",-1)]),2))])]),d("button",{onClick:u[9]||(u[9]=x=>i.value=!1),class:_(["rounded-full flex items-center",h(c).theme==="dark"?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"]),title:"收起网络请求"},u[29]||(u[29]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),2)])],2)):F("",!0)],2)):F("",!0),d("div",va,[h(e).currentChat?(v(),T(ge,{key:0},[h(e).currentChat.messages.length===0?(v(),T("div",ya,[d("div",Ta,[d("p",_a,[(v(),Cr(Qr,{key:(Y=h(e).currentChat)==null?void 0:Y.id,text:ze(),speed:20,onComplete:u[10]||(u[10]=x=>le.value=!1)},null,8,["text"]))])])])):F("",!0),(v(!0),T(ge,null,we(h(e).currentChat.messages,x=>(v(),T("div",{key:x.messageId,class:_(["flex items-start gap-1.5 max-w-full",{"justify-end":x.role==="user"}])},[d("div",{class:_(["flex-1 rounded-lg p-2 shadow-sm break-words min-w-0",x.role==="user"?h(c).theme==="dark"?"bg-[#323232]":"bg-[#fcfaed]":h(c).theme==="dark"?"bg-[#2b2b2b]":"bg-[#ffffff]"])},[d("div",{class:_([x.role==="user"?h(c).theme==="dark"?"text-[#a9b7c5]":"text-[#080808]":h(c).theme==="dark"?"text-[#a9b7c5]":"text-[#080808]","leading-tight text-[15px] markdown-content break-words whitespace-pre-wrap"]),innerHTML:ot(lt(x.content,x.role))},null,10,Ea)],2)],2))),128))],64)):(v(),T("div",Sa," 请选择数据源后再对话 "))]),h(s).status==="waiting"&&h(t).currentDatabase&&h(t).currentDatabaseId!==h(ce)?(v(),T("div",{key:4,class:_(["border-t py-2 px-4",h(c).theme==="dark"?"bg-blue-900 border-blue-800 text-blue-200":"bg-blue-50 border-blue-200 text-blue-700"])},[d("div",Aa,[d("span",Ca,j(h(s).statusMessage||"未知状态..."),1)])],2)):F("",!0),d("div",Ra,[d("div",Da,[ke(d("textarea",{"onUpdate:modelValue":u[11]||(u[11]=x=>h(o).message=x),class:"input w-full resize-none mb-0.5",rows:"1",placeholder:Tt.value,disabled:V.value,onInput:xe,style:{"min-height":"28px","max-height":"200px","overflow-y":"auto"}},null,40,Ma),[[Ee,h(o).message]]),d("div",La,[d("div",Ia,[d("span",{class:_(["text-xs",h(c).theme==="dark"?"text-gray-400":"text-gray-500"])},"模型",2),d("div",Na,[d("button",{onClick:u[12]||(u[12]=x=>S.value=!S.value),class:_(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",h(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,j((($=h(a).currentModel)==null?void 0:$.showName)||"请选择模型"),1),(v(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":S.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[30]||(u[30]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2),S.value?(v(),T("div",{key:0,class:_(["absolute bottom-full left-0 mb-2 w-80 border rounded-lg shadow-lg z-50",h(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[d("div",{class:_(["p-2 border-b",h(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("span",{class:_(["text-sm font-medium",h(c).theme==="dark"?"text-gray-200":"text-gray-700"])},"选择模型",2)],2),d("div",Oa,[(v(!0),T(ge,null,we(h(a).availableModels,x=>(v(),T("button",{key:x.uuid,onClick:()=>{h(a).setCurrentModel(x.uuid),S.value=!1},class:_(["w-full px-3 py-2 text-left text-sm transition-all duration-150 border-2 focus:outline-none",{"button-selected-dark":h(a).currentModelUuid===x.uuid&&h(c).theme==="dark","button-selected-light":h(a).currentModelUuid===x.uuid&&h(c).theme==="light","button-hover-dark":h(c).theme==="dark"&&h(a).currentModelUuid!==x.uuid,"button-hover-light":h(c).theme==="light"&&h(a).currentModelUuid!==x.uuid}])},[d("div",za,[d("span",null,j(x.showName),1),d("span",{class:_(["text-xs opacity-75",[h(a).currentModelUuid===x.uuid?h(c).theme==="dark"?"text-gray-300":"text-blue-500":h(c).theme==="dark"?"text-gray-500":"text-gray-400"]])},"("+j(x.modelName)+")",3)])],10,Pa))),128)),h(a).availableModels.length===0?(v(),T("div",{key:0,class:_(["px-3 py-2 text-sm",h(c).theme==="dark"?"text-gray-400":"text-gray-500"])}," 暂无可用模型 ",2)):F("",!0)]),d("div",{class:_(["border-t",h(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("button",{onClick:st,class:_(["w-full px-3 py-2 text-left text-sm transition-colors",h(c).theme==="dark"?"text-blue-400 hover:bg-gray-700":"text-blue-600 hover:bg-blue-50"])}," 管理模型 ",2)],2)],2)):F("",!0)])]),d("button",{onClick:$e,class:_(["p-0.5 rounded-full transition-colors",[h(c).theme==="dark"?"hover:bg-gray-700 text-gray-400 hover:text-gray-200":"hover:bg-gray-100 text-gray-600",{"opacity-50 cursor-not-allowed":!h(o).message.trim()||!h(t).currentDatabase||!h(t).currentDatabase.dataId||h(s).status==="waiting"}]]),disabled:!h(o).message.trim()||!h(t).currentDatabase||!h(t).currentDatabase.dataId||h(s).status==="waiting",title:"发送消息"},u[31]||(u[31]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),10,$a)])])]),C.value?(v(),T("div",{key:5,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:P},[d("div",{class:"model-manager-modal bg-white rounded-lg shadow-lg w-96 max-h-[80vh] flex flex-col",onClick:u[13]||(u[13]=Dr(()=>{},["stop"]))},[d("div",{class:"p-2 border-b border-gray-200 flex items-center justify-between"},[u[33]||(u[33]=d("h3",{class:"text-base font-medium text-gray-900"},"模型管理",-1)),d("button",{onClick:P,class:"text-gray-400 hover:text-gray-600"},u[32]||(u[32]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",Ua,[(v(!0),T(ge,null,we(h(a).availableModels,x=>(v(),T("div",{key:x.uuid,class:"flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-1"},[d("div",Ba,[d("div",Fa,[d("span",Ha,j(x.showName),1),d("span",Ga,"("+j(x.modelName)+")",1)]),d("div",Wa,j(x.baseUrl),1)]),d("button",{onClick:ue=>Fe(x),class:"ml-2 flex-shrink-0 text-red-500 hover:text-red-700",title:"删除模型"},u[34]||(u[34]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,ja)]))),128)),h(a).availableModels.length===0?(v(),T("div",qa," 暂无已添加的模型 ")):F("",!0)]),d("div",{class:"p-4 border-t border-gray-200"},[d("button",{onClick:ye,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"},u[35]||(u[35]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Rr(" 添加新模型 ")]))])])])):F("",!0),U.value?(v(),T("div",Va,[d("div",Ya,[d("div",{class:"p-4 border-b border-gray-200 flex items-center justify-between"},[u[37]||(u[37]=d("h3",{class:"text-lg font-medium text-gray-900"},"添加新模型",-1)),d("button",{onClick:Te,class:"text-gray-400 hover:text-gray-600"},u[36]||(u[36]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",Za,[d("div",null,[u[38]||(u[38]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型显示名称",-1)),ke(d("input",{"onUpdate:modelValue":u[14]||(u[14]=x=>D.value.showName=x),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入模型显示名称"},null,512),[[Ee,D.value.showName]]),u[39]||(u[39]=d("p",{class:"mt-1 text-xs text-gray-500"},"模型需要兼容OpenAI的API，需要支持 FunctionCall。",-1))]),d("div",null,[u[40]||(u[40]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Base URL",-1)),ke(d("input",{"onUpdate:modelValue":u[15]||(u[15]=x=>D.value.baseUrl=x),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: https://api.openai.com/v1"},null,512),[[Ee,D.value.baseUrl]]),u[41]||(u[41]=d("p",{class:"mt-1 text-xs text-gray-500"},"例如: https://dashscope.aliyuncs.com/compatible-mode/v1",-1))]),d("div",null,[u[42]||(u[42]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型名称",-1)),ke(d("input",{"onUpdate:modelValue":u[16]||(u[16]=x=>D.value.modelName=x),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: gpt-3.5-turbo"},null,512),[[Ee,D.value.modelName]]),u[43]||(u[43]=d("p",{class:"mt-1 text-xs text-gray-500"},"国内推荐阿里云qwen-max-latest 或者 deepseek-chat，国外推荐 Claude Sonnet 系列",-1))]),d("div",null,[u[44]||(u[44]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"API Key",-1)),ke(d("input",{"onUpdate:modelValue":u[17]||(u[17]=x=>D.value.apiKey=x),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入API Key"},null,512),[[Ee,D.value.apiKey]])]),q.value?(v(),T("div",Xa,[d("div",{class:_(["p-3 rounded-lg",{"bg-green-50 text-green-700":q.value.success,"bg-red-50 text-red-700":!q.value.success}])},[d("div",Ka,[q.value.success?(v(),T("svg",Qa,u[45]||(u[45]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(v(),T("svg",Ja,u[46]||(u[46]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 00-1.414-1.414L10 10l-1.293-1.293z","clip-rule":"evenodd"},null,-1)]))),d("span",null,j(q.value.message),1)])],2)])):F("",!0)]),d("div",el,[d("button",{onClick:Te,class:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),d("button",{onClick:at,class:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",disabled:O.value},[O.value?(v(),T("svg",nl,u[47]||(u[47]=[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):F("",!0),d("span",null,j(O.value?"测试中...":"保存"),1)],8,tl)])])])):F("",!0)],2)}}}),sl=$n(rl,[["__scopeId","data-v-8c6da11e"]]),nn=zr(sl);nn.use($r());nn.mount("#app");window.$vm=nn._instance;
