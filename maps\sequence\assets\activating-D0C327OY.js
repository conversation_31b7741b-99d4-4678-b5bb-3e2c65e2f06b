import{i as D,u as I,r as c,c as L,v as P,z as E,j as r,o as a,l as e,n as b,q as i,t as g,m as v,C as j,D as M,_ as $,S as F,T as R}from"./style-CyfHChcX.js";import{u as q}from"./activation-C4HdN75D.js";const z={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},B={class:"max-w-3xl mx-auto"},V={class:"text-center mb-8"},X={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},N={class:"ml-11"},O={key:0,class:"space-y-4"},W={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600"},G={class:"flex items-center justify-between"},H={class:"text-sm font-mono break-all flex-1 mr-3 text-gray-900 dark:text-gray-100 font-medium"},J={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},K={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},Q={class:"ml-11"},Y={key:0,class:"space-y-4"},Z={key:0,class:"text-center"},ee={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},te={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},se={class:"ml-11"},oe={key:0,class:"space-y-4"},re={class:"space-y-4"},ae=["disabled"],ie=["disabled"],ne={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},de={class:"space-y-4"},le={key:0,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},ce={key:1,class:"bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg"},ue={key:0,class:"text-sm mt-2"},ge={key:2,class:"bg-blue-100 dark:bg-blue-900 border border-blue-400 text-blue-700 dark:text-blue-200 px-4 py-3 rounded-lg"},ve=D({__name:"Activating",setup(pe){const m=I(),s=q(),d=c(""),n=c(""),u=c(!1),l=c(null),x=c(!1),y=c(1),_=L(()=>s.getCurrentStep()==="idle"||s.getCurrentStep()==="generating_device_code"?1:s.getCurrentStep()==="device_code_generated"&&!u.value?2:u.value?3:1),p=()=>{y.value=_.value};P(()=>{m.initTheme(),k(),w(),p()}),E(()=>{});const k=()=>{const t=new URLSearchParams(window.location.search).get("deviceCode");t&&(l.value=t,console.log("Device code found in URL parameters:",t))},w=async()=>{try{if(l.value)s.setDeviceCode(l.value),s.setStep("device_code_generated"),n.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(l.value)}`,u.value=!0,p(),f();else{await s.generateDeviceCode();const o=s.getDeviceCode();o&&(n.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(o)}`,u.value=!0,p(),f())}}catch(o){console.error("Failed to start activation process:",o)}},f=()=>{},C=async()=>{try{await navigator.clipboard.writeText(n.value),x.value=!0,setTimeout(()=>{x.value=!1},2e3),console.log("Activation link copied to clipboard")}catch(o){console.error("Failed to copy activation link:",o);const t=`clipboard://${n.value}`;console.log("Using clipboard protocol:",t),window.open(t,"_blank")}},S=()=>{n.value&&window.open(n.value,"_blank")},A=async()=>{if(d.value.trim())try{if(l.value){const o=`clipboard://xcm_fin_ack://${d.value.trim()}`;console.log("Using clipboard protocol:",o),window.open(o,"_blank"),s.setStep("finishing_activation")}else{const o=await s.finishActivationProcess(d.value.trim());if(console.log("Activation completed successfully, expired timestamp:",o),o){const t=new Date(o);console.log("License expires at:",t.toLocaleString())}}}catch(o){console.error("Failed to finish activation:",o)}},U=()=>{switch(s.getCurrentStep()){case"idle":return"准备开始激活流程...";case"generating_device_code":return"正在生成设备码...";case"device_code_generated":return"设备码生成成功，请获取激活码";case"finishing_activation":return"正在完成激活...";case"activated":return"激活成功！";case"error":return`激活失败: ${s.getError()}`;default:return"未知状态"}};return setInterval(()=>{p()},1e3),(o,t)=>(a(),r("div",{class:b(["h-screen w-full flex flex-col overflow-x-hidden",(i(m).theme==="dark","bg-[var(--bg-color)]")])},[e("div",z,[e("div",B,[e("div",V,[e("h1",{class:b(["text-3xl font-bold mb-4",i(m).theme==="dark"?"text-white":"text-gray-900"])}," XCodeMap 激活 ",2),e("p",{class:b(["text-lg",i(m).theme==="dark"?"text-gray-300":"text-gray-600"])},g(U()),3)]),e("div",X,[t[2]||(t[2]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 1 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第一步 生成激活链接 ")],-1)),e("div",N,[n.value?(a(),r("div",O,[e("div",W,[e("div",G,[e("code",H,g(n.value),1)])]),t[1]||(t[1]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," ✓ 激活链接已生成完成 ",-1))])):(a(),r("div",J," 正在生成您的专属激活链接... "))])]),e("div",K,[t[6]||(t[6]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 2 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第二步 访问链接生成激活码 ")],-1)),e("div",Q,[n.value?(a(),r("div",Y,[t[4]||(t[4]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," 请点击下方按钮访问链接，获取您的专属激活码 ",-1)),e("div",{class:"flex space-x-4"},[e("button",{onClick:S,class:"px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 访问链接 "),e("button",{onClick:C,class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 复制链接 ")]),x.value?(a(),r("div",Z,t[3]||(t[3]=[e("p",{class:"text-sm text-green-600 dark:text-green-400 font-medium"}," ✓ 链接已复制到剪贴板 ",-1)]))):v("",!0),t[5]||(t[5]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," 如果无法直接访问，请复制链接到能联网访问的浏览器打开 ",-1))])):(a(),r("div",ee," 等待激活链接生成... "))])]),e("div",te,[t[8]||(t[8]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 3 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第三步 输入激活码，完成激活 ")],-1)),e("div",se,[u.value?(a(),r("div",oe,[t[7]||(t[7]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," 请输入从XCodeMap官网 https://xcodemap.tech 获得的激活码 ",-1)),e("div",re,[j(e("textarea",{"onUpdate:modelValue":t[0]||(t[0]=T=>d.value=T),placeholder:"请输入激活码",rows:"4",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none",disabled:i(s).getCurrentStep()==="finishing_activation"},null,8,ae),[[M,d.value]]),e("button",{onClick:A,disabled:!d.value.trim()||i(s).getCurrentStep()==="finishing_activation",class:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-lg"},g(i(s).getCurrentStep()==="finishing_activation"?"激活中...":"完成激活"),9,ie)])])):(a(),r("div",ne," 等待前两步完成后，将显示激活码输入框 "))])]),e("div",de,[i(s).hasError()?(a(),r("div",le,g(i(s).getError()),1)):v("",!0),i(s).isActivated()?(a(),r("div",ce,[t[9]||(t[9]=e("div",{class:"font-medium"},"激活成功！您现在可以使用 XCodeMap 了。",-1)),i(s).getExpiredTimestamp()?(a(),r("div",ue," 许可证过期时间："+g(new Date(i(s).getExpiredTimestamp()).toLocaleString()),1)):v("",!0)])):v("",!0),l.value&&i(s).getCurrentStep()==="finishing_activation"?(a(),r("div",ge,t[10]||(t[10]=[e("div",{class:"font-medium"},"激活中...请等待成功通知。",-1)]))):v("",!0)])])])],2))}}),me=$(ve,[["__scopeId","data-v-2db69ca4"]]),h=F(me);h.use(R());h.mount("#app");
