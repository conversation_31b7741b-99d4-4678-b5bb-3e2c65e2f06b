<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>XCodeMap Detail</title>
    <link href="../assets/element-ui-2.15.14/index.min.css" rel="stylesheet">
    <link href="../assets/xcodemap/theme.css" rel="stylesheet">
    <link href="../assets/xcodemap/detail.css" rel="stylesheet">
    <script src="../assets/xcodemap/common.js"></script>
    <script>
    </script>
</head>
<body>
<div id="app">
    <el-container>
        <el-aside  :style="{ width: leftAside.asideWidth + 'px' }" class="left-aside">
            <div class="left-container">
                <el-scrollbar ref="scrollMenuLeftRef">
                    <div class="goto-button-wrap">
                        <el-tabs type="card" v-model="graph.detailTabName"
                                 @tab-click="graph.handleDetailTabClick">
                            <!--<el-tab-pane label="堆栈" name="stacks">
                            </el-tab-pane>-->
                            <el-tab-pane label="参数" name="args">
                            </el-tab-pane>

                        </el-tabs>
                        <div class="stepBar">
                            <!-- <div class="searchBarMenu " onclick="vue.graph.gotoCurrentDefAndFocus()" data-tooltip="查看当前函数的定义" >
                                <img src="../assets/xcodemap/icons/locate.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu " onclick="vue.graph.searchCurrentStack()" data-tooltip="在序列图中显示堆栈">
                                <img src="../assets/xcodemap/icons/CallStack_GrayDark.svg" class="x-icon">
                            </div> -->
                            <!--<div class="searchBarMenu " onclick="vue.graph.stepBackInPopup()" data-tooltip="Step Back (Shift+F8)">
                                <img src="../assets/xcodemap/icons/stepBack.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu" onclick="vue.graph.stepOverInPopup()" data-tooltip="Step Over (F8)">
                                <img src="../assets/xcodemap/icons/stepOver.svg" class="x-icon">
                            </div>-->
                            <div class="searchBarMenu" onclick="vue.graph.stepIntoInPopup()" data-tooltip="Step Into (F7)">
                                <img src="../assets/xcodemap/icons/stepInto.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu" onclick="vue.graph.stepOutInPopup()" data-tooltip="Step Out (Shift+F7)">
                                <img src="../assets/xcodemap/icons/stepOut.svg" class="x-icon">
                            </div>
                        </div>
                    </div>
                    <!--<div v-show="graph.detailTabName === 'stacks' " class="left-tab-content-stacks">
                        <div :key="index"  v-for="(item, index) in graph.functionCallStackData.stacks"
                             :class="{'left-tab-content-stacks-item-normal': !item.inPackage && !item.current,
                                    'left-tab-content-stacks-item-package': item.inPackage && !item.current,
                                    'left-tab-content-stacks-item-current': item.current}" class="left-tab-content-stacks-item" @click="vue.graph.clickStackItem(item)">
                            {{item.methodName}}:{{item.codeLine}},&nbsp;{{item.simpleClassName}}&nbsp;({{item.packageName}})
                        </div>
                    </div>-->
                    <div v-show="graph.detailTabName === 'args' ">
                        <el-divider></el-divider>
                        <div class="func-notes">
                            <div class="func-notes-head">
                                <span>{{graph.currFuncCallDetail.notesHead}}</span>
                                <div class="func-notes-head-button">
                                    <el-button size="mini" @click="handleViewLoop">查看循环</el-button>
                                    <el-button size="mini" @click="handleAddToChat">加入聊天</el-button>
                                </div>
                            </div>
                            <div class="func-notes-signature">{{graph.currFuncCallDetail.signature}}</div>
                            <el-input
                                placeholder="输入关键字进行过滤"
                                v-model="graph.functionCallDetailTree.filterText"
                                class="filter-input">
                            </el-input>
                            <el-tree
                                    :data="graph.functionCallDetailTree.treeData.rootNodes"
                                    :highlight-current="true"
                                    :indent="0"
                                    :props="graph.functionCallDetailTree.defaultProps"
                                    class="function-call-detail-tree"
                                    :default-expanded-keys="graph.functionCallDetailTree.treeData.expandedKeys"
                                    node-key="nodeKey"
                                    :render-content="graph.functionCallDetailTree.renderContent"
                                    @node-expand="graph.clickFuncTreeElement"
                                    @node-click="graph.clickFuncTreeElement"
                                    @node-contextmenu="rightClickFunctionCallDetailTree"
                                    ref="functionCallDetailTree">
                            </el-tree>
                            <div class="func-notes-body">{{graph.currFuncCallDetail.notesDetail}}</div>
                        </div>
                        <el-divider></el-divider>
                        <div class="sql-requests">
                            <div class="sql-requests-head"
                                 @click="graph.sqlRequestsCollapsed = !graph.sqlRequestsCollapsed; if (!graph.sqlRequestsCollapsed) { graph.loadInnerOutputRequestDetailsIfNeeded(); }">
                                <span class="sql-requests-title">SQL 请求 ({{ graph.sqlRequestsCount }})</span>
                                <el-button
                                    size="mini"
                                    type="text"
                                    class="sql-requests-toggle-btn"
                                    @click.stop="graph.sqlRequestsCollapsed = !graph.sqlRequestsCollapsed; if (!graph.sqlRequestsCollapsed) { graph.loadInnerOutputRequestDetailsIfNeeded(); }">
                                    <i :class="graph.sqlRequestsCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i>
                                </el-button>
                            </div>
                            <el-input
                                v-show="!graph.sqlRequestsCollapsed"
                                placeholder="输入关键字进行过滤"
                                v-model="graph.functionCallInnerOutputRequestsTree.filterText"
                                class="filter-input">
                            </el-input>
                            <el-tree
                                v-show="!graph.sqlRequestsCollapsed"
                                :data="graph.functionCallInnerOutputRequestsTree.treeData.rootNodes"
                                :highlight-current="true"
                                :indent="0"
                                :props="graph.functionCallInnerOutputRequestsTree.defaultProps"
                                class="function-call-detail-tree"
                                :default-expanded-keys="graph.functionCallInnerOutputRequestsTree.treeData.expandedKeys"
                                node-key="nodeKey"
                                :render-content="graph.functionCallInnerOutputRequestsTree.renderContent"
                                @node-expand="graph.clickFuncTreeElement"
                                @node-click="graph.clickFuncTreeElement"
                                @node-contextmenu="rightClickFunctionCallDetailTree"
                                ref="functionCallInnerOutputRequestsTree">
                            </el-tree>
                        </div>
                    </div>

                </el-scrollbar>
            </div>
        </el-aside>
    </el-container>
    <div class="custom-menu" id="custom-menu">
        <div :key="index" class="menu-item" v-for="(item, index) in graph.contextMenus">
            <div @click="graph.clickContextMenuItem(index)" class="menu-item-text">{{graph.contextMenus[index].desc}}</div>
        </div>
    </div>
    <div class="custom-tooltip" id="custom-tooltip">
    </div>
    <a id="graphDownloadStub" style="display: none"></a>
</div>
</body>

<script src="../assets/axios-1.6.3/axios.min.js"></script>
<!-- 导入 Mermaid 库 -->
<!--
<script src="../assets/mermaid-10.9.0/dist/mermaid.min.js"></script>
-->
<script src="../assets/vue-2.5.22/vue.min.js"></script>
<script src="../assets/element-ui-2.15.14/index.min.js"></script>
<script src="../assets/xcodemap/detail.graph.js"></script>

<script>
    // 按键状态标记
    let isMetaKeyPressed = false;
    // 注册按键按下事件监听器
    window.addEventListener('keydown', (event) => {
        isMetaKeyPressed = event.metaKey || event.ctrlKey;
        vue.graph.setMetaKeyPresses(isMetaKeyPressed)
        console.log("key down", event, isMetaKeyPressed)

        /*const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
        // 获取被按下的键和是否按下了控制键
        const key = event.key.toLowerCase();
        const ctrlOrCmdPressed = isMac ? event.metaKey : event.ctrlKey;*/
    });
    // 注册按键释放事件监听器
    window.addEventListener('keyup', (event) => {
        isMetaKeyPressed = false;
        vue.graph.setMetaKeyPresses(isMetaKeyPressed)
        console.log("key up", event, isMetaKeyPressed)
    });
    function rightClickFunctionCallDetailTree(event, data, node, other) {
        let effective = false;
        effective = vue.graph.initContextMenuForFuncTreeElement(event, data, node, other);
        if (effective) {
            setContextMenu(event)
        }
    }
    function adjustPopupBasedOnElement(el, menu) {
        const rect = el.getBoundingClientRect();
        if (window.innerHeight - rect.bottom >  menu.scrollHeight + 10) {
            menu.style.top = (rect.bottom + 10) + 'px';
        } else {
            let styleTop = window.innerHeight - menu.scrollHeight - 10;
            if (styleTop < 15) {
                styleTop = 15;
            }
            menu.style.top = styleTop + 'px';
        }
        if (window.innerWidth - rect.left > menu.scrollWidth + 5) {
            menu.style.left = (rect.left + 5) + 'px';
        } else {
            let styleLeft = window.innerWidth - menu.scrollWidth - 5;
            if (styleLeft < 15) {
                styleLeft = 15;
            }
            menu.style.left = styleLeft + 'px';
        }
    }
    function adjustContextMenu(e) {
        const menu = document.getElementById('custom-menu');
        console.log("context menu height:", menu.scrollHeight, window.innerHeight, e.pageY)
        console.log("context menu width:", menu.scrollWidth, window.innerWidth, e.pageX)
        if (window.innerHeight - e.pageY >  menu.scrollHeight + 15) {
            menu.style.top = (e.pageY + 15) + 'px';
        } else {
            let styleTop = window.innerHeight - menu.scrollHeight - 15;
            if (styleTop < 15) {
                styleTop = 15;
            }
            menu.style.top = styleTop + 'px';
        }
        if (window.innerWidth - e.pageX > menu.scrollWidth + 15) {
            menu.style.left = (e.pageX + 15) + 'px';
        } else {
            let styleLeft = window.innerWidth - menu.scrollWidth - 15;
            if (styleLeft < 15) {
                styleLeft = 15;
            }
            menu.style.left = styleLeft + 'px';
        }
    }
    function setContextMenu(e) {
        e.preventDefault();
        const menu = document.getElementById('custom-menu');
        menu.style.display = 'block';
        adjustContextMenu(e);
        document.addEventListener('click', function hideMenu() {
            menu.style.display = 'none';
            document.removeEventListener('click', hideMenu);
        });
        setTimeout(function () {
            adjustContextMenu(e)
        }, 100)
    }
    document.addEventListener('contextmenu', function (e) {
        console.log("Context menu", isMetaKeyPressed,  e)
        if (!isMetaKeyPressed) {
            const targetElement = e.target;
            const graphElement = targetElement.closest('#functionTraceGraph');
            let effective = false;
            if (graphElement != null) {
                effective = vue.graph.initContextMenusForGraphElement(targetElement)
            }
            if (effective) {
                setContextMenu(e)
            }
        }
        isMetaKeyPressed = false
        vue.graph.setMetaKeyPresses(isMetaKeyPressed)
    });

    function attachTitlePopover(el) {
        const tooltip = document.getElementById("custom-tooltip")
        el.addEventListener('mouseenter', function () {
            const titleText = el.getAttribute('data-tooltip');
            if (!titleText) return;
            el._tooltipTimer = setTimeout(function () {
                tooltip.innerText = titleText;
                tooltip.style.display = 'block';
                adjustPopupBasedOnElement(el, tooltip);
            }, 300);
        });
        el.addEventListener('mouseleave', function () {
            clearTimeout(el._tooltipTimer);
            tooltip.style.display = 'none';
        });
    }
    document.addEventListener('click', function (e) {
        const targetElement = e.target;
        const searchBarElement = targetElement.closest(".searchBar");
        if (searchBarElement == null) {
            {
                const graphElement = targetElement.closest('.search-tab');
                if (graphElement == null) {
                    vue.searchTab.show = false;
                }
            }
            {
                const graphElement = targetElement.closest('.setting-tab');
                if (graphElement == null) {
                    vue.settingTab.show = false;
                }
            }
        }
    })
    const revertWheelX = GetTempProject().revertWheelX;
    window.addEventListener("wheel", function (e) {
        if (revertWheelX === 'true') {
            const scrollMenu = vue.$refs.scrollMenuRef.wrap;
            // 判断事件是否发生在 scrollMenu 内部
            if (scrollMenu.contains(e.target)) {
                if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
                    // 在水平滚动时反转方向
                    e.preventDefault();
                    scrollMenu.scrollLeft +=  -e.deltaX;
                }
            }
        }
    }, {"passive": false})
</script>



<script>
    /*let tmpThemeVariables = {};
    if (theme === "light") {
        tmpThemeVariables = {
            darkMode: false,
            background: '#ffffff',
            lineColor: '#080808',
            primaryColor: '#ffffff',
            primaryTextColor: '#080808',
            actorBkg: "#edebfc",
            activationBkgColor: "#edebfc",
            actorBorder: "#edebfc"
        }
    } else {
        tmpThemeVariables = {
            darkMode: true,
            background: '#2b2b2b',
            lineColor: '#a9b7c5',
            primaryColor: '#2b2b2b',
            primaryTextColor: '#a9b7c5',
            actorBkg: "#344134",
            activationBkgColor: "#344134",
            actorBorder: "#344134"

        }
    }
    mermaid.initialize({
        "maxTextSize": 500000,
        "maxEdges": 5000,
        darkMode: true,
        fontSize: 24,
        sequence : {
            diagramMarginX:20,
            diagramMarginY:10,
            messageMargin:0,
            height: 40,
            width: 80,
            actorMargin: 10,
            wrap: false
        },
        theme: "base",
        themeVariables: tmpThemeVariables,
    })*/

    var vue = new Vue({
        el: '#app',
        data: function () {
            return {
                project: NewProject(),
                settingTab: NewSettingTab(),
                searchTab: NewSearchTab(),
                callGraph: NewGraph("functionTraceGraph"),
                graph: null,
                leftAside: NewAside("left", ""),
                rightAside: NewAside("right", "")
            }
        },
        created() {
            console.log("this", this)
            this.project.doInitProjectInfo();
            this.graph = this.callGraph;
            this.graph.setRoot(this.project.rootFunc);
            this.graph.currCallId = this.project.currCallId;
            this.graph.functionCallDetailTree.$refs = this.$refs;
            this.graph.functionCallDetailTree.$refsName = "functionCallDetailTree";
            this.graph.functionCallDetailTree.vue = this;
            this.graph.functionCallInnerOutputRequestsTree.$refs = this.$refs;
            this.graph.functionCallInnerOutputRequestsTree.$refsName = "functionCallInnerOutputRequestsTree";
            this.graph.functionCallInnerOutputRequestsTree.vue = this;
            document.addEventListener("mouseup", this.leftAside.mouseUp);
            document.addEventListener("mouseup", this.rightAside.mouseUp);
        },
        mounted() {
            const elements = document.querySelectorAll('.searchBarMenu');
            elements.forEach(el => {
                attachTitlePopover(el);
            });
        },
        destroyed() {
            document.removeEventListener("mouseup", this.leftAside.mouseUp);
            document.removeEventListener("mouseup", this.rightAside.mouseUp);
        },
        methods: {
            handleChangeScopeCommand(command) {
                this.searchTab.changeScope(command)
            },
            handleViewLoop() {
                const processId = this.project.processId;
                const threadId = this.project.threadId;
                const currCallId = this.graph.currCallId;
                let url = `./same_func.html?processId=${processId}&threadId=${threadId}&currCallId=${currCallId}&searchType=functionInSamePosition&searchId=${currCallId}`;
                if (this.project.navStateType) {
                    url += `&searchScope=${this.project.navStateType}`;
                }
                window.open(url, '_blank');
            },
            handleAddToChat() {
                const currCallId = this.graph.currCallId;
                const url = `./addToChat?currCallID=${currCallId}`;
                window.open(url, '_blank');
            }
        }
    })
    fixScrollClass();
    
    vue.$watch("graph.functionCallDetailTree.filterText", (newValue) => {
        vue.graph.functionCallDetailTree.setFilterText(newValue);
    });
    vue.$watch("graph.functionCallInnerOutputRequestsTree.filterText", (val) => {
        vue.graph.functionCallInnerOutputRequestsTree.setFilterText(val);
    });
    vue.leftAside.asideWidth = 500;
    vue.graph.startFuncDetail();
</script>

<style>
    #functionTraceGraph {
        padding-right: 10px;
    }
</style>

<style>
    .goto-button-wrap {
        margin-top: 5px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 95%;
        margin-left: 5px;
    }

    .goto-button {
        text-align: center;
        width: 32%;
    }

    .goto-button > .el-button {
        width: 100%;
        padding: 2px 2px;
        font-size: 14px;
    }
</style>
<style>
    .node-scroller {
        width: 32px; /* 设置组件宽度 */
        position: relative;
        height: 100vh;
    }

    .node-frames-container {
        overflow: scroll;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: calc(100% - 90px);
    }

    .node-frame {
        width: 100%;
        height: 24px; /* 设置每个框的高度 */
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #ccc;
        margin-bottom: 10px;
        /*background-color: gray;
        color: antiquewhite;*/
    }

    .node-frame:hover {
        cursor: pointer;
        color: #409EFF;
    }

    .node-scroll-button {
        width: 100%;
        height: 30px;
        cursor: pointer;
        text-align: center;
        /*background-color: gray;
        color: antiquewhite;*/
    }

    .node-scroll-button-up {
        top: 0;
    }
    .node-scroll-button-down {
        position: absolute;
        bottom: 0;
    }

</style>
<style>
    .custom-menu {
        display: none;
        position: absolute;
        z-index: 1000;
    }

    .custom-tooltip {
        display: none;
        position: absolute;
        transition: none;
        z-index: 1000;
    }

    .menu-item-text {
        cursor: pointer;
        margin: 10px 5px;
        line-height: 28px;
    }
</style>

<style>
    body {
        margin: 0px;
    }

    .el-scrollbar {
        height: 100vh;
    }

    .el-scrollbar__bar.is-vertical {
        width: 10px;
    }

    .el-scrollbar__bar.is-horizontal {
        height: 10px;
    }

    .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
        white-space: nowrap;
    }
</style>

<style>
    .el-tree-node {
        position: relative;
    }
    .el-tree-node::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        left: -3px;
        top: -26px;
        border-width: 1px;
        border-left: 1px dashed #52627c;
    }

    .el-tree-node:last-child::before {
        height: 38px;
    }
    .el-tree-node::after {
        content: '';
        width: 14px;
        height: 10px;
        position: absolute;
        left: -3px;
        top: 12px;
        border-width: 1px;
        border-top: 1px dashed #52627c;
    }
    .el-tree-node__label {
        white-space: normal;
        word-break: break-all;
    }
    .el-tree-node__children {
        padding-left: 10px;
    }

    .el-tree-node__content {
        height: inherit;
        padding-bottom: 5px;
    }
    .el-tree-node__content>.el-tree-node__expand-icon {
         padding: 1px;
    }
</style>

<style>
    .packageDeepBar {
        display: flex;
        flex-direction: row;
        position: absolute;
        right: 10px;
        top: 10px;
        /*
        top: 50%;
        */
        /*
        transform: translateY(-100%);
        */
        opacity: 0.8;
    }

    .packageDeepBarMenu:hover {
        cursor: pointer;
    }

    .packageDeepBarMenu {
        font-size: 20px;
        text-align: center;
        margin: 4px;
    }
</style>

<style>
    .controlBar {
        display: flex;
        flex-direction: row;
        position: absolute;
        right: 56px;
        width: 36px;
        /*
        top: 50%;
        */
        transform: translateY(-100%);
        opacity: 0.8;
    }

    .controlBarMenu:hover {
        cursor: pointer;
    }

    .controlBarMenu {
        font-size: 20px;
        text-align: center;
        margin: 4px;
    }

    .stepBar {
        display: flex;
        flex-direction: row;
        /*position: absolute;
        width: 36px;
        top: 28px;
        left: 10px;*/
        /*
        top: 50%;
        */
        /*transform: translateY(-100%);*/
        opacity: 1;
    }

    .searchBar {
        display: flex;
        flex-direction: column;
        position: absolute;
        width: 36px;
        top: 28px;
        right: 10px;
        /*
        top: 50%;
        */
        /*transform: translateY(-100%);*/
        opacity: 1;
    }
    .searchBarMenu:hover {
        cursor: pointer;
        color: #409EFF;
    }
    .searchBarMenu {
        font-size: 20px;
        text-align: center;
        margin: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2px;
    }
</style>

<style>

    .filter-button-wrap {
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 95%;
        margin-left: 5px;
    }

    .filter-button {
        text-align: center;
        width: 49%;
    }

    .filter-button > .el-button {
        width: 100%;
    }

    .el-input.is-disabled .el-input__inner {
        cursor: auto;
    }

    .included-filter-body .el-tag {
        font-size: 14px;
        white-space: normal;
        word-break: break-all;
        height: 100%;
    }

    .filterBarMenuChat {
        position: absolute;
        right: 16px;
    }

    .filterBarMenuChat:hover {
        cursor: pointer;
    }

    .var-exp-op {
            width: 220px;
            margin: 0px 2px;
        }

        .var-exp-name { display: inline-block; }

        .var-op-value { display: inline-block; }

    .filter-detail-list {
            width: 95%;
            margin-left: 5px;
        }

    .included-filter-label {
        height: 32px;
        padding: 0 10px;
        line-height: 30px;
        font-size: 12px;
        margin-top: 10px;
    }

    .included-filter-body .el-input__inner {
        text-align: center;
        padding: 0 0px;
    }

    .included-filter-body {
        margin-top: 4px;
        display: flex;
    }

    .included-filter-notes {
        font-style: italic;
        font-size: small;
    }

    .filterBarMenu:hover {
        cursor: pointer;
    }

    .filterBarMenu {
        display: inline-block;
    }

    .filterBarMenuMinus {
        height: 32px;
        line-height: 32px;
        padding: 4px;
    }

    .filterBarMenuPlus {
        height: 24px;
        line-height: 22px;
        padding: 4px;
        border-width: 1px;
    }

    .packageDeepBarInFilterTab {
        display: flex;
        flex-direction: row;
    }

    .packageDeepBarInFilterButton:hover {
        cursor: pointer;
    }

    .packageDeepBarInFilterMenu {
        /*
        font-size: 20px;
        */
        text-align: center;
        margin: 4px;
        height: 24px;
        line-height: 22px;
    }
</style>

<style>
    /* all tabs related*/
    .graph-popup-content  .el-scrollbar {
        position: absolute;
        top: 1%;
        left: 15%;
        /* transform: translate(-50%, -50%); */
        width: 70%;
        height: calc(99% - 10px);
        /*background-color: #45494a;*/
        z-index: 1;
    }
    .search-tab-header >.el-tabs__header {
        /*background-color: #45494a;*/
        border-bottom: 1px solid #272626;
        margin: 0;
    }
    .search-tab-header>.el-tabs__header .el-tabs__item.is-active {
        /*color: #409EFF;
        background-color: #5d5b5b;*/
        border-right-color: #DCDFE6;
        border-left-color: #DCDFE6;
    }

    .search-result-item { display: block; }
    .search-result-item:hover {
        /*color: #409EFF;*/
        cursor: pointer;
    }

    .el-tabs--border-card {
         border: 0px solid #DCDFE6;
    }

    .el-tabs--border-card>.el-tabs__content {
         padding: 0px;
    }

    .el-tabs__content {
        padding: 0px;
    }
    .el-tabs {
        padding: 0 0px;
    }

    .el-tabs__nav-wrap {
        margin-left: 0px;
    }

    .el-tabs__header {
        margin: 0 0 24px;
    }
    .el-tabs__item.is-left, .el-tabs__item.is-right {
        width: 25px;
        height: 100px;
        padding: 0;
        margin: 0;
        writing-mode: vertical-lr; /* 将文字垂直显示，从左到右
        line-height: 25px;
    }

    .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
        text-align: center; /* 文字居中 */
    }

    .el-tabs--right.el-tabs--card .el-tabs__item.is-right {
        text-align: center; /* 文字居中 */
    }
    .el-tabs__item.is-active {
        opacity: 1;
    }
    .el-tabs__header {
        margin: 0;
    }
    .goto-button-wrap .el-tabs__item {
        height: 30px;
        line-height: 30px;
    }
</style>

<style>
    #functionTraceGraphDetail {
        /*margin-top: 12px;*/
        margin-bottom: 32px;
    }

    .el-aside, .el-main, .el-header {
        height: 100%;
    }

    .el-container {
        width: 100%;
    }

    .el-header {
        padding: 0 32px;
        display: flex;
        /*
        justify-content: center; !* Center content horizontally *!
        */
        align-items: center; /* Center content vertically */
        height: 30px;
        margin-bottom: 5px;
    }

    .el-main {
        padding: 0px;
    }



    .el-breadcrumb {
        /*
        margin-top: 15px;
        */
        margin-left: 0px;
        font-size: 16px;
    }



    .el-table {
        margin-top: 24px;
    }



    .el-header, .el-tab-pane {
        white-space: pre-line;
    }

    .el-menu-item > a, .el-menu-item > * > a, .el-menu-item > * > * > a, .el-menu-item > * > * > * > a {
        text-decoration: none; /* 去掉下划线 */
    }

    .el-button > a, .el-button > * > a, .el-button > * > * > a, .el-button > * > * > * > a {
        text-decoration: none;
    }

    .codeCanvasDiv {
        height: 100%;
        width: 100%;
        overflow: auto;
    }

    .codeCanvasPre {
        width: 100%;
        overflow: auto;
    }

    .flowCanvasDiv {
        height: 100%;
        width: 550px;
        overflow: auto;
    }

    .left-aside {
        display: block;
        margin-right: 5px;
        justify-content: flex-end;
    }

    .left-tab-content {
        /* width: calc(100% - 25px); */
        margin-top: 5px;
        overflow-wrap: break-word;
    }

    .left-tab-content-stacks {
        margin-top: 5px;
        overflow-wrap: break-word;
        margin-left: 5px;
    }

    .left-tab-content-stacks-item {
        margin-bottom: 2px;
    }


    .right-aside {
        display: block;
        margin-left: 5px;
        justify-content: flex-end;
    }

    .right-tab-content {
        /* width: calc(100% - 25px); */
        margin-top: 24px;
        overflow-wrap: break-word;
    }

    .usageListSelectDiv {
        width: 270px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .focus-on-token {
        text-decoration: underline;
    }



    .x-handle {
        width: 3px;
        cursor: ew-resize;
        z-index: 10;
        background: #ccc;
    }



    .custom-tab-left {
        position: fixed;
        width: 25px;
        height: 100px;
    }

    .custom-tab-right {
        position: fixed;
        width: 25px;
        height: 100px;
    }


    .main-container {
        position: relative;
    }

    .left-container {
        position: relative;
        height: 100vh;
        /*
        margin-left: 25px;
        */
    }

    .right-container {
        position: relative;
        /*width: calc(100% - 25px);
        margin-right: 25px;*/
        height: 100vh;
    }

    .hide-icon-left {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: right;
        z-index: 2;
    }



    .hide-icon-right {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: left;
        z-index: 2;
    }





    .zdiv {
        margin: 0;
        border: 1px solid rgb(187, 186, 186);
        background: rgb(255, 255, 255);
        z-index: 1000;
        position: absolute;
        list-style-type: none;
        padding: 5px;
        border-radius: 7px;
        font-size: 12px;
    }

    .zdiv li {
        margin: 0;
        padding: 5px;
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .zdiv li:hover {
        background: #e1e1e1;
        cursor: pointer;
    }

    text.actor:hover, text.messageText:hover {
        cursor: pointer;
    }

    .usageTraceDetail {
        margin-bottom: 2px;
    }

    .usageTraceDetail:hover {
        cursor: pointer;
    }


    .code-content-widget-detail-parent {
        max-width: 600px;
    }

    .code-content-widget-detail-header {
        font-size: larger;
    }

    .code-content-widget-detail-header::after {
        clear: both;
        content: '';
        display: table;
    }

    .code-content-widget-detail-header div:first-child {
        float: left;
    }

    .code-content-widget-detail-header div:nth-child(2) {
        float: right;
    }

    .code-content-widget-detail-header div i {
        margin-left: 5px;
        font-size: larger;
    }

    .code-content-widget-detail-header div i:hover {
        cursor: pointer;
    }

    .code-content-widget-detail-desc {
        /*padding-top: 10px;*/
        padding-bottom: 32px
    }

    .code-content-widget-button-parent {
        height: 18px;
    }

    .code-content-widget-button {
        height: 18px;
        opacity: 0.7;
        margin-top: 18px;
    }

    .code-content-widget-button:hover {
        cursor: pointer;
    }

    .editor-extra-class {
        margin-right: 100px;
    }
</style>

<style>
    .el-divider--horizontal {
        margin: 2px 0;
    }
    .func-notes {
        padding-left: 12px;
        word-wrap: break-word;
        white-space: pre-wrap;
    }
    .func-notes-head {
        display: flex;
        justify-content: space-between;
        font-size: medium;
    }
    .func-notes-head-button .el-button {
        padding: 0px 6px;
        border-radius: 5px;
    }
    .func-notes-body {
        font-size: small;
    }
    .filter-input {
        margin-bottom: 2px;
    }
    .filter-input .el-input__inner {
        height: 28px;
    }
    
    .sql-requests {
        padding-left: 12px;
        word-wrap: break-word;
        white-space: pre-wrap;
    }
    .sql-requests-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: medium;
        cursor: pointer;
        padding-right: 6px;
    }
    .sql-requests-toggle-btn {
        padding: 0px 6px;
        border-radius: 5px;
        font-size: 12px;
    }
</style>

<script>
    function refreshData() {
        vue.graph.onNodeKeyChange();
    }
    function clickFunctionCodeByLink(psiLink) {
        vue.graph.clickFunctionCodeByLink(psiLink)
    }
    function refreshSearchParas(searchParasStr) {
        vue.project.doInitProjectInfoFromSearchParas(searchParasStr);
        if (vue.project.searchType !== null &&
            (vue.project.searchId !== null || vue.project.searchName != null)) {
            vue.graph.clearStatusForSearch();
            vue.graph.searchFunction({
                "type": vue.project.searchType,
                "name": vue.project.searchName,
                "id": vue.project.searchId,
                "scope": vue.project.searchScope
            }, true, 2)
        }
    }
    console.log("xcodemap-idea-plugin-ready");
</script>
</html>