<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
  <version>1.18.1</version>
  <idea-version since-build="222" />
  <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
  <id>com.adapgpt.xcodemap-idea</id>
  <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
  <name>XCodeMap</name>
  <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
  <vendor email="<EMAIL>" url="https://xcodemap.tech">https://xcodemap.tech</vendor>
  <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
  <description><![CDATA[XCodeMap is a runtime source code visualization tool designed specifically to tackle large projects.<br/>
    Please refer to <a href=https://xcodemap.tech>https://xcodemap.tech</a>.<br/><br/>

    B 站视频教程：<a href="https://space.bilibili.com/3546573562710135">https://space.bilibili.com/3546573562710135</a><br/><br/>
    XCodeMap 官网：<a href="https://xcodemap.tech">https://xcodemap.tech</a><br/><br/>

    XCodeMap 无需断点，随时查看每个函数的输入输出与堆栈<br/><br/>

    使用步骤：<br/>
    <strong>录制步骤：</strong><br/>
    1. 点击 Debug with XCodeMap 按钮<br/>
    2. 开始录制<br/>
    3. 触发业务请求<br/>
    4. 关闭录制<br/><br/>

    <strong>回放步骤：</strong><br/>
    1. 点击请求列表、或者点击行号栏 X 图标，以回放现场<br/>
    2. 鼠标悬停在函数名字上，查看每个函数的输入输出<br/>
    3. StepInto/StepOut/StepOver/StepBack 模拟程序的执行<br/>
    4. 单击（跳转调用处）/双击（跳转定义处）序列图上的函数名字可以与源码联动<br/>
    5. 点击序列图上的 +/- 可以展开/折叠函数，右上角的"浏览轨迹"按钮，可以修剪序列图<br/>
    6. “Services” 窗口中，关闭序列图，可以退出当前请求/线程的回放（此时仍可以看到行号栏 X 图标）<br/>
    7. 将数据源设置"待选择程序数据（取消行号 X 图标）"，可以退出当前程序数据的回放<br/><br/>

    <strong>高级技巧：</strong><br/>
    1. 在配置页面配置"默认随着 Debug 启动"，可以与 Jrebel 等热部署工具配合使用<br/>
    2. XCodeMap 默认只录制项目包，如需录制其他包，请在配置页面配置<br/>
    3. 不录制时，XCodeMap 对CPU、内存占用极低，可以长期使用<br/>
    4. 尽量按需录制，单次录制时间不宜过长，避免内存占用过高<br/>
    5. 更多功能查看配置页面，或者关注 B 站视频教程<br/><br/>

    遇到问题，B 站私信 XCodeMap 总工。]]></description>
  <change-notes><![CDATA[1. 紧急发版，优化页面卡顿的问题 <br/>
    2. 优化序列图的默认展示（把Spring体系的 Controller、Service、Component 都展示出来） <br/>
    3. 右键对象，展示成 JSON 格式查看 <br/>
    4. 支持关闭 XCodeMap 的弹窗 <br/>]]></change-notes>
  <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
  <depends>com.intellij.modules.java</depends>
  <depends>com.intellij.modules.platform</depends>
  <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
  <extensions defaultExtensionNs="com.intellij">
    <toolWindow id="XCodeMap" secondary="false" icon="com.adapgpt.xcodemapidea.x.XCodeMapIcons.LOGO" anchor="right" canCloseContents="true" factoryClass="com.adapgpt.xcodemapidea.x.XTW" />
    <httpRequestHandler implementation="com.adapgpt.xcodemapidea.x.XHR" />
    <executor implementation="com.adapgpt.xcodemapidea.x.XJE" />
    <programRunner implementation="com.adapgpt.xcodemapidea.x.XJD" />
    <projectService serviceImplementation="com.adapgpt.xcodemapidea.x.XPS" />
    <java.programPatcher implementation="com.adapgpt.xcodemapidea.x.XPP" />
    <notificationGroup displayType="BALLOON" id="XCodeMap-Notification" />
    <codeInsight.lineMarkerProvider language="JAVA" implementationClass="com.adapgpt.xcodemapidea.x.XLMP" />
    <xdebugger.breakpointType implementation="com.adapgpt.xcodemapidea.x.XABT" />
    <debugger.javaBreakpointHandlerFactory implementation="com.adapgpt.xcodemapidea.x.XABH" />
    <externalSystem.runConfigurationEx implementation="com.adapgpt.xcodemapidea.x.XGRC" />
    <!--<xdebugger.breakpointType implementation="com.adapgpt.xcodemapidea.e.XcmDaemonBreakPointType"/>
        <debugger.javaBreakpointHandlerFactory implementation="com.adapgpt.xcodemapidea.e.XcmDaemonBreakpointHandlerFactory" />-->
  </extensions>
  <extensions defaultExtensionNs="com.intellij">
    <directoryIndexExcludePolicy implementation="com.adapgpt.xcodemapidea.x.XDIE" />
  </extensions>
  <actions>
    <action id="XCodeMapGlobalSearch" class="com.adapgpt.xcodemapidea.x.XCM" text="Search XCodeMap" description="Search the code in XCodeMap" icon="com.adapgpt.xcodemapidea.x.XCodeMapIcons.LOGO">
      <add-to-group group-id="EditorPopupMenu" anchor="first" />
    </action>
    <action id="XCodeMapStepInto" class="com.adapgpt.xcodemapidea.x.XSI" text="X Step Into" description="XCodeMap Step Into">
      <keyboard-shortcut keymap="$default" first-keystroke="F7" />
    </action>
    <action id="XCodeMapStepOver" class="com.adapgpt.xcodemapidea.x.XSO" text="X Step Over" description="XCodeMap Step Over">
      <keyboard-shortcut keymap="$default" first-keystroke="F8" />
    </action>
    <action id="XCodeMapStepOut" class="com.adapgpt.xcodemapidea.x.XST" text="X Step Out" description="XCodeMap Step Out">
      <keyboard-shortcut keymap="$default" first-keystroke="shift F7" />
    </action>
    <action id="XCodeMapStepBack" class="com.adapgpt.xcodemapidea.x.XSB" text="X Step Back" description="XCodeMap Step Back">
      <keyboard-shortcut keymap="$default" first-keystroke="shift F8" />
    </action>
    <action id="XCodeMapOpenDevTools" class="com.adapgpt.xcodemapidea.x.XDT" text="X Open DevTools" description="XCodeMap Open DevTools for current browser">
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl shift I" />
    </action>
    <action id="XCodeMapOpenLog" class="com.adapgpt.xcodemapidea.x.XOL" text="X Open Log" description="XCodeMap Open log file">
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl shift L" />
    </action>
    <group>
      <action id="XCodeMapDebug" class="com.adapgpt.xcodemapidea.x.XMD" text="XCodeMap Debug" description="XCodeMap Debug" />
      <add-to-group group-id="RunDashboardContentToolbar" anchor="last" />
    </group>
  </actions>
</idea-plugin>
