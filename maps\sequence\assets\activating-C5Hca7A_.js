import{i as S,u as A,r as v,v as D,z as T,j as n,o as c,l as e,n as m,q as r,t as l,m as u,C as U,D as L,_ as P,S as E,T as I}from"./style-ZGiRjKX1.js";import{u as M}from"./activation-DCKqwZPe.js";const F={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},R={class:"max-w-2xl mx-auto"},$={class:"text-center mb-8"},j={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},q={class:"space-y-6"},z={key:0,class:"space-y-4"},B={class:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600"},V={class:"flex items-center justify-between"},X={class:"text-sm font-mono break-all flex-1 mr-3 text-gray-900 dark:text-gray-100 font-medium"},N={key:1,class:"space-y-4"},O={class:"space-y-4"},W=["disabled"],G=["disabled"],H={key:2,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},J={key:3,class:"bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg"},K={key:0,class:"text-sm mt-2"},Q=S({__name:"Activating",setup(Z){const g=A(),o=M(),i=v(""),s=v(""),p=v(!1),d=v(null);D(()=>{g.initTheme(),h(),f()}),T(()=>{});const h=()=>{const a=new URLSearchParams(window.location.search).get("deviceCode");a&&(d.value=a,console.log("Device code found in URL parameters:",a))},f=async()=>{try{if(d.value)o.setDeviceCode(d.value),o.setStep("device_code_generated"),s.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(d.value)}`,p.value=!0,x();else{await o.generateDeviceCode();const t=o.getDeviceCode();t&&(s.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(t)}`,p.value=!0,x())}}catch(t){console.error("Failed to start activation process:",t)}},x=()=>{},y=async()=>{try{await navigator.clipboard.writeText(s.value),console.log("Activation link copied to clipboard")}catch(t){console.error("Failed to copy activation link:",t)}},_=()=>{s.value&&window.open(s.value,"_blank")},k=async()=>{if(i.value.trim())try{if(d.value){const t=`clipboard://xcm_fin_ack://${i.value.trim()}`;console.log("Using clipboard protocol:",t),window.open(t,"_blank"),o.setStep("activated")}else{const t=await o.finishActivationProcess(i.value.trim());if(console.log("Activation completed successfully, expired timestamp:",t),t){const a=new Date(t);console.log("License expires at:",a.toLocaleString())}}}catch(t){console.error("Failed to finish activation:",t)}},w=()=>{switch(o.getCurrentStep()){case"idle":return"准备开始激活流程...";case"generating_device_code":return"正在生成设备码...";case"device_code_generated":return"设备码生成成功，请获取激活码";case"finishing_activation":return"正在完成激活...";case"activated":return"激活成功！";case"error":return`激活失败: ${o.getError()}`;default:return"未知状态"}};return(t,a)=>(c(),n("div",{class:m(["h-screen w-full flex flex-col overflow-x-hidden",(r(g).theme==="dark","bg-[var(--bg-color)]")])},[e("div",F,[e("div",R,[e("div",$,[e("h1",{class:m(["text-3xl font-bold mb-4",r(g).theme==="dark"?"text-white":"text-gray-900"])}," XCodeMap 激活 ",2),e("p",{class:m(["text-lg",r(g).theme==="dark"?"text-gray-300":"text-gray-600"])},l(w()),3)]),e("div",j,[e("div",q,[s.value?(c(),n("div",z,[a[1]||(a[1]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 专属激活链接 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," 请访问此专属链接以获取激活码 ")],-1)),e("div",B,[e("div",V,[e("code",X,l(s.value),1)])]),e("div",{class:"flex justify-center space-x-4"},[e("button",{onClick:_,class:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-200"}," 访问链接 "),e("button",{onClick:y,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"}," 复制链接 ")]),a[2]||(a[2]=e("div",{class:"text-center"},[e("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 如果无法直接访问，请复制链接到能联网访问的浏览器打开 ")],-1))])):u("",!0),p.value?(c(),n("div",N,[a[3]||(a[3]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 输入激活码 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请输入从XCodeMap官网 https://xcodemap.tech 获得的激活码 ")],-1)),e("div",O,[U(e("textarea",{"onUpdate:modelValue":a[0]||(a[0]=C=>i.value=C),placeholder:"请输入激活码",rows:"4",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none",disabled:r(o).getCurrentStep()==="finishing_activation"},null,8,W),[[L,i.value]]),e("button",{onClick:k,disabled:!i.value.trim()||r(o).getCurrentStep()==="finishing_activation",class:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-lg"},l(r(o).getCurrentStep()==="finishing_activation"?"激活中...":"完成激活"),9,G)])])):u("",!0),r(o).hasError()?(c(),n("div",H,l(r(o).getError()),1)):u("",!0),r(o).isActivated()?(c(),n("div",J,[a[4]||(a[4]=e("div",{class:"font-medium"},"激活成功！您现在可以使用 XCodeMap 了。",-1)),r(o).getExpiredTimestamp()?(c(),n("div",K," 许可证过期时间："+l(new Date(r(o).getExpiredTimestamp()).toLocaleString()),1)):u("",!0)])):u("",!0)])])])])],2))}}),Y=P(Q,[["__scopeId","data-v-9a9c0694"]]),b=E(Y);b.use(I());b.mount("#app");
