import { ar as me, s as ye, g as Ve, a as Le, b as ve, C as Ie, D as Re, c as et, l as J1, A as At, E as Ne, f as we, j as w1 } from "./mermaid-00886c59.js";
var dt = function() {
  var e = function(f1, a, l, f) {
    for (l = l || {}, f = f1.length; f--; l[f1[f]] = a)
      ;
    return l;
  }, u = [1, 4], n = [1, 3], r = [1, 5], c = [1, 8, 9, 10, 11, 27, 34, 36, 38, 42, 58, 81, 82, 83, 84, 85, 86, 99, 102, 103, 106, 108, 111, 112, 113, 118, 119, 120, 121], o = [2, 2], h = [1, 13], U = [1, 14], F = [1, 15], w = [1, 16], X = [1, 23], l1 = [1, 25], d1 = [1, 26], p1 = [1, 27], S = [1, 49], k = [1, 48], o1 = [1, 29], U1 = [1, 30], G1 = [1, 31], M1 = [1, 32], K1 = [1, 33], x = [1, 44], B = [1, 46], m = [1, 42], y = [1, 47], V = [1, 43], L = [1, 50], v = [1, 45], I = [1, 51], R = [1, 52], Y1 = [1, 34], j1 = [1, 35], z1 = [1, 36], X1 = [1, 37], I1 = [1, 57], b = [1, 8, 9, 10, 11, 27, 32, 34, 36, 38, 42, 58, 81, 82, 83, 84, 85, 86, 99, 102, 103, 106, 108, 111, 112, 113, 118, 119, 120, 121], q = [1, 61], Q = [1, 60], Z = [1, 62], H1 = [8, 9, 11, 73, 75], k1 = [1, 88], b1 = [1, 93], g1 = [1, 92], D1 = [1, 89], F1 = [1, 85], T1 = [1, 91], C1 = [1, 87], S1 = [1, 94], _1 = [1, 90], x1 = [1, 95], B1 = [1, 86], W1 = [8, 9, 10, 11, 73, 75], N = [8, 9, 10, 11, 44, 73, 75], M = [8, 9, 10, 11, 29, 42, 44, 46, 48, 50, 52, 54, 56, 58, 61, 63, 65, 66, 68, 73, 75, 86, 99, 102, 103, 106, 108, 111, 112, 113], Et = [8, 9, 11, 42, 58, 73, 75, 86, 99, 102, 103, 106, 108, 111, 112, 113], R1 = [42, 58, 86, 99, 102, 103, 106, 108, 111, 112, 113], kt = [1, 121], bt = [1, 120], gt = [1, 128], Dt = [1, 142], Ft = [1, 143], Tt = [1, 144], Ct = [1, 145], St = [1, 130], _t = [1, 132], xt = [1, 136], Bt = [1, 137], mt = [1, 138], yt = [1, 139], Vt = [1, 140], Lt = [1, 141], vt = [1, 146], It = [1, 147], Rt = [1, 126], Nt = [1, 127], wt = [1, 134], Ot = [1, 129], Pt = [1, 133], Ut = [1, 131], rt = [8, 9, 10, 11, 27, 32, 34, 36, 38, 42, 58, 81, 82, 83, 84, 85, 86, 99, 102, 103, 106, 108, 111, 112, 113, 118, 119, 120, 121], Gt = [1, 149], T = [8, 9, 11], K = [8, 9, 10, 11, 14, 42, 58, 86, 102, 103, 106, 108, 111, 112, 113], d = [1, 169], O = [1, 165], P = [1, 166], p = [1, 170], A = [1, 167], E = [1, 168], m1 = [75, 113, 116], g = [8, 9, 10, 11, 12, 14, 27, 29, 32, 42, 58, 73, 81, 82, 83, 84, 85, 86, 87, 102, 106, 108, 111, 112, 113], Mt = [10, 103], h1 = [31, 47, 49, 51, 53, 55, 60, 62, 64, 65, 67, 69, 113, 114, 115], J = [1, 235], $ = [1, 233], t1 = [1, 237], e1 = [1, 231], s1 = [1, 232], u1 = [1, 234], n1 = [1, 236], i1 = [1, 238], y1 = [1, 255], Kt = [8, 9, 11, 103], W = [8, 9, 10, 11, 58, 81, 102, 103, 106, 107, 108, 109], at = {
    trace: function() {
    },
    yy: {},
    symbols_: { error: 2, start: 3, graphConfig: 4, document: 5, line: 6, statement: 7, SEMI: 8, NEWLINE: 9, SPACE: 10, EOF: 11, GRAPH: 12, NODIR: 13, DIR: 14, FirstStmtSeparator: 15, ending: 16, endToken: 17, spaceList: 18, spaceListNewline: 19, vertexStatement: 20, separator: 21, styleStatement: 22, linkStyleStatement: 23, classDefStatement: 24, classStatement: 25, clickStatement: 26, subgraph: 27, textNoTags: 28, SQS: 29, text: 30, SQE: 31, end: 32, direction: 33, acc_title: 34, acc_title_value: 35, acc_descr: 36, acc_descr_value: 37, acc_descr_multiline_value: 38, link: 39, node: 40, styledVertex: 41, AMP: 42, vertex: 43, STYLE_SEPARATOR: 44, idString: 45, DOUBLECIRCLESTART: 46, DOUBLECIRCLEEND: 47, PS: 48, PE: 49, "(-": 50, "-)": 51, STADIUMSTART: 52, STADIUMEND: 53, SUBROUTINESTART: 54, SUBROUTINEEND: 55, VERTEX_WITH_PROPS_START: 56, "NODE_STRING[field]": 57, COLON: 58, "NODE_STRING[value]": 59, PIPE: 60, CYLINDERSTART: 61, CYLINDEREND: 62, DIAMOND_START: 63, DIAMOND_STOP: 64, TAGEND: 65, TRAPSTART: 66, TRAPEND: 67, INVTRAPSTART: 68, INVTRAPEND: 69, linkStatement: 70, arrowText: 71, TESTSTR: 72, START_LINK: 73, edgeText: 74, LINK: 75, edgeTextToken: 76, STR: 77, MD_STR: 78, textToken: 79, keywords: 80, STYLE: 81, LINKSTYLE: 82, CLASSDEF: 83, CLASS: 84, CLICK: 85, DOWN: 86, UP: 87, textNoTagsToken: 88, stylesOpt: 89, "idString[vertex]": 90, "idString[class]": 91, CALLBACKNAME: 92, CALLBACKARGS: 93, HREF: 94, LINK_TARGET: 95, "STR[link]": 96, "STR[tooltip]": 97, alphaNum: 98, DEFAULT: 99, numList: 100, INTERPOLATE: 101, NUM: 102, COMMA: 103, style: 104, styleComponent: 105, NODE_STRING: 106, UNIT: 107, BRKT: 108, PCT: 109, idStringToken: 110, MINUS: 111, MULT: 112, UNICODE_TEXT: 113, TEXT: 114, TAGSTART: 115, EDGE_TEXT: 116, alphaNumToken: 117, direction_tb: 118, direction_bt: 119, direction_rl: 120, direction_lr: 121, $accept: 0, $end: 1 },
    terminals_: { 2: "error", 8: "SEMI", 9: "NEWLINE", 10: "SPACE", 11: "EOF", 12: "GRAPH", 13: "NODIR", 14: "DIR", 27: "subgraph", 29: "SQS", 31: "SQE", 32: "end", 34: "acc_title", 35: "acc_title_value", 36: "acc_descr", 37: "acc_descr_value", 38: "acc_descr_multiline_value", 42: "AMP", 44: "STYLE_SEPARATOR", 46: "DOUBLECIRCLESTART", 47: "DOUBLECIRCLEEND", 48: "PS", 49: "PE", 50: "(-", 51: "-)", 52: "STADIUMSTART", 53: "STADIUMEND", 54: "SUBROUTINESTART", 55: "SUBROUTINEEND", 56: "VERTEX_WITH_PROPS_START", 57: "NODE_STRING[field]", 58: "COLON", 59: "NODE_STRING[value]", 60: "PIPE", 61: "CYLINDERSTART", 62: "CYLINDEREND", 63: "DIAMOND_START", 64: "DIAMOND_STOP", 65: "TAGEND", 66: "TRAPSTART", 67: "TRAPEND", 68: "INVTRAPSTART", 69: "INVTRAPEND", 72: "TESTSTR", 73: "START_LINK", 75: "LINK", 77: "STR", 78: "MD_STR", 81: "STYLE", 82: "LINKSTYLE", 83: "CLASSDEF", 84: "CLASS", 85: "CLICK", 86: "DOWN", 87: "UP", 90: "idString[vertex]", 91: "idString[class]", 92: "CALLBACKNAME", 93: "CALLBACKARGS", 94: "HREF", 95: "LINK_TARGET", 96: "STR[link]", 97: "STR[tooltip]", 99: "DEFAULT", 101: "INTERPOLATE", 102: "NUM", 103: "COMMA", 106: "NODE_STRING", 107: "UNIT", 108: "BRKT", 109: "PCT", 111: "MINUS", 112: "MULT", 113: "UNICODE_TEXT", 114: "TEXT", 115: "TAGSTART", 116: "EDGE_TEXT", 118: "direction_tb", 119: "direction_bt", 120: "direction_rl", 121: "direction_lr" },
    productions_: [0, [3, 2], [5, 0], [5, 2], [6, 1], [6, 1], [6, 1], [6, 1], [6, 1], [4, 2], [4, 2], [4, 2], [4, 3], [16, 2], [16, 1], [17, 1], [17, 1], [17, 1], [15, 1], [15, 1], [15, 2], [19, 2], [19, 2], [19, 1], [19, 1], [18, 2], [18, 1], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 9], [7, 6], [7, 4], [7, 1], [7, 2], [7, 2], [7, 1], [21, 1], [21, 1], [21, 1], [20, 3], [20, 4], [20, 2], [20, 1], [40, 1], [40, 5], [41, 1], [41, 3], [43, 4], [43, 4], [43, 6], [43, 4], [43, 4], [43, 4], [43, 8], [43, 4], [43, 4], [43, 4], [43, 6], [43, 4], [43, 4], [43, 4], [43, 4], [43, 4], [43, 1], [39, 2], [39, 3], [39, 3], [39, 1], [39, 3], [74, 1], [74, 2], [74, 1], [74, 1], [70, 1], [71, 3], [30, 1], [30, 2], [30, 1], [30, 1], [80, 1], [80, 1], [80, 1], [80, 1], [80, 1], [80, 1], [80, 1], [80, 1], [80, 1], [80, 1], [80, 1], [28, 1], [28, 2], [28, 1], [28, 1], [24, 5], [25, 5], [26, 2], [26, 4], [26, 3], [26, 5], [26, 3], [26, 5], [26, 5], [26, 7], [26, 2], [26, 4], [26, 2], [26, 4], [26, 4], [26, 6], [22, 5], [23, 5], [23, 5], [23, 9], [23, 9], [23, 7], [23, 7], [100, 1], [100, 3], [89, 1], [89, 3], [104, 1], [104, 2], [105, 1], [105, 1], [105, 1], [105, 1], [105, 1], [105, 1], [105, 1], [105, 1], [110, 1], [110, 1], [110, 1], [110, 1], [110, 1], [110, 1], [110, 1], [110, 1], [110, 1], [110, 1], [110, 1], [79, 1], [79, 1], [79, 1], [79, 1], [88, 1], [88, 1], [88, 1], [88, 1], [88, 1], [88, 1], [88, 1], [88, 1], [88, 1], [88, 1], [88, 1], [76, 1], [76, 1], [117, 1], [117, 1], [117, 1], [117, 1], [117, 1], [117, 1], [117, 1], [117, 1], [117, 1], [117, 1], [117, 1], [45, 1], [45, 2], [98, 1], [98, 2], [33, 1], [33, 1], [33, 1], [33, 1]],
    performAction: function(a, l, f, i, C, t, N1) {
      var s = t.length - 1;
      switch (C) {
        case 2:
          this.$ = [];
          break;
        case 3:
          (!Array.isArray(t[s]) || t[s].length > 0) && t[s - 1].push(t[s]), this.$ = t[s - 1];
          break;
        case 4:
        case 176:
          this.$ = t[s];
          break;
        case 11:
          i.setDirection("TB"), this.$ = "TB";
          break;
        case 12:
          i.setDirection(t[s - 1]), this.$ = t[s - 1];
          break;
        case 27:
          this.$ = t[s - 1].nodes;
          break;
        case 28:
        case 29:
        case 30:
        case 31:
        case 32:
          this.$ = [];
          break;
        case 33:
          this.$ = i.addSubGraph(t[s - 6], t[s - 1], t[s - 4]);
          break;
        case 34:
          this.$ = i.addSubGraph(t[s - 3], t[s - 1], t[s - 3]);
          break;
        case 35:
          this.$ = i.addSubGraph(void 0, t[s - 1], void 0);
          break;
        case 37:
          this.$ = t[s].trim(), i.setAccTitle(this.$);
          break;
        case 38:
        case 39:
          this.$ = t[s].trim(), i.setAccDescription(this.$);
          break;
        case 43:
          i.addLink(t[s - 2].stmt, t[s], t[s - 1]), this.$ = { stmt: t[s], nodes: t[s].concat(t[s - 2].nodes) };
          break;
        case 44:
          i.addLink(t[s - 3].stmt, t[s - 1], t[s - 2]), this.$ = { stmt: t[s - 1], nodes: t[s - 1].concat(t[s - 3].nodes) };
          break;
        case 45:
          this.$ = { stmt: t[s - 1], nodes: t[s - 1] };
          break;
        case 46:
          this.$ = { stmt: t[s], nodes: t[s] };
          break;
        case 47:
          this.$ = [t[s]];
          break;
        case 48:
          this.$ = t[s - 4].concat(t[s]);
          break;
        case 49:
          this.$ = t[s];
          break;
        case 50:
          this.$ = t[s - 2], i.setClass(t[s - 2], t[s]);
          break;
        case 51:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "square");
          break;
        case 52:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "doublecircle");
          break;
        case 53:
          this.$ = t[s - 5], i.addVertex(t[s - 5], t[s - 2], "circle");
          break;
        case 54:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "ellipse");
          break;
        case 55:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "stadium");
          break;
        case 56:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "subroutine");
          break;
        case 57:
          this.$ = t[s - 7], i.addVertex(t[s - 7], t[s - 1], "rect", void 0, void 0, void 0, Object.fromEntries([[t[s - 5], t[s - 3]]]));
          break;
        case 58:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "cylinder");
          break;
        case 59:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "round");
          break;
        case 60:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "diamond");
          break;
        case 61:
          this.$ = t[s - 5], i.addVertex(t[s - 5], t[s - 2], "hexagon");
          break;
        case 62:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "odd");
          break;
        case 63:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "trapezoid");
          break;
        case 64:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "inv_trapezoid");
          break;
        case 65:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "lean_right");
          break;
        case 66:
          this.$ = t[s - 3], i.addVertex(t[s - 3], t[s - 1], "lean_left");
          break;
        case 67:
          this.$ = t[s], i.addVertex(t[s]);
          break;
        case 68:
          t[s - 1].text = t[s], this.$ = t[s - 1];
          break;
        case 69:
        case 70:
          t[s - 2].text = t[s - 1], this.$ = t[s - 2];
          break;
        case 71:
          this.$ = t[s];
          break;
        case 72:
          var Y = i.destructLink(t[s], t[s - 2]);
          this.$ = { type: Y.type, stroke: Y.stroke, length: Y.length, text: t[s - 1] };
          break;
        case 73:
          this.$ = { text: t[s], type: "text" };
          break;
        case 74:
          this.$ = { text: t[s - 1].text + "" + t[s], type: t[s - 1].type };
          break;
        case 75:
          this.$ = { text: t[s], type: "string" };
          break;
        case 76:
          this.$ = { text: t[s], type: "markdown" };
          break;
        case 77:
          var Y = i.destructLink(t[s]);
          this.$ = { type: Y.type, stroke: Y.stroke, length: Y.length };
          break;
        case 78:
          this.$ = t[s - 1];
          break;
        case 79:
          this.$ = { text: t[s], type: "text" };
          break;
        case 80:
          this.$ = { text: t[s - 1].text + "" + t[s], type: t[s - 1].type };
          break;
        case 81:
          this.$ = { text: t[s], type: "string" };
          break;
        case 82:
        case 97:
          this.$ = { text: t[s], type: "markdown" };
          break;
        case 94:
          this.$ = { text: t[s], type: "text" };
          break;
        case 95:
          this.$ = { text: t[s - 1].text + "" + t[s], type: t[s - 1].type };
          break;
        case 96:
          this.$ = { text: t[s], type: "text" };
          break;
        case 98:
          this.$ = t[s - 4], i.addClass(t[s - 2], t[s]);
          break;
        case 99:
          this.$ = t[s - 4], i.setClass(t[s - 2], t[s]);
          break;
        case 100:
        case 108:
          this.$ = t[s - 1], i.setClickEvent(t[s - 1], t[s]);
          break;
        case 101:
        case 109:
          this.$ = t[s - 3], i.setClickEvent(t[s - 3], t[s - 2]), i.setTooltip(t[s - 3], t[s]);
          break;
        case 102:
          this.$ = t[s - 2], i.setClickEvent(t[s - 2], t[s - 1], t[s]);
          break;
        case 103:
          this.$ = t[s - 4], i.setClickEvent(t[s - 4], t[s - 3], t[s - 2]), i.setTooltip(t[s - 4], t[s]);
          break;
        case 104:
          this.$ = t[s - 2], i.setLink(t[s - 2], t[s]);
          break;
        case 105:
          this.$ = t[s - 4], i.setLink(t[s - 4], t[s - 2]), i.setTooltip(t[s - 4], t[s]);
          break;
        case 106:
          this.$ = t[s - 4], i.setLink(t[s - 4], t[s - 2], t[s]);
          break;
        case 107:
          this.$ = t[s - 6], i.setLink(t[s - 6], t[s - 4], t[s]), i.setTooltip(t[s - 6], t[s - 2]);
          break;
        case 110:
          this.$ = t[s - 1], i.setLink(t[s - 1], t[s]);
          break;
        case 111:
          this.$ = t[s - 3], i.setLink(t[s - 3], t[s - 2]), i.setTooltip(t[s - 3], t[s]);
          break;
        case 112:
          this.$ = t[s - 3], i.setLink(t[s - 3], t[s - 2], t[s]);
          break;
        case 113:
          this.$ = t[s - 5], i.setLink(t[s - 5], t[s - 4], t[s]), i.setTooltip(t[s - 5], t[s - 2]);
          break;
        case 114:
          this.$ = t[s - 4], i.addVertex(t[s - 2], void 0, void 0, t[s]);
          break;
        case 115:
          this.$ = t[s - 4], i.updateLink([t[s - 2]], t[s]);
          break;
        case 116:
          this.$ = t[s - 4], i.updateLink(t[s - 2], t[s]);
          break;
        case 117:
          this.$ = t[s - 8], i.updateLinkInterpolate([t[s - 6]], t[s - 2]), i.updateLink([t[s - 6]], t[s]);
          break;
        case 118:
          this.$ = t[s - 8], i.updateLinkInterpolate(t[s - 6], t[s - 2]), i.updateLink(t[s - 6], t[s]);
          break;
        case 119:
          this.$ = t[s - 6], i.updateLinkInterpolate([t[s - 4]], t[s]);
          break;
        case 120:
          this.$ = t[s - 6], i.updateLinkInterpolate(t[s - 4], t[s]);
          break;
        case 121:
        case 123:
          this.$ = [t[s]];
          break;
        case 122:
        case 124:
          t[s - 2].push(t[s]), this.$ = t[s - 2];
          break;
        case 126:
          this.$ = t[s - 1] + t[s];
          break;
        case 174:
          this.$ = t[s];
          break;
        case 175:
          this.$ = t[s - 1] + "" + t[s];
          break;
        case 177:
          this.$ = t[s - 1] + "" + t[s];
          break;
        case 178:
          this.$ = { stmt: "dir", value: "TB" };
          break;
        case 179:
          this.$ = { stmt: "dir", value: "BT" };
          break;
        case 180:
          this.$ = { stmt: "dir", value: "RL" };
          break;
        case 181:
          this.$ = { stmt: "dir", value: "LR" };
          break;
      }
    },
    table: [{ 3: 1, 4: 2, 9: u, 10: n, 12: r }, { 1: [3] }, e(c, o, { 5: 6 }), { 4: 7, 9: u, 10: n, 12: r }, { 4: 8, 9: u, 10: n, 12: r }, { 13: [1, 9], 14: [1, 10] }, { 1: [2, 1], 6: 11, 7: 12, 8: h, 9: U, 10: F, 11: w, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: X, 33: 24, 34: l1, 36: d1, 38: p1, 40: 28, 41: 38, 42: S, 43: 39, 45: 40, 58: k, 81: o1, 82: U1, 83: G1, 84: M1, 85: K1, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R, 118: Y1, 119: j1, 120: z1, 121: X1 }, e(c, [2, 9]), e(c, [2, 10]), e(c, [2, 11]), { 8: [1, 54], 9: [1, 55], 10: I1, 15: 53, 18: 56 }, e(b, [2, 3]), e(b, [2, 4]), e(b, [2, 5]), e(b, [2, 6]), e(b, [2, 7]), e(b, [2, 8]), { 8: q, 9: Q, 11: Z, 21: 58, 39: 59, 70: 63, 73: [1, 64], 75: [1, 65] }, { 8: q, 9: Q, 11: Z, 21: 66 }, { 8: q, 9: Q, 11: Z, 21: 67 }, { 8: q, 9: Q, 11: Z, 21: 68 }, { 8: q, 9: Q, 11: Z, 21: 69 }, { 8: q, 9: Q, 11: Z, 21: 70 }, { 8: q, 9: Q, 10: [1, 71], 11: Z, 21: 72 }, e(b, [2, 36]), { 35: [1, 73] }, { 37: [1, 74] }, e(b, [2, 39]), e(H1, [2, 46], { 18: 75, 10: I1 }), { 10: [1, 76] }, { 10: [1, 77] }, { 10: [1, 78] }, { 10: [1, 79] }, { 14: k1, 42: b1, 58: g1, 77: [1, 83], 86: D1, 92: [1, 80], 94: [1, 81], 98: 82, 102: F1, 103: T1, 106: C1, 108: S1, 111: _1, 112: x1, 113: B1, 117: 84 }, e(b, [2, 178]), e(b, [2, 179]), e(b, [2, 180]), e(b, [2, 181]), e(W1, [2, 47]), e(W1, [2, 49], { 44: [1, 96] }), e(N, [2, 67], { 110: 109, 29: [1, 97], 42: S, 46: [1, 98], 48: [1, 99], 50: [1, 100], 52: [1, 101], 54: [1, 102], 56: [1, 103], 58: k, 61: [1, 104], 63: [1, 105], 65: [1, 106], 66: [1, 107], 68: [1, 108], 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 111: v, 112: I, 113: R }), e(M, [2, 174]), e(M, [2, 135]), e(M, [2, 136]), e(M, [2, 137]), e(M, [2, 138]), e(M, [2, 139]), e(M, [2, 140]), e(M, [2, 141]), e(M, [2, 142]), e(M, [2, 143]), e(M, [2, 144]), e(M, [2, 145]), e(c, [2, 12]), e(c, [2, 18]), e(c, [2, 19]), { 9: [1, 110] }, e(Et, [2, 26], { 18: 111, 10: I1 }), e(b, [2, 27]), { 40: 112, 41: 38, 42: S, 43: 39, 45: 40, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R }, e(b, [2, 40]), e(b, [2, 41]), e(b, [2, 42]), e(R1, [2, 71], { 71: 113, 60: [1, 115], 72: [1, 114] }), { 74: 116, 76: 117, 77: [1, 118], 78: [1, 119], 113: kt, 116: bt }, e([42, 58, 60, 72, 86, 99, 102, 103, 106, 108, 111, 112, 113], [2, 77]), e(b, [2, 28]), e(b, [2, 29]), e(b, [2, 30]), e(b, [2, 31]), e(b, [2, 32]), { 10: gt, 12: Dt, 14: Ft, 27: Tt, 28: 122, 32: Ct, 42: St, 58: _t, 73: xt, 77: [1, 124], 78: [1, 125], 80: 135, 81: Bt, 82: mt, 83: yt, 84: Vt, 85: Lt, 86: vt, 87: It, 88: 123, 102: Rt, 106: Nt, 108: wt, 111: Ot, 112: Pt, 113: Ut }, e(rt, o, { 5: 148 }), e(b, [2, 37]), e(b, [2, 38]), e(H1, [2, 45], { 42: Gt }), { 42: S, 45: 150, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R }, { 99: [1, 151], 100: 152, 102: [1, 153] }, { 42: S, 45: 154, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R }, { 42: S, 45: 155, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R }, e(T, [2, 100], { 10: [1, 156], 93: [1, 157] }), { 77: [1, 158] }, e(T, [2, 108], { 117: 160, 10: [1, 159], 14: k1, 42: b1, 58: g1, 86: D1, 102: F1, 103: T1, 106: C1, 108: S1, 111: _1, 112: x1, 113: B1 }), e(T, [2, 110], { 10: [1, 161] }), e(K, [2, 176]), e(K, [2, 163]), e(K, [2, 164]), e(K, [2, 165]), e(K, [2, 166]), e(K, [2, 167]), e(K, [2, 168]), e(K, [2, 169]), e(K, [2, 170]), e(K, [2, 171]), e(K, [2, 172]), e(K, [2, 173]), { 42: S, 45: 162, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R }, { 30: 163, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 171, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 173, 48: [1, 172], 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 174, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 175, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 176, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 106: [1, 177] }, { 30: 178, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 179, 63: [1, 180], 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 181, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 182, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 30: 183, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, e(M, [2, 175]), e(c, [2, 20]), e(Et, [2, 25]), e(H1, [2, 43], { 18: 184, 10: I1 }), e(R1, [2, 68], { 10: [1, 185] }), { 10: [1, 186] }, { 30: 187, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 75: [1, 188], 76: 189, 113: kt, 116: bt }, e(m1, [2, 73]), e(m1, [2, 75]), e(m1, [2, 76]), e(m1, [2, 161]), e(m1, [2, 162]), { 8: q, 9: Q, 10: gt, 11: Z, 12: Dt, 14: Ft, 21: 191, 27: Tt, 29: [1, 190], 32: Ct, 42: St, 58: _t, 73: xt, 80: 135, 81: Bt, 82: mt, 83: yt, 84: Vt, 85: Lt, 86: vt, 87: It, 88: 192, 102: Rt, 106: Nt, 108: wt, 111: Ot, 112: Pt, 113: Ut }, e(g, [2, 94]), e(g, [2, 96]), e(g, [2, 97]), e(g, [2, 150]), e(g, [2, 151]), e(g, [2, 152]), e(g, [2, 153]), e(g, [2, 154]), e(g, [2, 155]), e(g, [2, 156]), e(g, [2, 157]), e(g, [2, 158]), e(g, [2, 159]), e(g, [2, 160]), e(g, [2, 83]), e(g, [2, 84]), e(g, [2, 85]), e(g, [2, 86]), e(g, [2, 87]), e(g, [2, 88]), e(g, [2, 89]), e(g, [2, 90]), e(g, [2, 91]), e(g, [2, 92]), e(g, [2, 93]), { 6: 11, 7: 12, 8: h, 9: U, 10: F, 11: w, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: X, 32: [1, 193], 33: 24, 34: l1, 36: d1, 38: p1, 40: 28, 41: 38, 42: S, 43: 39, 45: 40, 58: k, 81: o1, 82: U1, 83: G1, 84: M1, 85: K1, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R, 118: Y1, 119: j1, 120: z1, 121: X1 }, { 10: I1, 18: 194 }, { 10: [1, 195], 42: S, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 109, 111: v, 112: I, 113: R }, { 10: [1, 196] }, { 10: [1, 197], 103: [1, 198] }, e(Mt, [2, 121]), { 10: [1, 199], 42: S, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 109, 111: v, 112: I, 113: R }, { 10: [1, 200], 42: S, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 109, 111: v, 112: I, 113: R }, { 77: [1, 201] }, e(T, [2, 102], { 10: [1, 202] }), e(T, [2, 104], { 10: [1, 203] }), { 77: [1, 204] }, e(K, [2, 177]), { 77: [1, 205], 95: [1, 206] }, e(W1, [2, 50], { 110: 109, 42: S, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 111: v, 112: I, 113: R }), { 31: [1, 207], 65: d, 79: 208, 113: p, 114: A, 115: E }, e(h1, [2, 79]), e(h1, [2, 81]), e(h1, [2, 82]), e(h1, [2, 146]), e(h1, [2, 147]), e(h1, [2, 148]), e(h1, [2, 149]), { 47: [1, 209], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 30: 210, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 49: [1, 211], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 51: [1, 212], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 53: [1, 213], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 55: [1, 214], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 58: [1, 215] }, { 62: [1, 216], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 64: [1, 217], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 30: 218, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, { 31: [1, 219], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 65: d, 67: [1, 220], 69: [1, 221], 79: 208, 113: p, 114: A, 115: E }, { 65: d, 67: [1, 223], 69: [1, 222], 79: 208, 113: p, 114: A, 115: E }, e(H1, [2, 44], { 42: Gt }), e(R1, [2, 70]), e(R1, [2, 69]), { 60: [1, 224], 65: d, 79: 208, 113: p, 114: A, 115: E }, e(R1, [2, 72]), e(m1, [2, 74]), { 30: 225, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, e(rt, o, { 5: 226 }), e(g, [2, 95]), e(b, [2, 35]), { 41: 227, 42: S, 43: 39, 45: 40, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R }, { 10: J, 58: $, 81: t1, 89: 228, 102: e1, 104: 229, 105: 230, 106: s1, 107: u1, 108: n1, 109: i1 }, { 10: J, 58: $, 81: t1, 89: 239, 101: [1, 240], 102: e1, 104: 229, 105: 230, 106: s1, 107: u1, 108: n1, 109: i1 }, { 10: J, 58: $, 81: t1, 89: 241, 101: [1, 242], 102: e1, 104: 229, 105: 230, 106: s1, 107: u1, 108: n1, 109: i1 }, { 102: [1, 243] }, { 10: J, 58: $, 81: t1, 89: 244, 102: e1, 104: 229, 105: 230, 106: s1, 107: u1, 108: n1, 109: i1 }, { 42: S, 45: 245, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R }, e(T, [2, 101]), { 77: [1, 246] }, { 77: [1, 247], 95: [1, 248] }, e(T, [2, 109]), e(T, [2, 111], { 10: [1, 249] }), e(T, [2, 112]), e(N, [2, 51]), e(h1, [2, 80]), e(N, [2, 52]), { 49: [1, 250], 65: d, 79: 208, 113: p, 114: A, 115: E }, e(N, [2, 59]), e(N, [2, 54]), e(N, [2, 55]), e(N, [2, 56]), { 106: [1, 251] }, e(N, [2, 58]), e(N, [2, 60]), { 64: [1, 252], 65: d, 79: 208, 113: p, 114: A, 115: E }, e(N, [2, 62]), e(N, [2, 63]), e(N, [2, 65]), e(N, [2, 64]), e(N, [2, 66]), e([10, 42, 58, 86, 99, 102, 103, 106, 108, 111, 112, 113], [2, 78]), { 31: [1, 253], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 6: 11, 7: 12, 8: h, 9: U, 10: F, 11: w, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: X, 32: [1, 254], 33: 24, 34: l1, 36: d1, 38: p1, 40: 28, 41: 38, 42: S, 43: 39, 45: 40, 58: k, 81: o1, 82: U1, 83: G1, 84: M1, 85: K1, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R, 118: Y1, 119: j1, 120: z1, 121: X1 }, e(W1, [2, 48]), e(T, [2, 114], { 103: y1 }), e(Kt, [2, 123], { 105: 256, 10: J, 58: $, 81: t1, 102: e1, 106: s1, 107: u1, 108: n1, 109: i1 }), e(W, [2, 125]), e(W, [2, 127]), e(W, [2, 128]), e(W, [2, 129]), e(W, [2, 130]), e(W, [2, 131]), e(W, [2, 132]), e(W, [2, 133]), e(W, [2, 134]), e(T, [2, 115], { 103: y1 }), { 10: [1, 257] }, e(T, [2, 116], { 103: y1 }), { 10: [1, 258] }, e(Mt, [2, 122]), e(T, [2, 98], { 103: y1 }), e(T, [2, 99], { 110: 109, 42: S, 58: k, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 111: v, 112: I, 113: R }), e(T, [2, 103]), e(T, [2, 105], { 10: [1, 259] }), e(T, [2, 106]), { 95: [1, 260] }, { 49: [1, 261] }, { 60: [1, 262] }, { 64: [1, 263] }, { 8: q, 9: Q, 11: Z, 21: 264 }, e(b, [2, 34]), { 10: J, 58: $, 81: t1, 102: e1, 104: 265, 105: 230, 106: s1, 107: u1, 108: n1, 109: i1 }, e(W, [2, 126]), { 14: k1, 42: b1, 58: g1, 86: D1, 98: 266, 102: F1, 103: T1, 106: C1, 108: S1, 111: _1, 112: x1, 113: B1, 117: 84 }, { 14: k1, 42: b1, 58: g1, 86: D1, 98: 267, 102: F1, 103: T1, 106: C1, 108: S1, 111: _1, 112: x1, 113: B1, 117: 84 }, { 95: [1, 268] }, e(T, [2, 113]), e(N, [2, 53]), { 30: 269, 65: d, 77: O, 78: P, 79: 164, 113: p, 114: A, 115: E }, e(N, [2, 61]), e(rt, o, { 5: 270 }), e(Kt, [2, 124], { 105: 256, 10: J, 58: $, 81: t1, 102: e1, 106: s1, 107: u1, 108: n1, 109: i1 }), e(T, [2, 119], { 117: 160, 10: [1, 271], 14: k1, 42: b1, 58: g1, 86: D1, 102: F1, 103: T1, 106: C1, 108: S1, 111: _1, 112: x1, 113: B1 }), e(T, [2, 120], { 117: 160, 10: [1, 272], 14: k1, 42: b1, 58: g1, 86: D1, 102: F1, 103: T1, 106: C1, 108: S1, 111: _1, 112: x1, 113: B1 }), e(T, [2, 107]), { 31: [1, 273], 65: d, 79: 208, 113: p, 114: A, 115: E }, { 6: 11, 7: 12, 8: h, 9: U, 10: F, 11: w, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: X, 32: [1, 274], 33: 24, 34: l1, 36: d1, 38: p1, 40: 28, 41: 38, 42: S, 43: 39, 45: 40, 58: k, 81: o1, 82: U1, 83: G1, 84: M1, 85: K1, 86: x, 99: B, 102: m, 103: y, 106: V, 108: L, 110: 41, 111: v, 112: I, 113: R, 118: Y1, 119: j1, 120: z1, 121: X1 }, { 10: J, 58: $, 81: t1, 89: 275, 102: e1, 104: 229, 105: 230, 106: s1, 107: u1, 108: n1, 109: i1 }, { 10: J, 58: $, 81: t1, 89: 276, 102: e1, 104: 229, 105: 230, 106: s1, 107: u1, 108: n1, 109: i1 }, e(N, [2, 57]), e(b, [2, 33]), e(T, [2, 117], { 103: y1 }), e(T, [2, 118], { 103: y1 })],
    defaultActions: {},
    parseError: function(a, l) {
      if (l.recoverable)
        this.trace(a);
      else {
        var f = new Error(a);
        throw f.hash = l, f;
      }
    },
    parse: function(a) {
      var l = this, f = [0], i = [], C = [null], t = [], N1 = this.table, s = "", Y = 0, Yt = 0, Se = 2, jt = 1, _e = t.slice.call(arguments, 1), _ = Object.create(this.lexer), A1 = { yy: {} };
      for (var lt in this.yy)
        Object.prototype.hasOwnProperty.call(this.yy, lt) && (A1.yy[lt] = this.yy[lt]);
      _.setInput(a, A1.yy), A1.yy.lexer = _, A1.yy.parser = this, typeof _.yylloc > "u" && (_.yylloc = {});
      var ot = _.yylloc;
      t.push(ot);
      var xe = _.options && _.options.ranges;
      typeof A1.yy.parseError == "function" ? this.parseError = A1.yy.parseError : this.parseError = Object.getPrototypeOf(this).parseError;
      function Be() {
        var a1;
        return a1 = i.pop() || _.lex() || jt, typeof a1 != "number" && (a1 instanceof Array && (i = a1, a1 = i.pop()), a1 = l.symbols_[a1] || a1), a1;
      }
      for (var G, E1, j, ht, V1 = {}, q1, r1, zt, Q1; ; ) {
        if (E1 = f[f.length - 1], this.defaultActions[E1] ? j = this.defaultActions[E1] : ((G === null || typeof G > "u") && (G = Be()), j = N1[E1] && N1[E1][G]), typeof j > "u" || !j.length || !j[0]) {
          var ft = "";
          Q1 = [];
          for (q1 in N1[E1])
            this.terminals_[q1] && q1 > Se && Q1.push("'" + this.terminals_[q1] + "'");
          _.showPosition ? ft = "Parse error on line " + (Y + 1) + `:
` + _.showPosition() + `
Expecting ` + Q1.join(", ") + ", got '" + (this.terminals_[G] || G) + "'" : ft = "Parse error on line " + (Y + 1) + ": Unexpected " + (G == jt ? "end of input" : "'" + (this.terminals_[G] || G) + "'"), this.parseError(ft, {
            text: _.match,
            token: this.terminals_[G] || G,
            line: _.yylineno,
            loc: ot,
            expected: Q1
          });
        }
        if (j[0] instanceof Array && j.length > 1)
          throw new Error("Parse Error: multiple actions possible at state: " + E1 + ", token: " + G);
        switch (j[0]) {
          case 1:
            f.push(G), C.push(_.yytext), t.push(_.yylloc), f.push(j[1]), G = null, Yt = _.yyleng, s = _.yytext, Y = _.yylineno, ot = _.yylloc;
            break;
          case 2:
            if (r1 = this.productions_[j[1]][1], V1.$ = C[C.length - r1], V1._$ = {
              first_line: t[t.length - (r1 || 1)].first_line,
              last_line: t[t.length - 1].last_line,
              first_column: t[t.length - (r1 || 1)].first_column,
              last_column: t[t.length - 1].last_column
            }, xe && (V1._$.range = [
              t[t.length - (r1 || 1)].range[0],
              t[t.length - 1].range[1]
            ]), ht = this.performAction.apply(V1, [
              s,
              Yt,
              Y,
              A1.yy,
              j[1],
              C,
              t
            ].concat(_e)), typeof ht < "u")
              return ht;
            r1 && (f = f.slice(0, -1 * r1 * 2), C = C.slice(0, -1 * r1), t = t.slice(0, -1 * r1)), f.push(this.productions_[j[1]][0]), C.push(V1.$), t.push(V1._$), zt = N1[f[f.length - 2]][f[f.length - 1]], f.push(zt);
            break;
          case 3:
            return !0;
        }
      }
      return !0;
    }
  }, Ce = function() {
    var f1 = {
      EOF: 1,
      parseError: function(l, f) {
        if (this.yy.parser)
          this.yy.parser.parseError(l, f);
        else
          throw new Error(l);
      },
      // resets the lexer, sets new input
      setInput: function(a, l) {
        return this.yy = l || this.yy || {}, this._input = a, this._more = this._backtrack = this.done = !1, this.yylineno = this.yyleng = 0, this.yytext = this.matched = this.match = "", this.conditionStack = ["INITIAL"], this.yylloc = {
          first_line: 1,
          first_column: 0,
          last_line: 1,
          last_column: 0
        }, this.options.ranges && (this.yylloc.range = [0, 0]), this.offset = 0, this;
      },
      // consumes and returns one char from the input
      input: function() {
        var a = this._input[0];
        this.yytext += a, this.yyleng++, this.offset++, this.match += a, this.matched += a;
        var l = a.match(/(?:\r\n?|\n).*/g);
        return l ? (this.yylineno++, this.yylloc.last_line++) : this.yylloc.last_column++, this.options.ranges && this.yylloc.range[1]++, this._input = this._input.slice(1), a;
      },
      // unshifts one char (or a string) into the input
      unput: function(a) {
        var l = a.length, f = a.split(/(?:\r\n?|\n)/g);
        this._input = a + this._input, this.yytext = this.yytext.substr(0, this.yytext.length - l), this.offset -= l;
        var i = this.match.split(/(?:\r\n?|\n)/g);
        this.match = this.match.substr(0, this.match.length - 1), this.matched = this.matched.substr(0, this.matched.length - 1), f.length - 1 && (this.yylineno -= f.length - 1);
        var C = this.yylloc.range;
        return this.yylloc = {
          first_line: this.yylloc.first_line,
          last_line: this.yylineno + 1,
          first_column: this.yylloc.first_column,
          last_column: f ? (f.length === i.length ? this.yylloc.first_column : 0) + i[i.length - f.length].length - f[0].length : this.yylloc.first_column - l
        }, this.options.ranges && (this.yylloc.range = [C[0], C[0] + this.yyleng - l]), this.yyleng = this.yytext.length, this;
      },
      // When called from action, caches matched text and appends it on next action
      more: function() {
        return this._more = !0, this;
      },
      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
      reject: function() {
        if (this.options.backtrack_lexer)
          this._backtrack = !0;
        else
          return this.parseError("Lexical error on line " + (this.yylineno + 1) + `. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
` + this.showPosition(), {
            text: "",
            token: null,
            line: this.yylineno
          });
        return this;
      },
      // retain first n characters of the match
      less: function(a) {
        this.unput(this.match.slice(a));
      },
      // displays already matched input, i.e. for error messages
      pastInput: function() {
        var a = this.matched.substr(0, this.matched.length - this.match.length);
        return (a.length > 20 ? "..." : "") + a.substr(-20).replace(/\n/g, "");
      },
      // displays upcoming input, i.e. for error messages
      upcomingInput: function() {
        var a = this.match;
        return a.length < 20 && (a += this._input.substr(0, 20 - a.length)), (a.substr(0, 20) + (a.length > 20 ? "..." : "")).replace(/\n/g, "");
      },
      // displays the character position where the lexing error occurred, i.e. for error messages
      showPosition: function() {
        var a = this.pastInput(), l = new Array(a.length + 1).join("-");
        return a + this.upcomingInput() + `
` + l + "^";
      },
      // test the lexed token: return FALSE when not a match, otherwise return token
      test_match: function(a, l) {
        var f, i, C;
        if (this.options.backtrack_lexer && (C = {
          yylineno: this.yylineno,
          yylloc: {
            first_line: this.yylloc.first_line,
            last_line: this.last_line,
            first_column: this.yylloc.first_column,
            last_column: this.yylloc.last_column
          },
          yytext: this.yytext,
          match: this.match,
          matches: this.matches,
          matched: this.matched,
          yyleng: this.yyleng,
          offset: this.offset,
          _more: this._more,
          _input: this._input,
          yy: this.yy,
          conditionStack: this.conditionStack.slice(0),
          done: this.done
        }, this.options.ranges && (C.yylloc.range = this.yylloc.range.slice(0))), i = a[0].match(/(?:\r\n?|\n).*/g), i && (this.yylineno += i.length), this.yylloc = {
          first_line: this.yylloc.last_line,
          last_line: this.yylineno + 1,
          first_column: this.yylloc.last_column,
          last_column: i ? i[i.length - 1].length - i[i.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + a[0].length
        }, this.yytext += a[0], this.match += a[0], this.matches = a, this.yyleng = this.yytext.length, this.options.ranges && (this.yylloc.range = [this.offset, this.offset += this.yyleng]), this._more = !1, this._backtrack = !1, this._input = this._input.slice(a[0].length), this.matched += a[0], f = this.performAction.call(this, this.yy, this, l, this.conditionStack[this.conditionStack.length - 1]), this.done && this._input && (this.done = !1), f)
          return f;
        if (this._backtrack) {
          for (var t in C)
            this[t] = C[t];
          return !1;
        }
        return !1;
      },
      // return next match in input
      next: function() {
        if (this.done)
          return this.EOF;
        this._input || (this.done = !0);
        var a, l, f, i;
        this._more || (this.yytext = "", this.match = "");
        for (var C = this._currentRules(), t = 0; t < C.length; t++)
          if (f = this._input.match(this.rules[C[t]]), f && (!l || f[0].length > l[0].length)) {
            if (l = f, i = t, this.options.backtrack_lexer) {
              if (a = this.test_match(f, C[t]), a !== !1)
                return a;
              if (this._backtrack) {
                l = !1;
                continue;
              } else
                return !1;
            } else if (!this.options.flex)
              break;
          }
        return l ? (a = this.test_match(l, C[i]), a !== !1 ? a : !1) : this._input === "" ? this.EOF : this.parseError("Lexical error on line " + (this.yylineno + 1) + `. Unrecognized text.
` + this.showPosition(), {
          text: "",
          token: null,
          line: this.yylineno
        });
      },
      // return next match that has a token
      lex: function() {
        var l = this.next();
        return l || this.lex();
      },
      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
      begin: function(l) {
        this.conditionStack.push(l);
      },
      // pop the previously active lexer condition state off the condition stack
      popState: function() {
        var l = this.conditionStack.length - 1;
        return l > 0 ? this.conditionStack.pop() : this.conditionStack[0];
      },
      // produce the lexer rule set which is active for the currently active lexer condition state
      _currentRules: function() {
        return this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1] ? this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules : this.conditions.INITIAL.rules;
      },
      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
      topState: function(l) {
        return l = this.conditionStack.length - 1 - Math.abs(l || 0), l >= 0 ? this.conditionStack[l] : "INITIAL";
      },
      // alias for begin(condition)
      pushState: function(l) {
        this.begin(l);
      },
      // return the number of states currently on the stack
      stateStackSize: function() {
        return this.conditionStack.length;
      },
      options: {},
      performAction: function(l, f, i, C) {
        switch (i) {
          case 0:
            return this.begin("acc_title"), 34;
          case 1:
            return this.popState(), "acc_title_value";
          case 2:
            return this.begin("acc_descr"), 36;
          case 3:
            return this.popState(), "acc_descr_value";
          case 4:
            this.begin("acc_descr_multiline");
            break;
          case 5:
            this.popState();
            break;
          case 6:
            return "acc_descr_multiline_value";
          case 7:
            this.begin("callbackname");
            break;
          case 8:
            this.popState();
            break;
          case 9:
            this.popState(), this.begin("callbackargs");
            break;
          case 10:
            return 92;
          case 11:
            this.popState();
            break;
          case 12:
            return 93;
          case 13:
            return "MD_STR";
          case 14:
            this.popState();
            break;
          case 15:
            this.begin("md_string");
            break;
          case 16:
            return "STR";
          case 17:
            this.popState();
            break;
          case 18:
            this.pushState("string");
            break;
          case 19:
            return 81;
          case 20:
            return 99;
          case 21:
            return 82;
          case 22:
            return 101;
          case 23:
            return 83;
          case 24:
            return 84;
          case 25:
            return 94;
          case 26:
            this.begin("click");
            break;
          case 27:
            this.popState();
            break;
          case 28:
            return 85;
          case 29:
            return l.lex.firstGraph() && this.begin("dir"), 12;
          case 30:
            return l.lex.firstGraph() && this.begin("dir"), 12;
          case 31:
            return l.lex.firstGraph() && this.begin("dir"), 12;
          case 32:
            return 27;
          case 33:
            return 32;
          case 34:
            return 95;
          case 35:
            return 95;
          case 36:
            return 95;
          case 37:
            return 95;
          case 38:
            return this.popState(), 13;
          case 39:
            return this.popState(), 14;
          case 40:
            return this.popState(), 14;
          case 41:
            return this.popState(), 14;
          case 42:
            return this.popState(), 14;
          case 43:
            return this.popState(), 14;
          case 44:
            return this.popState(), 14;
          case 45:
            return this.popState(), 14;
          case 46:
            return this.popState(), 14;
          case 47:
            return this.popState(), 14;
          case 48:
            return this.popState(), 14;
          case 49:
            return 118;
          case 50:
            return 119;
          case 51:
            return 120;
          case 52:
            return 121;
          case 53:
            return 102;
          case 54:
            return 108;
          case 55:
            return 44;
          case 56:
            return 58;
          case 57:
            return 42;
          case 58:
            return 8;
          case 59:
            return 103;
          case 60:
            return 112;
          case 61:
            return this.popState(), 75;
          case 62:
            return this.pushState("edgeText"), 73;
          case 63:
            return 116;
          case 64:
            return this.popState(), 75;
          case 65:
            return this.pushState("thickEdgeText"), 73;
          case 66:
            return 116;
          case 67:
            return this.popState(), 75;
          case 68:
            return this.pushState("dottedEdgeText"), 73;
          case 69:
            return 116;
          case 70:
            return 75;
          case 71:
            return this.popState(), 51;
          case 72:
            return "TEXT";
          case 73:
            return this.pushState("ellipseText"), 50;
          case 74:
            return this.popState(), 53;
          case 75:
            return this.pushState("text"), 52;
          case 76:
            return this.popState(), 55;
          case 77:
            return this.pushState("text"), 54;
          case 78:
            return 56;
          case 79:
            return this.pushState("text"), 65;
          case 80:
            return this.popState(), 62;
          case 81:
            return this.pushState("text"), 61;
          case 82:
            return this.popState(), 47;
          case 83:
            return this.pushState("text"), 46;
          case 84:
            return this.popState(), 67;
          case 85:
            return this.popState(), 69;
          case 86:
            return 114;
          case 87:
            return this.pushState("trapText"), 66;
          case 88:
            return this.pushState("trapText"), 68;
          case 89:
            return 115;
          case 90:
            return 65;
          case 91:
            return 87;
          case 92:
            return "SEP";
          case 93:
            return 86;
          case 94:
            return 112;
          case 95:
            return 108;
          case 96:
            return 42;
          case 97:
            return 106;
          case 98:
            return 111;
          case 99:
            return 113;
          case 100:
            return this.popState(), 60;
          case 101:
            return this.pushState("text"), 60;
          case 102:
            return this.popState(), 49;
          case 103:
            return this.pushState("text"), 48;
          case 104:
            return this.popState(), 31;
          case 105:
            return this.pushState("text"), 29;
          case 106:
            return this.popState(), 64;
          case 107:
            return this.pushState("text"), 63;
          case 108:
            return "TEXT";
          case 109:
            return "QUOTE";
          case 110:
            return 9;
          case 111:
            return 10;
          case 112:
            return 11;
        }
      },
      rules: [/^(?:accTitle\s*:\s*)/, /^(?:(?!\n||)*[^\n]*)/, /^(?:accDescr\s*:\s*)/, /^(?:(?!\n||)*[^\n]*)/, /^(?:accDescr\s*\{\s*)/, /^(?:[\}])/, /^(?:[^\}]*)/, /^(?:call[\s]+)/, /^(?:\([\s]*\))/, /^(?:\()/, /^(?:[^(]*)/, /^(?:\))/, /^(?:[^)]*)/, /^(?:[^`"]+)/, /^(?:[`]["])/, /^(?:["][`])/, /^(?:[^"]+)/, /^(?:["])/, /^(?:["])/, /^(?:style\b)/, /^(?:default\b)/, /^(?:linkStyle\b)/, /^(?:interpolate\b)/, /^(?:classDef\b)/, /^(?:class\b)/, /^(?:href[\s])/, /^(?:click[\s]+)/, /^(?:[\s\n])/, /^(?:[^\s\n]*)/, /^(?:flowchart-elk\b)/, /^(?:graph\b)/, /^(?:flowchart\b)/, /^(?:subgraph\b)/, /^(?:end\b\s*)/, /^(?:_self\b)/, /^(?:_blank\b)/, /^(?:_parent\b)/, /^(?:_top\b)/, /^(?:(\r?\n)*\s*\n)/, /^(?:\s*LR\b)/, /^(?:\s*RL\b)/, /^(?:\s*TB\b)/, /^(?:\s*BT\b)/, /^(?:\s*TD\b)/, /^(?:\s*BR\b)/, /^(?:\s*<)/, /^(?:\s*>)/, /^(?:\s*\^)/, /^(?:\s*v\b)/, /^(?:.*direction\s+TB[^\n]*)/, /^(?:.*direction\s+BT[^\n]*)/, /^(?:.*direction\s+RL[^\n]*)/, /^(?:.*direction\s+LR[^\n]*)/, /^(?:[0-9]+)/, /^(?:#)/, /^(?::::)/, /^(?::)/, /^(?:&)/, /^(?:;)/, /^(?:,)/, /^(?:\*)/, /^(?:\s*[xo<]?--+[-xo>]\s*)/, /^(?:\s*[xo<]?--\s*)/, /^(?:[^-]|-(?!-)+)/, /^(?:\s*[xo<]?==+[=xo>]\s*)/, /^(?:\s*[xo<]?==\s*)/, /^(?:[^=]|=(?!))/, /^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/, /^(?:\s*[xo<]?-\.\s*)/, /^(?:[^\.]|\.(?!))/, /^(?:\s*~~[\~]+\s*)/, /^(?:[-/\)][\)])/, /^(?:[^\(\)\[\]\{\}]|!\)+)/, /^(?:\(-)/, /^(?:\]\))/, /^(?:\(\[)/, /^(?:\]\])/, /^(?:\[\[)/, /^(?:\[\|)/, /^(?:>)/, /^(?:\)\])/, /^(?:\[\()/, /^(?:\)\)\))/, /^(?:\(\(\()/, /^(?:[\\(?=\])][\]])/, /^(?:\/(?=\])\])/, /^(?:\/(?!\])|\\(?!\])|[^\\\[\]\(\)\{\}\/]+)/, /^(?:\[\/)/, /^(?:\[\\)/, /^(?:<)/, /^(?:>)/, /^(?:\^)/, /^(?:\\\|)/, /^(?:v\b)/, /^(?:\*)/, /^(?:#)/, /^(?:&)/, /^(?:([A-Za-z0-9!"\#$%&'*+\.`?\\_\/]|-(?=[^\>\-\.])|(?!))+)/, /^(?:-)/, /^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/, /^(?:\|)/, /^(?:\|)/, /^(?:\))/, /^(?:\()/, /^(?:\])/, /^(?:\[)/, /^(?:(\}))/, /^(?:\{)/, /^(?:[^\[\]\(\)\{\}\|\"]+)/, /^(?:")/, /^(?:(\r?\n)+)/, /^(?:\s)/, /^(?:$)/],
      conditions: { callbackargs: { rules: [11, 12, 15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, callbackname: { rules: [8, 9, 10, 15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, href: { rules: [15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, click: { rules: [15, 18, 27, 28, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, dottedEdgeText: { rules: [15, 18, 67, 69, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, thickEdgeText: { rules: [15, 18, 64, 66, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, edgeText: { rules: [15, 18, 61, 63, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, trapText: { rules: [15, 18, 70, 73, 75, 77, 81, 83, 84, 85, 86, 87, 88, 101, 103, 105, 107], inclusive: !1 }, ellipseText: { rules: [15, 18, 70, 71, 72, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, text: { rules: [15, 18, 70, 73, 74, 75, 76, 77, 80, 81, 82, 83, 87, 88, 100, 101, 102, 103, 104, 105, 106, 107, 108], inclusive: !1 }, vertex: { rules: [15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, dir: { rules: [15, 18, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, acc_descr_multiline: { rules: [5, 6, 15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, acc_descr: { rules: [3, 15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, acc_title: { rules: [1, 15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, md_string: { rules: [13, 14, 15, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, string: { rules: [15, 16, 17, 18, 70, 73, 75, 77, 81, 83, 87, 88, 101, 103, 105, 107], inclusive: !1 }, INITIAL: { rules: [0, 2, 4, 7, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 67, 68, 70, 73, 75, 77, 78, 79, 81, 83, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 101, 103, 105, 107, 109, 110, 111, 112], inclusive: !0 } }
    };
    return f1;
  }();
  at.lexer = Ce;
  function ct() {
    this.yy = {};
  }
  return ct.prototype = at, at.Parser = ct, new ct();
}();
dt.parser = dt;
const Xe = dt, Oe = "flowchart-";
let Xt = 0, v1 = et(), D = {}, H = [], L1 = {}, c1 = [], $1 = {}, tt = {}, Z1 = 0, pt = !0, z, st, ut = [];
const nt = (e) => we.sanitizeText(e, v1), P1 = function(e) {
  const u = Object.keys(D);
  for (const n of u)
    if (D[n].id === e)
      return D[n].domId;
  return e;
}, Ht = function(e, u, n, r, c, o, h = {}) {
  let U, F = e;
  F !== void 0 && F.trim().length !== 0 && (D[F] === void 0 && (D[F] = {
    id: F,
    labelType: "text",
    domId: Oe + F + "-" + Xt,
    styles: [],
    classes: []
  }), Xt++, u !== void 0 ? (v1 = et(), U = nt(u.text.trim()), D[F].labelType = u.type, U[0] === '"' && U[U.length - 1] === '"' && (U = U.substring(1, U.length - 1)), D[F].text = U) : D[F].text === void 0 && (D[F].text = e), n !== void 0 && (D[F].type = n), r != null && r.forEach(function(w) {
    D[F].styles.push(w);
  }), c != null && c.forEach(function(w) {
    D[F].classes.push(w);
  }), o !== void 0 && (D[F].dir = o), D[F].props === void 0 ? D[F].props = h : h !== void 0 && Object.assign(D[F].props, h));
}, Wt = function(e, u, n) {
  const o = { start: e, end: u, type: void 0, text: "", labelType: "text" };
  J1.info("abc78 Got edge...", o);
  const h = n.text;
  if (h !== void 0 && (o.text = nt(h.text.trim()), o.text[0] === '"' && o.text[o.text.length - 1] === '"' && (o.text = o.text.substring(1, o.text.length - 1)), o.labelType = h.type), n !== void 0 && (o.type = n.type, o.stroke = n.stroke, o.length = n.length), (o == null ? void 0 : o.length) > 10 && (o.length = 10), H.length < (v1.maxEdges ?? 500))
    J1.info("abc78 pushing edge..."), H.push(o);
  else
    throw new Error(
      `Edge limit exceeded. ${H.length} edges found, but the limit is ${v1.maxEdges}.

Initialize mermaid with maxEdges set to a higher number to allow more edges.
You cannot set this config via configuration inside the diagram as it is a secure config.
You have to call mermaid.initialize.`
    );
}, qt = function(e, u, n) {
  J1.info("addLink (abc78)", e, u, n);
  let r, c;
  for (r = 0; r < e.length; r++)
    for (c = 0; c < u.length; c++)
      Wt(e[r], u[c], n);
}, Qt = function(e, u) {
  e.forEach(function(n) {
    n === "default" ? H.defaultInterpolate = u : H[n].interpolate = u;
  });
}, Zt = function(e, u) {
  e.forEach(function(n) {
    if (n >= H.length)
      throw new Error(
        `The index ${n} for linkStyle is out of bounds. Valid indices for linkStyle are between 0 and ${H.length - 1}. (Help: Ensure that the index is within the range of existing edges.)`
      );
    n === "default" ? H.defaultStyle = u : (At.isSubstringInArray("fill", u) === -1 && u.push("fill:none"), H[n].style = u);
  });
}, Jt = function(e, u) {
  e.split(",").forEach(function(n) {
    L1[n] === void 0 && (L1[n] = { id: n, styles: [], textStyles: [] }), u != null && u.forEach(function(r) {
      if (r.match("color")) {
        const c = r.replace("fill", "bgFill").replace("color", "fill");
        L1[n].textStyles.push(c);
      }
      L1[n].styles.push(r);
    });
  });
}, $t = function(e) {
  z = e, z.match(/.*</) && (z = "RL"), z.match(/.*\^/) && (z = "BT"), z.match(/.*>/) && (z = "LR"), z.match(/.*v/) && (z = "TB"), z === "TD" && (z = "TB");
}, it = function(e, u) {
  e.split(",").forEach(function(n) {
    let r = n;
    D[r] !== void 0 && D[r].classes.push(u), $1[r] !== void 0 && $1[r].classes.push(u);
  });
}, Pe = function(e, u) {
  e.split(",").forEach(function(n) {
    u !== void 0 && (tt[st === "gen-1" ? P1(n) : n] = nt(u));
  });
}, Ue = function(e, u, n) {
  let r = P1(e);
  if (et().securityLevel !== "loose" || u === void 0)
    return;
  let c = [];
  if (typeof n == "string") {
    c = n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);
    for (let o = 0; o < c.length; o++) {
      let h = c[o].trim();
      h.charAt(0) === '"' && h.charAt(h.length - 1) === '"' && (h = h.substr(1, h.length - 2)), c[o] = h;
    }
  }
  c.length === 0 && c.push(e), D[e] !== void 0 && (D[e].haveCallback = !0, ut.push(function() {
    const o = document.querySelector(`[id="${r}"]`);
    o !== null && o.addEventListener(
      "click",
      function() {
        At.runFunc(u, ...c);
      },
      !1
    );
  }));
}, te = function(e, u, n) {
  e.split(",").forEach(function(r) {
    D[r] !== void 0 && (D[r].link = At.formatUrl(u, v1), D[r].linkTarget = n);
  }), it(e, "clickable");
}, ee = function(e) {
  if (tt.hasOwnProperty(e))
    return tt[e];
}, se = function(e, u, n) {
  e.split(",").forEach(function(r) {
    Ue(r, u, n);
  }), it(e, "clickable");
}, ue = function(e) {
  ut.forEach(function(u) {
    u(e);
  });
}, ne = function() {
  return z.trim();
}, ie = function() {
  return D;
}, re = function() {
  return H;
}, ae = function() {
  return L1;
}, ce = function(e) {
  let u = w1(".mermaidTooltip");
  (u._groups || u)[0][0] === null && (u = w1("body").append("div").attr("class", "mermaidTooltip").style("opacity", 0)), w1(e).select("svg").selectAll("g.node").on("mouseover", function() {
    const c = w1(this);
    if (c.attr("title") === null)
      return;
    const h = this.getBoundingClientRect();
    u.transition().duration(200).style("opacity", ".9"), u.text(c.attr("title")).style("left", window.scrollX + h.left + (h.right - h.left) / 2 + "px").style("top", window.scrollY + h.bottom + "px"), u.html(u.html().replace(/&lt;br\/&gt;/g, "<br/>")), c.classed("hover", !0);
  }).on("mouseout", function() {
    u.transition().duration(500).style("opacity", 0), w1(this).classed("hover", !1);
  });
};
ut.push(ce);
const le = function(e = "gen-1") {
  D = {}, L1 = {}, H = [], ut = [ce], c1 = [], $1 = {}, Z1 = 0, tt = {}, pt = !0, st = e, v1 = et(), Ne();
}, oe = (e) => {
  st = e || "gen-2";
}, he = function() {
  return "fill:#ffa;stroke: #f66; stroke-width: 3px; stroke-dasharray: 5, 5;fill:#ffa;stroke: #666;";
}, fe = function(e, u, n) {
  let r = e.text.trim(), c = n.text;
  e === n && n.text.match(/\s/) && (r = void 0);
  function o(X) {
    const l1 = { boolean: {}, number: {}, string: {} }, d1 = [];
    let p1;
    return { nodeList: X.filter(function(k) {
      const o1 = typeof k;
      return k.stmt && k.stmt === "dir" ? (p1 = k.value, !1) : k.trim() === "" ? !1 : o1 in l1 ? l1[o1].hasOwnProperty(k) ? !1 : l1[o1][k] = !0 : d1.includes(k) ? !1 : d1.push(k);
    }), dir: p1 };
  }
  let h = [];
  const { nodeList: U, dir: F } = o(h.concat.apply(h, u));
  if (h = U, st === "gen-1")
    for (let X = 0; X < h.length; X++)
      h[X] = P1(h[X]);
  r = r || "subGraph" + Z1, c = c || "", c = nt(c), Z1 = Z1 + 1;
  const w = {
    id: r,
    nodes: h,
    title: c.trim(),
    classes: [],
    dir: F,
    labelType: n.type
  };
  return J1.info("Adding", w.id, w.nodes, w.dir), w.nodes = Fe(w, c1).nodes, c1.push(w), $1[r] = w, r;
}, Ge = function(e) {
  for (const [u, n] of c1.entries())
    if (n.id === e)
      return u;
  return -1;
};
let O1 = -1;
const de = [], pe = function(e, u) {
  const n = c1[u].nodes;
  if (O1 = O1 + 1, O1 > 2e3)
    return;
  if (de[O1] = u, c1[u].id === e)
    return {
      result: !0,
      count: 0
    };
  let r = 0, c = 1;
  for (; r < n.length; ) {
    const o = Ge(n[r]);
    if (o >= 0) {
      const h = pe(e, o);
      if (h.result)
        return {
          result: !0,
          count: c + h.count
        };
      c = c + h.count;
    }
    r = r + 1;
  }
  return {
    result: !1,
    count: c
  };
}, Ae = function(e) {
  return de[e];
}, Ee = function() {
  O1 = -1, c1.length > 0 && pe("none", c1.length - 1);
}, ke = function() {
  return c1;
}, be = () => pt ? (pt = !1, !0) : !1, Me = (e) => {
  let u = e.trim(), n = "arrow_open";
  switch (u[0]) {
    case "<":
      n = "arrow_point", u = u.slice(1);
      break;
    case "x":
      n = "arrow_cross", u = u.slice(1);
      break;
    case "o":
      n = "arrow_circle", u = u.slice(1);
      break;
  }
  let r = "normal";
  return u.includes("=") && (r = "thick"), u.includes(".") && (r = "dotted"), { type: n, stroke: r };
}, Ke = (e, u) => {
  const n = u.length;
  let r = 0;
  for (let c = 0; c < n; ++c)
    u[c] === e && ++r;
  return r;
}, Ye = (e) => {
  const u = e.trim();
  let n = u.slice(0, -1), r = "arrow_open";
  switch (u.slice(-1)) {
    case "x":
      r = "arrow_cross", u[0] === "x" && (r = "double_" + r, n = n.slice(1));
      break;
    case ">":
      r = "arrow_point", u[0] === "<" && (r = "double_" + r, n = n.slice(1));
      break;
    case "o":
      r = "arrow_circle", u[0] === "o" && (r = "double_" + r, n = n.slice(1));
      break;
  }
  let c = "normal", o = n.length - 1;
  n[0] === "=" && (c = "thick"), n[0] === "~" && (c = "invisible");
  let h = Ke(".", n);
  return h && (c = "dotted", o = h), { type: r, stroke: c, length: o };
}, ge = (e, u) => {
  const n = Ye(e);
  let r;
  if (u) {
    if (r = Me(u), r.stroke !== n.stroke)
      return { type: "INVALID", stroke: "INVALID" };
    if (r.type === "arrow_open")
      r.type = n.type;
    else {
      if (r.type !== n.type)
        return { type: "INVALID", stroke: "INVALID" };
      r.type = "double_" + r.type;
    }
    return r.type === "double_arrow" && (r.type = "double_arrow_point"), r.length = n.length, r;
  }
  return n;
}, De = (e, u) => {
  let n = !1;
  return e.forEach((r) => {
    r.nodes.indexOf(u) >= 0 && (n = !0);
  }), n;
}, Fe = (e, u) => {
  const n = [];
  return e.nodes.forEach((r, c) => {
    De(u, r) || n.push(e.nodes[c]);
  }), { nodes: n };
}, Te = {
  firstGraph: be
}, je = {
  defaultConfig: () => me.flowchart,
  setAccTitle: ye,
  getAccTitle: Ve,
  getAccDescription: Le,
  setAccDescription: ve,
  addVertex: Ht,
  lookUpDomId: P1,
  addLink: qt,
  updateLinkInterpolate: Qt,
  updateLink: Zt,
  addClass: Jt,
  setDirection: $t,
  setClass: it,
  setTooltip: Pe,
  getTooltip: ee,
  setClickEvent: se,
  setLink: te,
  bindFunctions: ue,
  getDirection: ne,
  getVertices: ie,
  getEdges: re,
  getClasses: ae,
  clear: le,
  setGen: oe,
  defaultStyle: he,
  addSubGraph: fe,
  getDepthFirstPos: Ae,
  indexNodes: Ee,
  getSubGraphs: ke,
  destructLink: ge,
  lex: Te,
  exists: De,
  makeUniq: Fe,
  setDiagramTitle: Ie,
  getDiagramTitle: Re
}, He = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  addClass: Jt,
  addLink: qt,
  addSingleLink: Wt,
  addSubGraph: fe,
  addVertex: Ht,
  bindFunctions: ue,
  clear: le,
  default: je,
  defaultStyle: he,
  destructLink: ge,
  firstGraph: be,
  getClasses: ae,
  getDepthFirstPos: Ae,
  getDirection: ne,
  getEdges: re,
  getSubGraphs: ke,
  getTooltip: ee,
  getVertices: ie,
  indexNodes: Ee,
  lex: Te,
  lookUpDomId: P1,
  setClass: it,
  setClickEvent: se,
  setDirection: $t,
  setGen: oe,
  setLink: te,
  updateLink: Zt,
  updateLinkInterpolate: Qt
}, Symbol.toStringTag, { value: "Module" }));
export {
  He as d,
  je as f,
  Xe as p
};
