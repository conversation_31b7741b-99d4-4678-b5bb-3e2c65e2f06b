<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./assets/generated/logo-lk9YP_Ej.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JSON Viewer</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
        width: 100vw;
      }
      #app {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        max-width: none !important;
        text-align: left !important;
      }
      .p-2 {
        padding: 0 0.5rem !important;
      }
      .jsoneditor {
        border: 1px solid var(--border-color) !important;
        border-radius: 4px;
      }
      .jsoneditor-menu {
        background-color: var(--bg-color);
        border-bottom: 1px solid var(--border-color);
      }
      .jsoneditor-menu a {
        color: var(--text-color);
      }
      .jsoneditor-menu a:hover {
        background-color: var(--header-hover-bg-color);
      }
      .jsoneditor-contextmenu {
        background-color: var(--bg-color);
        border: 1px solid var(--border-color);
      }
      .jsoneditor-contextmenu .jsoneditor-menu {
        background-color: var(--bg-color);
      }
      .jsoneditor-contextmenu .jsoneditor-menu a {
        color: var(--text-color);
      }
      .jsoneditor-contextmenu .jsoneditor-menu a:hover {
        background-color: var(--header-hover-bg-color);
      }
    </style>
    <script type="module" crossorigin src="./assets/generated/json-viewer-DCfYH61N.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/generated/style-CvhRCPHz.js">
    <link rel="modulepreload" crossorigin href="./assets/generated/error-Bhv9ZvWl.js">
    <link rel="stylesheet" crossorigin href="./assets/generated/style-Bs2aKLra.css">
    <link rel="stylesheet" crossorigin href="./assets/generated/json-viewer-CC3rwVrA.css">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html> 