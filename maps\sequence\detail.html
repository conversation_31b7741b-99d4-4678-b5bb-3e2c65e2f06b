<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>XCodeMap Detail</title>
    <link href="../assets/element-ui-2.15.14/index.min.css" rel="stylesheet">
    <link href="../assets/xcodemap/theme.css" rel="stylesheet">
    <link href="../assets/xcodemap/detail.css" rel="stylesheet">
    <script src="../assets/xcodemap/common.js"></script>
    <script>
    </script>
</head>
<body>
<div id="app">
    <el-container>
        <el-aside  :style="{ width: leftAside.asideWidth + 'px' }" class="left-aside">
            <div class="left-container">
                <el-scrollbar ref="scrollMenuLeftRef">
                    <div class="goto-button-wrap">
                        <el-tabs type="card" v-model="graph.detailTabName"
                                 @tab-click="graph.handleDetailTabClick">
                            <el-tab-pane label="堆栈" name="stacks">
                            </el-tab-pane>
                            <el-tab-pane label="参数" name="args">
                            </el-tab-pane>

                        </el-tabs>
                        <div class="stepBar">
                            <div class="searchBarMenu " onclick="vue.graph.gotoCurrentDefAndFocus()" data-tooltip="查看当前函数的定义" >
                                <img src="../assets/xcodemap/icons/locate.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu " onclick="vue.graph.searchCurrentStack()" data-tooltip="在序列图中显示堆栈">
                                <img src="../assets/xcodemap/icons/CallStack_GrayDark.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu " onclick="vue.graph.prevLoop()" data-tooltip="Prev Loop">
                                <img src="../assets/xcodemap/icons/prevLoop.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu " onclick="vue.graph.nextLoop()" data-tooltip="Next Loop">
                                <img src="../assets/xcodemap/icons/nextLoop.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu " onclick="vue.graph.stepBack()" data-tooltip="Step Back (Shift+F8)">
                                <img src="../assets/xcodemap/icons/stepBack.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu" onclick="vue.graph.stepOver()" data-tooltip="Step Over (F8)">
                                <img src="../assets/xcodemap/icons/stepOver.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu" onclick="vue.graph.stepInto()" data-tooltip="Step Into (F7)">
                                <img src="../assets/xcodemap/icons/stepInto.svg" class="x-icon">
                            </div>
                            <div class="searchBarMenu" onclick="vue.graph.stepOut()" data-tooltip="Step Out (Shift+F7)">
                                <img src="../assets/xcodemap/icons/stepOut.svg" class="x-icon">
                            </div>
                        </div>
                        <!--<div class="goto-button">
                            <el-button @click="graph.gotoCurrentDef()" plain>定义</el-button>
                        </div>
                        <div class="goto-button">
                            <el-button @click="graph.searchCurrentUsage()" plain>用法</el-button>
                        </div>
                        <div class="goto-button">
                            <el-button @click="graph.searchCurrentStack()" plain>堆栈</el-button>
                        </div>
                        <div class="goto-button">
                            <el-button @click="graph.functionCallDetailTree.unfoldAllNodes()" plain>展开</el-button>
                        </div>
                        <div class="goto-button">
                            <el-button @click="graph.functionCallDetailTree.foldAllNodes()" plain>收起</el-button>
                        </div>-->
                    </div>
                    <div v-show="graph.detailTabName === 'stacks' " class="left-tab-content-stacks">
                        <div :key="index"  v-for="(item, index) in graph.functionCallStackData.stacks"
                             :class="{'left-tab-content-stacks-item-normal': !item.inPackage && !item.current,
                                    'left-tab-content-stacks-item-package': item.inPackage && !item.current,
                                    'left-tab-content-stacks-item-current': item.current}" class="left-tab-content-stacks-item" @click="vue.graph.clickStackItem(item)">
                            {{item.methodName}}:{{item.codeLine}},&nbsp;{{item.simpleClassName}}&nbsp;({{item.packageName}})
                        </div>
                    </div>
                    <div v-show="graph.detailTabName === 'args' ">
                        <el-divider></el-divider>
                        <div class="local-vars-section">
                            <div class="local-vars-header" @click="toggleLocalVars">
                                <span class="local-vars-title">局部变量 ({{ (graph.functionCallLocalVarTree.treeData.rootNodes && graph.functionCallLocalVarTree.treeData.rootNodes.length) || 0 }})</span>
                                <el-button 
                                    size="mini" 
                                    type="text" 
                                    @click.stop="toggleLocalVars"
                                    class="local-vars-toggle-btn">
                                    <i :class="localVarsCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i>
                                </el-button>
                            </div>
                            <div class="left-tab-content" v-show="!localVarsCollapsed">
                                <el-input
                                    placeholder="输入关键字进行过滤"
                                    v-model="graph.functionCallLocalVarTree.filterText"
                                    class="filter-input">
                                </el-input>
                                <el-tree
                                        :data="graph.functionCallLocalVarTree.treeData.rootNodes"
                                        :highlight-current="true"
                                        :indent="0"
                                        :props="graph.functionCallLocalVarTree.defaultProps"
                                        class="function-call-detail-tree"
                                        :default-expanded-keys="graph.functionCallLocalVarTree.treeData.expandedKeys"
                                        node-key="nodeKey"
                                        :render-content="graph.functionCallLocalVarTree.renderContent"
                                        @node-expand="graph.clickFuncTreeElement"
                                        @node-click="graph.clickFuncTreeElement"
                                        @node-contextmenu="rightClickFunctionCallDetailTree"
                                        ref="functionCallLocalVarTree">
                                </el-tree>
                            </div>
                        </div>
                        <el-divider></el-divider>
                        <div class="func-notes">
                            <div class="func-notes-head">
                                <span>{{graph.currFuncCallDetail.notesHead}}</span>
                                <div class="func-notes-head-button">
                                    <el-button size="mini" @click="handleViewLoop">查看循环</el-button>
                                    <el-button size="mini" @click="handleAddToChat">加入聊天</el-button>
                                </div>
                            </div>
                            <div class="func-notes-signature">{{graph.currFuncCallDetail.signature}}</div>
                            <el-input
                                placeholder="输入关键字进行过滤"
                                v-model="graph.functionCallDetailTree.filterText"
                                class="filter-input">
                            </el-input>
                            <el-tree
                                    :data="graph.functionCallDetailTree.treeData.rootNodes"
                                    :highlight-current="true"
                                    :indent="0"
                                    :props="graph.functionCallDetailTree.defaultProps"
                                    class="function-call-detail-tree"
                                    :default-expanded-keys="graph.functionCallDetailTree.treeData.expandedKeys"
                                    node-key="nodeKey"
                                    :render-content="graph.functionCallDetailTree.renderContent"
                                    @node-expand="graph.clickFuncTreeElement"
                                    @node-click="graph.clickFuncTreeElement"
                                    @node-contextmenu="rightClickFunctionCallDetailTree"
                                    ref="functionCallDetailTree">
                            </el-tree>
                            <div class="func-notes-body">{{graph.currFuncCallDetail.notesDetail}}</div>
                        </div>
                        <el-divider></el-divider>
                        <div class="sql-requests">
                            <div class="sql-requests-head" @click="handleToggleSqlRequests">
                                <span class="sql-requests-title">SQL 请求 ({{ graph.sqlRequestsCount }})</span>
                                <el-button 
                                    size="mini" 
                                    type="text" 
                                    class="sql-requests-toggle-btn"
                                    @click.stop="handleToggleSqlRequests">
                                    <i :class="graph.sqlRequestsCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i>
                                </el-button>
                            </div>
                            <el-input
                                v-show="!graph.sqlRequestsCollapsed"
                                placeholder="输入关键字进行过滤"
                                v-model="graph.functionCallInnerOutputRequestsTree.filterText"
                                class="filter-input">
                            </el-input>
                            <el-tree
                                v-show="!graph.sqlRequestsCollapsed"
                                :data="graph.functionCallInnerOutputRequestsTree.treeData.rootNodes"
                                :highlight-current="true"
                                :indent="0"
                                :props="graph.functionCallInnerOutputRequestsTree.defaultProps"
                                class="function-call-detail-tree"
                                :default-expanded-keys="graph.functionCallInnerOutputRequestsTree.treeData.expandedKeys"
                                node-key="nodeKey"
                                :render-content="graph.functionCallInnerOutputRequestsTree.renderContent"
                                @node-expand="graph.clickFuncTreeElement"
                                @node-click="graph.clickFuncTreeElement"
                                @node-contextmenu="rightClickFunctionCallDetailTree"
                                ref="functionCallInnerOutputRequestsTree">
                            </el-tree>
                        </div>
                    </div>

                </el-scrollbar>
            </div>
        </el-aside>
        <div class="x-handle" @mousedown="leftAside.mouseDown"></div>
        <el-main>
            <div class="main-container">
                <div class="search-tab graph-popup-content" v-if="searchTab.show">
                    <el-scrollbar>
                        <el-tabs type="border-card" class="search-tab-header" v-model="searchTab.activeTabName" @tab-click="searchTab.inputChange">
                            <el-tab-pane label="函数" name="funcName"></el-tab-pane>
                            <el-tab-pane label="类" name="class"></el-tab-pane>
                            <el-tab-pane label="对象" name="objectUsage"></el-tab-pane>
                            <el-tab-pane label="值" name="value"></el-tab-pane>
                            <el-tab-pane label="历史" name="history"></el-tab-pane>
                        </el-tabs>
                        <div class="search-tab-content">
                            <div v-if="searchTab.activeTabName === 'funcName'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchFuncByName(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{searchTab.names[index].name}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'class'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchFuncByName(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{searchTab.names[index].name}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'objectUsage'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchFuncByName(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{searchTab.names[index].name}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'value'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchFuncByName(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{formatText(searchTab.names[index].name)}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'history'">
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.history">
                                        <div class="search-result-item" @click="searchTab.searchFuncByName(searchTab.history[index], true)">
                                            <el-tag>{{searchTab.history[index].type}}</el-tag>
                                            {{formatText(searchTab.history[index].name)}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>
                <div class="setting-tab graph-popup-content" v-if="settingTab.show" >
                    <el-scrollbar>
                        <div class="filter-button-wrap">
                            <div class="filter-button">
                                <el-button @click="graph.saveGraphFilterConfig()" plain>保存</el-button>
                            </div>
                            <div class="filter-button">
                                <el-button @click="graph.cancelGraphFilterConfig()" plain>取消</el-button>
                            </div>
                        </div>
                        <div class="filter-detail-list">
                            <!--<div class="included-filter-label">
                                源码高亮（执行轨迹）
                            </div>
                            <div class="included-filter-body">
                                <template>
                                    <el-select v-model="graph.graphFilterConfig.sourceDisplayConfig.color"
                                               placeholder="请选择"
                                               @change="graph.setCurrentJbSourceColorClass"
                                               :class="graph.currSourceColorCls">
                                        <el-option
                                                v-for="item in graph.jbColors"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value" :class="item.cls">
                                            <span >{{ item.label }}</span>
                                        </el-option>
                                    </el-select>
                                </template>
                            </div>-->

                            <div class="included-filter-label">
                                隐藏函数（但可以被搜索到）
                            </div>
                            <div class="included-filter-body">
                                <template>
                                    <div class="function-display-config">
                                        <el-checkbox
                                                v-model="graph.graphFilterConfig.funcDisplayConfig.skipFieldAccess">
                                            忽略字段访问
                                        </el-checkbox>
                                        <el-checkbox
                                                v-model="graph.graphFilterConfig.funcDisplayConfig.skipFieldChange">
                                            忽略字段修改
                                        </el-checkbox>
                                        <br/>
                                        <el-checkbox
                                                v-model="graph.graphFilterConfig.funcDisplayConfig.skipGetters">
                                            忽略 Getters
                                        </el-checkbox>
                                        <el-checkbox
                                                v-model="graph.graphFilterConfig.funcDisplayConfig.skipSetters">
                                            忽略 Setters
                                        </el-checkbox>
                                        <br/>
                                        <el-checkbox
                                                v-model="graph.graphFilterConfig.funcDisplayConfig.skipConstructors">
                                            忽略构造函数
                                        </el-checkbox>
                                        <el-checkbox
                                                v-model="graph.graphFilterConfig.funcDisplayConfig.skipPrivateMethods">
                                            忽略私有方法
                                        </el-checkbox>
                                        <br/>
                                        <el-checkbox
                                                v-model="graph.graphFilterConfig.funcDisplayConfig.skipNonProjectPackages">
                                            忽略非项目包（配置为 false 会展示项目包以及这些包所直接调用的函数）
                                        </el-checkbox>
                                    </div>
                                </template>
                            </div>
                            <div class="included-filter-label">
                                隐藏类（但可以被搜索到）
                                <span @click="graph.setNotesLabel('2')" class="filterBarMenuChat">
                                        <i class="el-icon-chat-dot-round"></i>
                                    </span>
                            </div>
                            <div class="included-filter-notes">
                                右侧选择类，可将其从序列图中移除。
                            </div>
                            <div class="included-filter-body" v-for="(item, index) in graph.graphFilterConfig.removedObjects" :key="index">
                                <el-tag closable type="" @close="graph.deleteRemovedObject(index)">{{graph.graphFilterConfig.removedObjects[index].type}}:{{graph.graphFilterConfig.removedObjects[index].name}}</el-tag>
                            </div>
                            <!--<div class="included-filter-label">
                                Set Root from Diagram
                                <span @click="graph.setNotesLabel('3')" class="filterBarMenuChat">
                                        <i class="el-icon-chat-dot-round"></i>
                                    </span>
                            </div>
                            <div class="included-filter-notes" v-show="graph.notesLabel['3'] === true">
                                右侧选择任意函数，可将其设置为根节点。
                            </div>
                            <div class="included-filter-body" v-if="graph.graphFilterConfig.rootFunc !== null && graph.graphFilterConfig.rootFunc !== undefined">
                                <el-tag closable type="" @close="graph.deleteRootFunc()">{{graph.graphFilterConfig.rootFunc.name}}</el-tag>
                            </div>-->
                        </div>
                    </el-scrollbar>
                </div>
                <el-scrollbar ref="scrollMenuRef" @scroll='()=> {console.log("AA")}'>
                    <pre id="functionTraceGraph" style="margin: 0px 0px;"></pre>
                </el-scrollbar>
                <div class="controlBar">
                    <div class="controlBarMenu" onclick="vue.graph.zoomOut()">
                        <i class="el-icon-minus"></i>
                    </div>
                    <div class="controlBarMenu" onclick="vue.graph.resetZoom()">
                        rst
                    </div>
                    <div class="controlBarMenu" onclick="vue.graph.zoomIn()">
                        <i class="el-icon-plus"></i>
                    </div>
                </div>
                <div class="searchBar">
                    <div class="searchBarMenu " onclick="vue.graph.recoverToClickedNodes(true)" data-tooltip="查看浏览过的函数，并展示其共同堆栈">
                        <img src="../assets/xcodemap/icons/readinghistroy.svg" class="x-icon">
                    </div>
                    <div class="searchBarMenu searchBarMenuMain" onclick="vue.searchTab.showTab()" data-tooltip="搜索，快速到达类、函数、对象">
                        <i class="el-icon-search"></i>
                    </div>
                    <div class="searchBarMenu" onclick="vue.graph.downloadGraph()" data-tooltip="下载，导出序列图">
                        <i class="el-icon-download"></i>
                    </div>
                    <div class="searchBarMenu" onclick="vue.graph.clearStatus()" data-tooltip="清除状态，回到初始界面">
                        <i class="el-icon-delete"></i>
                    </div>
                    <div class="searchBarMenu" onclick="vue.settingTab.showTab()" data-tooltip="设置，序列图展示规则">
                        <i class="el-icon-setting"></i>
                    </div>
                </div>
            </div>
        </el-main>
        <div @mousedown="rightAside.mouseDown" class="x-handle"></div>
        <div class="node-scroller" v-if="searchTab.showNodeFrames">
            <div class="node-scroll-button">
                <el-dropdown @command="handleChangeScopeCommand">
                      <span class="el-dropdown-link">
                          <i class="el-icon-arrow-down"></i>
                      </span>
                    <el-dropdown-menu slot="dropdown">
                        <div :key="index" class="menu-item" v-for="(item, index) in searchTab.scopes">
                            <el-dropdown-item :command="searchTab.scopes[index].name">{{searchTab.scopes[index].desc}}</el-dropdown-item>
                        </div>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
            <button class="node-scroll-button node-scroll-button-up" @click="searchTab.nodeFrameScrollUp">▲</button>
            <div class="node-frames-container el-scrollbar__wrap--hidden-default">
                <div class="node-frame" v-for="(data, index) in searchTab.newlySearchedNumbers" :key="index" @click="searchTab.handleClickOnNodeFrame">
                    {{ data }}
                </div>
            </div>
            <button class="node-scroll-button node-scroll-button-down" @click="searchTab.nodeFrameScrollDown">▼</button>
        </div>
    </el-container>
    <div class="custom-menu" id="custom-menu">
        <div :key="index" class="menu-item" v-for="(item, index) in graph.contextMenus">
            <div @click="graph.clickContextMenuItem(index)" class="menu-item-text">{{graph.contextMenus[index].desc}}</div>
        </div>
    </div>
    <div class="custom-tooltip" id="custom-tooltip">

    </div>
    <a id="graphDownloadStub" style="display: none"></a>
</div>
</body>

<script src="../assets/axios-1.6.3/axios.min.js"></script>
<!-- 导入 Mermaid 库 -->
<script src="../assets/mermaid-10.9.0/dist/mermaid.min.js"></script>
<script src="../assets/vue-2.5.22/vue.min.js"></script>
<script src="../assets/element-ui-2.15.14/index.min.js"></script>
<script src="../assets/xcodemap/detail.graph.js"></script>



<script>
    // 按键状态标记
    let isMetaKeyPressed = false;
    // 注册按键按下事件监听器
    window.addEventListener('keydown', (event) => {
        isMetaKeyPressed = event.metaKey || event.ctrlKey;
        vue.graph.setMetaKeyPresses(isMetaKeyPressed)
        console.log("key down", event, isMetaKeyPressed)

        /*const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
        // 获取被按下的键和是否按下了控制键
        const key = event.key.toLowerCase();
        const ctrlOrCmdPressed = isMac ? event.metaKey : event.ctrlKey;*/
    });
    // 注册按键释放事件监听器
    window.addEventListener('keyup', (event) => {
        isMetaKeyPressed = false;
        vue.graph.setMetaKeyPresses(isMetaKeyPressed)
        console.log("key up", event, isMetaKeyPressed)
    });
    function rightClickFunctionCallDetailTree(event, data, node, other) {
        let effective = false;
        effective = vue.graph.initContextMenuForFuncTreeElement(event, data, node, other);
        if (effective) {
            setContextMenu(event)
        }
    }
    function adjustPopupBasedOnElement(el, menu) {
        const rect = el.getBoundingClientRect();
        if (window.innerHeight - rect.bottom >  menu.scrollHeight + 10) {
            menu.style.top = (rect.bottom + 10) + 'px';
        } else {
            let styleTop = window.innerHeight - menu.scrollHeight - 10;
            if (styleTop < 15) {
                styleTop = 15;
            }
            menu.style.top = styleTop + 'px';
        }
        if (window.innerWidth - rect.left > menu.scrollWidth + 5) {
            menu.style.left = (rect.left + 5) + 'px';
        } else {
            let styleLeft = window.innerWidth - menu.scrollWidth - 5;
            if (styleLeft < 15) {
                styleLeft = 15;
            }
            menu.style.left = styleLeft + 'px';
        }
    }
    function adjustContextMenu(e) {
        const menu = document.getElementById('custom-menu');
        console.log("context menu height:", menu.scrollHeight, window.innerHeight, e.pageY)
        console.log("context menu width:", menu.scrollWidth, window.innerWidth, e.pageX)
        if (window.innerHeight - e.pageY >  menu.scrollHeight + 15) {
            menu.style.top = (e.pageY + 15) + 'px';
        } else {
            let styleTop = window.innerHeight - menu.scrollHeight - 15;
            if (styleTop < 15) {
                styleTop = 15;
            }
            menu.style.top = styleTop + 'px';
        }
        if (window.innerWidth - e.pageX > menu.scrollWidth + 15) {
            menu.style.left = (e.pageX + 15) + 'px';
        } else {
            let styleLeft = window.innerWidth - menu.scrollWidth - 15;
            if (styleLeft < 15) {
                styleLeft = 15;
            }
            menu.style.left = styleLeft + 'px';
        }
    }
    function setContextMenu(e) {
        e.preventDefault();
        const menu = document.getElementById('custom-menu');
        menu.style.display = 'block';
        adjustContextMenu(e);
        document.addEventListener('click', function hideMenu() {
            menu.style.display = 'none';
            document.removeEventListener('click', hideMenu);
        });
        setTimeout(function () {
            adjustContextMenu(e)
        }, 100)
    }
    document.addEventListener('contextmenu', function (e) {
        console.log("Context menu", isMetaKeyPressed,  e)
        if (!isMetaKeyPressed) {
            const targetElement = e.target;
            const graphElement = targetElement.closest('#functionTraceGraph');
            let effective = false;
            if (graphElement != null) {
                effective = vue.graph.initContextMenusForGraphElement(targetElement)
            }
            if (effective) {
                setContextMenu(e)
            }
        }
        isMetaKeyPressed = false
        vue.graph.setMetaKeyPresses(isMetaKeyPressed)
    });

    function attachTitlePopover(el) {
        const tooltip = document.getElementById("custom-tooltip")
        el.addEventListener('mouseenter', function () {
            const titleText = el.getAttribute('data-tooltip');
            if (!titleText) return;
            el._tooltipTimer = setTimeout(function () {
                tooltip.innerText = titleText;
                tooltip.style.display = 'block';
                adjustPopupBasedOnElement(el, tooltip);
            }, 300);
        });
        el.addEventListener('mouseleave', function () {
            clearTimeout(el._tooltipTimer);
            tooltip.style.display = 'none';
        });
    }
    document.addEventListener('click', function (e) {
        const targetElement = e.target;
        const searchBarElement = targetElement.closest(".searchBar");
        if (searchBarElement == null) {
            {
                const graphElement = targetElement.closest('.search-tab');
                if (graphElement == null) {
                    vue.searchTab.show = false;
                }
            }
            {
                const graphElement = targetElement.closest('.setting-tab');
                if (graphElement == null) {
                    vue.settingTab.show = false;
                }
            }
        }
    })
    const revertWheelX = GetTempProject().revertWheelX;
    window.addEventListener("wheel", function (e) {
        if (revertWheelX === 'true') {
            const scrollMenu = vue.$refs.scrollMenuRef.wrap;
            // 判断事件是否发生在 scrollMenu 内部
            if (scrollMenu.contains(e.target)) {
                if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
                    // 在水平滚动时反转方向
                    e.preventDefault();
                    scrollMenu.scrollLeft +=  -e.deltaX;
                }
            }
        }
    }, {"passive": false})
</script>



<script>
    let tmpThemeVariables = {};
    if (theme === "light") {
        tmpThemeVariables = {
            darkMode: false,
            background: '#ffffff',
            lineColor: '#080808',
            primaryColor: '#ffffff',
            primaryTextColor: '#080808',
            actorBkg: "#edebfc",
            activationBkgColor: "#edebfc",
            actorBorder: "#edebfc"
        }
    } else {
        tmpThemeVariables = {
            darkMode: true,
            background: '#2b2b2b',
            lineColor: '#a9b7c5',
            primaryColor: '#2b2b2b',
            primaryTextColor: '#a9b7c5',
            actorBkg: "#344134",
            activationBkgColor: "#344134",
            actorBorder: "#344134"

        }
    }
    mermaid.initialize({
        "maxTextSize": 500000,
        "maxEdges": 5000,
        darkMode: true,
        fontSize: 24,
        sequence : {
            diagramMarginX:20,
            diagramMarginY:10,
            messageMargin:0,
            height: 40,
            width: 80,
            actorMargin: 10,
            wrap: false
        },
        theme: "base",
        themeVariables: tmpThemeVariables,
    })

    var vue = new Vue({
        el: '#app',
        data: function () {
            return {
                project: NewProject(),
                settingTab: NewSettingTab(),
                searchTab: NewSearchTab(),
                callGraph: NewGraph("functionTraceGraph"),
                graph: null,
                leftAside: NewAside("left", ""),
                rightAside: NewAside("right", ""),
                localVarsCollapsed: true
            }
        },
        created() {
            console.log("this", this)
            this.project.doInitProjectInfo();
            this.graph = this.callGraph;
            this.graph.setRoot(this.project.rootFunc);
            this.graph.currCallId = this.project.currCallId;
            this.graph.functionCallDetailTree.$refs = this.$refs;
            this.graph.functionCallDetailTree.$refsName = "functionCallDetailTree";
            this.graph.functionCallDetailTree.vue = this;
            this.graph.functionCallLocalVarTree.$refs = this.$refs;
            this.graph.functionCallLocalVarTree.$refsName = "functionCallLocalVarTree";
            this.graph.functionCallLocalVarTree.vue = this;
            this.graph.functionCallInnerOutputRequestsTree.$refs = this.$refs;
            this.graph.functionCallInnerOutputRequestsTree.$refsName = "functionCallInnerOutputRequestsTree";
            this.graph.functionCallInnerOutputRequestsTree.vue = this;
            document.addEventListener("mouseup", this.leftAside.mouseUp);
            document.addEventListener("mouseup", this.rightAside.mouseUp);
        },
        mounted() {
            this.$refs.scrollMenuRef.wrap.addEventListener("scroll", (e)=>{
                this.graph.adjustYAll(e);
            })
            const elements = document.querySelectorAll('.searchBarMenu');
            elements.forEach(el => {
                attachTitlePopover(el);
            });
        },
        destroyed() {
            document.removeEventListener("mouseup", this.leftAside.mouseUp);
            document.removeEventListener("mouseup", this.rightAside.mouseUp);
        },
        methods: {
            handleChangeScopeCommand(command) {
                this.searchTab.changeScope(command)
            },
            toggleLocalVars() {
                this.localVarsCollapsed = !this.localVarsCollapsed;
            },
            handleToggleSqlRequests() {
                this.graph.sqlRequestsCollapsed = !this.graph.sqlRequestsCollapsed;
                if (!this.graph.sqlRequestsCollapsed) {
                    // 用户展开时，首次拉取详情
                    this.graph.loadInnerOutputRequestDetailsIfNeeded();
                }
            },
            handleViewLoop() {
                const processId = this.project.processId;
                const threadId = this.project.threadId;
                const currCallId = this.graph.currCallId;
                let url = `./same_func.html?processId=${processId}&threadId=${threadId}&currCallId=${currCallId}&searchType=functionInSamePosition&searchId=${currCallId}`;
                if (this.graph.currNavState && this.graph.currNavState.type) {
                    url += `&searchScope=${this.graph.currNavState.type}`;
                }
                window.open(url, '_blank');
            },
            handleAddToChat() {
                const currCallId = this.graph.currCallId;
                const url = `./addToChat?currCallID=${currCallId}`;
                window.open(url, '_blank');
            }
        }
    })
    fixScrollClass();
    vue.$watch("graph.functionCallLocalVarTree.filterText", (val) => {
        vue.graph.functionCallLocalVarTree.setFilterText(val);
    });
    vue.$watch("graph.functionCallDetailTree.filterText", (val) => {
        vue.graph.functionCallDetailTree.setFilterText(val);
    });
    vue.$watch("graph.functionCallInnerOutputRequestsTree.filterText", (val) => {
        vue.graph.functionCallInnerOutputRequestsTree.setFilterText(val);
    });
    vue.graph.startShow();
</script>

<style>
    #functionTraceGraph {
        padding-right: 10px;
    }
</style>

<style>
    .goto-button-wrap {
        margin-top: 5px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 95%;
        margin-left: 5px;
    }

    .goto-button {
        text-align: center;
        width: 32%;
    }

    .goto-button > .el-button {
        width: 100%;
        padding: 2px 2px;
        font-size: 14px;
    }
</style>
<style>
    .node-scroller {
        width: 32px; /* 设置组件宽度 */
        position: relative;
        height: 100vh;
    }

    .node-frames-container {
        overflow: scroll;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: calc(100% - 90px);
    }

    .node-frame {
        width: 100%;
        height: 24px; /* 设置每个框的高度 */
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #ccc;
        margin-bottom: 10px;
        /*background-color: gray;
        color: antiquewhite;*/
    }

    .node-frame:hover {
        cursor: pointer;
        color: #409EFF;
    }

    .node-scroll-button {
        width: 100%;
        height: 30px;
        cursor: pointer;
        text-align: center;
        /*background-color: gray;
        color: antiquewhite;*/
    }

    .node-scroll-button-up {
        top: 0;
    }
    .node-scroll-button-down {
        position: absolute;
        bottom: 0;
    }

</style>
<style>
    .custom-menu {
        display: none;
        position: absolute;
        z-index: 1000;
    }

    .custom-tooltip {
        display: none;
        position: absolute;
        transition: none;
        z-index: 1000;
    }

    .menu-item-text {
        cursor: pointer;
        margin: 10px 5px;
        line-height: 28px;
    }
</style>

<style>
    body {
        margin: 0px;
    }

    .el-scrollbar {
        height: 100vh;
    }

    .el-scrollbar__bar.is-vertical {
        width: 10px;
    }

    .el-scrollbar__bar.is-horizontal {
        height: 10px;
    }

    .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
        white-space: nowrap;
    }
</style>

<style>
    .el-tree-node {
        position: relative;
    }
    .el-tree-node::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        left: -3px;
        top: -26px;
        border-width: 1px;
        border-left: 1px dashed #52627c;
    }

    .el-tree-node:last-child::before {
        height: 38px;
    }
    .el-tree-node::after {
        content: '';
        width: 14px;
        height: 10px;
        position: absolute;
        left: -3px;
        top: 12px;
        border-width: 1px;
        border-top: 1px dashed #52627c;
    }
    .el-tree-node__label {
        white-space: normal;
        word-break: break-all;
    }
    .el-tree-node__children {
        padding-left: 10px;
    }

    .el-tree-node__content {
        height: inherit;
        padding-bottom: 5px;
    }
    .el-tree-node__content>.el-tree-node__expand-icon {
         padding: 1px;
    }
</style>

<style>
    .packageDeepBar {
        display: flex;
        flex-direction: row;
        position: absolute;
        right: 10px;
        top: 10px;
        /*
        top: 50%;
        */
        /*
        transform: translateY(-100%);
        */
        opacity: 0.8;
    }

    .packageDeepBarMenu:hover {
        cursor: pointer;
    }

    .packageDeepBarMenu {
        font-size: 20px;
        text-align: center;
        margin: 4px;
    }
</style>

<style>
    .controlBar {
        display: flex;
        flex-direction: row;
        position: absolute;
        right: 56px;
        width: 36px;
        /*
        top: 50%;
        */
        transform: translateY(-100%);
        opacity: 0.8;
    }

    .controlBarMenu:hover {
        cursor: pointer;
    }

    .controlBarMenu {
        font-size: 20px;
        text-align: center;
        margin: 4px;
    }

    .stepBar {
        display: flex;
        flex-direction: row;
        /*position: absolute;
        width: 36px;
        top: 28px;
        left: 10px;*/
        /*
        top: 50%;
        */
        /*transform: translateY(-100%);*/
        opacity: 1;
    }

    .searchBar {
        display: flex;
        flex-direction: column;
        position: absolute;
        width: 36px;
        top: 28px;
        right: 10px;
        /*
        top: 50%;
        */
        /*transform: translateY(-100%);*/
        opacity: 1;
    }
    .searchBarMenu:hover {
        cursor: pointer;
        color: #409EFF;
    }
    .searchBarMenu {
        font-size: 20px;
        text-align: center;
        margin: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2px;
    }
</style>

<style>

    .filter-button-wrap {
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 95%;
        margin-left: 5px;
    }

    .filter-button {
        text-align: center;
        width: 49%;
    }

    .filter-button > .el-button {
        width: 100%;
    }

    .el-input.is-disabled .el-input__inner {
        cursor: auto;
    }

    .included-filter-body .el-tag {
        font-size: 14px;
        white-space: normal;
        word-break: break-all;
        height: 100%;
    }

    .filterBarMenuChat {
        position: absolute;
        right: 16px;
    }

    .filterBarMenuChat:hover {
        cursor: pointer;
    }

    .var-exp-op {
            width: 220px;
            margin: 0px 2px;
        }

        .var-exp-name { display: inline-block; }

        .var-op-value { display: inline-block; }

    .filter-detail-list {
            width: 95%;
            margin-left: 5px;
        }

    .included-filter-label {
        height: 32px;
        padding: 0 10px;
        line-height: 30px;
        font-size: 12px;
        margin-top: 10px;
    }

    .included-filter-body .el-input__inner {
        text-align: center;
        padding: 0 0px;
    }

    .included-filter-body {
        margin-top: 4px;
        display: flex;
    }

    .included-filter-notes {
        font-style: italic;
        font-size: small;
    }

    .filterBarMenu:hover {
        cursor: pointer;
    }

    .filterBarMenu {
        display: inline-block;
    }

    .filterBarMenuMinus {
        height: 32px;
        line-height: 32px;
        padding: 4px;
    }

    .filterBarMenuPlus {
        height: 24px;
        line-height: 22px;
        padding: 4px;
        border-width: 1px;
    }

    .packageDeepBarInFilterTab {
        display: flex;
        flex-direction: row;
    }

    .packageDeepBarInFilterButton:hover {
        cursor: pointer;
    }

    .packageDeepBarInFilterMenu {
        /*
        font-size: 20px;
        */
        text-align: center;
        margin: 4px;
        height: 24px;
        line-height: 22px;
    }
</style>

<style>
    /* all tabs related*/
    .graph-popup-content  .el-scrollbar {
        position: absolute;
        top: 1%;
        left: 15%;
        /* transform: translate(-50%, -50%); */
        width: 70%;
        height: calc(99% - 10px);
        /*background-color: #45494a;*/
        z-index: 1;
    }
    .search-tab-header >.el-tabs__header {
        /*background-color: #45494a;*/
        border-bottom: 1px solid #272626;
        margin: 0;
    }
    .search-tab-header>.el-tabs__header .el-tabs__item.is-active {
        /*color: #409EFF;
        background-color: #5d5b5b;*/
        border-right-color: #DCDFE6;
        border-left-color: #DCDFE6;
    }

    .search-result-item { display: block; }
    .search-result-item:hover {
        /*color: #409EFF;*/
        cursor: pointer;
    }

    .el-tabs--border-card {
         border: 0px solid #DCDFE6;
    }

    .el-tabs__item {
        padding: 0 5px;
    }
    .el-tabs--border-card>.el-tabs__content {
         padding: 0px;
    }

    .el-tabs__content {
        padding: 0px;
    }
    .el-tabs {
        padding: 0 0px;
    }

    .el-tabs__nav-wrap {
        margin-left: 0px;
    }

    .el-tabs__header {
        margin: 0 0 24px;
    }
    .el-tabs__item.is-left, .el-tabs__item.is-right {
        width: 25px;
        height: 100px;
        padding: 0;
        margin: 0;
        writing-mode: vertical-lr; /* 将文字垂直显示，从左到右
        line-height: 25px;
    }

    .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
        text-align: center; /* 文字居中 */
    }

    .el-tabs--right.el-tabs--card .el-tabs__item.is-right {
        text-align: center; /* 文字居中 */
    }
    .el-tabs__item.is-active {
        opacity: 1;
    }
    .el-tabs__header {
        margin: 0;
    }
    .goto-button-wrap .el-tabs__item {
        height: 30px;
        line-height: 30px;
    }
</style>

<style>
    #functionTraceGraphDetail {
        /*margin-top: 12px;*/
        margin-bottom: 32px;
    }

    .el-aside, .el-main, .el-header {
        height: 100%;
    }

    .el-container {
        width: 100%;
    }

    .el-header {
        padding: 0 32px;
        display: flex;
        /*
        justify-content: center; !* Center content horizontally *!
        */
        align-items: center; /* Center content vertically */
        height: 30px;
        margin-bottom: 5px;
    }

    .el-main {
        padding: 0px;
    }



    .el-breadcrumb {
        /*
        margin-top: 15px;
        */
        margin-left: 0px;
        font-size: 16px;
    }



    .el-table {
        margin-top: 24px;
    }



    .el-header, .el-tab-pane {
        white-space: pre-line;
    }

    .el-menu-item > a, .el-menu-item > * > a, .el-menu-item > * > * > a, .el-menu-item > * > * > * > a {
        text-decoration: none; /* 去掉下划线 */
    }

    .el-button > a, .el-button > * > a, .el-button > * > * > a, .el-button > * > * > * > a {
        text-decoration: none;
    }

    .codeCanvasDiv {
        height: 100%;
        width: 100%;
        overflow: auto;
    }

    .codeCanvasPre {
        width: 100%;
        overflow: auto;
    }

    .flowCanvasDiv {
        height: 100%;
        width: 550px;
        overflow: auto;
    }

    .left-aside {
        display: block;
        margin-right: 5px;
        justify-content: flex-end;
    }

    .left-tab-content {
        /* width: calc(100% - 25px); */
        margin-top: 5px;
        overflow-wrap: break-word;
    }

    .left-tab-content-stacks {
        margin-top: 5px;
        overflow-wrap: break-word;
        margin-left: 5px;
    }

    .left-tab-content-stacks-item {
        margin-bottom: 2px;
    }


    .right-aside {
        display: block;
        margin-left: 5px;
        justify-content: flex-end;
    }

    .right-tab-content {
        /* width: calc(100% - 25px); */
        margin-top: 24px;
        overflow-wrap: break-word;
    }

    .usageListSelectDiv {
        width: 270px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .focus-on-token {
        text-decoration: underline;
    }



    .x-handle {
        width: 3px;
        cursor: ew-resize;
        z-index: 10;
        background: #ccc;
    }



    .custom-tab-left {
        position: fixed;
        width: 25px;
        height: 100px;
    }

    .custom-tab-right {
        position: fixed;
        width: 25px;
        height: 100px;
    }


    .main-container {
        position: relative;
    }

    .left-container {
        position: relative;
        height: 100vh;
        /*
        margin-left: 25px;
        */
    }

    .right-container {
        position: relative;
        /*width: calc(100% - 25px);
        margin-right: 25px;*/
        height: 100vh;
    }

    .hide-icon-left {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: right;
        z-index: 2;
    }



    .hide-icon-right {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: left;
        z-index: 2;
    }





    .zdiv {
        margin: 0;
        border: 1px solid rgb(187, 186, 186);
        background: rgb(255, 255, 255);
        z-index: 1000;
        position: absolute;
        list-style-type: none;
        padding: 5px;
        border-radius: 7px;
        font-size: 12px;
    }

    .zdiv li {
        margin: 0;
        padding: 5px;
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .zdiv li:hover {
        background: #e1e1e1;
        cursor: pointer;
    }

    text.actor:hover, text.messageText:hover {
        cursor: pointer;
    }

    .usageTraceDetail {
        margin-bottom: 2px;
    }

    .usageTraceDetail:hover {
        cursor: pointer;
    }


    .code-content-widget-detail-parent {
        max-width: 600px;
    }

    .code-content-widget-detail-header {
        font-size: larger;
    }

    .code-content-widget-detail-header::after {
        clear: both;
        content: '';
        display: table;
    }

    .code-content-widget-detail-header div:first-child {
        float: left;
    }

    .code-content-widget-detail-header div:nth-child(2) {
        float: right;
    }

    .code-content-widget-detail-header div i {
        margin-left: 5px;
        font-size: larger;
    }

    .code-content-widget-detail-header div i:hover {
        cursor: pointer;
    }

    .code-content-widget-detail-desc {
        /*padding-top: 10px;*/
        padding-bottom: 32px;
    }

    .code-content-widget-button-parent {
        height: 18px;
    }

    .code-content-widget-button {
        height: 18px;
        opacity: 0.7;
        margin-top: 18px;
    }

    .code-content-widget-button:hover {
        cursor: pointer;
    }

    .editor-extra-class {
        margin-right: 100px;
    }
</style>

<style>
    .el-divider--horizontal {
        margin: 2px 0;
    }
    .func-notes {
        padding-left: 12px;
        word-wrap: break-word;
        white-space: pre-wrap;
    }
    .func-notes-head {
        display: flex;
        justify-content: space-between;
        font-size: medium;
    }
    .func-notes-head-button .el-button {
        padding: 0px 6px;
        border-radius: 5px;
    }
    .func-notes-body {
        font-size: small;
    }

    .filter-input {
        margin-bottom: 2px;
    }
    .filter-input .el-input__inner {
        height: 28px;
    }
    
    .sql-requests {
        padding-left: 12px;
        word-wrap: break-word;
        white-space: pre-wrap;
    }
    .sql-requests-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: medium;
        cursor: pointer;
        padding-right: 6px;
    }
    .sql-requests-head-button .el-button {
        padding: 0px 6px;
        border-radius: 5px;
    }

    .sql-requests-toggle-btn {
        padding: 0px 6px;
        border-radius: 5px;
        font-size: 12px;
    }
    
    .local-vars-section {
        padding-left: 12px;
        word-wrap: break-word;
        white-space: pre-wrap;
    }
    
    .local-vars-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: medium;
        cursor: pointer;
        padding-right: 6px;
    }
    

    
    .local-vars-toggle-btn {
        padding: 0px 6px;
        border-radius: 5px;
        font-size: 12px;
    }
    
    .local-vars-toggle-btn:hover {
        color: #409EFF;
    }
</style>

<script>
    function refreshData() {
        vue.graph.onNodeKeyChange();
    }
    function clickFunctionCodeByLink(psiLink) {
        vue.graph.clickFunctionCodeByLink(psiLink)
    }
    function refreshSearchParas(searchParasStr) {
        vue.project.doInitProjectInfoFromSearchParas(searchParasStr);
        if (vue.project.searchType !== null &&
            (vue.project.searchId !== null || vue.project.searchName != null)) {
            vue.graph.clearStatusForSearch();
            vue.graph.searchFunction({
                "type": vue.project.searchType,
                "name": vue.project.searchName,
                "id": vue.project.searchId,
                "scope": vue.project.searchScope
            }, true, vue.graph.getDefOrUsageNumber(vue.project.searchType))
        } else {
            vue.graph.startShow();
        }
    }
    function refreshLocalVars() {
        vue.graph.getCurrentLocalVars();
    }
    console.log("xcodemap-idea-plugin-ready");
</script>
</html>