<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>Mermaid Example</title>
  <!-- 导入 Mermaid 库 -->
  <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
  <!-- 设置 Mermaid 图表渲染 -->
  <script>
    // 初始化 Mermaid
    mermaid.initialize({ startOnLoad: true });
  </script>
</head>
<body>
<!-- 在 body 中添加 Mermaid 流程图 -->
<div class="mermaid">
  graph TD;
  A[方案选择] --> B(开发);
  A --> C(测试);
  B --> D{是否完成?};
  C --> D;
  D -->|是| E[发布];
  D -->|否| B;
  E --> F(结束);
</div>
</body>
</html>
