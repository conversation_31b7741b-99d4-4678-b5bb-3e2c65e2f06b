<html>
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <title>help_step1</title>
        <link href="../assets/xcodemap/doc.css" rel="stylesheet">
        <style>
        /* cspell:disable-file */
        /* webkit printing magic: print all background colors */
        html {
            -webkit-print-color-adjust: exact;
        }

        * {
            box-sizing: border-box;
            -webkit-print-color-adjust: exact;
        }

        html,
        body {
            margin: 0;
            padding: 0;
        }

        @media only screen {
            body {
                margin: 2em auto;
                max-width: 900px;
                color: rgb(55, 53, 47);
            }
        }

        body {
            line-height: 1.5;
            /*
            white-space: pre-wrap;
            */
        }

        a,
        a.visited {
            color: inherit;
            text-decoration: underline;
        }

        .pdf-relative-link-path {
            font-size: 80%;
            color: #444;
        }

        h1,
        h2,
        h3 {
            letter-spacing: -0.01em;
            line-height: 1.2;
            font-weight: 600;
            margin-bottom: 0;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-top: 0;
            margin-bottom: 0.75em;
        }

        h1 {
            font-size: 1.875rem;
            margin-top: 1.875rem;
        }

        h2 {
            font-size: 1.5rem;
            margin-top: 1.5rem;
        }

        h3 {
            font-size: 1.25rem;
            margin-top: 1.25rem;
        }

        .source {
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 1.5em;
            word-break: break-all;
        }

        .callout {
            border-radius: 3px;
            padding: 1rem;
        }

        figure {
            margin: 1.25em 0;
            page-break-inside: avoid;
        }

        figcaption {
            opacity: 0.5;
            font-size: 85%;
            margin-top: 0.5em;
        }

        mark {
            background-color: transparent;
        }

        .indented {
            padding-left: 1.5em;
        }

        hr {
            background: transparent;
            display: block;
            width: 100%;
            height: 1px;
            visibility: visible;
            border: none;
            border-bottom: 1px solid rgba(55, 53, 47, 0.09);
        }

        img {
            max-width: 100%;
        }

        @media only print {
            img {
                max-height: 100vh;
                object-fit: contain;
            }
        }

        @page {
            margin: 1in;
        }

        .collection-content {
            font-size: 0.875rem;
        }

        .column-list {
            display: flex;
            justify-content: space-between;
        }

        .column {
            padding: 0 1em;
        }

        .column:first-child {
            padding-left: 0;
        }

        .column:last-child {
            padding-right: 0;
        }

        .table_of_contents-item {
            display: block;
            font-size: 0.875rem;
            line-height: 1.3;
            padding: 0.125rem;
        }

        .table_of_contents-indent-1 {
            margin-left: 1.5rem;
        }

        .table_of_contents-indent-2 {
            margin-left: 3rem;
        }

        .table_of_contents-indent-3 {
            margin-left: 4.5rem;
        }

        .table_of_contents-link {
            text-decoration: none;
            opacity: 0.7;
            border-bottom: 1px solid rgba(55, 53, 47, 0.18);
        }

        table,
        th,
        td {
            border: 1px solid rgba(55, 53, 47, 0.09);
            border-collapse: collapse;
        }

        table {
            border-left: none;
            border-right: none;
        }

        th,
        td {
            font-weight: normal;
            padding: 0.25em 0.5em;
            line-height: 1.5;
            min-height: 1.5em;
            text-align: left;
        }

        th {
            color: rgba(55, 53, 47, 0.6);
        }

        ol,
        ul {
            margin: 0;
            margin-block-start: 0.6em;
            margin-block-end: 0.6em;
        }

        li > ol:first-child,
        li > ul:first-child {
            margin-block-start: 0.6em;
        }

        ul > li {
            list-style: disc;
        }

        ul.to-do-list {
            padding-inline-start: 0;
        }

        ul.to-do-list > li {
            list-style: none;
        }

        .to-do-children-checked {
            text-decoration: line-through;
            opacity: 0.375;
        }

        ul.toggle > li {
            list-style: none;
        }

        ul {
            padding-inline-start: 1.7em;
        }

        ul > li {
            padding-left: 0.1em;
        }

        ol {
            padding-inline-start: 1.6em;
        }

        ol > li {
            padding-left: 0.2em;
        }

        .mono ol {
            padding-inline-start: 2em;
        }

        .mono ol > li {
            text-indent: -0.4em;
        }

        .toggle {
            padding-inline-start: 0em;
            list-style-type: none;
        }

        /* Indent toggle children */
        .toggle > li > details {
            padding-left: 1.7em;
        }

        .toggle > li > details > summary {
            margin-left: -1.1em;
        }

        .selected-value {
            display: inline-block;
            padding: 0 0.5em;
            background: rgba(206, 205, 202, 0.5);
            border-radius: 3px;
            margin-right: 0.5em;
            margin-top: 0.3em;
            margin-bottom: 0.3em;
            white-space: nowrap;
        }

        .collection-title {
            display: inline-block;
            margin-right: 1em;
        }

        .page-description {
            margin-bottom: 2em;
        }

        .simple-table {
            margin-top: 1em;
            font-size: 0.875rem;
            empty-cells: show;
        }

        .simple-table td {
            height: 29px;
            min-width: 120px;
        }

        .simple-table th {
            height: 29px;
            min-width: 120px;
        }

        .simple-table-header-color {
            background: rgb(247, 246, 243);
            color: black;
        }

        .simple-table-header {
            font-weight: 500;
        }

        time {
            opacity: 0.5;
        }

        .icon {
            display: inline-block;
            max-width: 1.2em;
            max-height: 1.2em;
            text-decoration: none;
            vertical-align: text-bottom;
            margin-right: 0.5em;
        }

        img.icon {
            border-radius: 3px;
        }

        .user-icon {
            width: 1.5em;
            height: 1.5em;
            border-radius: 100%;
            margin-right: 0.5rem;
        }

        .user-icon-inner {
            font-size: 0.8em;
        }

        .text-icon {
            border: 1px solid #000;
            text-align: center;
        }

        .page-cover-image {
            display: block;
            object-fit: cover;
            width: 100%;
            max-height: 30vh;
        }

        .page-header-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .page-header-icon-with-cover {
            margin-top: -0.72em;
            margin-left: 0.07em;
        }

        .page-header-icon img {
            border-radius: 3px;
        }

        .link-to-page {
            margin: 1em 0;
            padding: 0;
            border: none;
            font-weight: 500;
        }

        p > .user {
            opacity: 0.5;
        }

        td > .user,
        td > time {
            white-space: nowrap;
        }

        input[type="checkbox"] {
            transform: scale(1.5);
            margin-right: 0.6em;
            vertical-align: middle;
        }

        p {
            margin-top: 0.5em;
            margin-bottom: 0.5em;
        }

        .image {
            border: none;
            margin: 1.5em 0;
            padding: 0;
            border-radius: 0;
            text-align: center;
        }

        .code,
        code {
            background: rgba(135, 131, 120, 0.15);
            border-radius: 3px;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
            tab-size: 2;
        }

        code {
            color: #eb5757;
        }

        .code {
            padding: 1.5em 1em;
        }

        .code-wrap {
            white-space: pre-wrap;
            word-break: break-all;
        }

        .code > code {
            background: none;
            padding: 0;
            font-size: 100%;
            color: inherit;
        }

        blockquote {
            font-size: 1.25em;
            margin: 1em 0;
            padding-left: 1em;
            border-left: 3px solid rgb(55, 53, 47);
        }

        .bookmark {
            text-decoration: none;
            max-height: 8em;
            padding: 0;
            display: flex;
            width: 100%;
            align-items: stretch;
        }

        .bookmark-title {
            font-size: 0.85em;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 1.75em;
            white-space: nowrap;
        }

        .bookmark-text {
            display: flex;
            flex-direction: column;
        }

        .bookmark-info {
            flex: 4 1 180px;
            padding: 12px 14px 14px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .bookmark-image {
            width: 33%;
            flex: 1 1 180px;
            display: block;
            position: relative;
            object-fit: cover;
            border-radius: 1px;
        }

        .bookmark-description {
            color: rgba(55, 53, 47, 0.6);
            font-size: 0.75em;
            overflow: hidden;
            max-height: 4.5em;
            word-break: break-word;
        }

        .bookmark-href {
            font-size: 0.75em;
            margin-top: 0.25em;
        }

        .sans {
            font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
        }

        .code {
            font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace;
        }

        .serif {
            font-family: Lyon-Text, Georgia, ui-serif, serif;
        }

        .mono {
            font-family: iawriter-mono, Nitti, Menlo, Courier, monospace;
        }

        .pdf .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP';
        }

        .pdf:lang(zh-CN) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC';
        }

        .pdf:lang(zh-TW) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC';
        }

        .pdf:lang(ko-KR) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR';
        }

        .pdf .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP';
        }

        .pdf:lang(zh-CN) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC';
        }

        .pdf:lang(zh-TW) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC';
        }

        .pdf:lang(ko-KR) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR';
        }

        .pdf .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP';
        }

        .pdf:lang(zh-CN) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC';
        }

        .pdf:lang(zh-TW) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC';
        }

        .pdf:lang(ko-KR) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR';
        }

        .pdf .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP';
        }

        .pdf:lang(zh-CN) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC';
        }

        .pdf:lang(zh-TW) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC';
        }

        .pdf:lang(ko-KR) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR';
        }

        .highlight-default {
            color: rgba(55, 53, 47, 1);
        }

        .highlight-gray {
            color: rgba(120, 119, 116, 1);
            fill: rgba(120, 119, 116, 1);
        }

        .highlight-brown {
            color: rgba(159, 107, 83, 1);
            fill: rgba(159, 107, 83, 1);
        }

        .highlight-orange {
            color: rgba(217, 115, 13, 1);
            fill: rgba(217, 115, 13, 1);
        }

        .highlight-yellow {
            color: rgba(203, 145, 47, 1);
            fill: rgba(203, 145, 47, 1);
        }

        .highlight-teal {
            color: rgba(68, 131, 97, 1);
            fill: rgba(68, 131, 97, 1);
        }

        .highlight-blue {
            color: rgba(51, 126, 169, 1);
            fill: rgba(51, 126, 169, 1);
        }

        .highlight-purple {
            color: rgba(144, 101, 176, 1);
            fill: rgba(144, 101, 176, 1);
        }

        .highlight-pink {
            color: rgba(193, 76, 138, 1);
            fill: rgba(193, 76, 138, 1);
        }

        .highlight-red {
            color: rgba(212, 76, 71, 1);
            fill: rgba(212, 76, 71, 1);
        }

        .highlight-default_background {
            color: rgba(55, 53, 47, 1);
        }

        .highlight-gray_background {
            background: rgba(241, 241, 239, 1);
        }

        .highlight-brown_background {
            background: rgba(244, 238, 238, 1);
        }

        .highlight-orange_background {
            background: rgba(251, 236, 221, 1);
        }

        .highlight-yellow_background {
            background: rgba(251, 243, 219, 1);
        }

        .highlight-teal_background {
            background: rgba(237, 243, 236, 1);
        }

        .highlight-blue_background {
            background: rgba(231, 243, 248, 1);
        }

        .highlight-purple_background {
            background: rgba(244, 240, 247, 0.8);
        }

        .highlight-pink_background {
            background: rgba(249, 238, 243, 0.8);
        }

        .highlight-red_background {
            background: rgba(253, 235, 236, 1);
        }

        .block-color-default {
            color: inherit;
            fill: inherit;
        }

        .block-color-gray {
            color: rgba(120, 119, 116, 1);
            fill: rgba(120, 119, 116, 1);
        }

        .block-color-brown {
            color: rgba(159, 107, 83, 1);
            fill: rgba(159, 107, 83, 1);
        }

        .block-color-orange {
            color: rgba(217, 115, 13, 1);
            fill: rgba(217, 115, 13, 1);
        }

        .block-color-yellow {
            color: rgba(203, 145, 47, 1);
            fill: rgba(203, 145, 47, 1);
        }

        .block-color-teal {
            color: rgba(68, 131, 97, 1);
            fill: rgba(68, 131, 97, 1);
        }

        .block-color-blue {
            color: rgba(51, 126, 169, 1);
            fill: rgba(51, 126, 169, 1);
        }

        .block-color-purple {
            color: rgba(144, 101, 176, 1);
            fill: rgba(144, 101, 176, 1);
        }

        .block-color-pink {
            color: rgba(193, 76, 138, 1);
            fill: rgba(193, 76, 138, 1);
        }

        .block-color-red {
            color: rgba(212, 76, 71, 1);
            fill: rgba(212, 76, 71, 1);
        }

        .block-color-default_background {
            color: inherit;
            fill: inherit;
        }

        .block-color-gray_background {
            background: rgba(241, 241, 239, 1);
        }

        .block-color-brown_background {
            background: rgba(244, 238, 238, 1);
        }

        .block-color-orange_background {
            background: rgba(251, 236, 221, 1);
        }

        .block-color-yellow_background {
            background: rgba(251, 243, 219, 1);
        }

        .block-color-teal_background {
            background: rgba(237, 243, 236, 1);
        }

        .block-color-blue_background {
            background: rgba(231, 243, 248, 1);
        }

        .block-color-purple_background {
            background: rgba(244, 240, 247, 0.8);
        }

        .block-color-pink_background {
            background: rgba(249, 238, 243, 0.8);
        }

        .block-color-red_background {
            background: rgba(253, 235, 236, 1);
        }

        .select-value-color-uiBlue {
            background-color: rgba(35, 131, 226, .07);
        }

        .select-value-color-pink {
            background-color: rgba(245, 224, 233, 1);
        }

        .select-value-color-purple {
            background-color: rgba(232, 222, 238, 1);
        }

        .select-value-color-green {
            background-color: rgba(219, 237, 219, 1);
        }

        .select-value-color-gray {
            background-color: rgba(227, 226, 224, 1);
        }

        .select-value-color-transparentGray {
            background-color: rgba(227, 226, 224, 0);
        }

        .select-value-color-translucentGray {
            background-color: rgba(0, 0, 0, 0.06);
        }

        .select-value-color-orange {
            background-color: rgba(250, 222, 201, 1);
        }

        .select-value-color-brown {
            background-color: rgba(238, 224, 218, 1);
        }

        .select-value-color-red {
            background-color: rgba(255, 226, 221, 1);
        }

        .select-value-color-yellow {
            background-color: rgba(253, 236, 200, 1);
        }

        .select-value-color-blue {
            background-color: rgba(211, 229, 239, 1);
        }

        .select-value-color-pageGlass {
            background-color: undefined;
        }

        .select-value-color-washGlass {
            background-color: undefined;
        }

        .checkbox {
            display: inline-flex;
            vertical-align: text-bottom;
            width: 16;
            height: 16;
            background-size: 16px;
            margin-left: 2px;
            margin-right: 5px;
        }

        .checkbox-on {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
        }

        .checkbox-off {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
        }

    </style>

</head>
<body>
<article class="page sans" id="16b7eaa6-85d5-8036-a26b-c6c1cbb673e2">
    <div class="page-body">
        <blockquote class="" id="16b7eaa6-85d5-803a-90ac-d5edb0b9eeeb"><em>
            <mark class="highlight-red">XCodeMap 的核心能力就是重现源码执行过程，首先需要准备数据。</mark>
        </em></blockquote>
        <p class="" id="16b7eaa6-85d5-8036-98fe-f54fd662417c">
        </p>
        <p class="" id="16b7eaa6-85d5-804d-a0c1-fce9f0154a99">第一步，Debug with XCodeMap，启动程序并开始录制。</p>
        <p class="" id="16b7eaa6-85d5-80b8-92b9-c407befb2a5c">
        </p>
        <p class="" id="16b7eaa6-85d5-80cf-8a43-d4fd6965a65c">本教程需要知道：</p>
        <ol class="numbered-list" id="16b7eaa6-85d5-801a-a5d8-feae84b0abc3" start="1" type="1">
            <li>启动程序</li>
        </ol>
        <ol class="numbered-list" id="16b7eaa6-85d5-80ed-b94a-c4790c15b97e" start="2" type="1">
            <li>入口界面</li>
        </ol>
        <ol class="numbered-list" id="16b7eaa6-85d5-80f8-97fa-f60d4bf37a35" start="3" type="1">
            <li>开启/关闭录制</li>
        </ol>
        <ol class="numbered-list" id="16b7eaa6-85d5-8065-a78b-e8f8c49762a7" start="4" type="1">
            <li>录制范围</li>
        </ol>
        <h2 class="" id="16b7eaa6-85d5-80d9-8fb5-dfcfd479e36f">1. 启动程序</h2>
        <h3 class="" id="f257179a-8f80-4f15-960e-d5829909f7eb">1.1 在源码 Main 方法处 Debug with XCodeMap</h3>
        <p class="" id="7be15b6b-9cf4-4087-b4c7-a0d91afb222f">找到 Main 方法后，点击左侧执行按钮旁的下拉列表。选择&quot;Debug
            xxx with XCodeMap&quot;选项，然后点击即可启动程序。</p>
        <figure class="image" id="b61f5abf-1250-47b5-b162-a6427490d76f"><a href="help_step1/image.png"><img
                src="help_step1/image.png" style="width:707.9765625px"/></a></figure>
        <h3 class="" id="8f064441-5c98-4459-bf84-ad7e79b1f3b8">1.2 在 Run Configuration 处启动</h3>
        <p class="" id="c28c2f2b-659f-404f-800c-f52bfe008fd9">选择一个 Run configuration，选择 XCodeMap
            的图标，点击执行。</p>
        <figure class="image" id="93873c0d-198f-49e6-9a13-a93c15832295"><a href="help_step1/image%201.png"><img
                src="help_step1/image%201.png" style="width:707.984375px"/></a></figure>
        <p class="" id="cf2d8f9d-2046-4314-ae7d-27a7f27dc349">
        </p>
        <p class="" id="243676c7-cd1c-4494-a3d2-8c111dabadee">程序带上 XCodeMap
            启动成功后，在控制台输出会看到类似以下内容：</p>
        <script crossorigin="anonymous"
                integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg=="
                referrerPolicy="no-referrer" src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
        <link crossorigin="anonymous" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css"
              integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ=="
              referrerPolicy="no-referrer" rel="stylesheet"/>
        <pre class="code" id="16b7eaa6-85d5-8024-bf85-e7b7c1a8678d"><code class="language-JavaScript">===&gt; XCodeMap 随着程序一起启动,  xcodemap.executionId:xxx  &lt;===</code></pre>
        <p class="" id="16b7eaa6-85d5-8038-a37b-c7fcde0eba50">如果是 Web 程序，你可以使用 Postman 等工具发送 HTTP
            请求，XCodeMap
            会在后台自动录制程序的运行时数据，包括函数调用堆栈、参数和返回值。这些数据会被保存在本地，为后续的重现和分析提供基础。</p>
        <h3 class="" id="16b7eaa6-85d5-80b4-84b3-d2de2bd1ed6f">1.3 找不到启动入口</h3>
        <p class="" id="16b7eaa6-85d5-800f-956a-f4130a570849">某些情况下，比如 services 界面，如果找不到启动入口 “Debug
            with XCodeMap”，可以在录制配置中配置“默认随着 Debug 一起启动”。该配置为“是”之后，正常 Debug 时会带上 XCodeMap
            一起启动。</p>
        <figure class="image" id="16b7eaa6-85d5-8014-b13c-e5d12de87926"><a href="help_step1/image%202.png"><img
                src="help_step1/image%202.png" style="width:708px"/></a></figure>
        <h2 class="" id="0b44d89e-cb4e-45cf-b834-4eee33eecb0e">2. 入口界面</h2>
        <p class="" id="16b7eaa6-85d5-80b4-88db-ee3a7f203f5b">对于所有程序，XCodeMap 按照线程列出重现入口，每个列表项是一个线程的入口，主入口一般是
            main 线程。</p>
        <p class="" id="16b7eaa6-85d5-8084-857b-e51138696dcf">对于 Web 程序，XCodeMap 还会按照 HTTP
            请求列出重现入口，每个列表项是一个请求的入口。</p>
        <p class="" id="16b7eaa6-85d5-80d5-b8ec-e754eb864b95">而这些重现人口的展示界面则有两处：</p>
        <ul class="bulleted-list" id="16b7eaa6-85d5-808c-a6ad-deb7543bf738">
            <li style="list-style-type:disc">在每个程序的输出界面，有 XCodeMap-I 窗口</li>
        </ul>
        <ul class="bulleted-list" id="16b7eaa6-85d5-809e-ba6d-d53a5ff0e575">
            <li style="list-style-type:disc">在插件主界面，有 XCodeMap 窗口</li>
        </ul>
        <h3 class="" id="43f8e5f9-13af-4412-88bc-522336394924">2.1 程序输出界面的 XCodeMap-I 窗口</h3>
        <p class="" id="16b7eaa6-85d5-80ee-bd02-de186adbb6e4">IDE 可以在一个 Project 内启动多个程序（比如微服务），因此，XCodeMap
            会在每个程序的输出界面内，增加一个 XCodeMap-I 窗口，这个窗口内只显示与该程序相关的重现入口。</p>
        <figure class="image" id="16b7eaa6-85d5-809e-9fee-cd5c866ba52c"><a href="help_step1/image%203.png"><img
                src="help_step1/image%203.png" style="width:707.9921875px"/></a></figure>
        <h3 class="" id="16b7eaa6-85d5-80ab-8d88-ed957472e10f"><strong>2.2 插件主界面 XCodeMap 窗口</strong></h3>
        <p class="" id="603cbc42-f4dc-4998-8b97-9d4edbe6331f">XCodeMap 主界面则会列出 Project 内所有程序的 重现入口。</p>
        <figure class="image" id="16b7eaa6-85d5-8039-9d92-f2a57887b0f4"><a href="help_step1/image%204.png"><img
                src="help_step1/image%204.png" style="width:707.984375px"/></a></figure>
        <p class="" id="16b7eaa6-85d5-80aa-b0a5-f70d0e698d5c">
        </p>
        <h2 class="" id="16b7eaa6-85d5-807b-a9fc-dd5d1f2ba1ae">3. 开启/关闭 录制</h2>
        <p class="" id="16b7eaa6-85d5-806b-9175-c8fda3b224e1">
            开始录制的操作与录制模式有关。如果是智能模式或者全录模式，则在启动程序后，会自动开始录制。如果是手工模式，则启动程序后，不会自动开始录制，需要手工开启。</p>
        <p class="" id="16b7eaa6-85d5-8069-b962-d95eb24a06ec">任何模式下，都可以随时结束录制。按需结束录制可以大大提升性能，避免录制时间过久，导致
            IDE 卡顿。</p>
        <figure class="image" id="16b7eaa6-85d5-8079-a520-e8e8d855ac1c"><a href="help_step1/image%205.png"><img
                src="help_step1/image%205.png" style="width:707.984375px"/></a></figure>
        <p class="" id="16b7eaa6-85d5-80f0-a021-cef465bffaef">
        </p>
        <h2 class="" id="16b7eaa6-85d5-8027-b1a7-f71673325f1b">4. 录制范围</h2>
        <p class="" id="16b7eaa6-85d5-808e-a2c3-e6728996f131">XCodeMap 默认会只录制项目包，对于依赖包，比如 spring、mabatis
            等，需要手工配置包范围。</p>
        <figure class="image" id="16b7eaa6-85d5-80cd-98c4-f39eb0158356"><a href="help_step1/image%206.png"><img
                src="help_step1/image%206.png" style="width:707.984375px"/></a></figure>
        <p class="" id="16b7eaa6-85d5-80ea-b508-cb0264aec80c">需要注意的是，XCodeMap
            在录制时，会录制范围内的函数（关注的函数），以及被这些函数所直接调用的函数（直接上下文）。</p>
        <p class="" id="16b7eaa6-85d5-8091-b53b-e1978ff29c84">比如，以下代码：</p>
        <script crossorigin="anonymous"
                integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg=="
                referrerPolicy="no-referrer" src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
        <link crossorigin="anonymous" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css"
              integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ=="
              referrerPolicy="no-referrer" rel="stylesheet"/>
        <pre class="code" id="16b7eaa6-85d5-808c-a7e7-fc5931d90708"><code class="language-JavaScript">public static void main(String[] args) throws Exception {
        load();
        new LoggerRunner().run();
        System.out.println(&quot;Run End&quot;);
}</code></pre>
        <p class="" id="16b7eaa6-85d5-80e6-9de0-ed96fdf995a8">如果 main、load、LoggerRunner().run()，都在录制范围内，则会被录制。
            println 被 main 所直接调用，也会被录制，因为，这是理解 main 函数的直接上下文。但 println
            内部的下一级调用，则不会录制，这些属于间接上下文了。</p>
        <p class="" id="16b7eaa6-85d5-8089-85c7-d40fd263e77e">
        </p>
        <p class="" id="f24085f4-0618-4dd1-a0cd-05904ae7770d">当你看到这里，准备工作就做完了。</p>
        <p class="" id="16b7eaa6-85d5-8006-a288-c89126e7ef01">接下来就是第二步，点击 +
            展开序列图，并用“浏览记录”和“忽略规则”来修剪序列图，以重现源码的关键执行过程。</p>
        <p class="" id="3c145e64-2388-4b19-b875-a976de03f00f">
        </p></div>
</article>
<span class="sans" style="font-size:14px;padding-top:2em"></span></body>
</html>