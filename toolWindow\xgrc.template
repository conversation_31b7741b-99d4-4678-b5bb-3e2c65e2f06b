def finalJvmArgs = [ %s ]

gradle.taskGraph.whenReady { taskGraph ->
    taskGraph.allTasks
        .findAll { it instanceof org.gradle.process.JavaForkOptions }
        .each { task ->
            task.doFirst {
                // 原有的所有 jvmArgs
                def existing = task.jvmArgs ?: []

                // 只移除我们自己曾经添加过的那几项，保留其它所有参数
                def cleaned = existing.findAll { !finalJvmArgs.contains(it) }

                // 追加到末尾
                task.jvmArgs = cleaned + finalJvmArgs
            }
        }
}

