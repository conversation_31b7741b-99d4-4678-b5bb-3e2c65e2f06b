var gn=Object.defineProperty;var mn=(r,e,t)=>e in r?gn(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var N=(r,e,t)=>mn(r,typeof e!="symbol"?e+"":e,t);import{d as st,r as L,u as yt,c as Pe,g as bn,a as xn,b as vn,e as Nt,f as kn,p as wn,s as yn,h as jt,i as $r,w as Vt,j as T,o as w,F as me,k as ye,l as p,m as F,n as _,q as h,t as j,_ as Ur,v as Br,x as Tn,y as _n,z as En,A as we,B as Ee,C as Sn,D as An,E as Cn,G as Rn,H as Dn,I as Ln,J as yr,K as In,L as Tr,M as Mn,N as Nn,O as On,P as Pn,Q as zn,R as $n}from"./style-D0LgP_vx.js";const Un=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href,Yt=st("chatStatus",()=>{const r=L("idle"),e=L("");return{status:r,statusMessage:e,setStatus:s=>{r.value=s},setStatusMessage:s=>{e.value=s}}}),Fr=st("modelStatus",()=>{const r=L(null),e=L(null),t=yt(),n=Pe(()=>!r.value||!e.value?null:e.value.modelDescList.find(l=>l.uuid===r.value)),s=Pe(()=>{var l;return((l=e.value)==null?void 0:l.modelDescList)||[]});return{currentModelUuid:r,modelConfig:e,currentModel:n,availableModels:s,getModelConfigData:async()=>{try{const l=await bn();if(l.success&&l.data)e.value=l.data,!r.value&&l.data.modelDescList.length>0&&(r.value=l.data.modelDescList[0].uuid);else throw new Error(l.error||"Failed to get model config")}catch(l){console.error("Failed to get model config:",l),t.setError(l instanceof Error?l.message:"Failed to get model config","GET_MODEL_CONFIG_ERROR")}},setCurrentModel:l=>{var i;((i=e.value)==null?void 0:i.modelDescList.find(u=>u.uuid===l))&&(r.value=l)}}}),ue="EMPTY_PLACE_HOLDER",Hr=st("database",()=>{const r=L([{id:ue,name:"请选择数据源",recordState:"idle",dataId:null,active:!1,serverSelected:!1}]),e=L(ue),t=Pe(()=>r.value.find(b=>b.id===e.value)||null),n=b=>{r.value.push(b)},s=b=>{e.value=b},a=async(b,g)=>{const A=r.value.find(E=>E.id===b);if(A)try{await xn({executionId:b,cmd:"change",state:g}),A.recordState="preparing"}catch(E){console.error("Failed to change record state:",E)}};return{databases:r,currentDatabase:t,currentDatabaseId:e,addDatabase:n,setCurrentDatabase:s,changeState:a,queryState:b=>{var g;return(g=r.value.find(A=>A.id===b))==null?void 0:g.recordState},startRecord:b=>{const g=r.value.find(A=>A.id===b);g&&g.recordState==="idle"&&a(b,"start")},endRecord:b=>{const g=r.value.find(A=>A.id===b);g&&g.recordState==="recording"&&a(b,"stop")},restartRecord:b=>{const g=r.value.find(A=>A.id===b);g&&g.recordState==="paused"&&(a(b,"start"),Wr().createNewChat())},getDatabase:async()=>{try{const b=await vn();r.value=[r.value[0]],b.forEach(g=>{n(g)})}catch(b){console.error("Failed to fetch process data:",b)}}}}),Gr=st("inputBox",()=>{const r=L(""),e=yt(),t=Yt(),n=Hr(),s=()=>!n.currentDatabase||!n.currentDatabase.dataId||t.status==="waiting"||n.currentDatabaseId===ue;return{message:r,appendText:i=>{if(s())return e.setError("当前状态不可添加文本到输入框"),!1;const u=r.value&&!r.value.endsWith(" ");return r.value+=(u?" ":"")+i,!0},setText:i=>{r.value=i},clearText:()=>{r.value=""},getText:()=>r.value,isDisabled:s}}),Bn="SystemStatus",Fn="AddToChat",Wr=st("chat",()=>{const r=L([]),e=L(null),t=L(!1);let n=null;const s=yt(),a=Yt(),c=Fr(),l=Gr(),f=Pe(()=>r.value.find(S=>S.id===e.value)),i=async()=>{var S;try{e.value&&await Nt(e.value);const C={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await kn(C.id,((S=c.currentModel)==null?void 0:S.uuid)??""))return r.value=[C],e.value=C.id,t.value||u(),C;throw new Error("Failed to create chat channel")}catch(C){throw console.error("Failed to create chat channel:",C),s.setError(C instanceof Error?C.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),e.value=null,r.value=[],C}},u=()=>{n&&clearInterval(n),t.value=!0,n=window.setInterval(async()=>{if(t.value)try{const S=await wn();if(S&&S.length>0)for(const C of S){if(C.messageId===Bn){a.setStatusMessage(C.content);continue}if(C.messageId===Fn){l.appendText(C.content);continue}const U=r.value.find(D=>D.id===C.chatId);if(U){a.setStatus("waiting");const D=U.messages[U.messages.length-1];D&&D.role===C.role?D.content+=C.content:U.messages.push(C),U.updatedAt=Date.now()}}else a.setStatus("sending")}catch(S){console.error("Failed to poll messages:",S),s.setError(S instanceof Error?S.message:"Failed to poll messages","POLL_ERROR")}},1e3)},b=()=>{n&&(clearInterval(n),n=null),t.value=!1};return{chats:r,currentChatId:e,currentChat:f,createNewChat:i,sendMessage:async S=>{e.value||await i();const C=f.value;if(!C)return;if(!c.currentModel){s.setError("请先选择模型再发送消息","SEND_MESSAGE_ERROR");return}a.setStatus("waiting");const U={messageId:crypto.randomUUID(),content:S,role:"user",timestamp:Date.now(),chatId:e.value,modelId:c.currentModel.uuid};try{if(!e.value)return;if(await yn(U))C.messages.push(U),C.updatedAt=Date.now();else throw new Error("会话可能已失效，请重新创建会话")}catch(D){throw console.error("Failed to send message:",D),s.setError(D instanceof Error?D.message:"会话可能已失效，请重新创建会话","SEND_MESSAGE_ERROR"),D}},removeChat:async S=>{try{if(await Nt(S))r.value=r.value.filter(U=>U.id!==S),e.value===S&&(e.value=null);else throw new Error("Failed to remove chat channel")}catch(C){throw console.error("Failed to remove chat:",C),s.setError(C instanceof Error?C.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),C}},startPolling:u,stopPolling:b,cleanup:()=>{b(),e.value&&Nt(e.value).catch(console.error)}}}),Hn={class:"space-y-0.5"},Gn=["onClick"],Wn=["onClick"],qn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},jn=["onClick"],Vn=["onClick"],Yn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Zn=["onClick"],Xn=jt({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(r,{emit:e}){const t=r,n=$r(),s=L(t.nodes.map(f=>({...f,isExpanded:t.defaultExpanded,children:f.children?f.children.map(i=>({...i,isExpanded:t.defaultExpanded,children:i.children?i.children.map(u=>({...u,isExpanded:t.defaultExpanded})):[]})):[]})));Vt(()=>t.nodes,f=>{s.value=f.map(i=>({...i,isExpanded:t.defaultExpanded,children:i.children?i.children.map(u=>({...u,isExpanded:t.defaultExpanded,children:u.children?u.children.map(b=>({...b,isExpanded:t.defaultExpanded})):[]})):[]}))},{deep:!0});const a=e,c=f=>{a("nodeClick",f)},l=f=>{f.isExpanded=!f.isExpanded};return(f,i)=>(w(),T("div",Hn,[(w(!0),T(me,null,ye(s.value,u=>(w(),T("div",{key:u.nodeKey,class:"tree-node"},[p("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",h(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[u.children&&u.children.length>0?(w(),T("button",{key:0,onClick:b=>l(u),class:_(["p-0.5 rounded",h(n).theme==="dark"?"hover:bg-gray-600":"hover:bg-gray-100"])},[(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":u.isExpanded,"text-gray-600 hover:text-gray-800":h(n).theme==="light","text-gray-400 hover:text-gray-200":h(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},i[0]||(i[0]=[p("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],10,Gn)):F("",!0),p("span",{class:_({"text-gray-700 hover:text-gray-900":h(n).theme==="light","text-gray-300 hover:text-gray-100":h(n).theme==="dark","cursor-pointer flex-grow":!0}),onClick:b=>c(u)},j(u.label),11,Wn)],2),u.children&&u.children.length>0&&u.isExpanded?(w(),T("div",qn,[(w(!0),T(me,null,ye(u.children,b=>(w(),T("div",{key:b.nodeKey,class:"tree-node"},[p("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",h(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[b.children&&b.children.length>0?(w(),T("button",{key:0,onClick:g=>l(b),class:_(["p-0.5 rounded",h(n).theme==="dark"?"hover:bg-gray-600":"hover:bg-gray-100"])},[(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":b.isExpanded,"text-gray-600 hover:text-gray-800":h(n).theme==="light","text-gray-400 hover:text-gray-200":h(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},i[1]||(i[1]=[p("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],10,jn)):F("",!0),p("span",{class:_({"text-gray-700 hover:text-gray-900":h(n).theme==="light","text-gray-300 hover:text-gray-100":h(n).theme==="dark","cursor-pointer flex-grow":!0}),onClick:g=>c(b)},j(b.label),11,Vn)],2),b.children&&b.children.length>0&&b.isExpanded?(w(),T("div",Yn,[(w(!0),T(me,null,ye(b.children,g=>(w(),T("div",{key:g.nodeKey,class:"pl-3"},[p("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",h(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[p("span",{class:_({"text-gray-700 hover:text-gray-900":h(n).theme==="light","text-gray-300 hover:text-gray-100":h(n).theme==="dark","cursor-pointer flex-grow":!0}),onClick:A=>c(g)},j(g.label),11,Zn)],2)]))),128))])):F("",!0)]))),128))])):F("",!0)]))),128))]))}}),Kn=Ur(Xn,[["__scopeId","data-v-a8953f9c"]]),Qn=jt({__name:"TypeWriter",props:{text:{},speed:{}},emits:["complete"],setup(r,{emit:e}){const t=r,n=e,s=L(""),a=L(0),c=L(!1),l=()=>{s.value="",a.value=0,c.value=!0;const f=setInterval(()=>{a.value<t.text.length?(s.value+=t.text[a.value],a.value++):(clearInterval(f),c.value=!1,n("complete"))},t.speed||50)};return Vt(()=>t.text,()=>{l()}),Br(()=>{l()}),(f,i)=>(w(),T("span",null,j(s.value),1))}});function Zt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var Ae=Zt();function qr(r){Ae=r}var nt={exec:()=>null};function M(r,e=""){let t=typeof r=="string"?r:r.source;const n={replace:(s,a)=>{let c=typeof a=="string"?a:a.source;return c=c.replace(Q.caret,"$1"),t=t.replace(s,c),n},getRegex:()=>new RegExp(t,e)};return n}var Q={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:r=>new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}#`),htmlBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}<(?:[a-z].*>|!--)`,"i")},Jn=/^(?:[ \t]*(?:\n|$))+/,es=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,ts=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,at=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,rs=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Xt=/(?:[*+-]|\d{1,9}[.)])/,jr=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Vr=M(jr).replace(/bull/g,Xt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),ns=M(jr).replace(/bull/g,Xt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Kt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,ss=/^[^\n]+/,Qt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,as=M(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Qt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),os=M(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Xt).getRegex(),Tt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Jt=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,ls=M("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Jt).replace("tag",Tt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Yr=M(Kt).replace("hr",at).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Tt).getRegex(),is=M(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Yr).getRegex(),er={blockquote:is,code:es,def:as,fences:ts,heading:rs,hr:at,html:ls,lheading:Vr,list:os,newline:Jn,paragraph:Yr,table:nt,text:ss},_r=M("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",at).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Tt).getRegex(),cs={...er,lheading:ns,table:_r,paragraph:M(Kt).replace("hr",at).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",_r).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Tt).getRegex()},us={...er,html:M(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Jt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:nt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:M(Kt).replace("hr",at).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Vr).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ds=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ps=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Zr=/^( {2,}|\\)\n(?!\s*$)/,hs=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,_t=/[\p{P}\p{S}]/u,tr=/[\s\p{P}\p{S}]/u,Xr=/[^\s\p{P}\p{S}]/u,fs=M(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,tr).getRegex(),Kr=/(?!~)[\p{P}\p{S}]/u,gs=/(?!~)[\s\p{P}\p{S}]/u,ms=/(?:[^\s\p{P}\p{S}]|~)/u,bs=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Qr=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,xs=M(Qr,"u").replace(/punct/g,_t).getRegex(),vs=M(Qr,"u").replace(/punct/g,Kr).getRegex(),Jr="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",ks=M(Jr,"gu").replace(/notPunctSpace/g,Xr).replace(/punctSpace/g,tr).replace(/punct/g,_t).getRegex(),ws=M(Jr,"gu").replace(/notPunctSpace/g,ms).replace(/punctSpace/g,gs).replace(/punct/g,Kr).getRegex(),ys=M("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Xr).replace(/punctSpace/g,tr).replace(/punct/g,_t).getRegex(),Ts=M(/\\(punct)/,"gu").replace(/punct/g,_t).getRegex(),_s=M(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Es=M(Jt).replace("(?:-->|$)","-->").getRegex(),Ss=M("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Es).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),vt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,As=M(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",vt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),en=M(/^!?\[(label)\]\[(ref)\]/).replace("label",vt).replace("ref",Qt).getRegex(),tn=M(/^!?\[(ref)\](?:\[\])?/).replace("ref",Qt).getRegex(),Cs=M("reflink|nolink(?!\\()","g").replace("reflink",en).replace("nolink",tn).getRegex(),rr={_backpedal:nt,anyPunctuation:Ts,autolink:_s,blockSkip:bs,br:Zr,code:ps,del:nt,emStrongLDelim:xs,emStrongRDelimAst:ks,emStrongRDelimUnd:ys,escape:ds,link:As,nolink:tn,punctuation:fs,reflink:en,reflinkSearch:Cs,tag:Ss,text:hs,url:nt},Rs={...rr,link:M(/^!?\[(label)\]\((.*?)\)/).replace("label",vt).getRegex(),reflink:M(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",vt).getRegex()},Ft={...rr,emStrongRDelimAst:ws,emStrongLDelim:vs,url:M(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ds={...Ft,br:M(Zr).replace("{2,}","*").getRegex(),text:M(Ft.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ft={normal:er,gfm:cs,pedantic:us},Xe={normal:rr,gfm:Ft,breaks:Ds,pedantic:Rs},Ls={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Er=r=>Ls[r];function ce(r,e){if(e){if(Q.escapeTest.test(r))return r.replace(Q.escapeReplace,Er)}else if(Q.escapeTestNoEncode.test(r))return r.replace(Q.escapeReplaceNoEncode,Er);return r}function Sr(r){try{r=encodeURI(r).replace(Q.percentDecode,"%")}catch{return null}return r}function Ar(r,e){var a;const t=r.replace(Q.findPipe,(c,l,f)=>{let i=!1,u=l;for(;--u>=0&&f[u]==="\\";)i=!i;return i?"|":" |"}),n=t.split(Q.splitPipe);let s=0;if(n[0].trim()||n.shift(),n.length>0&&!((a=n.at(-1))!=null&&a.trim())&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(Q.slashPipe,"|");return n}function Ke(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n&&r.charAt(n-s-1)===e;)s++;return r.slice(0,n-s)}function Is(r,e){if(r.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<r.length;n++)if(r[n]==="\\")n++;else if(r[n]===e[0])t++;else if(r[n]===e[1]&&(t--,t<0))return n;return t>0?-2:-1}function Cr(r,e,t,n,s){const a=e.href,c=e.title||null,l=r[1].replace(s.other.outputLinkReplace,"$1");n.state.inLink=!0;const f={type:r[0].charAt(0)==="!"?"image":"link",raw:t,href:a,title:c,text:l,tokens:n.inlineTokens(l)};return n.state.inLink=!1,f}function Ms(r,e,t){const n=r.match(t.other.indentCodeCompensation);if(n===null)return e;const s=n[1];return e.split(`
`).map(a=>{const c=a.match(t.other.beginningSpace);if(c===null)return a;const[l]=c;return l.length>=s.length?a.slice(s.length):a}).join(`
`)}var kt=class{constructor(r){N(this,"options");N(this,"rules");N(this,"lexer");this.options=r||Ae}space(r){const e=this.rules.block.newline.exec(r);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(r){const e=this.rules.block.code.exec(r);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:Ke(t,`
`)}}}fences(r){const e=this.rules.block.fences.exec(r);if(e){const t=e[0],n=Ms(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(r){const e=this.rules.block.heading.exec(r);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const n=Ke(t,"#");(this.options.pedantic||!n||this.rules.other.endingSpaceChar.test(n))&&(t=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(r){const e=this.rules.block.hr.exec(r);if(e)return{type:"hr",raw:Ke(e[0],`
`)}}blockquote(r){const e=this.rules.block.blockquote.exec(r);if(e){let t=Ke(e[0],`
`).split(`
`),n="",s="";const a=[];for(;t.length>0;){let c=!1;const l=[];let f;for(f=0;f<t.length;f++)if(this.rules.other.blockquoteStart.test(t[f]))l.push(t[f]),c=!0;else if(!c)l.push(t[f]);else break;t=t.slice(f);const i=l.join(`
`),u=i.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${i}`:i,s=s?`${s}
${u}`:u;const b=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,a,!0),this.lexer.state.top=b,t.length===0)break;const g=a.at(-1);if((g==null?void 0:g.type)==="code")break;if((g==null?void 0:g.type)==="blockquote"){const A=g,E=A.raw+`
`+t.join(`
`),S=this.blockquote(E);a[a.length-1]=S,n=n.substring(0,n.length-A.raw.length)+S.raw,s=s.substring(0,s.length-A.text.length)+S.text;break}else if((g==null?void 0:g.type)==="list"){const A=g,E=A.raw+`
`+t.join(`
`),S=this.list(E);a[a.length-1]=S,n=n.substring(0,n.length-g.raw.length)+S.raw,s=s.substring(0,s.length-A.raw.length)+S.raw,t=E.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:n,tokens:a,text:s}}}list(r){let e=this.rules.block.list.exec(r);if(e){let t=e[1].trim();const n=t.length>1,s={type:"list",raw:"",ordered:n,start:n?+t.slice(0,-1):"",loose:!1,items:[]};t=n?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=n?t:"[*+-]");const a=this.rules.other.listItemRegex(t);let c=!1;for(;r;){let f=!1,i="",u="";if(!(e=a.exec(r))||this.rules.block.hr.test(r))break;i=e[0],r=r.substring(i.length);let b=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,U=>" ".repeat(3*U.length)),g=r.split(`
`,1)[0],A=!b.trim(),E=0;if(this.options.pedantic?(E=2,u=b.trimStart()):A?E=e[1].length+1:(E=e[2].search(this.rules.other.nonSpaceChar),E=E>4?1:E,u=b.slice(E),E+=e[1].length),A&&this.rules.other.blankLine.test(g)&&(i+=g+`
`,r=r.substring(g.length+1),f=!0),!f){const U=this.rules.other.nextBulletRegex(E),D=this.rules.other.hrRegex(E),V=this.rules.other.fencesBeginRegex(E),O=this.rules.other.headingBeginRegex(E),re=this.rules.other.htmlBeginRegex(E);for(;r;){const ne=r.split(`
`,1)[0];let le;if(g=ne,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),le=g):le=g.replace(this.rules.other.tabCharGlobal,"    "),V.test(g)||O.test(g)||re.test(g)||U.test(g)||D.test(g))break;if(le.search(this.rules.other.nonSpaceChar)>=E||!g.trim())u+=`
`+le.slice(E);else{if(A||b.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||V.test(b)||O.test(b)||D.test(b))break;u+=`
`+g}!A&&!g.trim()&&(A=!0),i+=ne+`
`,r=r.substring(ne.length+1),b=le.slice(E)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(i)&&(c=!0));let S=null,C;this.options.gfm&&(S=this.rules.other.listIsTask.exec(u),S&&(C=S[0]!=="[ ] ",u=u.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:i,task:!!S,checked:C,loose:!1,text:u,tokens:[]}),s.raw+=i}const l=s.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let f=0;f<s.items.length;f++)if(this.lexer.state.top=!1,s.items[f].tokens=this.lexer.blockTokens(s.items[f].text,[]),!s.loose){const i=s.items[f].tokens.filter(b=>b.type==="space"),u=i.length>0&&i.some(b=>this.rules.other.anyLine.test(b.raw));s.loose=u}if(s.loose)for(let f=0;f<s.items.length;f++)s.items[f].loose=!0;return s}}html(r){const e=this.rules.block.html.exec(r);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(r){const e=this.rules.block.def.exec(r);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:n,title:s}}}table(r){var c;const e=this.rules.block.table.exec(r);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=Ar(e[1]),n=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===n.length){for(const l of n)this.rules.other.tableAlignRight.test(l)?a.align.push("right"):this.rules.other.tableAlignCenter.test(l)?a.align.push("center"):this.rules.other.tableAlignLeft.test(l)?a.align.push("left"):a.align.push(null);for(let l=0;l<t.length;l++)a.header.push({text:t[l],tokens:this.lexer.inline(t[l]),header:!0,align:a.align[l]});for(const l of s)a.rows.push(Ar(l,a.header.length).map((f,i)=>({text:f,tokens:this.lexer.inline(f),header:!1,align:a.align[i]})));return a}}lheading(r){const e=this.rules.block.lheading.exec(r);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(r){const e=this.rules.block.paragraph.exec(r);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(r){const e=this.rules.block.text.exec(r);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(r){const e=this.rules.inline.escape.exec(r);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(r){const e=this.rules.inline.tag.exec(r);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(r){const e=this.rules.inline.link.exec(r);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const a=Ke(t.slice(0,-1),"\\");if((t.length-a.length)%2===0)return}else{const a=Is(e[2],"()");if(a===-2)return;if(a>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+a;e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let n=e[2],s="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(n);a&&(n=a[1],s=a[3])}else s=e[3]?e[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?n=n.slice(1):n=n.slice(1,-1)),Cr(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(r,e){let t;if((t=this.rules.inline.reflink.exec(r))||(t=this.rules.inline.nolink.exec(r))){const n=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[n.toLowerCase()];if(!s){const a=t[0].charAt(0);return{type:"text",raw:a,text:a}}return Cr(t,s,t[0],this.lexer,this.rules)}}emStrong(r,e,t=""){let n=this.rules.inline.emStrongLDelim.exec(r);if(!n||n[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(n[1]||n[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const a=[...n[0]].length-1;let c,l,f=a,i=0;const u=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,e=e.slice(-1*r.length+a);(n=u.exec(e))!=null;){if(c=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!c)continue;if(l=[...c].length,n[3]||n[4]){f+=l;continue}else if((n[5]||n[6])&&a%3&&!((a+l)%3)){i+=l;continue}if(f-=l,f>0)continue;l=Math.min(l,l+f+i);const b=[...n[0]][0].length,g=r.slice(0,a+n.index+b+l);if(Math.min(a,l)%2){const E=g.slice(1,-1);return{type:"em",raw:g,text:E,tokens:this.lexer.inlineTokens(E)}}const A=g.slice(2,-2);return{type:"strong",raw:g,text:A,tokens:this.lexer.inlineTokens(A)}}}}codespan(r){const e=this.rules.inline.code.exec(r);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return n&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(r){const e=this.rules.inline.br.exec(r);if(e)return{type:"br",raw:e[0]}}del(r){const e=this.rules.inline.del.exec(r);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(r){const e=this.rules.inline.autolink.exec(r);if(e){let t,n;return e[2]==="@"?(t=e[1],n="mailto:"+t):(t=e[1],n=t),{type:"link",raw:e[0],text:t,href:n,tokens:[{type:"text",raw:t,text:t}]}}}url(r){var t;let e;if(e=this.rules.inline.url.exec(r)){let n,s;if(e[2]==="@")n=e[0],s="mailto:"+n;else{let a;do a=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(a!==e[0]);n=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(r){const e=this.rules.inline.text.exec(r);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},be=class Ht{constructor(e){N(this,"tokens");N(this,"options");N(this,"state");N(this,"tokenizer");N(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Ae,this.options.tokenizer=this.options.tokenizer||new kt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:Q,block:ft.normal,inline:Xe.normal};this.options.pedantic?(t.block=ft.pedantic,t.inline=Xe.pedantic):this.options.gfm&&(t.block=ft.gfm,this.options.breaks?t.inline=Xe.breaks:t.inline=Xe.gfm),this.tokenizer.rules=t}static get rules(){return{block:ft,inline:Xe}}static lex(e,t){return new Ht(t).lex(e)}static lexInline(e,t){return new Ht(t).inlineTokens(e)}lex(e){e=e.replace(Q.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){var s,a,c;for(this.options.pedantic&&(e=e.replace(Q.tabCharGlobal,"    ").replace(Q.spaceLine,""));e;){let l;if((a=(s=this.options.extensions)==null?void 0:s.block)!=null&&a.some(i=>(l=i.call({lexer:this},e,t))?(e=e.substring(l.raw.length),t.push(l),!0):!1))continue;if(l=this.tokenizer.space(e)){e=e.substring(l.raw.length);const i=t.at(-1);l.raw.length===1&&i!==void 0?i.raw+=`
`:t.push(l);continue}if(l=this.tokenizer.code(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(l=this.tokenizer.fences(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.heading(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.hr(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.blockquote(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.list(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.html(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.def(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.raw,this.inlineQueue.at(-1).src=i.text):this.tokens.links[l.tag]||(this.tokens.links[l.tag]={href:l.href,title:l.title});continue}if(l=this.tokenizer.table(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.lheading(e)){e=e.substring(l.raw.length),t.push(l);continue}let f=e;if((c=this.options.extensions)!=null&&c.startBlock){let i=1/0;const u=e.slice(1);let b;this.options.extensions.startBlock.forEach(g=>{b=g.call({lexer:this},u),typeof b=="number"&&b>=0&&(i=Math.min(i,b))}),i<1/0&&i>=0&&(f=e.substring(0,i+1))}if(this.state.top&&(l=this.tokenizer.paragraph(f))){const i=t.at(-1);n&&(i==null?void 0:i.type)==="paragraph"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l),n=f.length!==e.length,e=e.substring(l.raw.length);continue}if(l=this.tokenizer.text(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}else throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var l,f,i;let n=e,s=null;if(this.tokens.links){const u=Object.keys(this.tokens.links);if(u.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)u.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,c="";for(;e;){a||(c=""),a=!1;let u;if((f=(l=this.options.extensions)==null?void 0:l.inline)!=null&&f.some(g=>(u=g.call({lexer:this},e,t))?(e=e.substring(u.raw.length),t.push(u),!0):!1))continue;if(u=this.tokenizer.escape(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.tag(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.link(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(u.raw.length);const g=t.at(-1);u.type==="text"&&(g==null?void 0:g.type)==="text"?(g.raw+=u.raw,g.text+=u.text):t.push(u);continue}if(u=this.tokenizer.emStrong(e,n,c)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.codespan(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.br(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.del(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.autolink(e)){e=e.substring(u.raw.length),t.push(u);continue}if(!this.state.inLink&&(u=this.tokenizer.url(e))){e=e.substring(u.raw.length),t.push(u);continue}let b=e;if((i=this.options.extensions)!=null&&i.startInline){let g=1/0;const A=e.slice(1);let E;this.options.extensions.startInline.forEach(S=>{E=S.call({lexer:this},A),typeof E=="number"&&E>=0&&(g=Math.min(g,E))}),g<1/0&&g>=0&&(b=e.substring(0,g+1))}if(u=this.tokenizer.inlineText(b)){e=e.substring(u.raw.length),u.raw.slice(-1)!=="_"&&(c=u.raw.slice(-1)),a=!0;const g=t.at(-1);(g==null?void 0:g.type)==="text"?(g.raw+=u.raw,g.text+=u.text):t.push(u);continue}if(e){const g="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(g);break}else throw new Error(g)}}return t}},wt=class{constructor(r){N(this,"options");N(this,"parser");this.options=r||Ae}space(r){return""}code({text:r,lang:e,escaped:t}){var a;const n=(a=(e||"").match(Q.notSpaceStart))==null?void 0:a[0],s=r.replace(Q.endingNewline,"")+`
`;return n?'<pre><code class="language-'+ce(n)+'">'+(t?s:ce(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:ce(s,!0))+`</code></pre>
`}blockquote({tokens:r}){return`<blockquote>
${this.parser.parse(r)}</blockquote>
`}html({text:r}){return r}heading({tokens:r,depth:e}){return`<h${e}>${this.parser.parseInline(r)}</h${e}>
`}hr(r){return`<hr>
`}list(r){const e=r.ordered,t=r.start;let n="";for(let c=0;c<r.items.length;c++){const l=r.items[c];n+=this.listitem(l)}const s=e?"ol":"ul",a=e&&t!==1?' start="'+t+'"':"";return"<"+s+a+`>
`+n+"</"+s+`>
`}listitem(r){var t;let e="";if(r.task){const n=this.checkbox({checked:!!r.checked});r.loose?((t=r.tokens[0])==null?void 0:t.type)==="paragraph"?(r.tokens[0].text=n+" "+r.tokens[0].text,r.tokens[0].tokens&&r.tokens[0].tokens.length>0&&r.tokens[0].tokens[0].type==="text"&&(r.tokens[0].tokens[0].text=n+" "+ce(r.tokens[0].tokens[0].text),r.tokens[0].tokens[0].escaped=!0)):r.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):e+=n+" "}return e+=this.parser.parse(r.tokens,!!r.loose),`<li>${e}</li>
`}checkbox({checked:r}){return"<input "+(r?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:r}){return`<p>${this.parser.parseInline(r)}</p>
`}table(r){let e="",t="";for(let s=0;s<r.header.length;s++)t+=this.tablecell(r.header[s]);e+=this.tablerow({text:t});let n="";for(let s=0;s<r.rows.length;s++){const a=r.rows[s];t="";for(let c=0;c<a.length;c++)t+=this.tablecell(a[c]);n+=this.tablerow({text:t})}return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+n+`</table>
`}tablerow({text:r}){return`<tr>
${r}</tr>
`}tablecell(r){const e=this.parser.parseInline(r.tokens),t=r.header?"th":"td";return(r.align?`<${t} align="${r.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:r}){return`<strong>${this.parser.parseInline(r)}</strong>`}em({tokens:r}){return`<em>${this.parser.parseInline(r)}</em>`}codespan({text:r}){return`<code>${ce(r,!0)}</code>`}br(r){return"<br>"}del({tokens:r}){return`<del>${this.parser.parseInline(r)}</del>`}link({href:r,title:e,tokens:t}){const n=this.parser.parseInline(t),s=Sr(r);if(s===null)return n;r=s;let a='<a href="'+r+'"';return e&&(a+=' title="'+ce(e)+'"'),a+=">"+n+"</a>",a}image({href:r,title:e,text:t,tokens:n}){n&&(t=this.parser.parseInline(n,this.parser.textRenderer));const s=Sr(r);if(s===null)return ce(t);r=s;let a=`<img src="${r}" alt="${t}"`;return e&&(a+=` title="${ce(e)}"`),a+=">",a}text(r){return"tokens"in r&&r.tokens?this.parser.parseInline(r.tokens):"escaped"in r&&r.escaped?r.text:ce(r.text)}},nr=class{strong({text:r}){return r}em({text:r}){return r}codespan({text:r}){return r}del({text:r}){return r}html({text:r}){return r}text({text:r}){return r}link({text:r}){return""+r}image({text:r}){return""+r}br(){return""}},xe=class Gt{constructor(e){N(this,"options");N(this,"renderer");N(this,"textRenderer");this.options=e||Ae,this.options.renderer=this.options.renderer||new wt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new nr}static parse(e,t){return new Gt(t).parse(e)}static parseInline(e,t){return new Gt(t).parseInline(e)}parse(e,t=!0){var s,a;let n="";for(let c=0;c<e.length;c++){const l=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=l,u=this.options.extensions.renderers[i.type].call({parser:this},i);if(u!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){n+=u||"";continue}}const f=l;switch(f.type){case"space":{n+=this.renderer.space(f);continue}case"hr":{n+=this.renderer.hr(f);continue}case"heading":{n+=this.renderer.heading(f);continue}case"code":{n+=this.renderer.code(f);continue}case"table":{n+=this.renderer.table(f);continue}case"blockquote":{n+=this.renderer.blockquote(f);continue}case"list":{n+=this.renderer.list(f);continue}case"html":{n+=this.renderer.html(f);continue}case"paragraph":{n+=this.renderer.paragraph(f);continue}case"text":{let i=f,u=this.renderer.text(i);for(;c+1<e.length&&e[c+1].type==="text";)i=e[++c],u+=`
`+this.renderer.text(i);t?n+=this.renderer.paragraph({type:"paragraph",raw:u,text:u,tokens:[{type:"text",raw:u,text:u,escaped:!0}]}):n+=u;continue}default:{const i='Token with "'+f.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}parseInline(e,t=this.renderer){var s,a;let n="";for(let c=0;c<e.length;c++){const l=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=this.options.extensions.renderers[l.type].call({parser:this},l);if(i!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(l.type)){n+=i||"";continue}}const f=l;switch(f.type){case"escape":{n+=t.text(f);break}case"html":{n+=t.html(f);break}case"link":{n+=t.link(f);break}case"image":{n+=t.image(f);break}case"strong":{n+=t.strong(f);break}case"em":{n+=t.em(f);break}case"codespan":{n+=t.codespan(f);break}case"br":{n+=t.br(f);break}case"del":{n+=t.del(f);break}case"text":{n+=t.text(f);break}default:{const i='Token with "'+f.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}},Bt,bt=(Bt=class{constructor(r){N(this,"options");N(this,"block");this.options=r||Ae}preprocess(r){return r}postprocess(r){return r}processAllTokens(r){return r}provideLexer(){return this.block?be.lex:be.lexInline}provideParser(){return this.block?xe.parse:xe.parseInline}},N(Bt,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Bt),Ns=class{constructor(...r){N(this,"defaults",Zt());N(this,"options",this.setOptions);N(this,"parse",this.parseMarkdown(!0));N(this,"parseInline",this.parseMarkdown(!1));N(this,"Parser",xe);N(this,"Renderer",wt);N(this,"TextRenderer",nr);N(this,"Lexer",be);N(this,"Tokenizer",kt);N(this,"Hooks",bt);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const a of r)switch(t=t.concat(e.call(this,a)),a.type){case"table":{const c=a;for(const l of c.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of c.rows)for(const f of l)t=t.concat(this.walkTokens(f.tokens,e));break}case"list":{const c=a;t=t.concat(this.walkTokens(c.items,e));break}default:{const c=a;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(l=>{const f=c[l].flat(1/0);t=t.concat(this.walkTokens(f,e))}):c.tokens&&(t=t.concat(this.walkTokens(c.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=e.renderers[s.name];a?e.renderers[s.name]=function(...c){let l=s.renderer.apply(this,c);return l===!1&&(l=a.apply(this,c)),l}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=e[s.level];a?a.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new wt(this.defaults);for(const a in t.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const c=a,l=t.renderer[c],f=s[c];s[c]=(...i)=>{let u=l.apply(s,i);return u===!1&&(u=f.apply(s,i)),u||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new kt(this.defaults);for(const a in t.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const c=a,l=t.tokenizer[c],f=s[c];s[c]=(...i)=>{let u=l.apply(s,i);return u===!1&&(u=f.apply(s,i)),u}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new bt;for(const a in t.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const c=a,l=t.hooks[c],f=s[c];bt.passThroughHooks.has(a)?s[c]=i=>{if(this.defaults.async)return Promise.resolve(l.call(s,i)).then(b=>f.call(s,b));const u=l.call(s,i);return f.call(s,u)}:s[c]=(...i)=>{let u=l.apply(s,i);return u===!1&&(u=f.apply(s,i)),u}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,a=t.walkTokens;n.walkTokens=function(c){let l=[];return l.push(a.call(this,c)),s&&(l=l.concat(s.call(this,c))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return be.lex(r,e??this.defaults)}parser(r,e){return xe.parse(r,e??this.defaults)}parseMarkdown(r){return(t,n)=>{const s={...n},a={...this.defaults,...s},c=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&s.async===!1)return c(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return c(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return c(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=r);const l=a.hooks?a.hooks.provideLexer():r?be.lex:be.lexInline,f=a.hooks?a.hooks.provideParser():r?xe.parse:xe.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(i=>l(i,a)).then(i=>a.hooks?a.hooks.processAllTokens(i):i).then(i=>a.walkTokens?Promise.all(this.walkTokens(i,a.walkTokens)).then(()=>i):i).then(i=>f(i,a)).then(i=>a.hooks?a.hooks.postprocess(i):i).catch(c);try{a.hooks&&(t=a.hooks.preprocess(t));let i=l(t,a);a.hooks&&(i=a.hooks.processAllTokens(i)),a.walkTokens&&this.walkTokens(i,a.walkTokens);let u=f(i,a);return a.hooks&&(u=a.hooks.postprocess(u)),u}catch(i){return c(i)}}}onError(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+ce(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}}},Se=new Ns;function I(r,e){return Se.parse(r,e)}I.options=I.setOptions=function(r){return Se.setOptions(r),I.defaults=Se.defaults,qr(I.defaults),I};I.getDefaults=Zt;I.defaults=Ae;I.use=function(...r){return Se.use(...r),I.defaults=Se.defaults,qr(I.defaults),I};I.walkTokens=function(r,e){return Se.walkTokens(r,e)};I.parseInline=Se.parseInline;I.Parser=xe;I.parser=xe.parse;I.Renderer=wt;I.TextRenderer=nr;I.Lexer=be;I.lexer=be.lex;I.Tokenizer=kt;I.Hooks=bt;I.parse=I;I.options;I.setOptions;I.use;I.walkTokens;I.parseInline;xe.parse;be.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:rn,setPrototypeOf:Rr,isFrozen:Os,getPrototypeOf:Ps,getOwnPropertyDescriptor:zs}=Object;let{freeze:J,seal:se,create:nn}=Object,{apply:Wt,construct:qt}=typeof Reflect<"u"&&Reflect;J||(J=function(e){return e});se||(se=function(e){return e});Wt||(Wt=function(e,t,n){return e.apply(t,n)});qt||(qt=function(e,t){return new e(...t)});const gt=ee(Array.prototype.forEach),$s=ee(Array.prototype.lastIndexOf),Dr=ee(Array.prototype.pop),Qe=ee(Array.prototype.push),Us=ee(Array.prototype.splice),xt=ee(String.prototype.toLowerCase),Ot=ee(String.prototype.toString),Lr=ee(String.prototype.match),Je=ee(String.prototype.replace),Bs=ee(String.prototype.indexOf),Fs=ee(String.prototype.trim),oe=ee(Object.prototype.hasOwnProperty),K=ee(RegExp.prototype.test),et=Hs(TypeError);function ee(r){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return Wt(r,e,n)}}function Hs(r){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return qt(r,t)}}function R(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:xt;Rr&&Rr(r,null);let n=e.length;for(;n--;){let s=e[n];if(typeof s=="string"){const a=t(s);a!==s&&(Os(e)||(e[n]=a),s=a)}r[s]=!0}return r}function Gs(r){for(let e=0;e<r.length;e++)oe(r,e)||(r[e]=null);return r}function ge(r){const e=nn(null);for(const[t,n]of rn(r))oe(r,t)&&(Array.isArray(n)?e[t]=Gs(n):n&&typeof n=="object"&&n.constructor===Object?e[t]=ge(n):e[t]=n);return e}function tt(r,e){for(;r!==null;){const n=zs(r,e);if(n){if(n.get)return ee(n.get);if(typeof n.value=="function")return ee(n.value)}r=Ps(r)}function t(){return null}return t}const Ir=J(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Pt=J(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),zt=J(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ws=J(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),$t=J(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),qs=J(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Mr=J(["#text"]),Nr=J(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Ut=J(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Or=J(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),mt=J(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),js=se(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Vs=se(/<%[\w\W]*|[\w\W]*%>/gm),Ys=se(/\$\{[\w\W]*/gm),Zs=se(/^data-[\-\w.\u00B7-\uFFFF]+$/),Xs=se(/^aria-[\-\w]+$/),sn=se(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ks=se(/^(?:\w+script|data):/i),Qs=se(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),an=se(/^html$/i),Js=se(/^[a-z][.\w]*(-[.\w]+)+$/i);var Pr=Object.freeze({__proto__:null,ARIA_ATTR:Xs,ATTR_WHITESPACE:Qs,CUSTOM_ELEMENT:Js,DATA_ATTR:Zs,DOCTYPE_NAME:an,ERB_EXPR:Vs,IS_ALLOWED_URI:sn,IS_SCRIPT_OR_DATA:Ks,MUSTACHE_EXPR:js,TMPLIT_EXPR:Ys});const rt={element:1,text:3,progressingInstruction:7,comment:8,document:9},ea=function(){return typeof window>"u"?null:window},ta=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(n=t.getAttribute(s));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML(c){return c},createScriptURL(c){return c}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},zr=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function on(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ea();const e=y=>on(y);if(e.version="3.2.6",e.removed=[],!r||!r.document||r.document.nodeType!==rt.document||!r.Element)return e.isSupported=!1,e;let{document:t}=r;const n=t,s=n.currentScript,{DocumentFragment:a,HTMLTemplateElement:c,Node:l,Element:f,NodeFilter:i,NamedNodeMap:u=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:b,DOMParser:g,trustedTypes:A}=r,E=f.prototype,S=tt(E,"cloneNode"),C=tt(E,"remove"),U=tt(E,"nextSibling"),D=tt(E,"childNodes"),V=tt(E,"parentNode");if(typeof c=="function"){const y=t.createElement("template");y.content&&y.content.ownerDocument&&(t=y.content.ownerDocument)}let O,re="";const{implementation:ne,createNodeIterator:le,createDocumentFragment:ze,getElementsByTagName:Te}=t,{importNode:Ce}=n;let Y=zr();e.isSupported=typeof rn=="function"&&typeof V=="function"&&ne&&ne.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:$e,ERB_EXPR:Ue,TMPLIT_EXPR:Be,DATA_ATTR:Et,ARIA_ATTR:St,IS_SCRIPT_OR_DATA:Fe,ATTR_WHITESPACE:He,CUSTOM_ELEMENT:At}=Pr;let{IS_ALLOWED_URI:ot}=Pr,H=null;const Ge=R({},[...Ir,...Pt,...zt,...$t,...Mr]);let B=null;const We=R({},[...Nr,...Ut,...Or,...mt]);let $=Object.seal(nn(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ve=null,qe=null,Re=!0,je=!0,lt=!1,it=!0,ke=!1,De=!0,de=!1,x=!1,d=!1,G=!1,P=!1,v=!1,_e=!0,ct=!1;const ar="user-content-";let ae=!0,Ve=!1,Le={},Ie=null;const or=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let lr=null;const ir=R({},["audio","video","img","source","image","track"]);let Ct=null;const cr=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ut="http://www.w3.org/1998/Math/MathML",dt="http://www.w3.org/2000/svg",pe="http://www.w3.org/1999/xhtml";let Me=pe,Rt=!1,Dt=null;const ln=R({},[ut,dt,pe],Ot);let pt=R({},["mi","mo","mn","ms","mtext"]),ht=R({},["annotation-xml"]);const cn=R({},["title","style","font","a","script"]);let Ye=null;const un=["application/xhtml+xml","text/html"],dn="text/html";let q=null,Ne=null;const pn=t.createElement("form"),ur=function(o){return o instanceof RegExp||o instanceof Function},Lt=function(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Ne&&Ne===o)){if((!o||typeof o!="object")&&(o={}),o=ge(o),Ye=un.indexOf(o.PARSER_MEDIA_TYPE)===-1?dn:o.PARSER_MEDIA_TYPE,q=Ye==="application/xhtml+xml"?Ot:xt,H=oe(o,"ALLOWED_TAGS")?R({},o.ALLOWED_TAGS,q):Ge,B=oe(o,"ALLOWED_ATTR")?R({},o.ALLOWED_ATTR,q):We,Dt=oe(o,"ALLOWED_NAMESPACES")?R({},o.ALLOWED_NAMESPACES,Ot):ln,Ct=oe(o,"ADD_URI_SAFE_ATTR")?R(ge(cr),o.ADD_URI_SAFE_ATTR,q):cr,lr=oe(o,"ADD_DATA_URI_TAGS")?R(ge(ir),o.ADD_DATA_URI_TAGS,q):ir,Ie=oe(o,"FORBID_CONTENTS")?R({},o.FORBID_CONTENTS,q):or,ve=oe(o,"FORBID_TAGS")?R({},o.FORBID_TAGS,q):ge({}),qe=oe(o,"FORBID_ATTR")?R({},o.FORBID_ATTR,q):ge({}),Le=oe(o,"USE_PROFILES")?o.USE_PROFILES:!1,Re=o.ALLOW_ARIA_ATTR!==!1,je=o.ALLOW_DATA_ATTR!==!1,lt=o.ALLOW_UNKNOWN_PROTOCOLS||!1,it=o.ALLOW_SELF_CLOSE_IN_ATTR!==!1,ke=o.SAFE_FOR_TEMPLATES||!1,De=o.SAFE_FOR_XML!==!1,de=o.WHOLE_DOCUMENT||!1,G=o.RETURN_DOM||!1,P=o.RETURN_DOM_FRAGMENT||!1,v=o.RETURN_TRUSTED_TYPE||!1,d=o.FORCE_BODY||!1,_e=o.SANITIZE_DOM!==!1,ct=o.SANITIZE_NAMED_PROPS||!1,ae=o.KEEP_CONTENT!==!1,Ve=o.IN_PLACE||!1,ot=o.ALLOWED_URI_REGEXP||sn,Me=o.NAMESPACE||pe,pt=o.MATHML_TEXT_INTEGRATION_POINTS||pt,ht=o.HTML_INTEGRATION_POINTS||ht,$=o.CUSTOM_ELEMENT_HANDLING||{},o.CUSTOM_ELEMENT_HANDLING&&ur(o.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&($.tagNameCheck=o.CUSTOM_ELEMENT_HANDLING.tagNameCheck),o.CUSTOM_ELEMENT_HANDLING&&ur(o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&($.attributeNameCheck=o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),o.CUSTOM_ELEMENT_HANDLING&&typeof o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&($.allowCustomizedBuiltInElements=o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ke&&(je=!1),P&&(G=!0),Le&&(H=R({},Mr),B=[],Le.html===!0&&(R(H,Ir),R(B,Nr)),Le.svg===!0&&(R(H,Pt),R(B,Ut),R(B,mt)),Le.svgFilters===!0&&(R(H,zt),R(B,Ut),R(B,mt)),Le.mathMl===!0&&(R(H,$t),R(B,Or),R(B,mt))),o.ADD_TAGS&&(H===Ge&&(H=ge(H)),R(H,o.ADD_TAGS,q)),o.ADD_ATTR&&(B===We&&(B=ge(B)),R(B,o.ADD_ATTR,q)),o.ADD_URI_SAFE_ATTR&&R(Ct,o.ADD_URI_SAFE_ATTR,q),o.FORBID_CONTENTS&&(Ie===or&&(Ie=ge(Ie)),R(Ie,o.FORBID_CONTENTS,q)),ae&&(H["#text"]=!0),de&&R(H,["html","head","body"]),H.table&&(R(H,["tbody"]),delete ve.tbody),o.TRUSTED_TYPES_POLICY){if(typeof o.TRUSTED_TYPES_POLICY.createHTML!="function")throw et('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof o.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw et('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');O=o.TRUSTED_TYPES_POLICY,re=O.createHTML("")}else O===void 0&&(O=ta(A,s)),O!==null&&typeof re=="string"&&(re=O.createHTML(""));J&&J(o),Ne=o}},dr=R({},[...Pt,...zt,...Ws]),pr=R({},[...$t,...qs]),hn=function(o){let m=V(o);(!m||!m.tagName)&&(m={namespaceURI:Me,tagName:"template"});const k=xt(o.tagName),z=xt(m.tagName);return Dt[o.namespaceURI]?o.namespaceURI===dt?m.namespaceURI===pe?k==="svg":m.namespaceURI===ut?k==="svg"&&(z==="annotation-xml"||pt[z]):!!dr[k]:o.namespaceURI===ut?m.namespaceURI===pe?k==="math":m.namespaceURI===dt?k==="math"&&ht[z]:!!pr[k]:o.namespaceURI===pe?m.namespaceURI===dt&&!ht[z]||m.namespaceURI===ut&&!pt[z]?!1:!pr[k]&&(cn[k]||!dr[k]):!!(Ye==="application/xhtml+xml"&&Dt[o.namespaceURI]):!1},ie=function(o){Qe(e.removed,{element:o});try{V(o).removeChild(o)}catch{C(o)}},Oe=function(o,m){try{Qe(e.removed,{attribute:m.getAttributeNode(o),from:m})}catch{Qe(e.removed,{attribute:null,from:m})}if(m.removeAttribute(o),o==="is")if(G||P)try{ie(m)}catch{}else try{m.setAttribute(o,"")}catch{}},hr=function(o){let m=null,k=null;if(d)o="<remove></remove>"+o;else{const W=Lr(o,/^[\r\n\t ]+/);k=W&&W[0]}Ye==="application/xhtml+xml"&&Me===pe&&(o='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+o+"</body></html>");const z=O?O.createHTML(o):o;if(Me===pe)try{m=new g().parseFromString(z,Ye)}catch{}if(!m||!m.documentElement){m=ne.createDocument(Me,"template",null);try{m.documentElement.innerHTML=Rt?re:z}catch{}}const Z=m.body||m.documentElement;return o&&k&&Z.insertBefore(t.createTextNode(k),Z.childNodes[0]||null),Me===pe?Te.call(m,de?"html":"body")[0]:de?m.documentElement:Z},fr=function(o){return le.call(o.ownerDocument||o,o,i.SHOW_ELEMENT|i.SHOW_COMMENT|i.SHOW_TEXT|i.SHOW_PROCESSING_INSTRUCTION|i.SHOW_CDATA_SECTION,null)},It=function(o){return o instanceof b&&(typeof o.nodeName!="string"||typeof o.textContent!="string"||typeof o.removeChild!="function"||!(o.attributes instanceof u)||typeof o.removeAttribute!="function"||typeof o.setAttribute!="function"||typeof o.namespaceURI!="string"||typeof o.insertBefore!="function"||typeof o.hasChildNodes!="function")},gr=function(o){return typeof l=="function"&&o instanceof l};function he(y,o,m){gt(y,k=>{k.call(e,o,m,Ne)})}const mr=function(o){let m=null;if(he(Y.beforeSanitizeElements,o,null),It(o))return ie(o),!0;const k=q(o.nodeName);if(he(Y.uponSanitizeElement,o,{tagName:k,allowedTags:H}),De&&o.hasChildNodes()&&!gr(o.firstElementChild)&&K(/<[/\w!]/g,o.innerHTML)&&K(/<[/\w!]/g,o.textContent)||o.nodeType===rt.progressingInstruction||De&&o.nodeType===rt.comment&&K(/<[/\w]/g,o.data))return ie(o),!0;if(!H[k]||ve[k]){if(!ve[k]&&xr(k)&&($.tagNameCheck instanceof RegExp&&K($.tagNameCheck,k)||$.tagNameCheck instanceof Function&&$.tagNameCheck(k)))return!1;if(ae&&!Ie[k]){const z=V(o)||o.parentNode,Z=D(o)||o.childNodes;if(Z&&z){const W=Z.length;for(let te=W-1;te>=0;--te){const fe=S(Z[te],!0);fe.__removalCount=(o.__removalCount||0)+1,z.insertBefore(fe,U(o))}}}return ie(o),!0}return o instanceof f&&!hn(o)||(k==="noscript"||k==="noembed"||k==="noframes")&&K(/<\/no(script|embed|frames)/i,o.innerHTML)?(ie(o),!0):(ke&&o.nodeType===rt.text&&(m=o.textContent,gt([$e,Ue,Be],z=>{m=Je(m,z," ")}),o.textContent!==m&&(Qe(e.removed,{element:o.cloneNode()}),o.textContent=m)),he(Y.afterSanitizeElements,o,null),!1)},br=function(o,m,k){if(_e&&(m==="id"||m==="name")&&(k in t||k in pn))return!1;if(!(je&&!qe[m]&&K(Et,m))){if(!(Re&&K(St,m))){if(!B[m]||qe[m]){if(!(xr(o)&&($.tagNameCheck instanceof RegExp&&K($.tagNameCheck,o)||$.tagNameCheck instanceof Function&&$.tagNameCheck(o))&&($.attributeNameCheck instanceof RegExp&&K($.attributeNameCheck,m)||$.attributeNameCheck instanceof Function&&$.attributeNameCheck(m))||m==="is"&&$.allowCustomizedBuiltInElements&&($.tagNameCheck instanceof RegExp&&K($.tagNameCheck,k)||$.tagNameCheck instanceof Function&&$.tagNameCheck(k))))return!1}else if(!Ct[m]){if(!K(ot,Je(k,He,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&o!=="script"&&Bs(k,"data:")===0&&lr[o])){if(!(lt&&!K(Fe,Je(k,He,"")))){if(k)return!1}}}}}}return!0},xr=function(o){return o!=="annotation-xml"&&Lr(o,At)},vr=function(o){he(Y.beforeSanitizeAttributes,o,null);const{attributes:m}=o;if(!m||It(o))return;const k={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:B,forceKeepAttr:void 0};let z=m.length;for(;z--;){const Z=m[z],{name:W,namespaceURI:te,value:fe}=Z,Ze=q(W),Mt=fe;let X=W==="value"?Mt:Fs(Mt);if(k.attrName=Ze,k.attrValue=X,k.keepAttr=!0,k.forceKeepAttr=void 0,he(Y.uponSanitizeAttribute,o,k),X=k.attrValue,ct&&(Ze==="id"||Ze==="name")&&(Oe(W,o),X=ar+X),De&&K(/((--!?|])>)|<\/(style|title)/i,X)){Oe(W,o);continue}if(k.forceKeepAttr)continue;if(!k.keepAttr){Oe(W,o);continue}if(!it&&K(/\/>/i,X)){Oe(W,o);continue}ke&&gt([$e,Ue,Be],wr=>{X=Je(X,wr," ")});const kr=q(o.nodeName);if(!br(kr,Ze,X)){Oe(W,o);continue}if(O&&typeof A=="object"&&typeof A.getAttributeType=="function"&&!te)switch(A.getAttributeType(kr,Ze)){case"TrustedHTML":{X=O.createHTML(X);break}case"TrustedScriptURL":{X=O.createScriptURL(X);break}}if(X!==Mt)try{te?o.setAttributeNS(te,W,X):o.setAttribute(W,X),It(o)?ie(o):Dr(e.removed)}catch{Oe(W,o)}}he(Y.afterSanitizeAttributes,o,null)},fn=function y(o){let m=null;const k=fr(o);for(he(Y.beforeSanitizeShadowDOM,o,null);m=k.nextNode();)he(Y.uponSanitizeShadowNode,m,null),mr(m),vr(m),m.content instanceof a&&y(m.content);he(Y.afterSanitizeShadowDOM,o,null)};return e.sanitize=function(y){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,k=null,z=null,Z=null;if(Rt=!y,Rt&&(y="<!-->"),typeof y!="string"&&!gr(y))if(typeof y.toString=="function"){if(y=y.toString(),typeof y!="string")throw et("dirty is not a string, aborting")}else throw et("toString is not a function");if(!e.isSupported)return y;if(x||Lt(o),e.removed=[],typeof y=="string"&&(Ve=!1),Ve){if(y.nodeName){const fe=q(y.nodeName);if(!H[fe]||ve[fe])throw et("root node is forbidden and cannot be sanitized in-place")}}else if(y instanceof l)m=hr("<!---->"),k=m.ownerDocument.importNode(y,!0),k.nodeType===rt.element&&k.nodeName==="BODY"||k.nodeName==="HTML"?m=k:m.appendChild(k);else{if(!G&&!ke&&!de&&y.indexOf("<")===-1)return O&&v?O.createHTML(y):y;if(m=hr(y),!m)return G?null:v?re:""}m&&d&&ie(m.firstChild);const W=fr(Ve?y:m);for(;z=W.nextNode();)mr(z),vr(z),z.content instanceof a&&fn(z.content);if(Ve)return y;if(G){if(P)for(Z=ze.call(m.ownerDocument);m.firstChild;)Z.appendChild(m.firstChild);else Z=m;return(B.shadowroot||B.shadowrootmode)&&(Z=Ce.call(n,Z,!0)),Z}let te=de?m.outerHTML:m.innerHTML;return de&&H["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&K(an,m.ownerDocument.doctype.name)&&(te="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+te),ke&&gt([$e,Ue,Be],fe=>{te=Je(te,fe," ")}),O&&v?O.createHTML(te):te},e.setConfig=function(){let y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Lt(y),x=!0},e.clearConfig=function(){Ne=null,x=!1},e.isValidAttribute=function(y,o,m){Ne||Lt({});const k=q(y),z=q(o);return br(k,z,m)},e.addHook=function(y,o){typeof o=="function"&&Qe(Y[y],o)},e.removeHook=function(y,o){if(o!==void 0){const m=$s(Y[y],o);return m===-1?void 0:Us(Y[y],m,1)[0]}return Dr(Y[y])},e.removeHooks=function(y){Y[y]=[]},e.removeAllHooks=function(){Y=zr()},e}var ra=on();const na={class:"flex-1"},sa={class:"flex items-center"},aa={class:"relative"},oa={key:1,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5"},la={key:0,class:"flex items-center gap-1 text-gray-500 text-xs"},ia={key:1,class:"flex flex-col gap-1"},ca=["onClick"],ua={key:2,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5"},da={class:"flex items-center gap-2"},pa={class:"px-0.5"},ha={class:"mb-1"},fa={key:0},ga={key:1,class:"text-gray-500"},ma={class:"flex items-center justify-between mt-0.5"},ba={class:"flex items-center gap-1"},xa={class:"flex flex-wrap gap-1"},va=["onClick"],ka={key:0,class:"flex items-start gap-1.5 max-w-full"},wa={class:"flex-1 bg-[var(--bg-color)] rounded-lg p-2 shadow-sm break-words"},ya={class:"text-[var(--text-color)] leading-tight text-[15px]"},Ta=["innerHTML"],_a={key:1,class:"h-full flex items-center justify-center text-gray-500 text-[15px]"},Ea={class:"flex items-center justify-between"},Sa={class:"text-xs opacity-75"},Aa={class:"border-t border-[var(--border-color)] bg-[var(--bg-color)] w-full"},Ca={class:"p-0.5 max-w-full"},Ra=["placeholder","disabled"],Da={class:"flex items-center justify-between"},La={class:"flex items-center gap-1"},Ia={class:"relative model-selector"},Ma={class:"max-h-[60vh] overflow-y-auto"},Na=["onClick"],Oa={class:"flex items-center gap-2"},Pa=["disabled"],za={class:"flex-1 overflow-y-auto p-4"},$a={class:"flex-1 min-w-0"},Ua={class:"flex items-center gap-2"},Ba={class:"font-medium text-gray-900 truncate"},Fa={class:"text-xs text-gray-400"},Ha={class:"text-xs text-gray-500 break-all"},Ga=["onClick"],Wa={key:0,class:"text-center text-gray-500 py-8"},qa={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},ja={class:"add-model-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 flex flex-col"},Va={class:"p-4 space-y-4"},Ya={key:0,class:"mt-4"},Za={class:"flex items-center gap-2"},Xa={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},Ka={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},Qa={class:"p-4 border-t border-[var(--border-color)] flex gap-2"},Ja=["disabled"],eo={key:0,class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},to=jt({__name:"Main",setup(r){const e=Wr(),t=Hr(),n=yt(),s=Yt(),a=Fr(),c=$r(),l=Gr(),f=L(!1),i=L(!1),u=L(null),b=L(""),g=L({entryDisplayConfig:{skipJsCss:!0,excludedPathPatterns:[]}}),A=L(!1),E=L(""),S=L(!1),C=L(!1),U=L(!1),D=L({showName:"",baseUrl:"",modelName:"",apiKey:""}),V=L(null),O=L(!1);let re=null;const ne=L(!0),le=L(!0),ze=ue;let Te=0;const Ce=L(null),Y=Pe(()=>!t.currentDatabase||t.currentDatabaseId===ue?"请选择数据源再对话":t.currentDatabase.dataId?"Type your message...":"请先录制程序数据然后再对话"),$e=Pe(()=>!t.currentDatabase||!t.currentDatabase.dataId||s.status==="waiting"),Ue=()=>{var x;return t.databases.length===1&&t.currentDatabaseId===ue?"当前无可用的数据源，请使用 Debug with XCodeMap 录制程序数据。":t.currentDatabaseId===ue?"请先选择数据源再对话":(x=t.currentDatabase)!=null&&x.dataId?"你好！我是你的代码排查助手 XCodeMap。请告诉我具体是哪一个函数调用（CallID_Number）让你感到困惑，或者提供相关的类名、函数名等信息，我将尽力为你分析和解释。":"请先录制程序数据然后再对话"},Be=async()=>{var d;const x=l.getText();if(x.trim()){if(!((d=t.currentDatabase)!=null&&d.dataId)){n.setError("当前没有数据，不可聊天。请先选择数据源。或者使用 Debug with XCodeMap 创建新的数据源。");return}if(!a.currentModel){n.setError("请先选择模型再发送消息"),S.value=!0;return}l.clearText(),await e.sendMessage(x)}},Et=()=>{f.value=!f.value},St=async x=>{var P;const d=t.currentDatabase,G=!d||d.id!==x;if(Te=0,t.setCurrentDatabase(x),f.value=!1,G){if(!await yr(x,((P=t.currentDatabase)==null?void 0:P.dataId)||"")){n.setError("Failed to switch process data");return}e.createNewChat(),u.value=null,ne.value=!0,i.value=!0}},Fe=async()=>{var x;if((x=t.currentDatabase)!=null&&x.dataId)try{const d=await In({processId:t.currentDatabase.dataId,first:ne.value,filterText:b.value}),G=JSON.stringify(d),P=JSON.stringify(u.value);G!==P&&(console.log("Data has changed, updating treeData",G),u.value=d),ne.value=!1}catch(d){console.error("Failed to fetch tree data:",d)}},He=async()=>{const x=[...t.databases],d=t.currentDatabase;await t.getDatabase();const G=t.databases,P=t.currentDatabase,v=new Set(x.map(ae=>ae.id)),_e=G.filter(ae=>!v.has(ae.id)),ct=x.length===0||x.length===1&&x[0].id===ze;if(_e.filter(ae=>!ae.id.toLowerCase().includes("local")&&!ae.id.toLowerCase().includes("remote")).length>0&&!ct&&n.setError("发现了新的数据源"),d&&P&&d.id===P.id&&d.dataId!==P.dataId&&!await yr(P.id,P.dataId||"")){n.setError("Failed to switch process data");return}P&&P.id!==ze&&(P.serverSelected?Te=0:(Te++,Te>=3&&(console.log("Database consistency check failed 3 times, resetting to default"),t.setCurrentDatabase(ze),f.value=!0,u.value=null,ne.value=!0,i.value=!1,e.createNewChat(),Te=0)))};Tn(()=>{Fe()});const At=x=>{x.labelKey==="url"&&x.labelValue&&window.open(x.labelValue,"_blank"),console.log("Clicked tree node:",x)},ot=x=>{const d=g.value.entryDisplayConfig.excludedPathPatterns.indexOf(x);d>-1&&(g.value.entryDisplayConfig.excludedPathPatterns.splice(d,1),B())},H=()=>{A.value=!0,Tr(()=>{const x=document.querySelector(".input-new-tag input");x&&x.focus()})},Ge=()=>{E.value&&(g.value.entryDisplayConfig.excludedPathPatterns.push(E.value),B()),A.value=!1,E.value=""},B=async()=>{try{await Mn(g.value)||n.setError("Failed to update filter configuration")}catch(x){console.error("Failed to update filter configuration:",x)}},We=x=>{const d=x.target;d.closest(".model-selector")||(S.value=!1),d.closest(".model-manager-modal")||(C.value=!1)},$=async()=>{S.value=!1;try{await a.getModelConfigData(),C.value=!0}catch(x){console.error("Failed to open model manager:",x),n.setError("Failed to open model manager")}},ve=()=>{C.value=!1},qe=()=>{U.value=!0,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""}},Re=()=>{U.value=!1,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""},V.value=null},je=async()=>{const x={showName:D.value.showName.trim(),baseUrl:D.value.baseUrl.trim(),modelName:D.value.modelName.trim(),apiKey:D.value.apiKey.trim()};if(!x.showName||!x.baseUrl||!x.modelName||!x.apiKey){n.setError("请填写所有必填字段");return}O.value=!0,V.value=null;const d={uuid:crypto.randomUUID(),showName:x.showName,baseUrl:x.baseUrl,modelName:x.modelName,apiKey:x.apiKey},G=await Nn(d);if(!G.success){V.value={success:!1,message:G.error||"模型连通性测试失败"},O.value=!1;return}const P=await On(d);P.success?(Re(),await a.getModelConfigData()):V.value={success:!1,message:P.error||"添加模型失败"},O.value=!1},lt=async x=>{const d=await Pn(x);d.success?await a.getModelConfigData():n.setError(d.error||"删除模型失败")};Br(()=>{c.initTheme(),He(),Fe(),_n().then(x=>{x.success&&x.data&&(g.value.entryDisplayConfig=x.data.entryDisplayConfig)}),a.getModelConfigData(),document.addEventListener("click",We),re=window.setInterval(()=>{He(),Fe()},1e3)}),En(()=>{re!==null&&(clearInterval(re),re=null),document.removeEventListener("click",We),C.value=!1,U.value=!1});const it=(x,d)=>d!=="tool"?x.replace(/`?CallID_(\d+)`?/g,"[CallID_$1](./findFunc?callId=$1)"):x,ke=x=>{I.setOptions({breaks:!0,gfm:!0,pedantic:!0});const d=I.parse(x);return ra.sanitize(d,{ALLOWED_TAGS:["p","br","strong","em","code","pre","blockquote","ul","ol","li","h1","h2","h3","h4","h5","h6","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title","class"]})},De=x=>{const d=x.target;d.style.height="auto",d.style.height=Math.min(d.scrollHeight,200)+"px"},de=()=>{Tr(()=>{Ce.value&&(Ce.value.scrollTop=Ce.value.scrollHeight)})};return Vt(()=>{var x;return(x=e.currentChat)==null?void 0:x.messages},()=>{de()},{deep:!0}),(x,d)=>{var G,P;return w(),T("div",{class:_(["h-screen w-full flex flex-col overflow-x-hidden",(h(c).theme==="dark","bg-[var(--bg-color)]")])},[h(n).error?(w(),T("div",{key:0,class:_(["fixed top-0 left-0 right-0 px-4 py-3 z-[9999] flex items-center justify-between",h(c).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[p("span",na,j(h(n).error.message),1),p("button",{onClick:d[0]||(d[0]=v=>h(n).clearError()),class:_(["ml-4 p-1 rounded-full hover:bg-opacity-20",h(c).theme==="dark"?"hover:bg-red-100":"hover:bg-red-200"])},d[18]||(d[18]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[p("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),2)],2)):F("",!0),p("div",{class:_(["border-b py-0.5 px-0.5 flex items-center justify-between",(h(c).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]")])},[p("div",sa,[p("div",aa,[p("button",{onClick:Et,class:_(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",h(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[p("span",null,j(h(t).currentDatabase?h(t).currentDatabase.name:"请选择数据源"),1),(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":f.value}]),viewBox:"0 0 20 20",fill:"currentColor"},d[19]||(d[19]=[p("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2)])]),p("button",{onClick:d[1]||(d[1]=()=>{h(e).createNewChat(),le.value=!0}),class:_(["p-1 rounded-full",h(c).theme==="dark"?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"]),title:"新建聊天"},d[20]||(d[20]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[p("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)],2),f.value||h(t).currentDatabaseId===h(ue)?(w(),T("div",oa,[h(t).databases.length===1?(w(),T("div",la,d[21]||(d[21]=[p("span",null,"当前无可用的数据源，请使用",-1),p("img",{src:Un,alt:"XCodeMap Logo",class:"w-4 h-4"},null,-1),p("span",null,"Debug with XCodeMap 录制程序数据。",-1)]))):(w(),T("div",ia,[(w(!0),T(me,null,ye(h(t).databases,v=>(w(),T("button",{key:v.id,onClick:_e=>St(v.id),class:_(["w-full px-2 py-0.5 rounded-lg text-xs font-medium text-left border-2 transition-all duration-150 focus:outline-none",[v.id===h(t).currentDatabaseId?h(c).theme==="dark"?"button-selected-dark":"button-selected-light":h(c).theme==="dark"?"button-hover-dark":"button-hover-light"]])},j(v.name),11,ca))),128))]))])):F("",!0),h(t).currentDatabase&&h(t).currentDatabase.active?(w(),T("div",ua,[p("div",da,[p("span",{class:_(["px-1.5 py-0.5 text-xs rounded-full opacity-75",{"bg-green-50 text-green-600":h(t).currentDatabase.recordState==="recording","bg-gray-50 text-gray-500":h(t).currentDatabase.recordState==="idle","bg-yellow-50 text-yellow-600":h(t).currentDatabase.recordState==="paused"}])},j(h(t).currentDatabase.recordState),3),h(t).currentDatabase.recordState==="idle"?(w(),T("button",{key:0,onClick:d[2]||(d[2]=v=>h(t).startRecord(h(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-green-50 text-green-600 hover:bg-green-200 hover:text-green-800 border border-green-200 hover:border-green-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"开始录制"}," 开始录制 ")):F("",!0),h(t).currentDatabase.recordState==="recording"?(w(),T("button",{key:1,onClick:d[3]||(d[3]=v=>h(t).endRecord(h(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-red-50 text-red-600 hover:bg-red-200 hover:text-red-800 border border-red-200 hover:border-red-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"结束录制"}," 结束录制 ")):F("",!0),h(t).currentDatabase.recordState==="paused"?(w(),T("button",{key:2,onClick:d[4]||(d[4]=v=>h(t).restartRecord(h(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-yellow-50 text-yellow-600 hover:bg-yellow-200 hover:text-yellow-800 border border-yellow-200 hover:border-yellow-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"重新录制"}," 重新录制 ")):F("",!0)])])):F("",!0),h(t).currentDatabaseId!==h(ue)?(w(),T("div",{key:3,class:_([(h(c).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]"),"border-b border-t"])},[p("div",pa,[p("div",{onClick:d[5]||(d[5]=v=>i.value=!i.value),class:_(["flex items-center cursor-pointer rounded-lg px-1 py-0.5 border-b border-[var(--border-color)] bg-[var(--undercaret-bg-color)] transition-colors duration-150",(h(c).theme==="dark","hover:bg-[var(--header-hover-bg-color)]")])},[(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-3 w-3 transition-transform duration-200",{"transform rotate-90":i.value,"text-[var(--text-color)]":!0}]),viewBox:"0 0 20 20",fill:"currentColor"},d[22]||(d[22]=[p("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),d[23]||(d[23]=p("span",{class:"text-xs font-medium px-1 text-[var(--text-color)]"},"请求与线程入口",-1))],2)]),i.value?(w(),T("div",{key:0,class:_(["p-1 bg-[var(--undercaret-bg-color)]",(h(c).theme==="dark","border-t border-[var(--border-color)]")])},[p("div",ha,[we(p("input",{"onUpdate:modelValue":d[6]||(d[6]=v=>b.value=v),type:"text",placeholder:"搜索网络请求...",class:_(["w-full px-2 py-0.5 rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",h(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400":"bg-white border-gray-300 text-gray-700"])},null,2),[[Ee,b.value]])]),u.value?(w(),T("div",fa,[Sn(Kn,{nodes:u.value.rootNodes,onNodeClick:At},null,8,["nodes"])])):(w(),T("div",ga,"Loading tree data...")),p("div",ma,[p("div",ba,[p("span",{class:_(["text-xs opacity-50",h(c).theme==="dark"?"text-gray-400":"text-gray-600"])},"排除请求：",2),p("label",{class:_(["flex items-center gap-1 text-xs cursor-pointer opacity-50 hover:opacity-100 transition-opacity",h(c).theme==="dark"?"text-gray-500":"text-gray-400"])},[we(p("input",{type:"checkbox","onUpdate:modelValue":d[7]||(d[7]=v=>g.value.entryDisplayConfig.skipJsCss=v),onChange:B,class:_(["rounded focus:ring-0 focus:ring-offset-0 h-2.5 w-2.5 cursor-pointer",h(c).theme==="dark"?"bg-gray-800 border-gray-600 text-gray-500":"border-gray-300 text-gray-400"])},null,34),[[An,g.value.entryDisplayConfig.skipJsCss]]),d[24]||(d[24]=p("span",{class:"text-[11px]"},"忽略css/js",-1))],2),p("div",xa,[(w(!0),T(me,null,ye(g.value.entryDisplayConfig.excludedPathPatterns,v=>(w(),T("div",{key:v,class:_(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",h(c).theme==="dark"?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"])},[p("span",null,j(v),1),p("button",{onClick:_e=>ot(v),class:_(h(c).theme==="dark"?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700")},d[25]||(d[25]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[p("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,va)],2))),128)),A.value?we((w(),T("input",{key:0,"onUpdate:modelValue":d[8]||(d[8]=v=>E.value=v),class:_(["input-new-tag px-1 py-0 border rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",h(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200":"border-gray-300 text-gray-700"]),placeholder:"输入排除表达式，* 匹配一级路径，** 匹配多级路径",onKeyup:Cn(Ge,["enter"]),onBlur:Ge},null,34)),[[Ee,E.value]]):(w(),T("button",{key:1,onClick:H,class:_(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",h(c).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-blue-400":"bg-gray-100 hover:bg-gray-200 text-blue-700"])},d[26]||(d[26]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[p("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),p("span",null,"New Filter",-1)]),2))])]),p("button",{onClick:d[9]||(d[9]=v=>i.value=!1),class:_(["rounded-full flex items-center",h(c).theme==="dark"?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"]),title:"收起网络请求"},d[27]||(d[27]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[p("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),2)])],2)):F("",!0)],2)):F("",!0),p("div",{ref_key:"messageContainerRef",ref:Ce,class:"flex-1 overflow-y-auto overflow-x-hidden p-0.5 space-y-0.5 w-full border-b border-[var(--border-color)]"},[h(e).currentChat?(w(),T(me,{key:0},[h(e).currentChat.messages.length===0?(w(),T("div",ka,[p("div",wa,[p("p",ya,[(w(),Rn(Qn,{key:(G=h(e).currentChat)==null?void 0:G.id,text:Ue(),speed:20,onComplete:d[10]||(d[10]=v=>le.value=!1)},null,8,["text"]))])])])):F("",!0),(w(!0),T(me,null,ye(h(e).currentChat.messages,v=>(w(),T("div",{key:v.messageId,class:_(["flex items-start gap-1.5 max-w-full",{"justify-end":v.role==="user"}])},[p("div",{class:_(["flex-1 rounded-lg p-2 shadow-sm break-words min-w-0",v.role==="user"?(h(c).theme==="dark","bg-[var(--undercaret-bg-color)]"):(h(c).theme==="dark","bg-[var(--bg-color)]")])},[p("div",{class:_([v.role==="user"?(h(c).theme==="dark","text-[var(--text-color)]"):(h(c).theme==="dark","text-[var(--text-color)]"),"leading-tight text-[15px] markdown-content break-words whitespace-pre-wrap"]),innerHTML:ke(it(v.content,v.role))},null,10,Ta)],2)],2))),128))],64)):(w(),T("div",_a," 请选择数据源后再对话 "))],512),h(s).status==="waiting"&&h(t).currentDatabase&&h(t).currentDatabaseId!==h(ue)?(w(),T("div",{key:4,class:_(["border-t border-b py-1.5 px-3",(h(c).theme==="dark","bg-[var(--undercaret-bg-color)] border-[var(--border-color)] text-[var(--text-color)]")])},[p("div",Ea,[p("span",Sa,j(h(s).statusMessage||"未知状态..."),1)])],2)):F("",!0),p("div",Aa,[p("div",Ca,[we(p("textarea",{"onUpdate:modelValue":d[11]||(d[11]=v=>h(l).message=v),class:"input w-full resize-none mb-0.5",rows:"1",placeholder:Y.value,disabled:$e.value,onInput:De,style:{"min-height":"28px","max-height":"200px","overflow-y":"auto"}},null,40,Ra),[[Ee,h(l).message]]),p("div",Da,[p("div",La,[p("span",{class:_(["text-xs",h(c).theme==="dark"?"text-gray-400":"text-gray-500"])},"模型",2),p("div",Ia,[p("button",{onClick:d[12]||(d[12]=v=>S.value=!S.value),class:_(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",h(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[p("span",null,j(((P=h(a).currentModel)==null?void 0:P.showName)||"请选择模型"),1),(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":S.value}]),viewBox:"0 0 20 20",fill:"currentColor"},d[28]||(d[28]=[p("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2),S.value?(w(),T("div",{key:0,class:_(["absolute bottom-full left-0 mb-2 w-80 border rounded-lg shadow-lg z-50",h(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[p("div",{class:_(["p-2 border-b",h(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[p("span",{class:_(["text-sm font-medium",h(c).theme==="dark"?"text-gray-200":"text-gray-700"])},"选择模型",2)],2),p("div",Ma,[(w(!0),T(me,null,ye(h(a).availableModels,v=>(w(),T("button",{key:v.uuid,onClick:()=>{h(a).setCurrentModel(v.uuid),S.value=!1},class:_(["w-full px-3 py-2 text-left text-sm transition-all duration-150 border-2 focus:outline-none",{"button-selected-dark":h(a).currentModelUuid===v.uuid&&h(c).theme==="dark","button-selected-light":h(a).currentModelUuid===v.uuid&&h(c).theme==="light","button-hover-dark":h(c).theme==="dark"&&h(a).currentModelUuid!==v.uuid,"button-hover-light":h(c).theme==="light"&&h(a).currentModelUuid!==v.uuid}])},[p("div",Oa,[p("span",null,j(v.showName),1),p("span",{class:_(["text-xs opacity-75",[h(a).currentModelUuid===v.uuid?h(c).theme==="dark"?"text-gray-300":"text-blue-500":h(c).theme==="dark"?"text-gray-500":"text-gray-400"]])},"("+j(v.modelName)+")",3)])],10,Na))),128)),h(a).availableModels.length===0?(w(),T("div",{key:0,class:_(["px-3 py-2 text-sm",h(c).theme==="dark"?"text-gray-400":"text-gray-500"])}," 暂无可用模型 ",2)):F("",!0)]),p("div",{class:_(["border-t",h(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[p("button",{onClick:$,class:_(["w-full px-3 py-2 text-left text-sm transition-colors",h(c).theme==="dark"?"text-blue-400 hover:bg-gray-700":"text-blue-600 hover:bg-blue-50"])}," 管理模型 ",2)],2)],2)):F("",!0)])]),p("button",{onClick:Be,class:_(["p-0.5 rounded-full transition-colors",[h(c).theme==="dark"?"hover:bg-gray-700 text-gray-400 hover:text-gray-200":"hover:bg-gray-100 text-gray-600",{"opacity-50 cursor-not-allowed":!h(l).message.trim()||!h(t).currentDatabase||!h(t).currentDatabase.dataId||h(s).status==="waiting"}]]),disabled:!h(l).message.trim()||!h(t).currentDatabase||!h(t).currentDatabase.dataId||h(s).status==="waiting",title:"发送消息"},d[29]||(d[29]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[p("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),10,Pa)])])]),C.value?(w(),T("div",{key:5,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:ve},[p("div",{class:"model-manager-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 max-h-[80vh] flex flex-col",onClick:d[13]||(d[13]=Ln(()=>{},["stop"]))},[p("div",{class:"p-2 border-b border-[var(--border-color)] flex items-center justify-between"},[d[31]||(d[31]=p("h3",{class:"text-base font-medium text-[var(--text-color)]"},"模型管理",-1)),p("button",{onClick:ve,class:"text-gray-400 hover:text-gray-600"},d[30]||(d[30]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),p("div",za,[(w(!0),T(me,null,ye(h(a).availableModels,v=>(w(),T("div",{key:v.uuid,class:"flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-1"},[p("div",$a,[p("div",Ua,[p("span",Ba,j(v.showName),1),p("span",Fa,"("+j(v.modelName)+")",1)]),p("div",Ha,j(v.baseUrl),1)]),p("button",{onClick:_e=>lt(v),class:"ml-2 flex-shrink-0 text-red-500 hover:text-red-700",title:"删除模型"},d[32]||(d[32]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ga)]))),128)),h(a).availableModels.length===0?(w(),T("div",Wa," 暂无已添加的模型 ")):F("",!0)]),p("div",{class:"p-4 border-t border-[var(--border-color)]"},[p("button",{onClick:qe,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"},d[33]||(d[33]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Dn(" 添加新模型 ")]))])])])):F("",!0),U.value?(w(),T("div",qa,[p("div",ja,[p("div",{class:"p-4 border-b border-[var(--border-color)] flex items-center justify-between"},[d[35]||(d[35]=p("h3",{class:"text-lg font-medium text-[var(--text-color)]"},"添加新模型",-1)),p("button",{onClick:Re,class:"text-gray-400 hover:text-gray-600"},d[34]||(d[34]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),p("div",Va,[p("div",null,[d[36]||(d[36]=p("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型显示名称",-1)),we(p("input",{"onUpdate:modelValue":d[14]||(d[14]=v=>D.value.showName=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入模型显示名称"},null,512),[[Ee,D.value.showName]]),d[37]||(d[37]=p("p",{class:"mt-1 text-xs text-gray-500"},"模型需要兼容OpenAI的API，需要支持 FunctionCall。",-1))]),p("div",null,[d[38]||(d[38]=p("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Base URL",-1)),we(p("input",{"onUpdate:modelValue":d[15]||(d[15]=v=>D.value.baseUrl=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: https://api.openai.com/v1"},null,512),[[Ee,D.value.baseUrl]]),d[39]||(d[39]=p("p",{class:"mt-1 text-xs text-gray-500"},"例如: https://dashscope.aliyuncs.com/compatible-mode/v1",-1))]),p("div",null,[d[40]||(d[40]=p("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型名称",-1)),we(p("input",{"onUpdate:modelValue":d[16]||(d[16]=v=>D.value.modelName=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: gpt-3.5-turbo"},null,512),[[Ee,D.value.modelName]]),d[41]||(d[41]=p("p",{class:"mt-1 text-xs text-gray-500"},"国内推荐阿里云qwen-max-latest 或者 deepseek-chat，国外推荐 Claude Sonnet 系列",-1))]),p("div",null,[d[42]||(d[42]=p("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"API Key",-1)),we(p("input",{"onUpdate:modelValue":d[17]||(d[17]=v=>D.value.apiKey=v),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入API Key"},null,512),[[Ee,D.value.apiKey]])]),V.value?(w(),T("div",Ya,[p("div",{class:_(["p-3 rounded-lg",{"bg-green-50 text-green-700":V.value.success,"bg-red-50 text-red-700":!V.value.success}])},[p("div",Za,[V.value.success?(w(),T("svg",Xa,d[43]||(d[43]=[p("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(w(),T("svg",Ka,d[44]||(d[44]=[p("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 00-1.414-1.414L10 10l-1.293-1.293z","clip-rule":"evenodd"},null,-1)]))),p("span",null,j(V.value.message),1)])],2)])):F("",!0)]),p("div",Qa,[p("button",{onClick:Re,class:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),p("button",{onClick:je,class:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",disabled:O.value},[O.value?(w(),T("svg",eo,d[45]||(d[45]=[p("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),p("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):F("",!0),p("span",null,j(O.value?"测试中...":"保存"),1)],8,Ja)])])])):F("",!0)],2)}}}),ro=Ur(to,[["__scopeId","data-v-77f7efe3"]]),sr=zn(ro);sr.use($n());sr.mount("#app");window.$vm=sr._instance;
