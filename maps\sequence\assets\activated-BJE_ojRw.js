import{i as de,u as fe,r as q,v as ge,z as he,X as me,Y as ye,j as N,o as L,l as u,m as U,t as F,F as ve,k as pe,n as be,B as It,Z as we,$ as xe,_ as Ce,S as ke,T as Ee}from"./style-B4DuZL2N.js";import{u as _e}from"./activation-BJZ13vkS.js";function Be(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Q={},ot,St;function Ae(){return St||(St=1,ot=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),ot}var st={},K={},Mt;function J(){if(Mt)return K;Mt=1;let r;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return K.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17},K.getSymbolTotalCodewords=function(n){return o[n]},K.getBCHDigit=function(s){let n=0;for(;s!==0;)n++,s>>>=1;return n},K.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');r=n},K.isKanjiModeEnabled=function(){return typeof r<"u"},K.toSJIS=function(n){return r(n)},K}var it={},Nt;function Rt(){return Nt||(Nt=1,function(r){r.L={bit:1},r.M={bit:0},r.Q={bit:3},r.H={bit:2};function o(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"l":case"low":return r.L;case"m":case"medium":return r.M;case"q":case"quartile":return r.Q;case"h":case"high":return r.H;default:throw new Error("Unknown EC Level: "+s)}}r.isValid=function(n){return n&&typeof n.bit<"u"&&n.bit>=0&&n.bit<4},r.from=function(n,t){if(r.isValid(n))return n;try{return o(n)}catch{return t}}}(it)),it}var at,Lt;function Re(){if(Lt)return at;Lt=1;function r(){this.buffer=[],this.length=0}return r.prototype={get:function(o){const s=Math.floor(o/8);return(this.buffer[s]>>>7-o%8&1)===1},put:function(o,s){for(let n=0;n<s;n++)this.putBit((o>>>s-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const s=Math.floor(this.length/8);this.buffer.length<=s&&this.buffer.push(0),o&&(this.buffer[s]|=128>>>this.length%8),this.length++}},at=r,at}var lt,Dt;function Te(){if(Dt)return lt;Dt=1;function r(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return r.prototype.set=function(o,s,n,t){const e=o*this.size+s;this.data[e]=n,t&&(this.reservedBit[e]=!0)},r.prototype.get=function(o,s){return this.data[o*this.size+s]},r.prototype.xor=function(o,s,n){this.data[o*this.size+s]^=n},r.prototype.isReserved=function(o,s){return this.reservedBit[o*this.size+s]},lt=r,lt}var ut={},Ft;function Pe(){return Ft||(Ft=1,function(r){const o=J().getSymbolSize;r.getRowColCoords=function(n){if(n===1)return[];const t=Math.floor(n/7)+2,e=o(n),i=e===145?26:Math.ceil((e-13)/(2*t-2))*2,l=[e-7];for(let a=1;a<t-1;a++)l[a]=l[a-1]-i;return l.push(6),l.reverse()},r.getPositions=function(n){const t=[],e=r.getRowColCoords(n),i=e.length;for(let l=0;l<i;l++)for(let a=0;a<i;a++)l===0&&a===0||l===0&&a===i-1||l===i-1&&a===0||t.push([e[l],e[a]]);return t}}(ut)),ut}var ct={},qt;function Ie(){if(qt)return ct;qt=1;const r=J().getSymbolSize,o=7;return ct.getPositions=function(n){const t=r(n);return[[0,0],[t-o,0],[0,t-o]]},ct}var dt={},Ut;function Se(){return Ut||(Ut=1,function(r){r.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};r.isValid=function(t){return t!=null&&t!==""&&!isNaN(t)&&t>=0&&t<=7},r.from=function(t){return r.isValid(t)?parseInt(t,10):void 0},r.getPenaltyN1=function(t){const e=t.size;let i=0,l=0,a=0,d=null,h=null;for(let E=0;E<e;E++){l=a=0,d=h=null;for(let p=0;p<e;p++){let f=t.get(E,p);f===d?l++:(l>=5&&(i+=o.N1+(l-5)),d=f,l=1),f=t.get(p,E),f===h?a++:(a>=5&&(i+=o.N1+(a-5)),h=f,a=1)}l>=5&&(i+=o.N1+(l-5)),a>=5&&(i+=o.N1+(a-5))}return i},r.getPenaltyN2=function(t){const e=t.size;let i=0;for(let l=0;l<e-1;l++)for(let a=0;a<e-1;a++){const d=t.get(l,a)+t.get(l,a+1)+t.get(l+1,a)+t.get(l+1,a+1);(d===4||d===0)&&i++}return i*o.N2},r.getPenaltyN3=function(t){const e=t.size;let i=0,l=0,a=0;for(let d=0;d<e;d++){l=a=0;for(let h=0;h<e;h++)l=l<<1&2047|t.get(d,h),h>=10&&(l===1488||l===93)&&i++,a=a<<1&2047|t.get(h,d),h>=10&&(a===1488||a===93)&&i++}return i*o.N3},r.getPenaltyN4=function(t){let e=0;const i=t.data.length;for(let a=0;a<i;a++)e+=t.data[a];return Math.abs(Math.ceil(e*100/i/5)-10)*o.N4};function s(n,t,e){switch(n){case r.Patterns.PATTERN000:return(t+e)%2===0;case r.Patterns.PATTERN001:return t%2===0;case r.Patterns.PATTERN010:return e%3===0;case r.Patterns.PATTERN011:return(t+e)%3===0;case r.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(e/3))%2===0;case r.Patterns.PATTERN101:return t*e%2+t*e%3===0;case r.Patterns.PATTERN110:return(t*e%2+t*e%3)%2===0;case r.Patterns.PATTERN111:return(t*e%3+(t+e)%2)%2===0;default:throw new Error("bad maskPattern:"+n)}}r.applyMask=function(t,e){const i=e.size;for(let l=0;l<i;l++)for(let a=0;a<i;a++)e.isReserved(a,l)||e.xor(a,l,s(t,a,l))},r.getBestMask=function(t,e){const i=Object.keys(r.Patterns).length;let l=0,a=1/0;for(let d=0;d<i;d++){e(d),r.applyMask(d,t);const h=r.getPenaltyN1(t)+r.getPenaltyN2(t)+r.getPenaltyN3(t)+r.getPenaltyN4(t);r.applyMask(d,t),h<a&&(a=h,l=d)}return l}}(dt)),dt}var W={},zt;function ie(){if(zt)return W;zt=1;const r=Rt(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return W.getBlocksCount=function(t,e){switch(e){case r.L:return o[(t-1)*4+0];case r.M:return o[(t-1)*4+1];case r.Q:return o[(t-1)*4+2];case r.H:return o[(t-1)*4+3];default:return}},W.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return s[(t-1)*4+0];case r.M:return s[(t-1)*4+1];case r.Q:return s[(t-1)*4+2];case r.H:return s[(t-1)*4+3];default:return}},W}var ft={},Z={},jt;function Me(){if(jt)return Z;jt=1;const r=new Uint8Array(512),o=new Uint8Array(256);return function(){let n=1;for(let t=0;t<255;t++)r[t]=n,o[n]=t,n<<=1,n&256&&(n^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),Z.log=function(n){if(n<1)throw new Error("log("+n+")");return o[n]},Z.exp=function(n){return r[n]},Z.mul=function(n,t){return n===0||t===0?0:r[o[n]+o[t]]},Z}var Vt;function Ne(){return Vt||(Vt=1,function(r){const o=Me();r.mul=function(n,t){const e=new Uint8Array(n.length+t.length-1);for(let i=0;i<n.length;i++)for(let l=0;l<t.length;l++)e[i+l]^=o.mul(n[i],t[l]);return e},r.mod=function(n,t){let e=new Uint8Array(n);for(;e.length-t.length>=0;){const i=e[0];for(let a=0;a<t.length;a++)e[a]^=o.mul(t[a],i);let l=0;for(;l<e.length&&e[l]===0;)l++;e=e.slice(l)}return e},r.generateECPolynomial=function(n){let t=new Uint8Array([1]);for(let e=0;e<n;e++)t=r.mul(t,new Uint8Array([1,o.exp(e)]));return t}}(ft)),ft}var gt,Ot;function Le(){if(Ot)return gt;Ot=1;const r=Ne();function o(s){this.genPoly=void 0,this.degree=s,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(n){this.degree=n,this.genPoly=r.generateECPolynomial(this.degree)},o.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(n.length+this.degree);t.set(n);const e=r.mod(t,this.genPoly),i=this.degree-e.length;if(i>0){const l=new Uint8Array(this.degree);return l.set(e,i),l}return e},gt=o,gt}var ht={},mt={},yt={},Kt;function ae(){return Kt||(Kt=1,yt.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),yt}var j={},Ht;function le(){if(Ht)return j;Ht=1;const r="[0-9]+",o="[A-Z $%*+\\-./:]+";let s="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";s=s.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+s+`)(?:.|[\r
]))+`;j.KANJI=new RegExp(s,"g"),j.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),j.BYTE=new RegExp(n,"g"),j.NUMERIC=new RegExp(r,"g"),j.ALPHANUMERIC=new RegExp(o,"g");const t=new RegExp("^"+s+"$"),e=new RegExp("^"+r+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return j.testKanji=function(a){return t.test(a)},j.testNumeric=function(a){return e.test(a)},j.testAlphanumeric=function(a){return i.test(a)},j}var Jt;function Y(){return Jt||(Jt=1,function(r){const o=ae(),s=le();r.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},r.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},r.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},r.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},r.MIXED={bit:-1},r.getCharCountIndicator=function(e,i){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!o.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?e.ccBits[0]:i<27?e.ccBits[1]:e.ccBits[2]},r.getBestModeForData=function(e){return s.testNumeric(e)?r.NUMERIC:s.testAlphanumeric(e)?r.ALPHANUMERIC:s.testKanji(e)?r.KANJI:r.BYTE},r.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},r.isValid=function(e){return e&&e.bit&&e.ccBits};function n(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return r.NUMERIC;case"alphanumeric":return r.ALPHANUMERIC;case"kanji":return r.KANJI;case"byte":return r.BYTE;default:throw new Error("Unknown mode: "+t)}}r.from=function(e,i){if(r.isValid(e))return e;try{return n(e)}catch{return i}}}(mt)),mt}var Yt;function De(){return Yt||(Yt=1,function(r){const o=J(),s=ie(),n=Rt(),t=Y(),e=ae(),i=7973,l=o.getBCHDigit(i);function a(p,f,R){for(let I=1;I<=40;I++)if(f<=r.getCapacity(I,R,p))return I}function d(p,f){return t.getCharCountIndicator(p,f)+4}function h(p,f){let R=0;return p.forEach(function(I){const M=d(I.mode,f);R+=M+I.getBitsLength()}),R}function E(p,f){for(let R=1;R<=40;R++)if(h(p,R)<=r.getCapacity(R,f,t.MIXED))return R}r.from=function(f,R){return e.isValid(f)?parseInt(f,10):R},r.getCapacity=function(f,R,I){if(!e.isValid(f))throw new Error("Invalid QR Code version");typeof I>"u"&&(I=t.BYTE);const M=o.getSymbolTotalCodewords(f),B=s.getTotalCodewordsCount(f,R),T=(M-B)*8;if(I===t.MIXED)return T;const _=T-d(I,f);switch(I){case t.NUMERIC:return Math.floor(_/10*3);case t.ALPHANUMERIC:return Math.floor(_/11*2);case t.KANJI:return Math.floor(_/13);case t.BYTE:default:return Math.floor(_/8)}},r.getBestVersionForData=function(f,R){let I;const M=n.from(R,n.M);if(Array.isArray(f)){if(f.length>1)return E(f,M);if(f.length===0)return 1;I=f[0]}else I=f;return a(I.mode,I.getLength(),M)},r.getEncodedBits=function(f){if(!e.isValid(f)||f<7)throw new Error("Invalid QR Code version");let R=f<<12;for(;o.getBCHDigit(R)-l>=0;)R^=i<<o.getBCHDigit(R)-l;return f<<12|R}}(ht)),ht}var vt={},$t;function Fe(){if($t)return vt;$t=1;const r=J(),o=1335,s=21522,n=r.getBCHDigit(o);return vt.getEncodedBits=function(e,i){const l=e.bit<<3|i;let a=l<<10;for(;r.getBCHDigit(a)-n>=0;)a^=o<<r.getBCHDigit(a)-n;return(l<<10|a)^s},vt}var pt={},bt,Gt;function qe(){if(Gt)return bt;Gt=1;const r=Y();function o(s){this.mode=r.NUMERIC,this.data=s.toString()}return o.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(n){let t,e,i;for(t=0;t+3<=this.data.length;t+=3)e=this.data.substr(t,3),i=parseInt(e,10),n.put(i,10);const l=this.data.length-t;l>0&&(e=this.data.substr(t),i=parseInt(e,10),n.put(i,l*3+1))},bt=o,bt}var wt,Qt;function Ue(){if(Qt)return wt;Qt=1;const r=Y(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(n){this.mode=r.ALPHANUMERIC,this.data=n}return s.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let i=o.indexOf(this.data[e])*45;i+=o.indexOf(this.data[e+1]),t.put(i,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},wt=s,wt}var xt,Xt;function ze(){if(Xt)return xt;Xt=1;const r=Y();function o(s){this.mode=r.BYTE,typeof s=="string"?this.data=new TextEncoder().encode(s):this.data=new Uint8Array(s)}return o.getBitsLength=function(n){return n*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(s){for(let n=0,t=this.data.length;n<t;n++)s.put(this.data[n],8)},xt=o,xt}var Ct,Zt;function je(){if(Zt)return Ct;Zt=1;const r=Y(),o=J();function s(n){this.mode=r.KANJI,this.data=n}return s.getBitsLength=function(t){return t*13},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(n){let t;for(t=0;t<this.data.length;t++){let e=o.toSJIS(this.data[t]);if(e>=33088&&e<=40956)e-=33088;else if(e>=57408&&e<=60351)e-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);e=(e>>>8&255)*192+(e&255),n.put(e,13)}},Ct=s,Ct}var kt={exports:{}},Wt;function Ve(){return Wt||(Wt=1,function(r){var o={single_source_shortest_paths:function(s,n,t){var e={},i={};i[n]=0;var l=o.PriorityQueue.make();l.push(n,0);for(var a,d,h,E,p,f,R,I,M;!l.empty();){a=l.pop(),d=a.value,E=a.cost,p=s[d]||{};for(h in p)p.hasOwnProperty(h)&&(f=p[h],R=E+f,I=i[h],M=typeof i[h]>"u",(M||I>R)&&(i[h]=R,l.push(h,R),e[h]=d))}if(typeof t<"u"&&typeof i[t]>"u"){var B=["Could not find a path from ",n," to ",t,"."].join("");throw new Error(B)}return e},extract_shortest_path_from_predecessor_list:function(s,n){for(var t=[],e=n;e;)t.push(e),s[e],e=s[e];return t.reverse(),t},find_path:function(s,n,t){var e=o.single_source_shortest_paths(s,n,t);return o.extract_shortest_path_from_predecessor_list(e,t)},PriorityQueue:{make:function(s){var n=o.PriorityQueue,t={},e;s=s||{};for(e in n)n.hasOwnProperty(e)&&(t[e]=n[e]);return t.queue=[],t.sorter=s.sorter||n.default_sorter,t},default_sorter:function(s,n){return s.cost-n.cost},push:function(s,n){var t={value:s,cost:n};this.queue.push(t),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};r.exports=o}(kt)),kt.exports}var te;function Oe(){return te||(te=1,function(r){const o=Y(),s=qe(),n=Ue(),t=ze(),e=je(),i=le(),l=J(),a=Ve();function d(B){return unescape(encodeURIComponent(B)).length}function h(B,T,_){const b=[];let D;for(;(D=B.exec(_))!==null;)b.push({data:D[0],index:D.index,mode:T,length:D[0].length});return b}function E(B){const T=h(i.NUMERIC,o.NUMERIC,B),_=h(i.ALPHANUMERIC,o.ALPHANUMERIC,B);let b,D;return l.isKanjiModeEnabled()?(b=h(i.BYTE,o.BYTE,B),D=h(i.KANJI,o.KANJI,B)):(b=h(i.BYTE_KANJI,o.BYTE,B),D=[]),T.concat(_,b,D).sort(function(C,k){return C.index-k.index}).map(function(C){return{data:C.data,mode:C.mode,length:C.length}})}function p(B,T){switch(T){case o.NUMERIC:return s.getBitsLength(B);case o.ALPHANUMERIC:return n.getBitsLength(B);case o.KANJI:return e.getBitsLength(B);case o.BYTE:return t.getBitsLength(B)}}function f(B){return B.reduce(function(T,_){const b=T.length-1>=0?T[T.length-1]:null;return b&&b.mode===_.mode?(T[T.length-1].data+=_.data,T):(T.push(_),T)},[])}function R(B){const T=[];for(let _=0;_<B.length;_++){const b=B[_];switch(b.mode){case o.NUMERIC:T.push([b,{data:b.data,mode:o.ALPHANUMERIC,length:b.length},{data:b.data,mode:o.BYTE,length:b.length}]);break;case o.ALPHANUMERIC:T.push([b,{data:b.data,mode:o.BYTE,length:b.length}]);break;case o.KANJI:T.push([b,{data:b.data,mode:o.BYTE,length:d(b.data)}]);break;case o.BYTE:T.push([{data:b.data,mode:o.BYTE,length:d(b.data)}])}}return T}function I(B,T){const _={},b={start:{}};let D=["start"];for(let y=0;y<B.length;y++){const C=B[y],k=[];for(let m=0;m<C.length;m++){const A=C[m],x=""+y+m;k.push(x),_[x]={node:A,lastCount:0},b[x]={};for(let w=0;w<D.length;w++){const g=D[w];_[g]&&_[g].node.mode===A.mode?(b[g][x]=p(_[g].lastCount+A.length,A.mode)-p(_[g].lastCount,A.mode),_[g].lastCount+=A.length):(_[g]&&(_[g].lastCount=A.length),b[g][x]=p(A.length,A.mode)+4+o.getCharCountIndicator(A.mode,T))}}D=k}for(let y=0;y<D.length;y++)b[D[y]].end=0;return{map:b,table:_}}function M(B,T){let _;const b=o.getBestModeForData(B);if(_=o.from(T,b),_!==o.BYTE&&_.bit<b.bit)throw new Error('"'+B+'" cannot be encoded with mode '+o.toString(_)+`.
 Suggested mode is: `+o.toString(b));switch(_===o.KANJI&&!l.isKanjiModeEnabled()&&(_=o.BYTE),_){case o.NUMERIC:return new s(B);case o.ALPHANUMERIC:return new n(B);case o.KANJI:return new e(B);case o.BYTE:return new t(B)}}r.fromArray=function(T){return T.reduce(function(_,b){return typeof b=="string"?_.push(M(b,null)):b.data&&_.push(M(b.data,b.mode)),_},[])},r.fromString=function(T,_){const b=E(T,l.isKanjiModeEnabled()),D=R(b),y=I(D,_),C=a.find_path(y.map,"start","end"),k=[];for(let m=1;m<C.length-1;m++)k.push(y.table[C[m]].node);return r.fromArray(f(k))},r.rawSplit=function(T){return r.fromArray(E(T,l.isKanjiModeEnabled()))}}(pt)),pt}var ee;function Ke(){if(ee)return st;ee=1;const r=J(),o=Rt(),s=Re(),n=Te(),t=Pe(),e=Ie(),i=Se(),l=ie(),a=Le(),d=De(),h=Fe(),E=Y(),p=Oe();function f(y,C){const k=y.size,m=e.getPositions(C);for(let A=0;A<m.length;A++){const x=m[A][0],w=m[A][1];for(let g=-1;g<=7;g++)if(!(x+g<=-1||k<=x+g))for(let P=-1;P<=7;P++)w+P<=-1||k<=w+P||(g>=0&&g<=6&&(P===0||P===6)||P>=0&&P<=6&&(g===0||g===6)||g>=2&&g<=4&&P>=2&&P<=4?y.set(x+g,w+P,!0,!0):y.set(x+g,w+P,!1,!0))}}function R(y){const C=y.size;for(let k=8;k<C-8;k++){const m=k%2===0;y.set(k,6,m,!0),y.set(6,k,m,!0)}}function I(y,C){const k=t.getPositions(C);for(let m=0;m<k.length;m++){const A=k[m][0],x=k[m][1];for(let w=-2;w<=2;w++)for(let g=-2;g<=2;g++)w===-2||w===2||g===-2||g===2||w===0&&g===0?y.set(A+w,x+g,!0,!0):y.set(A+w,x+g,!1,!0)}}function M(y,C){const k=y.size,m=d.getEncodedBits(C);let A,x,w;for(let g=0;g<18;g++)A=Math.floor(g/3),x=g%3+k-8-3,w=(m>>g&1)===1,y.set(A,x,w,!0),y.set(x,A,w,!0)}function B(y,C,k){const m=y.size,A=h.getEncodedBits(C,k);let x,w;for(x=0;x<15;x++)w=(A>>x&1)===1,x<6?y.set(x,8,w,!0):x<8?y.set(x+1,8,w,!0):y.set(m-15+x,8,w,!0),x<8?y.set(8,m-x-1,w,!0):x<9?y.set(8,15-x-1+1,w,!0):y.set(8,15-x-1,w,!0);y.set(m-8,8,1,!0)}function T(y,C){const k=y.size;let m=-1,A=k-1,x=7,w=0;for(let g=k-1;g>0;g-=2)for(g===6&&g--;;){for(let P=0;P<2;P++)if(!y.isReserved(A,g-P)){let z=!1;w<C.length&&(z=(C[w]>>>x&1)===1),y.set(A,g-P,z),x--,x===-1&&(w++,x=7)}if(A+=m,A<0||k<=A){A-=m,m=-m;break}}}function _(y,C,k){const m=new s;k.forEach(function(P){m.put(P.mode.bit,4),m.put(P.getLength(),E.getCharCountIndicator(P.mode,y)),P.write(m)});const A=r.getSymbolTotalCodewords(y),x=l.getTotalCodewordsCount(y,C),w=(A-x)*8;for(m.getLengthInBits()+4<=w&&m.put(0,4);m.getLengthInBits()%8!==0;)m.putBit(0);const g=(w-m.getLengthInBits())/8;for(let P=0;P<g;P++)m.put(P%2?17:236,8);return b(m,y,C)}function b(y,C,k){const m=r.getSymbolTotalCodewords(C),A=l.getTotalCodewordsCount(C,k),x=m-A,w=l.getBlocksCount(C,k),g=m%w,P=w-g,z=Math.floor(m/w),H=Math.floor(x/w),tt=H+1,X=z-H,et=new a(X);let v=0;const c=new Array(w),S=new Array(w);let $=0;const Tt=new Uint8Array(y.buffer);for(let G=0;G<w;G++){const rt=G<P?H:tt;c[G]=Tt.slice(v,v+rt),S[G]=et.encode(c[G]),v+=rt,$=Math.max($,rt)}const nt=new Uint8Array(m);let Pt=0,V,O;for(V=0;V<$;V++)for(O=0;O<w;O++)V<c[O].length&&(nt[Pt++]=c[O][V]);for(V=0;V<X;V++)for(O=0;O<w;O++)nt[Pt++]=S[O][V];return nt}function D(y,C,k,m){let A;if(Array.isArray(y))A=p.fromArray(y);else if(typeof y=="string"){let z=C;if(!z){const H=p.rawSplit(y);z=d.getBestVersionForData(H,k)}A=p.fromString(y,z||40)}else throw new Error("Invalid data");const x=d.getBestVersionForData(A,k);if(!x)throw new Error("The amount of data is too big to be stored in a QR Code");if(!C)C=x;else if(C<x)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+x+`.
`);const w=_(C,k,A),g=r.getSymbolSize(C),P=new n(g);return f(P,C),R(P),I(P,C),B(P,k,0),C>=7&&M(P,C),T(P,w),isNaN(m)&&(m=i.getBestMask(P,B.bind(null,P,k))),i.applyMask(m,P),B(P,k,m),{modules:P,version:C,errorCorrectionLevel:k,maskPattern:m,segments:A}}return st.create=function(C,k){if(typeof C>"u"||C==="")throw new Error("No input text");let m=o.M,A,x;return typeof k<"u"&&(m=o.from(k.errorCorrectionLevel,o.M),A=d.from(k.version),x=i.from(k.maskPattern),k.toSJISFunc&&r.setToSJISFunction(k.toSJISFunc)),D(C,A,m,x)},st}var Et={},_t={},ne;function ue(){return ne||(ne=1,function(r){function o(s){if(typeof s=="number"&&(s=s.toString()),typeof s!="string")throw new Error("Color should be defined as hex string");let n=s.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+s);(n.length===3||n.length===4)&&(n=Array.prototype.concat.apply([],n.map(function(e){return[e,e]}))),n.length===6&&n.push("F","F");const t=parseInt(n.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:t&255,hex:"#"+n.slice(0,6).join("")}}r.getOptions=function(n){n||(n={}),n.color||(n.color={});const t=typeof n.margin>"u"||n.margin===null||n.margin<0?4:n.margin,e=n.width&&n.width>=21?n.width:void 0,i=n.scale||4;return{width:e,scale:e?4:i,margin:t,color:{dark:o(n.color.dark||"#000000ff"),light:o(n.color.light||"#ffffffff")},type:n.type,rendererOpts:n.rendererOpts||{}}},r.getScale=function(n,t){return t.width&&t.width>=n+t.margin*2?t.width/(n+t.margin*2):t.scale},r.getImageWidth=function(n,t){const e=r.getScale(n,t);return Math.floor((n+t.margin*2)*e)},r.qrToImageData=function(n,t,e){const i=t.modules.size,l=t.modules.data,a=r.getScale(i,e),d=Math.floor((i+e.margin*2)*a),h=e.margin*a,E=[e.color.light,e.color.dark];for(let p=0;p<d;p++)for(let f=0;f<d;f++){let R=(p*d+f)*4,I=e.color.light;if(p>=h&&f>=h&&p<d-h&&f<d-h){const M=Math.floor((p-h)/a),B=Math.floor((f-h)/a);I=E[l[M*i+B]?1:0]}n[R++]=I.r,n[R++]=I.g,n[R++]=I.b,n[R]=I.a}}}(_t)),_t}var re;function He(){return re||(re=1,function(r){const o=ue();function s(t,e,i){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=i,e.width=i,e.style.height=i+"px",e.style.width=i+"px"}function n(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}r.render=function(e,i,l){let a=l,d=i;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),i||(d=n()),a=o.getOptions(a);const h=o.getImageWidth(e.modules.size,a),E=d.getContext("2d"),p=E.createImageData(h,h);return o.qrToImageData(p.data,e,a),s(E,d,h),E.putImageData(p,0,0),d},r.renderToDataURL=function(e,i,l){let a=l;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),a||(a={});const d=r.render(e,i,a),h=a.type||"image/png",E=a.rendererOpts||{};return d.toDataURL(h,E.quality)}}(Et)),Et}var Bt={},oe;function Je(){if(oe)return Bt;oe=1;const r=ue();function o(t,e){const i=t.a/255,l=e+'="'+t.hex+'"';return i<1?l+" "+e+'-opacity="'+i.toFixed(2).slice(1)+'"':l}function s(t,e,i){let l=t+e;return typeof i<"u"&&(l+=" "+i),l}function n(t,e,i){let l="",a=0,d=!1,h=0;for(let E=0;E<t.length;E++){const p=Math.floor(E%e),f=Math.floor(E/e);!p&&!d&&(d=!0),t[E]?(h++,E>0&&p>0&&t[E-1]||(l+=d?s("M",p+i,.5+f+i):s("m",a,0),a=0,d=!1),p+1<e&&t[E+1]||(l+=s("h",h),h=0)):a++}return l}return Bt.render=function(e,i,l){const a=r.getOptions(i),d=e.modules.size,h=e.modules.data,E=d+a.margin*2,p=a.color.light.a?"<path "+o(a.color.light,"fill")+' d="M0 0h'+E+"v"+E+'H0z"/>':"",f="<path "+o(a.color.dark,"stroke")+' d="'+n(h,d,a.margin)+'"/>',R='viewBox="0 0 '+E+" "+E+'"',M='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+R+' shape-rendering="crispEdges">'+p+f+`</svg>
`;return typeof l=="function"&&l(null,M),M},Bt}var se;function Ye(){if(se)return Q;se=1;const r=Ae(),o=Ke(),s=He(),n=Je();function t(e,i,l,a,d){const h=[].slice.call(arguments,1),E=h.length,p=typeof h[E-1]=="function";if(!p&&!r())throw new Error("Callback required as last argument");if(p){if(E<2)throw new Error("Too few arguments provided");E===2?(d=l,l=i,i=a=void 0):E===3&&(i.getContext&&typeof d>"u"?(d=a,a=void 0):(d=a,a=l,l=i,i=void 0))}else{if(E<1)throw new Error("Too few arguments provided");return E===1?(l=i,i=a=void 0):E===2&&!i.getContext&&(a=l,l=i,i=void 0),new Promise(function(f,R){try{const I=o.create(l,a);f(e(I,i,a))}catch(I){R(I)}})}try{const f=o.create(l,a);d(null,e(f,i,a))}catch(f){d(f)}}return Q.create=o.create,Q.toCanvas=t.bind(null,s.render),Q.toDataURL=t.bind(null,s.renderToDataURL),Q.toString=t.bind(null,function(e,i,l){return n.render(e,l)}),Q}var $e=Ye();const Ge=Be($e),Qe={class:"h-screen w-full flex flex-col overflow-x-hidden bg-gray-50 dark:bg-gray-900"},Xe={class:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4"},Ze={class:"max-w-4xl mx-auto flex justify-between items-center"},We={class:"flex items-center space-x-4"},tn={key:0,class:"flex items-center space-x-2"},en={key:1,class:"flex items-center space-x-4"},nn={class:"text-sm"},rn={class:"ml-1 font-medium text-gray-900 dark:text-white"},on={class:"text-sm"},sn={class:"ml-1 font-medium text-gray-900 dark:text-white"},an={key:2,class:"text-sm text-gray-500 dark:text-gray-400"},ln={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},un={class:"max-w-4xl mx-auto"},cn={class:"text-center mb-8"},dn={class:"text-lg text-gray-700 dark:text-gray-200"},fn={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},gn={class:"flex justify-between items-center mb-4"},hn={key:0,class:"text-center py-8"},mn={key:1,class:"space-y-4"},yn=["onClick"],vn={class:"flex justify-between items-center"},pn={class:"font-medium text-gray-900 dark:text-white"},bn={class:"text-sm text-gray-500 dark:text-gray-400"},wn={class:"text-right"},xn={class:"text-lg font-semibold text-gray-900 dark:text-white"},Cn={class:"text-xs text-gray-500 dark:text-gray-400"},kn={key:2,class:"text-center py-8"},En={class:"text-center mb-6"},_n=["disabled"],Bn={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},An={class:"space-y-6"},Rn={key:0,class:"text-center py-8"},Tn={key:1,class:"space-y-4"},Pn={class:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600"},In={class:"text-center space-y-2"},Sn={key:0,class:"text-sm"},Mn={key:0,class:"text-green-600 dark:text-green-400"},Nn={key:1,class:"text-red-600 dark:text-red-400"},Ln={key:1,class:"text-xs text-gray-500 dark:text-gray-400"},Dn={key:2,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},Fn={key:0,class:"text-center"},qn=["disabled"],Un={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},zn={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},jn={class:"flex justify-between items-center mb-4"},Vn={class:"space-y-4"},On={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},Kn=["disabled"],Hn={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},Jn=["disabled"],Yn={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},$n={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},Gn={class:"flex justify-between items-center mb-4"},Qn={class:"text-center space-y-4"},Xn={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Zn={class:"font-medium text-gray-900 dark:text-white mb-2"},Wn={class:"text-2xl font-bold text-green-600 dark:text-green-400"},tr={class:"text-sm text-gray-500 dark:text-gray-400"},er={class:"flex justify-center"},nr={key:0,class:"flex items-center space-x-2"},rr={key:1,class:"bg-white p-4 rounded-lg"},or=["src"],sr={class:"text-sm text-gray-600 dark:text-gray-300 space-y-2"},ir={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg"},ar={class:"flex items-center space-x-2"},lr={class:"text-sm text-blue-600 dark:text-blue-400"},ur={class:"flex space-x-2"},cr=["disabled"],At=300,dr=de({__name:"Activated",setup(r){const o=fe(),s=_e(),n=q(""),t=q(""),e=q(!1),i=q(""),l=q(null),a=q(!1),d=q([]),h=q(null),E=q(!1),p=q(!1),f=q(!1),R=q(null),I=q(""),M=q(!1),B=q(!1),T=q(!1),_=q(null),b=q(0);ge(()=>{o.initTheme();const c=new URLSearchParams(window.location.search).get("deviceCode");c?n.value=c:i.value="未找到设备码参数",C(),y()}),he(()=>{w()});const D=async()=>{if(!h.value){i.value="请先选择一个许可证";return}try{e.value=!0,i.value="";const v=await s.generateActivateCode(n.value,h.value.orderId);v&&(t.value=v)}catch(v){i.value=v instanceof Error?v.message:"生成激活码失败",console.error("Failed to generate activate code:",v)}finally{e.value=!1}},y=async()=>{try{p.value=!0;const v=await me();v.success?d.value=v.data||[]:console.error("Failed to load licenses:",v.error)}catch(v){console.error("Failed to load licenses:",v)}finally{p.value=!1}},C=async()=>{try{a.value=!0;const v=await ye();v.success&&v.data?l.value=v.data:console.error("Failed to load user info:",v.error)}catch(v){console.error("Failed to load user info:",v)}finally{a.value=!1}},k=v=>{h.value=v},m=async v=>{try{f.value=!0;const S=await we({licenseType:v,payMethod:"wechat",payPrice:v===1?9900:19900});S.success?(R.value=S.data,E.value=!1,M.value=!0,await A(S.data.wxCodeUrl),x(S.data.orderId)):console.error("Failed to create order:",S.error)}catch(c){console.error("Failed to create order:",c)}finally{f.value=!1}},A=async v=>{try{B.value=!0;const c=await Ge.toDataURL(v,{width:256,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});I.value=c}catch(c){console.error("Failed to generate QR code:",c)}finally{B.value=!1}},x=v=>{if(T.value)return;T.value=!0,b.value=0;const c=async()=>{try{b.value++,console.log(`Polling order status, attempt ${b.value}`),(await xe({orderId:v})).success?(console.log("Order payment successful!"),w(),M.value=!1,await y(),alert("支付成功! 您现在可以使用许可证生成激活码了！")):b.value>=At?(console.log("Max polling count reached, stopping polling"),w(),alert("支付超时，请重新创建订单。")):_.value=setTimeout(c,1e3)}catch(S){console.error("Error polling order status:",S),b.value>=At?(w(),alert("检查订单状态失败，请手动刷新页面。")):_.value=setTimeout(c,1e3)}};c()},w=()=>{_.value&&(clearTimeout(_.value),_.value=null),T.value=!1,b.value=0},g=q(""),P=async()=>{try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(t.value),g.value="复制成功!",console.log("Activate code copied to clipboard");else{const v=document.createElement("textarea");v.value=t.value,v.style.position="fixed",v.style.left="-999999px",v.style.top="-999999px",document.body.appendChild(v),v.focus(),v.select();const c=document.execCommand("copy");document.body.removeChild(v),c?(g.value="复制成功!",console.log("Activate code copied to clipboard")):(g.value="复制失败，请手动复制",console.error("Failed to copy activate code"))}setTimeout(()=>{g.value=""},3e3)}catch(v){g.value="复制失败，请手动复制",console.error("Failed to copy activate code:",v)}},z=()=>{const v=document.querySelector(".activate-code-text");if(v){const c=document.createRange();c.selectNodeContents(v);const S=window.getSelection();S&&(S.removeAllRanges(),S.addRange(c),g.value="文本已选中，请按 Ctrl+C 复制",setTimeout(()=>{g.value=""},3e3))}},H=()=>i.value?"生成激活码失败":e.value?"正在生成激活码...":t.value?"激活码生成成功":"准备生成激活码...",tt=v=>v===1?"一年有效期":"永久有效期",X=v=>(v/100).toFixed(0)+"元",et=v=>new Date(v).toLocaleDateString("zh-CN");return(v,c)=>(L(),N("div",Qe,[u("div",Xe,[u("div",Ze,[u("div",We,[c[10]||(c[10]=u("div",{class:"flex items-center space-x-2"},[u("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})]),u("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"用户信息")],-1)),a.value?(L(),N("div",tn,c[7]||(c[7]=[u("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1),u("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"加载中...",-1)]))):l.value?(L(),N("div",en,[u("div",nn,[c[8]||(c[8]=u("span",{class:"text-gray-500 dark:text-gray-400"},"ID:",-1)),u("span",rn,F(l.value.uniqueId),1)]),u("div",on,[c[9]||(c[9]=u("span",{class:"text-gray-500 dark:text-gray-400"},"昵称:",-1)),u("span",sn,F(l.value.username),1)])])):(L(),N("div",an," 未获取到用户信息 "))]),u("div",{class:"flex items-center space-x-2"},[u("button",{onClick:C,class:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200"}," 刷新 ")])])]),u("div",ln,[u("div",un,[u("div",cn,[c[11]||(c[11]=u("h1",{class:"text-3xl font-bold mb-4 text-gray-900 dark:text-white"}," XCodeMap 激活码生成 ",-1)),u("p",dn,F(H()),1)]),u("div",fn,[u("div",gn,[c[12]||(c[12]=u("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 可用许可证 ",-1)),u("button",{onClick:c[0]||(c[0]=S=>E.value=!0),class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200"}," 购买许可证 ")]),p.value?(L(),N("div",hn,c[13]||(c[13]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在加载许可证...",-1)]))):d.value.length>0?(L(),N("div",mn,[(L(!0),N(ve,null,pe(d.value,S=>{var $;return L(),N("div",{key:S.id,onClick:Tt=>k(S),class:be(["p-4 border rounded-lg cursor-pointer transition-colors duration-200",(($=h.value)==null?void 0:$.id)===S.id?"border-blue-500 bg-blue-50 dark:bg-blue-900":"border-gray-300 dark:border-gray-600 hover:border-blue-300"])},[u("div",vn,[u("div",null,[u("h3",pn,F(tt(S.licenseType)),1),u("p",bn," 过期时间: "+F(et(S.activationExpiredTime)),1)]),u("div",wn,[u("p",xn,F(X(S.payPrice)),1),u("p",Cn," 订单号: "+F(S.orderId),1)])])],10,yn)}),128))])):(L(),N("div",kn,c[14]||(c[14]=[u("p",{class:"text-gray-500 dark:text-gray-400"},"暂无可用许可证",-1)])))]),u("div",En,[u("button",{onClick:D,disabled:e.value||!h.value,class:"px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-md transition-colors duration-200"},F(e.value?"正在生成激活码...":"激活许可证"),9,_n)]),u("div",Bn,[u("div",An,[e.value?(L(),N("div",Rn,c[15]||(c[15]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在生成激活码...",-1)]))):U("",!0),t.value&&!e.value?(L(),N("div",Tn,[c[17]||(c[17]=u("div",{class:"text-center"},[u("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 激活码 "),u("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," 请复制此激活码并发送给用户 ")],-1)),u("div",Pn,[u("code",{class:"activate-code-text text-sm font-mono break-all text-gray-900 dark:text-gray-100 font-medium select-all cursor-pointer",onClick:P,title:"点击复制激活码"},F(t.value),1)]),u("div",In,[u("div",{class:"flex justify-center space-x-2"},[u("button",{onClick:P,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200"}," 复制激活码 "),u("button",{onClick:z,class:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-md transition-colors duration-200"}," 选择文本 ")]),g.value?(L(),N("div",Sn,[g.value==="复制成功!"?(L(),N("span",Mn," ✓ "+F(g.value),1)):(L(),N("span",Nn," ✗ "+F(g.value),1))])):U("",!0),g.value==="复制失败，请手动复制"?(L(),N("div",Ln,c[16]||(c[16]=[u("p",null,"您可以点击上方的激活码文本进行手动复制",-1)]))):U("",!0)]),c[18]||(c[18]=u("div",{class:"text-center"},[u("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 激活码已生成，请将此激活码提供给用户完成激活 ")],-1))])):U("",!0),i.value?(L(),N("div",Dn,F(i.value),1)):U("",!0)])]),i.value?(L(),N("div",Fn,[u("button",{onClick:D,disabled:!h.value,class:"bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"}," 重新生成 ",8,qn)])):U("",!0)])]),E.value?(L(),N("div",Un,[u("div",zn,[u("div",jn,[c[20]||(c[20]=u("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 购买许可证 ",-1)),u("button",{onClick:c[1]||(c[1]=S=>E.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},c[19]||(c[19]=[u("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",Vn,[u("div",On,[c[21]||(c[21]=It('<div class="flex justify-between items-center" data-v-4cba3ce1><div data-v-4cba3ce1><h4 class="font-medium text-gray-900 dark:text-white" data-v-4cba3ce1>一年有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-4cba3ce1>适合短期使用</p></div><div class="text-right" data-v-4cba3ce1><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-4cba3ce1>99元</p></div></div>',1)),u("button",{onClick:c[2]||(c[2]=S=>m(1)),disabled:f.value,class:"w-full mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},F(f.value?"创建订单中...":"立即购买"),9,Kn)]),u("div",Hn,[c[22]||(c[22]=It('<div class="flex justify-between items-center" data-v-4cba3ce1><div data-v-4cba3ce1><h4 class="font-medium text-gray-900 dark:text-white" data-v-4cba3ce1>永久有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-4cba3ce1>一次购买，永久使用</p></div><div class="text-right" data-v-4cba3ce1><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-4cba3ce1>199元</p></div></div>',1)),u("button",{onClick:c[3]||(c[3]=S=>m(2)),disabled:f.value,class:"w-full mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},F(f.value?"创建订单中...":"立即购买"),9,Jn)])])])])):U("",!0),M.value?(L(),N("div",Yn,[u("div",$n,[u("div",Gn,[c[24]||(c[24]=u("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 微信支付 ",-1)),u("button",{onClick:c[4]||(c[4]=S=>M.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},c[23]||(c[23]=[u("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",Qn,[R.value?(L(),N("div",Xn,[u("h4",Zn,F(R.value.licenseType===1?"一年有效期许可证":"永久许可证"),1),u("p",Wn,F(X(R.value.payPrice)),1),u("p",tr," 订单号: "+F(R.value.orderId),1)])):U("",!0),u("div",er,[B.value?(L(),N("div",nr,c[25]||(c[25]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("span",{class:"text-sm text-gray-600 dark:text-gray-300"},"生成二维码中...",-1)]))):I.value?(L(),N("div",rr,[u("img",{src:I.value,alt:"微信支付二维码",class:"w-64 h-64"},null,8,or)])):U("",!0)]),u("div",sr,[c[28]||(c[28]=u("p",null,"请使用微信扫描二维码完成支付",-1)),c[29]||(c[29]=u("p",null,"支付成功后，您可以使用许可证生成激活码",-1)),T.value?(L(),N("div",ir,[u("div",ar,[c[26]||(c[26]=u("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1)),u("span",lr," 正在检查支付状态... ("+F(b.value)+"/"+F(At)+") ",1)]),c[27]||(c[27]=u("p",{class:"text-xs text-blue-500 dark:text-blue-300 mt-1"}," 系统正在自动检查支付状态，请稍候 ",-1))])):U("",!0)]),u("div",ur,[u("button",{onClick:c[5]||(c[5]=S=>M.value=!1),disabled:T.value,class:"flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},F(T.value?"检查中...":"关闭"),9,cr),T.value?U("",!0):(L(),N("button",{key:0,onClick:c[6]||(c[6]=()=>{M.value=!1,y()}),class:"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"}," 刷新许可证 "))])])])])):U("",!0)]))}}),fr=Ce(dr,[["__scopeId","data-v-4cba3ce1"]]),ce=ke(fr);ce.use(Ee());ce.mount("#app");
