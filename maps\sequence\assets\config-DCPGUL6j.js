import{h as $,u as _,i as q,r as u,v as H,z as N,j as a,o as s,m as x,l as e,q as n,n as c,t as P,A as p,T as h,F as M,k as V,B as F,M as R,L as I,y as j,_ as W,R as X,S as G}from"./style-6os41Lhe.js";const J={class:"block sm:inline"},K={class:"flex-1 overflow-y-auto overflow-x-hidden p-2"},Q={class:"max-w-4xl mx-auto space-y-1.5"},Y={class:"flex items-center gap-1.5 mb-1.5"},Z={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},ee={class:"flex flex-col gap-2"},oe={class:"flex items-center gap-1.5 cursor-pointer"},te={class:"flex items-center gap-1.5 cursor-pointer"},re={class:"flex items-center gap-1.5 cursor-pointer"},le={class:"flex gap-3"},ae={class:"flex items-center gap-1.5 cursor-pointer"},se={class:"flex items-center gap-1.5 cursor-pointer"},ne={class:"flex items-center gap-1.5 mb-1.5"},ie={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},de={key:1,class:"flex flex-wrap gap-1"},ue={class:"flex items-center gap-1.5 mb-1.5"},ce={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},ve={class:"flex flex-col gap-1.5"},xe=["onClick"],pe={class:"flex flex-col gap-1.5"},ge={key:1,class:"flex gap-1 mt-1"},fe={class:"flex items-center gap-1.5 mb-1.5"},be={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},me={class:"flex flex-col gap-1.5"},we=["onClick"],he={key:0,class:"flex flex-col gap-1 w-full"},ke={class:"flex gap-1"},ye=$({__name:"Config",setup(Pe){const g=_(),i=q(),L=u(!0),t=u({startOnDebug:!1,recordMode:"smart",enableAutoDetect:!0,autoDetectedPackages:[],includedPackagePrefixes:[],includedParentClasses:[]}),k=u(null),f=u({0:!1,1:!1,2:!1,3:!1}),w=u(!1),b=u(""),y=u(!1),m=u(""),C=r=>{f.value[r]=!f.value[r]},S=r=>{t.value.includedPackagePrefixes&&(t.value.includedPackagePrefixes.splice(r,1),d())},T=r=>{t.value.includedParentClasses&&(t.value.includedParentClasses.splice(r,1),d())},d=async()=>{try{const r=await R(t.value);if(!r.success){console.error("Failed to update filter configuration:",r.error),g.setError(r.error||"Failed to update filter configuration");return}await z()}catch(r){console.error("Failed to update filter configuration:",r),g.setError("Failed to update filter configuration")}},A=()=>{w.value=!0,I(()=>{const r=document.querySelector(".input-new-package-prefix input");r&&r.focus()})},U=()=>{b.value&&(t.value.includedPackagePrefixes||(t.value.includedPackagePrefixes=[]),t.value.includedPackagePrefixes.push(b.value),d()),w.value=!1,b.value=""},E=()=>{y.value=!0,I(()=>{const r=document.querySelector(".input-new-parent-class input");r&&r.focus()})},O=()=>{m.value&&(t.value.includedParentClasses||(t.value.includedParentClasses=[]),t.value.includedParentClasses.push({type:m.value}),d()),y.value=!1,m.value=""},z=async()=>{try{const r=await j();r.success&&r.data?t.value=r.data:g.setError(r.error||"Failed to get configuration")}catch(r){console.error("Failed to get configuration:",r),g.setError("Failed to get configuration")}};return H(async()=>{i.initTheme(),await z(),L.value=!1,k.value=window.setInterval(()=>{z()},3e4)}),N(()=>{k.value&&(clearInterval(k.value),k.value=null)}),(r,o)=>{var D;return s(),a("div",{class:c(["h-screen min-w-full w-screen flex flex-col overflow-x-hidden",(n(i).theme==="dark","bg-[var(--bg-color)]")])},[(D=n(g))!=null&&D.error?(s(),a("div",{key:0,class:c(["fixed top-2 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300",n(i).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[e("span",J,P(n(g).error.message),1)],2)):x("",!0),e("div",{class:c(["border-b py-2 px-4 shadow-sm",(n(i).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]")])},o[13]||(o[13]=[e("div",{class:"flex items-center"},[e("span",{class:"text-lg font-semibold text-[var(--text-color)]"},"录制配置")],-1)]),2),e("div",K,[e("div",Q,[e("div",{class:c(["rounded p-3 shadow-sm",(n(i).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Y,[o[15]||(o[15]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制模式",-1)),e("button",{onClick:o[0]||(o[0]=l=>C("0")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[14]||(o[14]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[0]?(s(),a("p",Z," 智能模式，针对 SpringBoot 或者 Tomcat，只录制 HTTP/DB 等请求。 全录模式，默认录制所有链路。手工模式，默认不录制，需手工开启录制。 ")):x("",!0),e("div",ee,[e("label",oe,[p(e("input",{type:"radio","onUpdate:modelValue":o[1]||(o[1]=l=>t.value.recordMode=l),value:"smart",onChange:d,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.recordMode]]),o[16]||(o[16]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"智能模式，跳过Web应用的启动阶段",-1))]),e("label",te,[p(e("input",{type:"radio","onUpdate:modelValue":o[2]||(o[2]=l=>t.value.recordMode=l),value:"all",onChange:d,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.recordMode]]),o[17]||(o[17]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"全录模式，包括应用启动过程",-1))]),e("label",re,[p(e("input",{type:"radio","onUpdate:modelValue":o[3]||(o[3]=l=>t.value.recordMode=l),value:"manual",onChange:d,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.recordMode]]),o[18]||(o[18]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"手工模式，需手工开启录制",-1))])])],2),e("div",{class:c(["rounded p-3 shadow-sm",(n(i).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[o[21]||(o[21]=e("h3",{class:"text-sm font-semibold mb-1.5 text-[var(--text-color)]"},"是否默认随着 Debug 启动",-1)),e("div",le,[e("label",ae,[p(e("input",{type:"radio","onUpdate:modelValue":o[4]||(o[4]=l=>t.value.startOnDebug=l),value:!0,onChange:d,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.startOnDebug]]),o[19]||(o[19]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"是",-1))]),e("label",se,[p(e("input",{type:"radio","onUpdate:modelValue":o[5]||(o[5]=l=>t.value.startOnDebug=l),value:!1,onChange:d,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[h,t.value.startOnDebug]]),o[20]||(o[20]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"否",-1))])])],2),e("div",{class:c(["rounded p-3 shadow-sm",(n(i).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",ne,[o[23]||(o[23]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制项目包",-1)),e("button",{onClick:o[6]||(o[6]=l=>C("1")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[22]||(o[22]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[1]?(s(),a("p",ie," XCodeMap 默认检测并录制项目包。如果要录制依赖包，请在后面手工添加。如果发现项目包遗漏，也可在后面添加。 ")):x("",!0),t.value.enableAutoDetect?(s(),a("div",de,[(s(!0),a(M,null,V(t.value.autoDetectedPackages,(l,v)=>(s(),a("div",{key:v,class:"px-1.5 py-0.5 rounded-full text-xs font-medium bg-[var(--bg-color)] text-[var(--text-color)]"},P(l),1))),128))])):x("",!0)],2),e("div",{class:c(["rounded p-3 shadow-sm",(n(i).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",ue,[o[25]||(o[25]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制依赖包",-1)),e("button",{onClick:o[7]||(o[7]=l=>C("2")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[24]||(o[24]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[2]?(s(),a("p",ce," 配置包的前缀即可。例如配置 org.spring，将录制 spring 模块的调用数据。注意，如果包范围内的类处于循环或者递归中，则只采集部分调用。 ")):x("",!0),e("div",ve,[(s(!0),a(M,null,V(t.value.includedPackagePrefixes||[],(l,v)=>(s(),a("div",{key:v,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,P(l),1),e("button",{onClick:()=>{S(v)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},o[26]||(o[26]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,xe)]))),128)),e("div",pe,[w.value?p((s(),a("input",{key:0,"onUpdate:modelValue":o[8]||(o[8]=l=>b.value=l),class:"input-new-package-prefix px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写包前缀，例如 org.spring"},null,512)),[[F,b.value]]):x("",!0),w.value?(s(),a("div",ge,[e("button",{onClick:U,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:o[9]||(o[9]=()=>{w.value=!1,b.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])):(s(),a("button",{key:2,onClick:A,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},o[27]||(o[27]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加包前缀",-1)])))])])],2),e("div",{class:c(["rounded p-3 shadow-sm",(n(i).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",fe,[o[29]||(o[29]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制重点类或接口",-1)),e("button",{onClick:o[10]||(o[10]=l=>C("3")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},o[28]||(o[28]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[3]?(s(),a("p",be," 配置类（接口）的完整名称。例如配置 org.springframework.beans.factory.BeanFactory，将录制该类（接口）本身及其子类的调用。 这些类即使处于循环或者递归中，也会录制每个函数调用。 ")):x("",!0),e("div",me,[(s(!0),a(M,null,V(t.value.includedParentClasses,(l,v)=>(s(),a("div",{key:v,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,P(l.type),1),e("button",{onClick:()=>{T(v)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},o[30]||(o[30]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,we)]))),128)),y.value?(s(),a("div",he,[p(e("input",{"onUpdate:modelValue":o[11]||(o[11]=l=>m.value=l),class:"input-new-parent-class px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写类完整名称，如 org.springframework.beans.factory.BeanFactory"},null,512),[[F,m.value]]),e("div",ke,[e("button",{onClick:O,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:o[12]||(o[12]=()=>{y.value=!1,m.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("button",{key:1,onClick:E,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},o[31]||(o[31]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加类",-1)])))])],2)])])],2)}}}),Ce=W(ye,[["__scopeId","data-v-d041a67e"]]),B=X(Ce);B.use(G());B.mount("#app");window.$vm=B._instance;
