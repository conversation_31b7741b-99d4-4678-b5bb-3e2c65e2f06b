<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>XCodeMap Sequence Diagram</title>
  <script src="../assets/axios-1.6.3/axios.min.js"></script>
  <!-- 导入 Mermaid 库 -->
  <script src="../assets/mermaid-10.9.0/dist/mermaid.min.js"></script>
  <script src="../assets/vue-2.5.22/vue.min.js"></script>
  <script src="../assets/element-ui-2.15.14/index.min.js"></script>
  <script src="../assets/lodash-4.17.15/core.min.js"></script>


  <!-- 设置 Mermaid 图表渲染 -->
  <!--<script>
    // 初始化 Mermaid
    mermaid.initialize({ startOnLoad: true });
  </script>-->
</head>
<body>
<div id="app">
  <!-- 在 body 中添加 Mermaid 流程图 -->
  <div id="diagram">
    {{diagramInfo.data}}
  </div>
</div>

<script>
  function NewMermaidDiagram(elId) {
    const obj = {
      elId: elId
    }
    obj.render = async function (diagramText) {
      const diagramElement = document.getElementById(obj.elId)
      const {svg, bindFunctions} = await mermaid.render(obj.elId + 'Detail', diagramText);
      diagramElement.innerHTML = svg
      //console.log("refresh graph", svg)
      if (bindFunctions) {
        bindFunctions(diagramElement);
      }
    }
    return obj;
  }
  function NewPageInfo() {
    return {
      sequenceId:"0",
      doInitPageInfo: function () {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has("sequenceId") ) {
          this.sequenceId = urlParams.get("sequenceId")
        } else {
          console.log("No sequence id in searchParas", window.location.search);
        }
      }
    }
  }
  function NewDiagramInfo() {
    const obj = {
      data: "graph TD;\n" +
              "    A[方案选择] --> B(开发);\n" +
              "    A --> C(测试);\n" +
              "    B --> D{是否完成?};\n" +
              "    C --> D;\n" +
              "    D -->|是| E[发布];\n" +
              "    D -->|否| B;\n" +
              "    E --> F(结束);"
    }
    obj.doGetDiagramInfo = function (callback) {
      let pathName = window.location.pathname;
      const urlParams = new URLSearchParams(window.location.search);
      if (!pathName.endsWith(".html")) {
        console.log("Unexpected pathName", pathName)
        return
      }
      pathName = pathName.replace(".html", "");
      pathName = pathName + '?'  + urlParams;
      console.log("Try to get path:", pathName)
      // 获取用户
      axios.get(pathName).then(res => {
        const errorCode = res.data.errorCode;
        if (errorCode === '200') {
          obj.data = res.data.data;
        } else {
          console.log("Get diagram info failed", res.data)
        }
      }).catch(err => {
        console.log(err)
      }).finally(() => {
        callback();
      })
    }
    return obj;
  }
</script>
<script>
  function initMermaid() {
    console.log("Start to init the mermaid")
    //mermaid.initialize({ startOnLoad: true });
  }
</script>
<script>
  const vue = new Vue({
    el: '#app',
    data: function () {
      return {
        pageInfo: NewPageInfo(),
        diagramInfo: NewDiagramInfo(),
        mermaidDiagram: NewMermaidDiagram("diagram")
      }
    },
    created() {
      console.log("The vue object", this)
      this.pageInfo.doInitPageInfo();
      const diagramInfoRef = this.diagramInfo;
      const mermaidDiagramRef = this.mermaidDiagram;
      this.diagramInfo.doGetDiagramInfo(() => {
        mermaidDiagramRef.render(diagramInfoRef.data)
      })
    },
    destroyed() {
    },
    methods: {}
  });
</script>

</body>
</html>
