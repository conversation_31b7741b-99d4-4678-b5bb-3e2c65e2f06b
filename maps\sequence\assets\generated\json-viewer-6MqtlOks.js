const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./jsoneditor.min-CWWzlBzm.js","./_commonjsHelpers-CqkleIqs.js"])))=>i.map(i=>d[i]);
import{i as j,u as R,r as x,w as J,x as A,j as p,o as h,m as O,l as s,q as b,n as w,t as N,a4 as F,_ as z,R as q,S as B}from"./style-Nwn58W2R.js";import{u as T}from"./error-BStZaeDB.js";const V="modulepreload",L=function(S,c){return new URL(S,c).href},I={},U=function(c,d,g){let n=Promise.resolve();if(d&&d.length>0){let a=function(l){return Promise.all(l.map(f=>Promise.resolve(f).then(m=>({status:"fulfilled",value:m}),m=>({status:"rejected",reason:m}))))};const i=document.getElementsByTagName("link"),v=document.querySelector("meta[property=csp-nonce]"),y=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));n=a(d.map(l=>{if(l=L(l,g),l in I)return;I[l]=!0;const f=l.endsWith(".css"),m=f?'[rel="stylesheet"]':"";if(!!g)for(let o=i.length-1;o>=0;o--){const r=i[o];if(r.href===l&&(!f||r.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${m}`))return;const e=document.createElement("link");if(e.rel=f?"stylesheet":V,f||(e.as="script"),e.crossOrigin="",e.href=l,y&&e.setAttribute("nonce",y),document.head.appendChild(e),f)return new Promise((o,r)=>{e.addEventListener("load",o),e.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${l}`)))})}))}function t(a){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=a,window.dispatchEvent(i),!i.defaultPrevented)throw a}return n.then(a=>{for(const i of a||[])i.status==="rejected"&&t(i.reason);return c().catch(t)})},D=["data-theme"],M={class:"block sm:inline"},K={class:"flex items-center"},$={class:"text-lg font-semibold text-[var(--text-color)]"},W={key:0,class:"absolute top-16 left-1/2 transform -translate-x-1/2 z-50"},G={key:1,class:"flex-1 flex items-center justify-center"},H={key:2,class:"flex-1 p-2"},Q={key:0,class:"mt-4 p-4 border rounded bg-[var(--bg-color)] border-[var(--border-color)]"},X={class:"text-sm text-[var(--text-color)] overflow-auto max-h-96"},Y=j({__name:"JsonViewer",setup(S){const c=T(),d=R(),g=x(!0),n=x(""),t=x(null),a=x(),i=x(!1),v=()=>{const e=new URLSearchParams(window.location.search),o=e.get("processId"),r=e.get("labelKey"),u=e.get("callId"),k=e.get("maxVersion"),E=e.get("uniqId"),P=e.get("depth");return!o||!k||!E?(c.setError("Missing required parameters: processId, maxVersion, and uniqId"),null):{processId:o,labelKey:r||void 0,callId:u?parseInt(u):0,maxVersion:parseInt(k),uniqId:parseInt(E),depth:P?parseInt(P):1}},y=async()=>{const e=v();if(e)try{g.value=!0,console.log("Loading JSON data with params:",e);const o=await F(e);console.log("API response:",o),o.success&&o.data?(n.value=o.data,console.log("JSON data loaded:",n.value)):(console.error("API error:",o.error),c.setError(o.error||"Failed to load JSON data"))}catch(o){console.error("Failed to load JSON data:",o),c.setError("Failed to load JSON data")}finally{g.value=!1}},l=()=>{if(console.log("Initializing JSON editor..."),console.log("Container ref:",a.value),console.log("JSON data:",n.value),!a.value||!n.value){console.error("Missing container ref or JSON data"),setTimeout(()=>{a.value&&n.value&&(console.log("Retrying JSON editor initialization..."),l())},200);return}try{U(async()=>{const{default:e}=await import("./jsoneditor.min-CWWzlBzm.js").then(o=>o.j);return{default:e}},__vite__mapDeps([0,1]),import.meta.url).then(({default:e})=>{console.log("JSONEditor imported successfully");const o={mode:"view",modes:["view","tree","form","code","text"],search:!0,navigationBar:!0,statusBar:!0,colorPicker:!1,colorPickerOptions:{left:0,top:0},readOnly:!0,readonly:!0,theme:d.theme==="dark"?"ace/theme/monokai":"ace/theme/chrome",onError:r=>{console.error("JSONEditor error:",r)},onModeChange:(r,u)=>{console.log("Mode changed from",u,"to",r)}};if(a.value){console.log("Creating JSONEditor instance..."),t.value=new e(a.value,o);try{console.log("Parsing JSON data...");const r=JSON.parse(n.value);console.log("Parsed JSON:",r),t.value.set(r),t.value.expandAll(),t.value.setReadOnly&&t.value.setReadOnly(!0),t.value.setMode&&t.value.setMode("view"),console.log("JSON editor initialized successfully")}catch(r){console.error("Failed to parse JSON:",r),t.value.setMode("text"),t.value.setText(n.value),console.log("Set JSON editor to text mode with raw data")}}}).catch(e=>{console.error("Failed to load JSONEditor:",e),c.setError("Failed to load JSON editor")})}catch(e){console.error("Failed to initialize JSON editor:",e),c.setError("Failed to initialize JSON editor")}},f=async()=>{if(t.value)try{const e=t.value.get(),o=JSON.stringify(e,null,2);await navigator.clipboard.writeText(o),i.value=!0,setTimeout(()=>{i.value=!1},2e3),console.log("JSON copied to clipboard")}catch(e){console.error("Failed to copy JSON:",e);try{const o=t.value.get(),u=`clipboard://${JSON.stringify(o,null,2)}`;console.log("Using clipboard protocol:",u),window.open(u,"_blank")}catch(o){console.error("Failed to use fallback copy method:",o),c.setError("Failed to copy JSON to clipboard")}}},m=()=>{t.value&&t.value.expandAll()},_=()=>{t.value&&t.value.collapseAll()};return J(a,e=>{console.log("Container ref changed:",e),e&&n.value&&!t.value&&(console.log("Container ref is now available, initializing editor..."),setTimeout(()=>{l()},100))},{immediate:!0}),J(n,e=>{console.log("JSON data changed:",e?"has data":"no data"),e&&a.value&&!t.value&&(console.log("JSON data is now available, initializing editor..."),setTimeout(()=>{l()},100))},{immediate:!0}),J(()=>d.theme,e=>{if(console.log("Theme changed to:",e),t.value){const o=a.value;o&&(o.style.setProperty("--bg-color",e==="dark"?"#1f2937":"#ffffff"),o.style.setProperty("--text-color",e==="dark"?"#f9fafb":"#111827"),o.style.setProperty("--border-color",e==="dark"?"#374151":"#d1d5db"))}},{immediate:!0}),A(()=>{d.initTheme(),console.log("Component mounted, container ref:",a.value),y()}),(e,o)=>{var r,u;return h(),p("div",{class:w(["h-screen min-w-full w-screen flex flex-col overflow-x-hidden",(b(d).theme==="dark","bg-[var(--bg-color)]")]),"data-theme":b(d).theme},[(r=b(c))!=null&&r.error?(h(),p("div",{key:0,class:w(["fixed top-2 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300",b(d).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[s("span",M,N(b(c).error.message),1)],2)):O("",!0),s("div",{class:w(["border-b py-2 px-4 shadow-sm flex items-center justify-between",(b(d).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]")])},[s("div",K,[s("span",$,N(((u=v())==null?void 0:u.labelKey)||"JSON Viewer"),1)]),s("div",{class:"flex items-center gap-2"},[s("button",{onClick:m,class:"px-3 py-1 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)] border border-[var(--border-color)]"}," 展开全部 "),s("button",{onClick:_,class:"px-3 py-1 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)] border border-[var(--border-color)]"}," 折叠全部 "),s("button",{onClick:f,class:"px-3 py-1 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 复制JSON ")]),i.value?(h(),p("div",W,o[0]||(o[0]=[s("div",{class:"px-4 py-2 rounded shadow-lg bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300"},[s("p",{class:"text-sm font-medium"}," ✓ JSON已复制到剪贴板 ")],-1)]))):O("",!0)],2),g.value?(h(),p("div",G,o[1]||(o[1]=[s("div",{class:"text-center"},[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--text-color)] mx-auto mb-2"}),s("p",{class:"text-sm text-[var(--text-color-secondary)]"},"加载中...")],-1)]))):(h(),p("div",H,[s("div",{ref_key:"containerRef",ref:a,class:"w-full h-full min-h-[400px]"},null,512),n.value&&!t.value?(h(),p("div",Q,[o[2]||(o[2]=s("h3",{class:"text-lg font-semibold mb-2 text-[var(--text-color)]"},"JSON 数据",-1)),s("pre",X,N(n.value),1)])):O("",!0)]))],10,D)}}}),Z=z(Y,[["__scopeId","data-v-8523abba"]]),C=q(Z);C.use(B());C.mount("#app");
