<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>XCodeMap - 运行时源码可视化工具</title>
  <link rel="icon" type="image/svg+xml" href="./assets/generated/index-favicon-BwQOAmn5.svg" />

  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .gradient-bg-alt {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .glass-effect {
      background: rgba(255, 255, 255, 0.1);
      -webkit-backdrop-filter: blur(10px);
              backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .card-hover {
      transition: all 0.3s ease;
    }
    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .feature-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      color: white;
      font-size: 1.5rem;
    }
  </style>
  <link rel="stylesheet" crossorigin href="./assets/generated/index-n5D6QAW9.css">
</head>
<body class="bg-gray-50 text-gray-800 font-sans">
  <!-- Hero Section -->
  <section class="gradient-bg text-white py-20 px-6 relative overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-10"></div>
    <div class="max-w-6xl mx-auto text-center relative z-10">
      <div class="animate-fade-in">
        <h1 class="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent">
          XCodeMap 1.17 重磅发布
        </h1>
        <p class="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed">
          运行时源码可视化工具，无需断点，随时查看每个函数的输入输出与堆栈
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <a href="./user/license.html" class="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold px-8 py-4 rounded-full shadow-lg transform hover:scale-105 transition-all duration-300 animate-bounce-slow">
            🚀 限时入手 ¥69 / 年 →
          </a>
          <a href="#features" class="bg-white text-gray-800 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 shadow-lg border-2 border-white">
            了解更多
          </a>
        </div>
      </div>
    </div>
    <!-- Floating elements -->
    <div class="absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse-slow"></div>
    <div class="absolute bottom-20 right-20 w-32 h-32 bg-white bg-opacity-5 rounded-full animate-pulse-slow"></div>
  </section>

  <!-- 产品亮点 -->
  <section id="features" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent">
          为什么选择 XCodeMap？
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          专为 Java 开发者打造的调试神器，让代码调试变得简单高效
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover border border-gray-100 animate-slide-up">
          <div class="feature-icon">🎬</div>
          <h3 class="text-xl font-bold mb-4 text-gray-800">可视化回放</h3>
          <p class="text-gray-600 leading-relaxed">重播请求现场，像看电影一样调试程序逻辑，让调试过程变得直观有趣。</p>
        </div>
        
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover border border-gray-100 animate-slide-up">
          <div class="feature-icon">⚡</div>
          <h3 class="text-xl font-bold mb-4 text-gray-800">无需断点</h3>
          <p class="text-gray-600 leading-relaxed">不用提前猜测位置，录一次请求即可完整分析调用链，大大提升调试效率。</p>
        </div>
        
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover border border-gray-100 animate-slide-up">
          <div class="feature-icon">🔍</div>
          <h3 class="text-xl font-bold mb-4 text-gray-800">输入输出全还原</h3>
          <p class="text-gray-600 leading-relaxed">查看每个函数的入参出参、抛出异常、执行 SQL，完整还原执行现场。</p>
        </div>
        
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover border border-gray-100 animate-slide-up">
          <div class="feature-icon">🌐</div>
          <h3 class="text-xl font-bold mb-4 text-gray-800">远程支持</h3>
          <p class="text-gray-600 leading-relaxed">无需安装 agent，即可录制远程服务程序，支持分布式系统调试。</p>
        </div>
        
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover border border-gray-100 animate-slide-up">
          <div class="feature-icon">🔥</div>
          <h3 class="text-xl font-bold mb-4 text-gray-800">热部署兼容</h3>
          <p class="text-gray-600 leading-relaxed">完美兼容 JRebel、HotSwap，适配主流开发环境，不影响开发流程。</p>
        </div>
        
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover border border-gray-100 animate-slide-up">
          <div class="feature-icon">🔒</div>
          <h3 class="text-xl font-bold mb-4 text-gray-800">本地私有数据</h3>
          <p class="text-gray-600 leading-relaxed">所有数据本地保存，无需担心隐私泄漏，确保代码安全。</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 使用流程 -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent">使用步骤</h2>
        <p class="text-xl text-gray-600">简单两步，开启高效调试之旅</p>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover animate-slide-up">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">1</div>
            <h3 class="text-2xl font-bold text-gray-800">🎬 录制</h3>
          </div>
          <ol class="space-y-4 text-lg text-gray-700">
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
              <span>点击 "Debug with XCodeMap"</span>
            </li>
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
              <span>开始录制</span>
            </li>
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
              <span>触发请求</span>
            </li>
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
              <span>关闭录制</span>
            </li>
          </ol>
        </div>
        
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover animate-slide-up">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">2</div>
            <h3 class="text-2xl font-bold text-gray-800">🎞️ 回放</h3>
          </div>
          <ol class="space-y-4 text-lg text-gray-700">
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
              <span>点击请求记录或行号图标</span>
            </li>
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
              <span>鼠标悬停在函数上，查看函数入参、出参</span>
            </li>
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
              <span> StepInto / StepOut/ StepOver / StepBack</span>
            </li>
            <li class="flex items-start">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
              <span> 单击 / 双击 序列图与源码联动</span>
            </li>
          </ol>
        </div>
      </div>
    </div>
  </section>

  <!-- 视频与社区 -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent">
          📺 视频教程 & 技术支持
        </h2>
        <p class="text-xl text-gray-600 mb-8">不懂就看，不会就问！</p>
        <div class="bg-white p-8 rounded-2xl shadow-lg card-hover">
          <a href="https://space.bilibili.com/3546573562710135" target="_blank" class="inline-block bg-gradient-to-r from-blue-600 to-cyan-500 hover:from-blue-700 hover:to-cyan-600 text-white px-8 py-4 rounded-full font-bold text-lg transform hover:scale-105 transition-all duration-300">
            🎥 前往 B 站观看教程
          </a>
          <p class="mt-6 text-gray-600">B 站私信「XCodeMap 总工」，获取一对一技术支持</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 定价 -->
  <section id="pricing" class="py-20 bg-white">
    <div class="max-w-6xl mx-auto px-4 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent">
          💰 限时优惠
        </h2>
        <p class="text-xl text-gray-600 mb-12">选择最适合您的方案，开启高效调试之旅</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <div class="bg-gradient-to-br from-blue-50 to-cyan-50 border-2 border-blue-200 rounded-2xl p-8 shadow-lg card-hover relative overflow-hidden">
            <div class="absolute top-0 right-0 bg-blue-500 text-white px-4 py-1 text-sm font-bold rounded-bl-lg">
              推荐
            </div>
            <h3 class="text-2xl font-bold mb-4 text-gray-800">年费版</h3>
            <div class="mb-6">
              <span class="text-4xl font-bold text-blue-600">¥69</span>
              <span class="text-gray-600">/ 年</span>
            </div>
            <p class="line-through text-gray-500 mb-4">原价 ¥119</p>
            <p class="text-gray-600 mb-6">适合持续更新、年度订阅用户</p>
            <ul class="text-left text-gray-600 mb-8 space-y-2">
              <li class="flex items-center">
                <span class="text-blue-500 mr-2">✓</span>
                全年免费更新
              </li>
              <li class="flex items-center">
                <span class="text-blue-500 mr-2">✓</span>
                优先技术支持
              </li>
              <li class="flex items-center">
                <span class="text-blue-500 mr-2">✓</span>
                新功能优先体验
              </li>
            </ul>
          </div>
          
          <div class="bg-gradient-to-br from-cyan-50 to-blue-50 border-2 border-cyan-200 rounded-2xl p-8 shadow-lg card-hover relative overflow-hidden">
            <div class="absolute top-0 right-0 bg-cyan-500 text-white px-4 py-1 text-sm font-bold rounded-bl-lg">
              超值
            </div>
            <h3 class="text-2xl font-bold mb-4 text-gray-800">永久版</h3>
            <div class="mb-6">
              <span class="text-4xl font-bold text-cyan-600">¥169</span>
              <span class="text-gray-600">一次买断</span>
            </div>
            <p class="line-through text-gray-500 mb-4">原价 ¥299</p>
            <p class="text-gray-600 mb-6">适合长期使用者，支持永久免费更新</p>
            <ul class="text-left text-gray-600 mb-8 space-y-2">
              <li class="flex items-center">
                <span class="text-cyan-500 mr-2">✓</span>
                终身免费更新
              </li>
              <li class="flex items-center">
                <span class="text-cyan-500 mr-2">✓</span>
                专属技术支持
              </li>
              <li class="flex items-center">
                <span class="text-cyan-500 mr-2">✓</span>
                所有功能无限制
              </li>
            </ul>
          </div>
        </div>
        
        <a href="./user/license.html" class="mt-8 inline-block bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-10 py-4 rounded-full font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-lg">
          🚀 立即购买 →
        </a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-4xl mx-auto px-4 text-center">
      <div class="mb-6">
        <h3 class="text-2xl font-bold mb-4">XCodeMap</h3>
        <p class="text-gray-400">专为 Java 开发者打造的调试神器</p>
      </div>
      <div class="border-t border-gray-800 pt-6">
        <p class="text-gray-400">© 2025 XCodeMap.tech - Made with ❤️ for Java Developers</p>
        <p class="text-sm text-gray-500 mt-2">有问题请私信 B 站：XCodeMap 总工</p>
      </div>
    </div>
  </footer>


</body>
</html>

