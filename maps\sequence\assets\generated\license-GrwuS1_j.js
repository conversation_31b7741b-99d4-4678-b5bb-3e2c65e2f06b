import{i as $,u as Q,r as o,x as V,z as X,W as q,a3 as J,j as r,o as l,l as e,m as f,t as d,B as P,F as G,k as H,n as j,Y as W,Z as Y,$ as Z,_ as K,R as ee,S as te}from"./style-BxxvIZDh.js";import{Q as se}from"./browser-BtZzEF0O.js";const ae={class:"h-screen w-full flex flex-col overflow-x-hidden bg-gray-50 dark:bg-gray-900"},re={class:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4"},le={class:"max-w-6xl mx-auto flex justify-between items-center"},oe={class:"flex items-center space-x-4"},de={key:0,class:"flex items-center space-x-2"},ne={key:1,class:"flex items-center space-x-4"},ie={class:"text-sm"},ce={class:"ml-1 font-medium text-gray-900 dark:text-white"},ue={class:"text-sm"},fe={class:"ml-1 font-medium text-gray-900 dark:text-white"},xe={key:2,class:"text-sm text-gray-500 dark:text-gray-400"},ve={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},me={class:"max-w-6xl mx-auto"},ge={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},be={class:"ml-11"},pe={class:"bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900 dark:to-red-900 border border-orange-200 dark:border-orange-700 rounded-lg p-4 mb-4"},ye={class:"flex items-start space-x-3"},ke={class:"flex-1"},he={class:"flex items-center space-x-2"},we=["disabled"],_e=["disabled"],Ce=["disabled"],Le={key:0,class:"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg"},je={class:"flex items-center space-x-2"},Me={class:"text-sm text-red-700 dark:text-red-300"},Ie={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},Fe={class:"ml-11"},Pe={key:0,class:"text-center py-8"},Ne={key:1,class:"space-y-4"},ze={class:"flex items-center justify-between"},Se={class:"flex items-center space-x-4"},Be={class:"flex-shrink-0"},Ae={class:"w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center"},De=["d"],Oe={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Ee={class:"font-medium text-gray-900 dark:text-white"},Re={class:"text-sm text-gray-500 dark:text-gray-400 space-y-1"},Ue={class:"flex items-center space-x-2"},$e={key:0,class:"flex items-center space-x-1 text-red-600 dark:text-red-400"},Qe={key:1,class:"flex items-center space-x-1 text-yellow-600 dark:text-yellow-400"},Ve={key:2,class:"flex items-center space-x-1 text-gray-500 dark:text-gray-400"},Xe={key:2,class:"text-center py-8"},qe={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Je={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},Ge={class:"flex justify-between items-center mb-4"},He={class:"text-center space-y-4"},We={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Ye={class:"font-medium text-gray-900 dark:text-white mb-2"},Ze={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ke={class:"text-sm text-gray-500 dark:text-gray-400"},et={class:"flex justify-center"},tt={key:0,class:"flex items-center space-x-2"},st={key:1,class:"bg-white p-4 rounded-lg"},at=["src"],rt={class:"text-sm text-gray-600 dark:text-gray-300 space-y-2"},lt={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg"},N=300,ot=$({__name:"License",setup(nt){const S=Q(),m=o(null),w=o(!1),_=o([]),B=o(!1),i=o(null),C=o(""),g=o(!1),L=o(!1),b=o(!1),p=o(!1),y=o(!1),x=o(null),c=o(0),k=o(!1),v=o(""),M=o(null);V(()=>{S.initTheme();const t=new URLSearchParams(window.location.search).get("utype");t!==null&&(M.value=parseInt(t)),h()}),X(()=>{u()});const h=async()=>{try{w.value=!0;const[s,t]=await Promise.all([q(),J()]);s.success&&s.data?m.value=s.data:console.error("Failed to load user info:",s.error),t.success?_.value=t.data||[]:console.error("Failed to load licenses:",t.error)}catch(s){console.error("Failed to load user info:",s)}finally{w.value=!1}},I=async s=>{s===1?b.value=!0:s===2&&(p.value=!0);try{const a=await W({licenseType:s,payMethod:"wechat",payPrice:s===0?0:s===1?6900:16900});a.success?(u(),i.value=a.data,g.value=!0,await A(a.data.wxCodeUrl),D(a.data.orderId)):console.error("Failed to create order:",a.error)}catch(t){console.error("Failed to create order:",t)}finally{s===1?b.value=!1:s===2&&(p.value=!1)}},A=async s=>{try{L.value=!0;const t=await se.toDataURL(s,{width:256,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});C.value=t}catch(t){console.error("Failed to generate QR code:",t)}finally{L.value=!1}},D=s=>{if(y.value)return;y.value=!0,c.value=0;const t=async()=>{try{c.value++,console.log(`Polling order status, attempt ${c.value}`),(await Z({orderId:s})).success?(console.log("Order payment successful!"),u(),g.value=!1,await h(),alert("支付成功! 您的许可证已添加到账户中！")):c.value>=N?(console.log("Max polling count reached, stopping polling"),u(),alert("支付超时，请重新创建订单。")):x.value=setTimeout(t,1e3)}catch(a){console.error("Error polling order status:",a),c.value>=N?(u(),alert("检查订单状态失败，请手动刷新页面。")):x.value=setTimeout(t,1e3)}};t()},u=()=>{x.value&&(clearTimeout(x.value),x.value=null),y.value=!1,c.value=0},O=async()=>{try{k.value=!0,v.value="";const s=await Y();s.success?(await h(),alert("试用许可证申请成功！")):v.value=s.error||"申请试用许可证失败"}catch(s){v.value=s instanceof Error?s.message:"申请试用许可证失败",console.error("Failed to apply trial license:",s)}finally{k.value=!1}},T=s=>s===0?"试用许可证":s===1?"一年有效期":s===2?"永久有效期":"未知类型",F=s=>s===0?"免费":(s/100).toFixed(0)+"元",E=s=>new Date(s).toLocaleDateString("zh-CN"),n=s=>{const t=s.activationNum||0,a=s.maxActivationNum||1;return t>=a?{text:"已用完",class:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",description:`已激活 ${t}/${a} 台设备`}:t>0?{text:"部分激活",class:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",description:`已激活 ${t}/${a} 台设备`}:{text:"未激活",class:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",description:`可激活 ${a} 台设备`}},R=s=>{const t=s.activationNum||0,a=s.maxActivationNum||1;return t>=a?"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z":t>0?"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z":"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},U=s=>{const t=s.activationNum||0,a=s.maxActivationNum||1;return t>=a?"border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900":t>0?"border-yellow-300 dark:border-yellow-600 bg-yellow-50 dark:bg-yellow-900":"border-gray-300 dark:border-gray-600 hover:border-blue-300"};return(s,t)=>(l(),r("div",ae,[e("div",re,[e("div",le,[e("div",oe,[t[6]||(t[6]=e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})]),e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"用户信息")],-1)),w.value?(l(),r("div",de,t[3]||(t[3]=[e("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1),e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"加载中...",-1)]))):m.value?(l(),r("div",ne,[e("div",ie,[t[4]||(t[4]=e("span",{class:"text-gray-500 dark:text-gray-400"},"ID:",-1)),e("span",ce,d(m.value.uniqueId),1)]),e("div",ue,[t[5]||(t[5]=e("span",{class:"text-gray-500 dark:text-gray-400"},"昵称:",-1)),e("span",fe,d(m.value.username),1)])])):(l(),r("div",xe," 未获取到用户信息 "))]),e("div",{class:"flex items-center space-x-2"},[e("button",{onClick:h,class:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200"}," 刷新 ")])])]),e("div",ve,[e("div",me,[t[18]||(t[18]=e("div",{class:"text-center mb-8"},[e("h1",{class:"text-3xl font-bold mb-4 text-gray-900 dark:text-white"}," XCodeMap 许可证管理 "),e("p",{class:"text-gray-600 dark:text-gray-400"}," 管理您的许可证，查看激活状态，购买新的许可证 ")],-1)),e("div",ge,[t[11]||(t[11]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 💝 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 购买新许可证 ")],-1)),e("div",be,[e("div",pe,[e("div",ye,[t[9]||(t[9]=e("div",{class:"flex-shrink-0 mt-1"},[e("svg",{class:"w-5 h-5 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",ke,[t[7]||(t[7]=e("h4",{class:"font-medium text-orange-800 dark:text-orange-200 mb-2"}," 🚨 项目需要您的支持！ ",-1)),t[8]||(t[8]=e("p",{class:"text-sm text-orange-700 dark:text-orange-300 mb-3 leading-relaxed"}," 再不收费，项目就要黄了(╥﹏╥)，请购买许可证，以支持 XCodeMap 的持续发展，为开发者创造颠覆性的源码调试体验。 ",-1)),e("div",he,[e("button",{onClick:t[0]||(t[0]=a=>I(1)),disabled:b.value,class:"px-6 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-md transition-colors duration-200 shadow-sm"},d(b.value?"创建订单中...":"💝 限时 69 元购买一年许可证"),9,we),e("button",{onClick:t[1]||(t[1]=a=>I(2)),disabled:p.value,class:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-md transition-colors duration-200 shadow-sm"},d(p.value?"创建订单中...":"💝 限时 169 元购买永久许可证"),9,_e),M.value===0?(l(),r("button",{key:0,onClick:O,disabled:k.value,class:"px-4 py-2 bg-gray-800 hover:bg-gray-900 disabled:bg-gray-400 text-white text-xs font-medium rounded-md transition-colors duration-200 shadow-sm"},d(k.value?"申请中...":"申请试用许可证"),9,Ce)):f("",!0)])])])]),v.value?(l(),r("div",Le,[e("div",je,[t[10]||(t[10]=e("svg",{class:"w-4 h-4 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("span",Me,d(v.value),1)])])):f("",!0)])]),e("div",Ie,[t[17]||(t[17]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-green-600 text-white flex items-center justify-center font-bold mr-3"}," 📋 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 我的许可证 ")],-1)),e("div",Fe,[B.value?(l(),r("div",Pe,t[12]||(t[12]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在加载许可证...",-1)]))):_.value.length>0?(l(),r("div",Ne,[(l(!0),r(G,null,H(_.value,a=>(l(),r("div",{key:a.id,class:j(["p-4 border rounded-lg transition-colors duration-200",U(a)])},[e("div",ze,[e("div",Se,[e("div",Be,[e("div",Ae,[(l(),r("svg",{class:j(["w-5 h-5",n(a).text==="已用完"?"text-red-600":n(a).text==="部分激活"?"text-yellow-600":"text-gray-400"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:R(a)},null,8,De)],2))])]),e("div",Oe,[e("div",Te,[e("h3",Ee,d(T(a.licenseType)),1),e("span",{class:j(["px-2 py-1 text-xs font-medium rounded-full",n(a).class])},d(n(a).text),3)]),e("div",Re,[e("p",null,d(n(a).description),1),e("p",null,"过期时间: "+d(E(a.activationExpiredTime)),1),e("p",null,"订单号: "+d(a.orderId),1),e("p",null,"价格: "+d(F(a.payPrice)),1)])])]),e("div",Ue,[n(a).text==="已用完"?(l(),r("div",$e,t[13]||(t[13]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("span",{class:"text-sm font-medium"},"已用完",-1)]))):n(a).text==="部分激活"?(l(),r("div",Qe,t[14]||(t[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1),e("span",{class:"text-sm font-medium"},"部分激活",-1)]))):(l(),r("div",Ve,t[15]||(t[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("span",{class:"text-sm"},"未激活",-1)])))])])],2))),128))])):(l(),r("div",Xe,t[16]||(t[16]=[P('<div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center" data-v-e5f5aff8><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-e5f5aff8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-e5f5aff8></path></svg></div><p class="text-gray-500 dark:text-gray-400 mb-2" data-v-e5f5aff8>暂无许可证</p><p class="text-sm text-gray-400 dark:text-gray-500" data-v-e5f5aff8>购买许可证后，它们将显示在这里</p>',3)])))])]),t[19]||(t[19]=P('<div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6" data-v-e5f5aff8><div class="flex items-center mb-4" data-v-e5f5aff8><div class="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3" data-v-e5f5aff8> 📖 </div><h3 class="text-xl font-semibold text-gray-900 dark:text-white" data-v-e5f5aff8> 安装指南 </h3></div><div class="ml-11" data-v-e5f5aff8><div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-6 mb-6" data-v-e5f5aff8><h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-4 text-lg" data-v-e5f5aff8>请按照以下步骤安装 XCodeMap</h4><div class="space-y-4" data-v-e5f5aff8><div class="flex items-start" data-v-e5f5aff8><div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5" data-v-e5f5aff8>1</div><div data-v-e5f5aff8><p class="text-blue-800 dark:text-blue-200 font-semibold" data-v-e5f5aff8>打开 IntelliJ IDEA</p><p class="text-blue-700 dark:text-blue-300 text-sm" data-v-e5f5aff8>确保您使用的是 IntelliJ IDEA</p></div></div><div class="flex items-start" data-v-e5f5aff8><div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5" data-v-e5f5aff8>2</div><div data-v-e5f5aff8><p class="text-blue-800 dark:text-blue-200 font-semibold" data-v-e5f5aff8>进入插件市场</p><p class="text-blue-700 dark:text-blue-300 text-sm" data-v-e5f5aff8>File → Settings → Plugins → Marketplace</p></div></div><div class="flex items-start" data-v-e5f5aff8><div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5" data-v-e5f5aff8>3</div><div data-v-e5f5aff8><p class="text-blue-800 dark:text-blue-200 font-semibold" data-v-e5f5aff8>搜索插件</p><p class="text-blue-700 dark:text-blue-300 text-sm" data-v-e5f5aff8>在搜索框中输入 <span class="bg-blue-200 dark:bg-blue-800 px-2 py-1 rounded font-mono text-blue-800 dark:text-blue-200" data-v-e5f5aff8>xcodemap</span></p></div></div><div class="flex items-start" data-v-e5f5aff8><div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5" data-v-e5f5aff8>4</div><div data-v-e5f5aff8><p class="text-blue-800 dark:text-blue-200 font-semibold" data-v-e5f5aff8>安装并重启</p><p class="text-blue-700 dark:text-blue-300 text-sm" data-v-e5f5aff8>点击 Install，重启 IDEA 即可使用</p></div></div></div><div class="mt-6 p-4 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg" data-v-e5f5aff8><p class="text-green-800 dark:text-green-200 text-sm" data-v-e5f5aff8><span class="font-semibold" data-v-e5f5aff8>💡 提示：</span>安装完成后，按照插件主页的使用步骤使用起来，使用过程中会触发激活流程，按照流程，打开设备专属激活链接，选择许可证，生成激活码，然后输入激活码即可。 </p></div><div class="flex gap-3 mt-6" data-v-e5f5aff8><a href="https://plugins.jetbrains.com/plugin/24648-xcodemap" target="_blank" class="flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold py-3 px-4 rounded-lg text-center transition-all duration-300" data-v-e5f5aff8> 前往插件市场 </a></div></div></div></div>',1))])]),g.value?(l(),r("div",qe,[e("div",Je,[e("div",Ge,[t[21]||(t[21]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 微信支付 ",-1)),e("button",{onClick:t[2]||(t[2]=()=>{g.value=!1,u()}),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[20]||(t[20]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",He,[i.value?(l(),r("div",We,[e("h4",Ye,d(i.value.licenseType===0?"试用许可证":i.value.licenseType===1?"一年有效期许可证":"永久许可证"),1),e("p",Ze,d(F(i.value.payPrice)),1),e("p",Ke," 订单号: "+d(i.value.orderId),1)])):f("",!0),e("div",et,[L.value?(l(),r("div",tt,t[22]||(t[22]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"text-sm text-gray-600 dark:text-gray-300"},"生成二维码中...",-1)]))):C.value?(l(),r("div",st,[e("img",{src:C.value,alt:"微信支付二维码",class:"w-64 h-64"},null,8,at)])):f("",!0)]),e("div",rt,[t[24]||(t[24]=e("p",null,"请使用微信扫描二维码完成支付",-1)),t[25]||(t[25]=e("p",null,"支付成功后，您的许可证将自动添加到账户中",-1)),y.value?(l(),r("div",lt,t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e("span",{class:"text-sm text-blue-600 dark:text-blue-400"}," 正在等待支付完成...请尽快完成支付 ")],-1)]))):f("",!0)])])])])):f("",!0)]))}}),dt=K(ot,[["__scopeId","data-v-e5f5aff8"]]),z=ee(dt);z.use(te());z.mount("#app");
