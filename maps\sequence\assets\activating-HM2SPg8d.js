import{i as D,u as U,r as d,v as I,z as L,j as n,o as i,l as e,n as h,q as s,t as l,m as u,C as P,D as E,_ as M,S as F,T as R}from"./style-ZGiRjKX1.js";import{u as $}from"./activation-DCKqwZPe.js";const j={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},q={class:"max-w-2xl mx-auto"},z={class:"text-center mb-8"},B={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},V={class:"space-y-6"},X={key:0,class:"space-y-4"},N={class:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600"},O={class:"flex items-center justify-between"},W={class:"text-sm font-mono break-all flex-1 mr-3 text-gray-900 dark:text-gray-100 font-medium"},G={key:0,class:"text-center"},H={class:"text-sm text-blue-600 dark:text-blue-400 font-medium"},J={key:1,class:"space-y-4"},K={class:"space-y-4"},Q=["disabled"],Y=["disabled"],Z={key:2,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},ee={key:3,class:"bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg"},te={key:0,class:"text-sm mt-2"},oe=D({__name:"Activating",setup(se){const p=U(),o=$(),c=d(""),r=d(""),x=d(!1),m=d(3),b=d(!1),v=d(null);let g=null;I(()=>{p.initTheme(),w(),k()}),L(()=>{g&&clearInterval(g)});const w=()=>{const a=new URLSearchParams(window.location.search).get("deviceCode");a&&(v.value=a,console.log("Device code found in URL parameters:",a))},k=async()=>{try{if(v.value)o.setDeviceCode(v.value),o.setStep("device_code_generated"),r.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(v.value)}`,x.value=!0,f();else{await o.generateDeviceCode();const t=o.getDeviceCode();t&&(r.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(t)}`,x.value=!0,f())}}catch(t){console.error("Failed to start activation process:",t)}},f=()=>{b.value=!0,m.value=3,g=window.setInterval(()=>{m.value--,m.value<=0&&(clearInterval(g),g=null,b.value=!1,_())},1e3)},C=async()=>{try{await navigator.clipboard.writeText(r.value),console.log("Activation link copied to clipboard")}catch(t){console.error("Failed to copy activation link:",t)}},_=()=>{r.value&&window.open(r.value,"_blank")},S=async()=>{if(c.value.trim())try{if(v.value){const t=`clipboard://xcm_fin_ack://${c.value.trim()}`;console.log("Using clipboard protocol:",t),window.open(t,"_blank"),o.setStep("activated")}else{const t=await o.finishActivationProcess(c.value.trim());if(console.log("Activation completed successfully, expired timestamp:",t),t){const a=new Date(t);console.log("License expires at:",a.toLocaleString())}}}catch(t){console.error("Failed to finish activation:",t)}},A=()=>{switch(o.getCurrentStep()){case"idle":return"准备开始激活流程...";case"generating_device_code":return"正在生成设备码...";case"device_code_generated":return"设备码生成成功，请获取激活码";case"finishing_activation":return"正在完成激活...";case"activated":return"激活成功！";case"error":return`激活失败: ${o.getError()}`;default:return"未知状态"}};return(t,a)=>(i(),n("div",{class:h(["h-screen w-full flex flex-col overflow-x-hidden",(s(p).theme==="dark","bg-[var(--bg-color)]")])},[e("div",j,[e("div",q,[e("div",z,[e("h1",{class:h(["text-3xl font-bold mb-4",s(p).theme==="dark"?"text-white":"text-gray-900"])}," XCodeMap 激活 ",2),e("p",{class:h(["text-lg",s(p).theme==="dark"?"text-gray-300":"text-gray-600"])},l(A()),3)]),e("div",B,[e("div",V,[r.value?(i(),n("div",X,[a[1]||(a[1]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 专属激活链接 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," 请访问此专属链接以获取激活码 ")],-1)),e("div",N,[e("div",O,[e("code",W,l(r.value),1)])]),e("div",{class:"flex justify-center space-x-4"},[e("button",{onClick:_,class:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-200"}," 访问链接 "),e("button",{onClick:C,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"}," 复制链接 ")]),b.value?(i(),n("div",G,[e("p",H,l(m.value)+"秒后将自动访问专属链接 ",1)])):u("",!0),a[2]||(a[2]=e("div",{class:"text-center"},[e("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 如果无法直接访问，请复制链接到能联网访问的浏览器打开 ")],-1))])):u("",!0),x.value?(i(),n("div",J,[a[3]||(a[3]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 输入激活码 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请输入从XCodeMap官网 https://xcodemap.tech 获得的激活码 ")],-1)),e("div",K,[P(e("textarea",{"onUpdate:modelValue":a[0]||(a[0]=T=>c.value=T),placeholder:"请输入激活码",rows:"4",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none",disabled:s(o).getCurrentStep()==="finishing_activation"},null,8,Q),[[E,c.value]]),e("button",{onClick:S,disabled:!c.value.trim()||s(o).getCurrentStep()==="finishing_activation",class:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-lg"},l(s(o).getCurrentStep()==="finishing_activation"?"激活中...":"完成激活"),9,Y)])])):u("",!0),s(o).hasError()?(i(),n("div",Z,l(s(o).getError()),1)):u("",!0),s(o).isActivated()?(i(),n("div",ee,[a[4]||(a[4]=e("div",{class:"font-medium"},"激活成功！您现在可以使用 XCodeMap 了。",-1)),s(o).getExpiredTimestamp()?(i(),n("div",te," 许可证过期时间："+l(new Date(s(o).getExpiredTimestamp()).toLocaleString()),1)):u("",!0)])):u("",!0)])])])])],2))}}),ae=M(oe,[["__scopeId","data-v-06837cc8"]]),y=F(ae);y.use(R());y.mount("#app");
