import{i as A,u as T,r as g,v as D,z as I,j as r,o as i,l as e,n as m,q as s,t as d,m as c,C as E,D as L,_ as M,S as j,T as z}from"./style-ZGiRjKX1.js";import{u as B}from"./activation-C-gBTruV.js";const F={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},P={class:"max-w-2xl mx-auto"},U={class:"text-center mb-8"},V={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},X={class:"space-y-6"},q={key:0,class:"space-y-4"},N={class:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600"},$={class:"flex items-center justify-between"},O={class:"text-sm font-mono break-all flex-1 mr-3 text-gray-900 dark:text-gray-100 font-medium"},R={key:0,class:"text-center"},W={class:"text-sm text-blue-600 dark:text-blue-400 font-medium"},G={key:1,class:"space-y-4"},H={class:"space-y-4"},J=["disabled"],K=["disabled"],Q={key:2,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},Y={key:3,class:"bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg"},Z={key:0,class:"text-sm mt-2"},ee=A({__name:"Activating",setup(oe){const v=T(),t=B(),l=g(""),n=g(""),b=g(!1),p=g(3),x=g(!1);let u=null;D(()=>{v.initTheme(),y()}),I(()=>{u&&clearInterval(u)});const y=async()=>{try{await t.generateDeviceCode();const o=t.getDeviceCode();o&&(n.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(o)}`,b.value=!0,_())}catch(o){console.error("Failed to start activation process:",o)}},_=()=>{x.value=!0,p.value=3,u=window.setInterval(()=>{p.value--,p.value<=0&&(clearInterval(u),u=null,x.value=!1,h())},1e3)},w=async()=>{try{await navigator.clipboard.writeText(n.value),console.log("Activation link copied to clipboard")}catch(o){console.error("Failed to copy activation link:",o)}},h=()=>{n.value&&window.open(n.value,"_blank")},k=async()=>{if(l.value.trim())try{const o=await t.finishActivationProcess(l.value.trim());if(console.log("Activation completed successfully, expired timestamp:",o),o){const a=new Date(o);console.log("License expires at:",a.toLocaleString())}}catch(o){console.error("Failed to finish activation:",o)}},C=()=>{switch(t.getCurrentStep()){case"idle":return"准备开始激活流程...";case"generating_device_code":return"正在生成设备码...";case"device_code_generated":return"设备码生成成功，请获取激活码";case"finishing_activation":return"正在完成激活...";case"activated":return"激活成功！";case"error":return`激活失败: ${t.getError()}`;default:return"未知状态"}};return(o,a)=>(i(),r("div",{class:m(["h-screen w-full flex flex-col overflow-x-hidden",(s(v).theme==="dark","bg-[var(--bg-color)]")])},[e("div",F,[e("div",P,[e("div",U,[e("h1",{class:m(["text-3xl font-bold mb-4",s(v).theme==="dark"?"text-white":"text-gray-900"])}," XCodeMap 激活 ",2),e("p",{class:m(["text-lg",s(v).theme==="dark"?"text-gray-300":"text-gray-600"])},d(C()),3)]),e("div",V,[e("div",X,[n.value?(i(),r("div",q,[a[1]||(a[1]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 专属激活链接 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," 请访问此专属链接以获取激活码 ")],-1)),e("div",N,[e("div",$,[e("code",O,d(n.value),1)])]),e("div",{class:"flex justify-center space-x-4"},[e("button",{onClick:h,class:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-200"}," 访问链接 "),e("button",{onClick:w,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"}," 复制链接 ")]),x.value?(i(),r("div",R,[e("p",W,d(p.value)+"秒后将自动访问专属链接 ",1)])):c("",!0),a[2]||(a[2]=e("div",{class:"text-center"},[e("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 如果无法直接访问，请复制链接到能联网访问的浏览器打开 ")],-1))])):c("",!0),b.value?(i(),r("div",G,[a[3]||(a[3]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 输入激活码 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请输入从XCodeMap官网 https://xcodemap.tech 获得的激活码 ")],-1)),e("div",H,[E(e("textarea",{"onUpdate:modelValue":a[0]||(a[0]=S=>l.value=S),placeholder:"请输入激活码",rows:"4",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none",disabled:s(t).getCurrentStep()==="finishing_activation"},null,8,J),[[L,l.value]]),e("button",{onClick:k,disabled:!l.value.trim()||s(t).getCurrentStep()==="finishing_activation",class:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-lg"},d(s(t).getCurrentStep()==="finishing_activation"?"激活中...":"完成激活"),9,K)])])):c("",!0),s(t).hasError()?(i(),r("div",Q,d(s(t).getError()),1)):c("",!0),s(t).isActivated()?(i(),r("div",Y,[a[4]||(a[4]=e("div",{class:"font-medium"},"激活成功！您现在可以使用 XCodeMap 了。",-1)),s(t).getExpiredTimestamp()?(i(),r("div",Z," 许可证过期时间："+d(new Date(s(t).getExpiredTimestamp()).toLocaleString()),1)):c("",!0)])):c("",!0)])])])])],2))}}),te=M(ee,[["__scopeId","data-v-0e5aa522"]]),f=j(te);f.use(z());f.mount("#app");
