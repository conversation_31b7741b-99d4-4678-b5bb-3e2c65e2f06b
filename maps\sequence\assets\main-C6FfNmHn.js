var gn=Object.defineProperty;var mn=(r,e,t)=>e in r?gn(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var N=(r,e,t)=>mn(r,typeof e!="symbol"?e+"":e,t);import{d as ot,r as M,c as Ue,g as bn,a as xn,b as vn,e as wn,f as zt,h as kn,p as yn,s as Tn,i as Zt,u as Ur,w as kt,j as y,o as k,F as ve,k as Ce,l as d,m as O,n as C,q as f,t as q,_ as Br,v as Fr,x as _n,y as En,z as Sn,A as $t,B as Cn,C as Se,D as Re,E as An,G as Rn,H as Dn,I as Mn,J as In,K as Ln,L as gt,M as Nn,N as On,O as Pn,P as zn,Q as $n,R as Un,S as Bn,T as Fn}from"./style-lyg0-ODQ.js";import{u as Et}from"./error-CJpCodTp.js";const Hn=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href,Kt=ot("chatStatus",()=>{const r=M("idle"),e=M("");return{status:r,statusMessage:e,setStatus:s=>{r.value=s},setStatusMessage:s=>{e.value=s}}}),Hr=ot("modelStatus",()=>{const r=M(null),e=M(null),t=Et(),n=Ue(()=>!r.value||!e.value?null:e.value.modelDescList.find(l=>l.uuid===r.value)),s=Ue(()=>{var l;return((l=e.value)==null?void 0:l.modelDescList)||[]});return{currentModelUuid:r,modelConfig:e,currentModel:n,availableModels:s,getModelConfigData:async()=>{var l;try{const g=await bn();if(g.success&&g.data)e.value=g.data,g.data.defaultModelId?g.data.modelDescList.find(p=>p.uuid===g.data.defaultModelId)?r.value=g.data.defaultModelId:r.value=((l=g.data.modelDescList[0])==null?void 0:l.uuid)||null:!r.value&&g.data.modelDescList.length>0&&(r.value=g.data.modelDescList[0].uuid);else throw new Error(g.error||"Failed to get model config")}catch(g){console.error("Failed to get model config:",g),t.setError(g instanceof Error?g.message:"Failed to get model config","GET_MODEL_CONFIG_ERROR")}},setCurrentModel:l=>{var i;((i=e.value)==null?void 0:i.modelDescList.find(p=>p.uuid===l))&&(r.value=l)}}}),J="EMPTY_PLACE_HOLDER",Gr=ot("database",()=>{const r=M([{id:J,name:"待选择程序数据(取消行号X图标)",recordState:"idle",dataId:null,active:!1,serverSelected:!1}]),e=M(J),t=Ue(()=>r.value.find(h=>h.id===e.value)||null),n=h=>{r.value.push(h)},s=h=>{e.value=h},a=async(h,w)=>{const x=r.value.find(_=>_.id===h);if(x)try{await xn({executionId:h,cmd:"change",state:w}),x.recordState="preparing"}catch(_){console.error("Failed to change record state:",_)}};return{databases:r,currentDatabase:t,currentDatabaseId:e,addDatabase:n,setCurrentDatabaseId:s,changeState:a,queryState:h=>{var w;return(w=r.value.find(x=>x.id===h))==null?void 0:w.recordState},startRecord:h=>{const w=r.value.find(x=>x.id===h);w&&w.recordState==="idle"&&a(h,"start")},endRecord:h=>{const w=r.value.find(x=>x.id===h);w&&w.recordState==="recording"&&a(h,"stop")},restartRecord:h=>{const w=r.value.find(x=>x.id===h);w&&w.recordState==="paused"&&(a(h,"start"),Wr().createNewChat())},getDatabase:async()=>{try{const h=await vn();r.value=[r.value[0]],h.forEach(w=>{n(w)})}catch(h){console.error("Failed to fetch process data:",h)}},deleteDatabase:async h=>{try{if(await wn(h)){const x=r.value.findIndex(_=>_.id===h);return x>-1&&r.value.splice(x,1),e.value===h&&(e.value=J),!0}return!1}catch(w){return console.error("Failed to delete database:",w),!1}}}}),Qt=ot("inputBox",()=>{const r=M(""),e=Et(),t=Kt(),n=Gr(),s=()=>!n.currentDatabase||!n.currentDatabase.dataId||t.status==="waiting"||n.currentDatabaseId===J;return{message:r,appendText:i=>{if(s())return e.setError("当前状态不可添加文本到输入框"),!1;const p=r.value&&!r.value.endsWith(" ");return r.value+=(p?" ":"")+i,window.dispatchEvent(new CustomEvent("textarea-content-changed")),!0},setText:i=>{r.value=i,window.dispatchEvent(new CustomEvent("textarea-content-changed"))},clearText:()=>{r.value="",window.dispatchEvent(new CustomEvent("textarea-content-changed"))},getText:()=>r.value,isDisabled:s}}),Gn="SystemStatus",Wn="AddToChat",Wr=ot("chat",()=>{const r=M([]),e=M(null),t=M(!1);let n=null;const s=Et(),a=Kt(),c=Hr(),l=Qt(),g=Ue(()=>r.value.find(_=>_.id===e.value)),i=async()=>{var _;try{e.value&&await zt(e.value);const A={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await kn(A.id,((_=c.currentModel)==null?void 0:_.uuid)??""))return r.value=[A],e.value=A.id,t.value||p(),A;throw new Error("Failed to create chat channel")}catch(A){throw console.error("Failed to create chat channel:",A),s.setError(A instanceof Error?A.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),e.value=null,r.value=[],A}},p=()=>{n&&clearInterval(n),t.value=!0,n=window.setInterval(async()=>{if(t.value)try{const _=await yn();if(_&&_.length>0)for(const A of _){if(A.messageId===Wn){l.appendText(A.content);continue}if(A.messageId===Gn){a.setStatus("waiting"),a.setStatusMessage(A.content);continue}const B=r.value.find(D=>D.id===A.chatId);if(B){a.setStatus("waiting");const D=B.messages[B.messages.length-1];D&&D.role===A.role?D.content+=A.content:B.messages.push(A),B.updatedAt=Date.now()}}else a.setStatus("sending"),a.setStatusMessage("正在思考中...")}catch(_){console.error("Failed to poll messages:",_),s.setError(_ instanceof Error?_.message:"Failed to poll messages","POLL_ERROR")}},1e3)},E=()=>{n&&(clearInterval(n),n=null),t.value=!1};return{chats:r,currentChatId:e,currentChat:g,createNewChat:i,sendMessage:async _=>{e.value||await i();const A=g.value;if(!A)return;if(!c.currentModel){s.setError("请先选择模型再发送消息","SEND_MESSAGE_ERROR");return}a.setStatus("waiting");const B={messageId:crypto.randomUUID(),content:_,role:"user",timestamp:Date.now(),chatId:e.value,modelId:c.currentModel.uuid};try{if(!e.value)return;if(await Tn(B))A.messages.push(B),A.updatedAt=Date.now();else throw new Error("会话可能已失效，请重新创建会话")}catch(D){throw console.error("Failed to send message:",D),s.setError(D instanceof Error?D.message:"会话可能已失效，请重新创建会话","SEND_MESSAGE_ERROR"),D}},removeChat:async _=>{try{if(await zt(_))r.value=r.value.filter(B=>B.id!==_),e.value===_&&(e.value=null);else throw new Error("Failed to remove chat channel")}catch(A){throw console.error("Failed to remove chat:",A),s.setError(A instanceof Error?A.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),A}},startPolling:p,stopPolling:E,cleanup:()=>{E(),e.value&&zt(e.value).catch(console.error)}}}),jn={class:"space-y-0.5"},qn=["onClick"],Vn={key:0,class:"p-0.5 rounded flex-shrink-0"},Xn=["onClick"],Yn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Zn=["onClick"],Kn={key:0,class:"p-0.5 rounded flex-shrink-0"},Qn=["onClick"],Jn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},es=["onClick"],ts=["onClick"],rs=Zt({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(r,{emit:e}){const t=r,n=Ur(),s=Qt(),a=M(t.nodes.map(E=>({...E,isExpanded:t.defaultExpanded,children:E.children?E.children.map(h=>({...h,isExpanded:t.defaultExpanded,children:h.children?h.children.map(w=>({...w,isExpanded:t.defaultExpanded})):[]})):[]})));kt(()=>t.nodes,E=>{a.value=E.map(h=>({...h,isExpanded:t.defaultExpanded,children:h.children?h.children.map(w=>({...w,isExpanded:t.defaultExpanded,children:w.children?w.children.map(x=>({...x,isExpanded:t.defaultExpanded})):[]})):[]}))},{deep:!0});const c=e,l=E=>{c("nodeClick",E)},g=E=>{E.isExpanded=!E.isExpanded},i=(E,h)=>{E.children&&E.children.length>0?g(E):l(E)},p=(E,h)=>{h.stopPropagation(),E.chatText&&s.setText(E.chatText)};return(E,h)=>(k(),y("div",jn,[(k(!0),y(ve,null,Ce(a.value,w=>(k(),y("div",{key:w.nodeKey,class:"tree-node"},[d("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:x=>i(w)},[w.children&&w.children.length>0?(k(),y("div",Vn,[(k(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":w.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},h[0]||(h[0]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):O("",!0),d("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},q(w.label),3),w.chatText?(k(),y("button",{key:1,onClick:x=>p(w,x),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},h[1]||(h[1]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,Xn)):O("",!0)],10,qn),w.children&&w.children.length>0&&w.isExpanded?(k(),y("div",Yn,[(k(!0),y(ve,null,Ce(w.children,x=>(k(),y("div",{key:x.nodeKey,class:"tree-node"},[d("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:_=>i(x)},[x.children&&x.children.length>0?(k(),y("div",Kn,[(k(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":x.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},h[2]||(h[2]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):O("",!0),d("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},q(x.label),3),x.chatText?(k(),y("button",{key:1,onClick:_=>p(x,_),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},h[3]||(h[3]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,Qn)):O("",!0)],10,Zn),x.children&&x.children.length>0&&x.isExpanded?(k(),y("div",Jn,[(k(!0),y(ve,null,Ce(x.children,_=>(k(),y("div",{key:_.nodeKey,class:"pl-3"},[d("div",{class:C(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:A=>i(_)},[d("span",{class:C({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},q(_.label),3),_.chatText?(k(),y("button",{key:0,onClick:A=>p(_,A),class:C(["p-1 rounded flex-shrink-0 transition-colors",f(n).theme==="dark"?"hover:bg-gray-600 text-gray-400 hover:text-gray-200":"hover:bg-gray-200 text-gray-500 hover:text-gray-700"]),title:"添加到聊天"},h[4]||(h[4]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z","clip-rule":"evenodd"})],-1)]),10,ts)):O("",!0)],10,es)]))),128))])):O("",!0)]))),128))])):O("",!0)]))),128))]))}}),ns=Br(rs,[["__scopeId","data-v-4bd5633e"]]),ss=Zt({__name:"TypeWriter",props:{text:{},speed:{}},emits:["complete"],setup(r,{emit:e}){const t=r,n=e,s=M(""),a=M(0),c=M(!1),l=()=>{s.value="",a.value=0,c.value=!0;const g=setInterval(()=>{a.value<t.text.length?(s.value+=t.text[a.value],a.value++):(clearInterval(g),c.value=!1,n("complete"))},t.speed||50)};return kt(()=>t.text,()=>{l()}),Fr(()=>{l()}),(g,i)=>(k(),y("span",null,q(s.value),1))}});function Jt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var Me=Jt();function jr(r){Me=r}var at={exec:()=>null};function L(r,e=""){let t=typeof r=="string"?r:r.source;const n={replace:(s,a)=>{let c=typeof a=="string"?a:a.source;return c=c.replace(ee.caret,"$1"),t=t.replace(s,c),n},getRegex:()=>new RegExp(t,e)};return n}var ee={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:r=>new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}#`),htmlBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}<(?:[a-z].*>|!--)`,"i")},as=/^(?:[ \t]*(?:\n|$))+/,os=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,ls=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,lt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,is=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,er=/(?:[*+-]|\d{1,9}[.)])/,qr=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Vr=L(qr).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),cs=L(qr).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),tr=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,us=/^[^\n]+/,rr=/(?!\s*\])(?:\\.|[^\[\]\\])+/,ds=L(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",rr).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ps=L(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,er).getRegex(),St="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",nr=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,hs=L("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",nr).replace("tag",St).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Xr=L(tr).replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex(),fs=L(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Xr).getRegex(),sr={blockquote:fs,code:os,def:ds,fences:ls,heading:is,hr:lt,html:hs,lheading:Vr,list:ps,newline:as,paragraph:Xr,table:at,text:us},Er=L("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex(),gs={...sr,lheading:cs,table:Er,paragraph:L(tr).replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Er).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex()},ms={...sr,html:L(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",nr).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:at,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:L(tr).replace("hr",lt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Vr).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},bs=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,xs=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Yr=/^( {2,}|\\)\n(?!\s*$)/,vs=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Ct=/[\p{P}\p{S}]/u,ar=/[\s\p{P}\p{S}]/u,Zr=/[^\s\p{P}\p{S}]/u,ws=L(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,ar).getRegex(),Kr=/(?!~)[\p{P}\p{S}]/u,ks=/(?!~)[\s\p{P}\p{S}]/u,ys=/(?:[^\s\p{P}\p{S}]|~)/u,Ts=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Qr=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,_s=L(Qr,"u").replace(/punct/g,Ct).getRegex(),Es=L(Qr,"u").replace(/punct/g,Kr).getRegex(),Jr="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Ss=L(Jr,"gu").replace(/notPunctSpace/g,Zr).replace(/punctSpace/g,ar).replace(/punct/g,Ct).getRegex(),Cs=L(Jr,"gu").replace(/notPunctSpace/g,ys).replace(/punctSpace/g,ks).replace(/punct/g,Kr).getRegex(),As=L("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Zr).replace(/punctSpace/g,ar).replace(/punct/g,Ct).getRegex(),Rs=L(/\\(punct)/,"gu").replace(/punct/g,Ct).getRegex(),Ds=L(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Ms=L(nr).replace("(?:-->|$)","-->").getRegex(),Is=L("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Ms).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),yt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ls=L(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",yt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),en=L(/^!?\[(label)\]\[(ref)\]/).replace("label",yt).replace("ref",rr).getRegex(),tn=L(/^!?\[(ref)\](?:\[\])?/).replace("ref",rr).getRegex(),Ns=L("reflink|nolink(?!\\()","g").replace("reflink",en).replace("nolink",tn).getRegex(),or={_backpedal:at,anyPunctuation:Rs,autolink:Ds,blockSkip:Ts,br:Yr,code:xs,del:at,emStrongLDelim:_s,emStrongRDelimAst:Ss,emStrongRDelimUnd:As,escape:bs,link:Ls,nolink:tn,punctuation:ws,reflink:en,reflinkSearch:Ns,tag:Is,text:vs,url:at},Os={...or,link:L(/^!?\[(label)\]\((.*?)\)/).replace("label",yt).getRegex(),reflink:L(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",yt).getRegex()},jt={...or,emStrongRDelimAst:Cs,emStrongLDelim:Es,url:L(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ps={...jt,br:L(Yr).replace("{2,}","*").getRegex(),text:L(jt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},mt={normal:sr,gfm:gs,pedantic:ms},Qe={normal:or,gfm:jt,breaks:Ps,pedantic:Os},zs={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Sr=r=>zs[r];function pe(r,e){if(e){if(ee.escapeTest.test(r))return r.replace(ee.escapeReplace,Sr)}else if(ee.escapeTestNoEncode.test(r))return r.replace(ee.escapeReplaceNoEncode,Sr);return r}function Cr(r){try{r=encodeURI(r).replace(ee.percentDecode,"%")}catch{return null}return r}function Ar(r,e){var a;const t=r.replace(ee.findPipe,(c,l,g)=>{let i=!1,p=l;for(;--p>=0&&g[p]==="\\";)i=!i;return i?"|":" |"}),n=t.split(ee.splitPipe);let s=0;if(n[0].trim()||n.shift(),n.length>0&&!((a=n.at(-1))!=null&&a.trim())&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(ee.slashPipe,"|");return n}function Je(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n&&r.charAt(n-s-1)===e;)s++;return r.slice(0,n-s)}function $s(r,e){if(r.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<r.length;n++)if(r[n]==="\\")n++;else if(r[n]===e[0])t++;else if(r[n]===e[1]&&(t--,t<0))return n;return t>0?-2:-1}function Rr(r,e,t,n,s){const a=e.href,c=e.title||null,l=r[1].replace(s.other.outputLinkReplace,"$1");n.state.inLink=!0;const g={type:r[0].charAt(0)==="!"?"image":"link",raw:t,href:a,title:c,text:l,tokens:n.inlineTokens(l)};return n.state.inLink=!1,g}function Us(r,e,t){const n=r.match(t.other.indentCodeCompensation);if(n===null)return e;const s=n[1];return e.split(`
`).map(a=>{const c=a.match(t.other.beginningSpace);if(c===null)return a;const[l]=c;return l.length>=s.length?a.slice(s.length):a}).join(`
`)}var Tt=class{constructor(r){N(this,"options");N(this,"rules");N(this,"lexer");this.options=r||Me}space(r){const e=this.rules.block.newline.exec(r);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(r){const e=this.rules.block.code.exec(r);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:Je(t,`
`)}}}fences(r){const e=this.rules.block.fences.exec(r);if(e){const t=e[0],n=Us(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(r){const e=this.rules.block.heading.exec(r);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const n=Je(t,"#");(this.options.pedantic||!n||this.rules.other.endingSpaceChar.test(n))&&(t=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(r){const e=this.rules.block.hr.exec(r);if(e)return{type:"hr",raw:Je(e[0],`
`)}}blockquote(r){const e=this.rules.block.blockquote.exec(r);if(e){let t=Je(e[0],`
`).split(`
`),n="",s="";const a=[];for(;t.length>0;){let c=!1;const l=[];let g;for(g=0;g<t.length;g++)if(this.rules.other.blockquoteStart.test(t[g]))l.push(t[g]),c=!0;else if(!c)l.push(t[g]);else break;t=t.slice(g);const i=l.join(`
`),p=i.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${i}`:i,s=s?`${s}
${p}`:p;const E=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(p,a,!0),this.lexer.state.top=E,t.length===0)break;const h=a.at(-1);if((h==null?void 0:h.type)==="code")break;if((h==null?void 0:h.type)==="blockquote"){const w=h,x=w.raw+`
`+t.join(`
`),_=this.blockquote(x);a[a.length-1]=_,n=n.substring(0,n.length-w.raw.length)+_.raw,s=s.substring(0,s.length-w.text.length)+_.text;break}else if((h==null?void 0:h.type)==="list"){const w=h,x=w.raw+`
`+t.join(`
`),_=this.list(x);a[a.length-1]=_,n=n.substring(0,n.length-h.raw.length)+_.raw,s=s.substring(0,s.length-w.raw.length)+_.raw,t=x.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:n,tokens:a,text:s}}}list(r){let e=this.rules.block.list.exec(r);if(e){let t=e[1].trim();const n=t.length>1,s={type:"list",raw:"",ordered:n,start:n?+t.slice(0,-1):"",loose:!1,items:[]};t=n?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=n?t:"[*+-]");const a=this.rules.other.listItemRegex(t);let c=!1;for(;r;){let g=!1,i="",p="";if(!(e=a.exec(r))||this.rules.block.hr.test(r))break;i=e[0],r=r.substring(i.length);let E=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,B=>" ".repeat(3*B.length)),h=r.split(`
`,1)[0],w=!E.trim(),x=0;if(this.options.pedantic?(x=2,p=E.trimStart()):w?x=e[1].length+1:(x=e[2].search(this.rules.other.nonSpaceChar),x=x>4?1:x,p=E.slice(x),x+=e[1].length),w&&this.rules.other.blankLine.test(h)&&(i+=h+`
`,r=r.substring(h.length+1),g=!0),!g){const B=this.rules.other.nextBulletRegex(x),D=this.rules.other.hrRegex(x),V=this.rules.other.fencesBeginRegex(x),P=this.rules.other.headingBeginRegex(x),ae=this.rules.other.htmlBeginRegex(x);for(;r;){const ne=r.split(`
`,1)[0];let ue;if(h=ne,this.options.pedantic?(h=h.replace(this.rules.other.listReplaceNesting,"  "),ue=h):ue=h.replace(this.rules.other.tabCharGlobal,"    "),V.test(h)||P.test(h)||ae.test(h)||B.test(h)||D.test(h))break;if(ue.search(this.rules.other.nonSpaceChar)>=x||!h.trim())p+=`
`+ue.slice(x);else{if(w||E.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||V.test(E)||P.test(E)||D.test(E))break;p+=`
`+h}!w&&!h.trim()&&(w=!0),i+=ne+`
`,r=r.substring(ne.length+1),E=ue.slice(x)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(i)&&(c=!0));let _=null,A;this.options.gfm&&(_=this.rules.other.listIsTask.exec(p),_&&(A=_[0]!=="[ ] ",p=p.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:i,task:!!_,checked:A,loose:!1,text:p,tokens:[]}),s.raw+=i}const l=s.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let g=0;g<s.items.length;g++)if(this.lexer.state.top=!1,s.items[g].tokens=this.lexer.blockTokens(s.items[g].text,[]),!s.loose){const i=s.items[g].tokens.filter(E=>E.type==="space"),p=i.length>0&&i.some(E=>this.rules.other.anyLine.test(E.raw));s.loose=p}if(s.loose)for(let g=0;g<s.items.length;g++)s.items[g].loose=!0;return s}}html(r){const e=this.rules.block.html.exec(r);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(r){const e=this.rules.block.def.exec(r);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:n,title:s}}}table(r){var c;const e=this.rules.block.table.exec(r);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=Ar(e[1]),n=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===n.length){for(const l of n)this.rules.other.tableAlignRight.test(l)?a.align.push("right"):this.rules.other.tableAlignCenter.test(l)?a.align.push("center"):this.rules.other.tableAlignLeft.test(l)?a.align.push("left"):a.align.push(null);for(let l=0;l<t.length;l++)a.header.push({text:t[l],tokens:this.lexer.inline(t[l]),header:!0,align:a.align[l]});for(const l of s)a.rows.push(Ar(l,a.header.length).map((g,i)=>({text:g,tokens:this.lexer.inline(g),header:!1,align:a.align[i]})));return a}}lheading(r){const e=this.rules.block.lheading.exec(r);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(r){const e=this.rules.block.paragraph.exec(r);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(r){const e=this.rules.block.text.exec(r);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(r){const e=this.rules.inline.escape.exec(r);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(r){const e=this.rules.inline.tag.exec(r);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(r){const e=this.rules.inline.link.exec(r);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const a=Je(t.slice(0,-1),"\\");if((t.length-a.length)%2===0)return}else{const a=$s(e[2],"()");if(a===-2)return;if(a>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+a;e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let n=e[2],s="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(n);a&&(n=a[1],s=a[3])}else s=e[3]?e[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?n=n.slice(1):n=n.slice(1,-1)),Rr(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(r,e){let t;if((t=this.rules.inline.reflink.exec(r))||(t=this.rules.inline.nolink.exec(r))){const n=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[n.toLowerCase()];if(!s){const a=t[0].charAt(0);return{type:"text",raw:a,text:a}}return Rr(t,s,t[0],this.lexer,this.rules)}}emStrong(r,e,t=""){let n=this.rules.inline.emStrongLDelim.exec(r);if(!n||n[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(n[1]||n[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const a=[...n[0]].length-1;let c,l,g=a,i=0;const p=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,e=e.slice(-1*r.length+a);(n=p.exec(e))!=null;){if(c=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!c)continue;if(l=[...c].length,n[3]||n[4]){g+=l;continue}else if((n[5]||n[6])&&a%3&&!((a+l)%3)){i+=l;continue}if(g-=l,g>0)continue;l=Math.min(l,l+g+i);const E=[...n[0]][0].length,h=r.slice(0,a+n.index+E+l);if(Math.min(a,l)%2){const x=h.slice(1,-1);return{type:"em",raw:h,text:x,tokens:this.lexer.inlineTokens(x)}}const w=h.slice(2,-2);return{type:"strong",raw:h,text:w,tokens:this.lexer.inlineTokens(w)}}}}codespan(r){const e=this.rules.inline.code.exec(r);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return n&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(r){const e=this.rules.inline.br.exec(r);if(e)return{type:"br",raw:e[0]}}del(r){const e=this.rules.inline.del.exec(r);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(r){const e=this.rules.inline.autolink.exec(r);if(e){let t,n;return e[2]==="@"?(t=e[1],n="mailto:"+t):(t=e[1],n=t),{type:"link",raw:e[0],text:t,href:n,tokens:[{type:"text",raw:t,text:t}]}}}url(r){var t;let e;if(e=this.rules.inline.url.exec(r)){let n,s;if(e[2]==="@")n=e[0],s="mailto:"+n;else{let a;do a=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(a!==e[0]);n=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(r){const e=this.rules.inline.text.exec(r);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},we=class qt{constructor(e){N(this,"tokens");N(this,"options");N(this,"state");N(this,"tokenizer");N(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Me,this.options.tokenizer=this.options.tokenizer||new Tt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:ee,block:mt.normal,inline:Qe.normal};this.options.pedantic?(t.block=mt.pedantic,t.inline=Qe.pedantic):this.options.gfm&&(t.block=mt.gfm,this.options.breaks?t.inline=Qe.breaks:t.inline=Qe.gfm),this.tokenizer.rules=t}static get rules(){return{block:mt,inline:Qe}}static lex(e,t){return new qt(t).lex(e)}static lexInline(e,t){return new qt(t).inlineTokens(e)}lex(e){e=e.replace(ee.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){var s,a,c;for(this.options.pedantic&&(e=e.replace(ee.tabCharGlobal,"    ").replace(ee.spaceLine,""));e;){let l;if((a=(s=this.options.extensions)==null?void 0:s.block)!=null&&a.some(i=>(l=i.call({lexer:this},e,t))?(e=e.substring(l.raw.length),t.push(l),!0):!1))continue;if(l=this.tokenizer.space(e)){e=e.substring(l.raw.length);const i=t.at(-1);l.raw.length===1&&i!==void 0?i.raw+=`
`:t.push(l);continue}if(l=this.tokenizer.code(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(l=this.tokenizer.fences(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.heading(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.hr(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.blockquote(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.list(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.html(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.def(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.raw,this.inlineQueue.at(-1).src=i.text):this.tokens.links[l.tag]||(this.tokens.links[l.tag]={href:l.href,title:l.title});continue}if(l=this.tokenizer.table(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.lheading(e)){e=e.substring(l.raw.length),t.push(l);continue}let g=e;if((c=this.options.extensions)!=null&&c.startBlock){let i=1/0;const p=e.slice(1);let E;this.options.extensions.startBlock.forEach(h=>{E=h.call({lexer:this},p),typeof E=="number"&&E>=0&&(i=Math.min(i,E))}),i<1/0&&i>=0&&(g=e.substring(0,i+1))}if(this.state.top&&(l=this.tokenizer.paragraph(g))){const i=t.at(-1);n&&(i==null?void 0:i.type)==="paragraph"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l),n=g.length!==e.length,e=e.substring(l.raw.length);continue}if(l=this.tokenizer.text(e)){e=e.substring(l.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="text"?(i.raw+=`
`+l.raw,i.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(l);continue}if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}else throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var l,g,i;let n=e,s=null;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)p.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,c="";for(;e;){a||(c=""),a=!1;let p;if((g=(l=this.options.extensions)==null?void 0:l.inline)!=null&&g.some(h=>(p=h.call({lexer:this},e,t))?(e=e.substring(p.raw.length),t.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);const h=t.at(-1);p.type==="text"&&(h==null?void 0:h.type)==="text"?(h.raw+=p.raw,h.text+=p.text):t.push(p);continue}if(p=this.tokenizer.emStrong(e,n,c)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),t.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),t.push(p);continue}let E=e;if((i=this.options.extensions)!=null&&i.startInline){let h=1/0;const w=e.slice(1);let x;this.options.extensions.startInline.forEach(_=>{x=_.call({lexer:this},w),typeof x=="number"&&x>=0&&(h=Math.min(h,x))}),h<1/0&&h>=0&&(E=e.substring(0,h+1))}if(p=this.tokenizer.inlineText(E)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(c=p.raw.slice(-1)),a=!0;const h=t.at(-1);(h==null?void 0:h.type)==="text"?(h.raw+=p.raw,h.text+=p.text):t.push(p);continue}if(e){const h="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(h);break}else throw new Error(h)}}return t}},_t=class{constructor(r){N(this,"options");N(this,"parser");this.options=r||Me}space(r){return""}code({text:r,lang:e,escaped:t}){var a;const n=(a=(e||"").match(ee.notSpaceStart))==null?void 0:a[0],s=r.replace(ee.endingNewline,"")+`
`;return n?'<pre><code class="language-'+pe(n)+'">'+(t?s:pe(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:pe(s,!0))+`</code></pre>
`}blockquote({tokens:r}){return`<blockquote>
${this.parser.parse(r)}</blockquote>
`}html({text:r}){return r}heading({tokens:r,depth:e}){return`<h${e}>${this.parser.parseInline(r)}</h${e}>
`}hr(r){return`<hr>
`}list(r){const e=r.ordered,t=r.start;let n="";for(let c=0;c<r.items.length;c++){const l=r.items[c];n+=this.listitem(l)}const s=e?"ol":"ul",a=e&&t!==1?' start="'+t+'"':"";return"<"+s+a+`>
`+n+"</"+s+`>
`}listitem(r){var t;let e="";if(r.task){const n=this.checkbox({checked:!!r.checked});r.loose?((t=r.tokens[0])==null?void 0:t.type)==="paragraph"?(r.tokens[0].text=n+" "+r.tokens[0].text,r.tokens[0].tokens&&r.tokens[0].tokens.length>0&&r.tokens[0].tokens[0].type==="text"&&(r.tokens[0].tokens[0].text=n+" "+pe(r.tokens[0].tokens[0].text),r.tokens[0].tokens[0].escaped=!0)):r.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):e+=n+" "}return e+=this.parser.parse(r.tokens,!!r.loose),`<li>${e}</li>
`}checkbox({checked:r}){return"<input "+(r?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:r}){return`<p>${this.parser.parseInline(r)}</p>
`}table(r){let e="",t="";for(let s=0;s<r.header.length;s++)t+=this.tablecell(r.header[s]);e+=this.tablerow({text:t});let n="";for(let s=0;s<r.rows.length;s++){const a=r.rows[s];t="";for(let c=0;c<a.length;c++)t+=this.tablecell(a[c]);n+=this.tablerow({text:t})}return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+n+`</table>
`}tablerow({text:r}){return`<tr>
${r}</tr>
`}tablecell(r){const e=this.parser.parseInline(r.tokens),t=r.header?"th":"td";return(r.align?`<${t} align="${r.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:r}){return`<strong>${this.parser.parseInline(r)}</strong>`}em({tokens:r}){return`<em>${this.parser.parseInline(r)}</em>`}codespan({text:r}){return`<code>${pe(r,!0)}</code>`}br(r){return"<br>"}del({tokens:r}){return`<del>${this.parser.parseInline(r)}</del>`}link({href:r,title:e,tokens:t}){const n=this.parser.parseInline(t),s=Cr(r);if(s===null)return n;r=s;let a='<a href="'+r+'"';return e&&(a+=' title="'+pe(e)+'"'),a+=">"+n+"</a>",a}image({href:r,title:e,text:t,tokens:n}){n&&(t=this.parser.parseInline(n,this.parser.textRenderer));const s=Cr(r);if(s===null)return pe(t);r=s;let a=`<img src="${r}" alt="${t}"`;return e&&(a+=` title="${pe(e)}"`),a+=">",a}text(r){return"tokens"in r&&r.tokens?this.parser.parseInline(r.tokens):"escaped"in r&&r.escaped?r.text:pe(r.text)}},lr=class{strong({text:r}){return r}em({text:r}){return r}codespan({text:r}){return r}del({text:r}){return r}html({text:r}){return r}text({text:r}){return r}link({text:r}){return""+r}image({text:r}){return""+r}br(){return""}},ke=class Vt{constructor(e){N(this,"options");N(this,"renderer");N(this,"textRenderer");this.options=e||Me,this.options.renderer=this.options.renderer||new _t,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new lr}static parse(e,t){return new Vt(t).parse(e)}static parseInline(e,t){return new Vt(t).parseInline(e)}parse(e,t=!0){var s,a;let n="";for(let c=0;c<e.length;c++){const l=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=l,p=this.options.extensions.renderers[i.type].call({parser:this},i);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){n+=p||"";continue}}const g=l;switch(g.type){case"space":{n+=this.renderer.space(g);continue}case"hr":{n+=this.renderer.hr(g);continue}case"heading":{n+=this.renderer.heading(g);continue}case"code":{n+=this.renderer.code(g);continue}case"table":{n+=this.renderer.table(g);continue}case"blockquote":{n+=this.renderer.blockquote(g);continue}case"list":{n+=this.renderer.list(g);continue}case"html":{n+=this.renderer.html(g);continue}case"paragraph":{n+=this.renderer.paragraph(g);continue}case"text":{let i=g,p=this.renderer.text(i);for(;c+1<e.length&&e[c+1].type==="text";)i=e[++c],p+=`
`+this.renderer.text(i);t?n+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):n+=p;continue}default:{const i='Token with "'+g.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}parseInline(e,t=this.renderer){var s,a;let n="";for(let c=0;c<e.length;c++){const l=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[l.type]){const i=this.options.extensions.renderers[l.type].call({parser:this},l);if(i!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(l.type)){n+=i||"";continue}}const g=l;switch(g.type){case"escape":{n+=t.text(g);break}case"html":{n+=t.html(g);break}case"link":{n+=t.link(g);break}case"image":{n+=t.image(g);break}case"strong":{n+=t.strong(g);break}case"em":{n+=t.em(g);break}case"codespan":{n+=t.codespan(g);break}case"br":{n+=t.br(g);break}case"del":{n+=t.del(g);break}case"text":{n+=t.text(g);break}default:{const i='Token with "'+g.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}},Wt,vt=(Wt=class{constructor(r){N(this,"options");N(this,"block");this.options=r||Me}preprocess(r){return r}postprocess(r){return r}processAllTokens(r){return r}provideLexer(){return this.block?we.lex:we.lexInline}provideParser(){return this.block?ke.parse:ke.parseInline}},N(Wt,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Wt),Bs=class{constructor(...r){N(this,"defaults",Jt());N(this,"options",this.setOptions);N(this,"parse",this.parseMarkdown(!0));N(this,"parseInline",this.parseMarkdown(!1));N(this,"Parser",ke);N(this,"Renderer",_t);N(this,"TextRenderer",lr);N(this,"Lexer",we);N(this,"Tokenizer",Tt);N(this,"Hooks",vt);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const a of r)switch(t=t.concat(e.call(this,a)),a.type){case"table":{const c=a;for(const l of c.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of c.rows)for(const g of l)t=t.concat(this.walkTokens(g.tokens,e));break}case"list":{const c=a;t=t.concat(this.walkTokens(c.items,e));break}default:{const c=a;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(l=>{const g=c[l].flat(1/0);t=t.concat(this.walkTokens(g,e))}):c.tokens&&(t=t.concat(this.walkTokens(c.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=e.renderers[s.name];a?e.renderers[s.name]=function(...c){let l=s.renderer.apply(this,c);return l===!1&&(l=a.apply(this,c)),l}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=e[s.level];a?a.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new _t(this.defaults);for(const a in t.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const c=a,l=t.renderer[c],g=s[c];s[c]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=g.apply(s,i)),p||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new Tt(this.defaults);for(const a in t.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const c=a,l=t.tokenizer[c],g=s[c];s[c]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=g.apply(s,i)),p}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new vt;for(const a in t.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const c=a,l=t.hooks[c],g=s[c];vt.passThroughHooks.has(a)?s[c]=i=>{if(this.defaults.async)return Promise.resolve(l.call(s,i)).then(E=>g.call(s,E));const p=l.call(s,i);return g.call(s,p)}:s[c]=(...i)=>{let p=l.apply(s,i);return p===!1&&(p=g.apply(s,i)),p}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,a=t.walkTokens;n.walkTokens=function(c){let l=[];return l.push(a.call(this,c)),s&&(l=l.concat(s.call(this,c))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return we.lex(r,e??this.defaults)}parser(r,e){return ke.parse(r,e??this.defaults)}parseMarkdown(r){return(t,n)=>{const s={...n},a={...this.defaults,...s},c=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&s.async===!1)return c(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return c(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return c(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=r);const l=a.hooks?a.hooks.provideLexer():r?we.lex:we.lexInline,g=a.hooks?a.hooks.provideParser():r?ke.parse:ke.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(i=>l(i,a)).then(i=>a.hooks?a.hooks.processAllTokens(i):i).then(i=>a.walkTokens?Promise.all(this.walkTokens(i,a.walkTokens)).then(()=>i):i).then(i=>g(i,a)).then(i=>a.hooks?a.hooks.postprocess(i):i).catch(c);try{a.hooks&&(t=a.hooks.preprocess(t));let i=l(t,a);a.hooks&&(i=a.hooks.processAllTokens(i)),a.walkTokens&&this.walkTokens(i,a.walkTokens);let p=g(i,a);return a.hooks&&(p=a.hooks.postprocess(p)),p}catch(i){return c(i)}}}onError(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+pe(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}}},De=new Bs;function I(r,e){return De.parse(r,e)}I.options=I.setOptions=function(r){return De.setOptions(r),I.defaults=De.defaults,jr(I.defaults),I};I.getDefaults=Jt;I.defaults=Me;I.use=function(...r){return De.use(...r),I.defaults=De.defaults,jr(I.defaults),I};I.walkTokens=function(r,e){return De.walkTokens(r,e)};I.parseInline=De.parseInline;I.Parser=ke;I.parser=ke.parse;I.Renderer=_t;I.TextRenderer=lr;I.Lexer=we;I.lexer=we.lex;I.Tokenizer=Tt;I.Hooks=vt;I.parse=I;I.options;I.setOptions;I.use;I.walkTokens;I.parseInline;ke.parse;we.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:rn,setPrototypeOf:Dr,isFrozen:Fs,getPrototypeOf:Hs,getOwnPropertyDescriptor:Gs}=Object;let{freeze:te,seal:le,create:nn}=Object,{apply:Xt,construct:Yt}=typeof Reflect<"u"&&Reflect;te||(te=function(e){return e});le||(le=function(e){return e});Xt||(Xt=function(e,t,n){return e.apply(t,n)});Yt||(Yt=function(e,t){return new e(...t)});const bt=re(Array.prototype.forEach),Ws=re(Array.prototype.lastIndexOf),Mr=re(Array.prototype.pop),et=re(Array.prototype.push),js=re(Array.prototype.splice),wt=re(String.prototype.toLowerCase),Ut=re(String.prototype.toString),Ir=re(String.prototype.match),tt=re(String.prototype.replace),qs=re(String.prototype.indexOf),Vs=re(String.prototype.trim),ce=re(Object.prototype.hasOwnProperty),Q=re(RegExp.prototype.test),rt=Xs(TypeError);function re(r){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return Xt(r,e,n)}}function Xs(r){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Yt(r,t)}}function R(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:wt;Dr&&Dr(r,null);let n=e.length;for(;n--;){let s=e[n];if(typeof s=="string"){const a=t(s);a!==s&&(Fs(e)||(e[n]=a),s=a)}r[s]=!0}return r}function Ys(r){for(let e=0;e<r.length;e++)ce(r,e)||(r[e]=null);return r}function xe(r){const e=nn(null);for(const[t,n]of rn(r))ce(r,t)&&(Array.isArray(n)?e[t]=Ys(n):n&&typeof n=="object"&&n.constructor===Object?e[t]=xe(n):e[t]=n);return e}function nt(r,e){for(;r!==null;){const n=Gs(r,e);if(n){if(n.get)return re(n.get);if(typeof n.value=="function")return re(n.value)}r=Hs(r)}function t(){return null}return t}const Lr=te(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Bt=te(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ft=te(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Zs=te(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ht=te(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Ks=te(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Nr=te(["#text"]),Or=te(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Gt=te(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Pr=te(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),xt=te(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Qs=le(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Js=le(/<%[\w\W]*|[\w\W]*%>/gm),ea=le(/\$\{[\w\W]*/gm),ta=le(/^data-[\-\w.\u00B7-\uFFFF]+$/),ra=le(/^aria-[\-\w]+$/),sn=le(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),na=le(/^(?:\w+script|data):/i),sa=le(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),an=le(/^html$/i),aa=le(/^[a-z][.\w]*(-[.\w]+)+$/i);var zr=Object.freeze({__proto__:null,ARIA_ATTR:ra,ATTR_WHITESPACE:sa,CUSTOM_ELEMENT:aa,DATA_ATTR:ta,DOCTYPE_NAME:an,ERB_EXPR:Js,IS_ALLOWED_URI:sn,IS_SCRIPT_OR_DATA:na,MUSTACHE_EXPR:Qs,TMPLIT_EXPR:ea});const st={element:1,text:3,progressingInstruction:7,comment:8,document:9},oa=function(){return typeof window>"u"?null:window},la=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(n=t.getAttribute(s));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML(c){return c},createScriptURL(c){return c}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},$r=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function on(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:oa();const e=S=>on(S);if(e.version="3.2.6",e.removed=[],!r||!r.document||r.document.nodeType!==st.document||!r.Element)return e.isSupported=!1,e;let{document:t}=r;const n=t,s=n.currentScript,{DocumentFragment:a,HTMLTemplateElement:c,Node:l,Element:g,NodeFilter:i,NamedNodeMap:p=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:E,DOMParser:h,trustedTypes:w}=r,x=g.prototype,_=nt(x,"cloneNode"),A=nt(x,"remove"),B=nt(x,"nextSibling"),D=nt(x,"childNodes"),V=nt(x,"parentNode");if(typeof c=="function"){const S=t.createElement("template");S.content&&S.content.ownerDocument&&(t=S.content.ownerDocument)}let P,ae="";const{implementation:ne,createNodeIterator:ue,createDocumentFragment:ye,getElementsByTagName:he}=t,{importNode:Ie}=n;let X=$r();e.isSupported=typeof rn=="function"&&typeof V=="function"&&ne&&ne.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Be,ERB_EXPR:Fe,TMPLIT_EXPR:He,DATA_ATTR:At,ARIA_ATTR:Rt,IS_SCRIPT_OR_DATA:Ge,ATTR_WHITESPACE:We,CUSTOM_ELEMENT:Dt}=zr;let{IS_ALLOWED_URI:it}=zr,G=null;const je=R({},[...Lr,...Bt,...Ft,...Ht,...Nr]);let F=null;const qe=R({},[...Or,...Gt,...Pr,...xt]);let $=Object.seal(nn(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Te=null,Ve=null,Le=!0,Xe=!0,ct=!1,ut=!0,_e=!1,Ne=!0,fe=!1,Ye=!1,Oe=!1,Ee=!1,b=!1,u=!1,Y=!0,U=!1;const v="user-content-";let oe=!0,ie=!1,H={},Ae=null;const cr=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ur=null;const dr=R({},["audio","video","img","source","image","track"]);let Mt=null;const pr=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),dt="http://www.w3.org/1998/Math/MathML",pt="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let Pe=ge,It=!1,Lt=null;const ln=R({},[dt,pt,ge],Ut);let ht=R({},["mi","mo","mn","ms","mtext"]),ft=R({},["annotation-xml"]);const cn=R({},["title","style","font","a","script"]);let Ze=null;const un=["application/xhtml+xml","text/html"],dn="text/html";let j=null,ze=null;const pn=t.createElement("form"),hr=function(o){return o instanceof RegExp||o instanceof Function},Nt=function(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(ze&&ze===o)){if((!o||typeof o!="object")&&(o={}),o=xe(o),Ze=un.indexOf(o.PARSER_MEDIA_TYPE)===-1?dn:o.PARSER_MEDIA_TYPE,j=Ze==="application/xhtml+xml"?Ut:wt,G=ce(o,"ALLOWED_TAGS")?R({},o.ALLOWED_TAGS,j):je,F=ce(o,"ALLOWED_ATTR")?R({},o.ALLOWED_ATTR,j):qe,Lt=ce(o,"ALLOWED_NAMESPACES")?R({},o.ALLOWED_NAMESPACES,Ut):ln,Mt=ce(o,"ADD_URI_SAFE_ATTR")?R(xe(pr),o.ADD_URI_SAFE_ATTR,j):pr,ur=ce(o,"ADD_DATA_URI_TAGS")?R(xe(dr),o.ADD_DATA_URI_TAGS,j):dr,Ae=ce(o,"FORBID_CONTENTS")?R({},o.FORBID_CONTENTS,j):cr,Te=ce(o,"FORBID_TAGS")?R({},o.FORBID_TAGS,j):xe({}),Ve=ce(o,"FORBID_ATTR")?R({},o.FORBID_ATTR,j):xe({}),H=ce(o,"USE_PROFILES")?o.USE_PROFILES:!1,Le=o.ALLOW_ARIA_ATTR!==!1,Xe=o.ALLOW_DATA_ATTR!==!1,ct=o.ALLOW_UNKNOWN_PROTOCOLS||!1,ut=o.ALLOW_SELF_CLOSE_IN_ATTR!==!1,_e=o.SAFE_FOR_TEMPLATES||!1,Ne=o.SAFE_FOR_XML!==!1,fe=o.WHOLE_DOCUMENT||!1,Ee=o.RETURN_DOM||!1,b=o.RETURN_DOM_FRAGMENT||!1,u=o.RETURN_TRUSTED_TYPE||!1,Oe=o.FORCE_BODY||!1,Y=o.SANITIZE_DOM!==!1,U=o.SANITIZE_NAMED_PROPS||!1,oe=o.KEEP_CONTENT!==!1,ie=o.IN_PLACE||!1,it=o.ALLOWED_URI_REGEXP||sn,Pe=o.NAMESPACE||ge,ht=o.MATHML_TEXT_INTEGRATION_POINTS||ht,ft=o.HTML_INTEGRATION_POINTS||ft,$=o.CUSTOM_ELEMENT_HANDLING||{},o.CUSTOM_ELEMENT_HANDLING&&hr(o.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&($.tagNameCheck=o.CUSTOM_ELEMENT_HANDLING.tagNameCheck),o.CUSTOM_ELEMENT_HANDLING&&hr(o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&($.attributeNameCheck=o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),o.CUSTOM_ELEMENT_HANDLING&&typeof o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&($.allowCustomizedBuiltInElements=o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),_e&&(Xe=!1),b&&(Ee=!0),H&&(G=R({},Nr),F=[],H.html===!0&&(R(G,Lr),R(F,Or)),H.svg===!0&&(R(G,Bt),R(F,Gt),R(F,xt)),H.svgFilters===!0&&(R(G,Ft),R(F,Gt),R(F,xt)),H.mathMl===!0&&(R(G,Ht),R(F,Pr),R(F,xt))),o.ADD_TAGS&&(G===je&&(G=xe(G)),R(G,o.ADD_TAGS,j)),o.ADD_ATTR&&(F===qe&&(F=xe(F)),R(F,o.ADD_ATTR,j)),o.ADD_URI_SAFE_ATTR&&R(Mt,o.ADD_URI_SAFE_ATTR,j),o.FORBID_CONTENTS&&(Ae===cr&&(Ae=xe(Ae)),R(Ae,o.FORBID_CONTENTS,j)),oe&&(G["#text"]=!0),fe&&R(G,["html","head","body"]),G.table&&(R(G,["tbody"]),delete Te.tbody),o.TRUSTED_TYPES_POLICY){if(typeof o.TRUSTED_TYPES_POLICY.createHTML!="function")throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof o.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');P=o.TRUSTED_TYPES_POLICY,ae=P.createHTML("")}else P===void 0&&(P=la(w,s)),P!==null&&typeof ae=="string"&&(ae=P.createHTML(""));te&&te(o),ze=o}},fr=R({},[...Bt,...Ft,...Zs]),gr=R({},[...Ht,...Ks]),hn=function(o){let m=V(o);(!m||!m.tagName)&&(m={namespaceURI:Pe,tagName:"template"});const T=wt(o.tagName),z=wt(m.tagName);return Lt[o.namespaceURI]?o.namespaceURI===pt?m.namespaceURI===ge?T==="svg":m.namespaceURI===dt?T==="svg"&&(z==="annotation-xml"||ht[z]):!!fr[T]:o.namespaceURI===dt?m.namespaceURI===ge?T==="math":m.namespaceURI===pt?T==="math"&&ft[z]:!!gr[T]:o.namespaceURI===ge?m.namespaceURI===pt&&!ft[z]||m.namespaceURI===dt&&!ht[z]?!1:!gr[T]&&(cn[T]||!fr[T]):!!(Ze==="application/xhtml+xml"&&Lt[o.namespaceURI]):!1},de=function(o){et(e.removed,{element:o});try{V(o).removeChild(o)}catch{A(o)}},$e=function(o,m){try{et(e.removed,{attribute:m.getAttributeNode(o),from:m})}catch{et(e.removed,{attribute:null,from:m})}if(m.removeAttribute(o),o==="is")if(Ee||b)try{de(m)}catch{}else try{m.setAttribute(o,"")}catch{}},mr=function(o){let m=null,T=null;if(Oe)o="<remove></remove>"+o;else{const W=Ir(o,/^[\r\n\t ]+/);T=W&&W[0]}Ze==="application/xhtml+xml"&&Pe===ge&&(o='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+o+"</body></html>");const z=P?P.createHTML(o):o;if(Pe===ge)try{m=new h().parseFromString(z,Ze)}catch{}if(!m||!m.documentElement){m=ne.createDocument(Pe,"template",null);try{m.documentElement.innerHTML=It?ae:z}catch{}}const Z=m.body||m.documentElement;return o&&T&&Z.insertBefore(t.createTextNode(T),Z.childNodes[0]||null),Pe===ge?he.call(m,fe?"html":"body")[0]:fe?m.documentElement:Z},br=function(o){return ue.call(o.ownerDocument||o,o,i.SHOW_ELEMENT|i.SHOW_COMMENT|i.SHOW_TEXT|i.SHOW_PROCESSING_INSTRUCTION|i.SHOW_CDATA_SECTION,null)},Ot=function(o){return o instanceof E&&(typeof o.nodeName!="string"||typeof o.textContent!="string"||typeof o.removeChild!="function"||!(o.attributes instanceof p)||typeof o.removeAttribute!="function"||typeof o.setAttribute!="function"||typeof o.namespaceURI!="string"||typeof o.insertBefore!="function"||typeof o.hasChildNodes!="function")},xr=function(o){return typeof l=="function"&&o instanceof l};function me(S,o,m){bt(S,T=>{T.call(e,o,m,ze)})}const vr=function(o){let m=null;if(me(X.beforeSanitizeElements,o,null),Ot(o))return de(o),!0;const T=j(o.nodeName);if(me(X.uponSanitizeElement,o,{tagName:T,allowedTags:G}),Ne&&o.hasChildNodes()&&!xr(o.firstElementChild)&&Q(/<[/\w!]/g,o.innerHTML)&&Q(/<[/\w!]/g,o.textContent)||o.nodeType===st.progressingInstruction||Ne&&o.nodeType===st.comment&&Q(/<[/\w]/g,o.data))return de(o),!0;if(!G[T]||Te[T]){if(!Te[T]&&kr(T)&&($.tagNameCheck instanceof RegExp&&Q($.tagNameCheck,T)||$.tagNameCheck instanceof Function&&$.tagNameCheck(T)))return!1;if(oe&&!Ae[T]){const z=V(o)||o.parentNode,Z=D(o)||o.childNodes;if(Z&&z){const W=Z.length;for(let se=W-1;se>=0;--se){const be=_(Z[se],!0);be.__removalCount=(o.__removalCount||0)+1,z.insertBefore(be,B(o))}}}return de(o),!0}return o instanceof g&&!hn(o)||(T==="noscript"||T==="noembed"||T==="noframes")&&Q(/<\/no(script|embed|frames)/i,o.innerHTML)?(de(o),!0):(_e&&o.nodeType===st.text&&(m=o.textContent,bt([Be,Fe,He],z=>{m=tt(m,z," ")}),o.textContent!==m&&(et(e.removed,{element:o.cloneNode()}),o.textContent=m)),me(X.afterSanitizeElements,o,null),!1)},wr=function(o,m,T){if(Y&&(m==="id"||m==="name")&&(T in t||T in pn))return!1;if(!(Xe&&!Ve[m]&&Q(At,m))){if(!(Le&&Q(Rt,m))){if(!F[m]||Ve[m]){if(!(kr(o)&&($.tagNameCheck instanceof RegExp&&Q($.tagNameCheck,o)||$.tagNameCheck instanceof Function&&$.tagNameCheck(o))&&($.attributeNameCheck instanceof RegExp&&Q($.attributeNameCheck,m)||$.attributeNameCheck instanceof Function&&$.attributeNameCheck(m))||m==="is"&&$.allowCustomizedBuiltInElements&&($.tagNameCheck instanceof RegExp&&Q($.tagNameCheck,T)||$.tagNameCheck instanceof Function&&$.tagNameCheck(T))))return!1}else if(!Mt[m]){if(!Q(it,tt(T,We,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&o!=="script"&&qs(T,"data:")===0&&ur[o])){if(!(ct&&!Q(Ge,tt(T,We,"")))){if(T)return!1}}}}}}return!0},kr=function(o){return o!=="annotation-xml"&&Ir(o,Dt)},yr=function(o){me(X.beforeSanitizeAttributes,o,null);const{attributes:m}=o;if(!m||Ot(o))return;const T={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let z=m.length;for(;z--;){const Z=m[z],{name:W,namespaceURI:se,value:be}=Z,Ke=j(W),Pt=be;let K=W==="value"?Pt:Vs(Pt);if(T.attrName=Ke,T.attrValue=K,T.keepAttr=!0,T.forceKeepAttr=void 0,me(X.uponSanitizeAttribute,o,T),K=T.attrValue,U&&(Ke==="id"||Ke==="name")&&($e(W,o),K=v+K),Ne&&Q(/((--!?|])>)|<\/(style|title)/i,K)){$e(W,o);continue}if(T.forceKeepAttr)continue;if(!T.keepAttr){$e(W,o);continue}if(!ut&&Q(/\/>/i,K)){$e(W,o);continue}_e&&bt([Be,Fe,He],_r=>{K=tt(K,_r," ")});const Tr=j(o.nodeName);if(!wr(Tr,Ke,K)){$e(W,o);continue}if(P&&typeof w=="object"&&typeof w.getAttributeType=="function"&&!se)switch(w.getAttributeType(Tr,Ke)){case"TrustedHTML":{K=P.createHTML(K);break}case"TrustedScriptURL":{K=P.createScriptURL(K);break}}if(K!==Pt)try{se?o.setAttributeNS(se,W,K):o.setAttribute(W,K),Ot(o)?de(o):Mr(e.removed)}catch{$e(W,o)}}me(X.afterSanitizeAttributes,o,null)},fn=function S(o){let m=null;const T=br(o);for(me(X.beforeSanitizeShadowDOM,o,null);m=T.nextNode();)me(X.uponSanitizeShadowNode,m,null),vr(m),yr(m),m.content instanceof a&&S(m.content);me(X.afterSanitizeShadowDOM,o,null)};return e.sanitize=function(S){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,T=null,z=null,Z=null;if(It=!S,It&&(S="<!-->"),typeof S!="string"&&!xr(S))if(typeof S.toString=="function"){if(S=S.toString(),typeof S!="string")throw rt("dirty is not a string, aborting")}else throw rt("toString is not a function");if(!e.isSupported)return S;if(Ye||Nt(o),e.removed=[],typeof S=="string"&&(ie=!1),ie){if(S.nodeName){const be=j(S.nodeName);if(!G[be]||Te[be])throw rt("root node is forbidden and cannot be sanitized in-place")}}else if(S instanceof l)m=mr("<!---->"),T=m.ownerDocument.importNode(S,!0),T.nodeType===st.element&&T.nodeName==="BODY"||T.nodeName==="HTML"?m=T:m.appendChild(T);else{if(!Ee&&!_e&&!fe&&S.indexOf("<")===-1)return P&&u?P.createHTML(S):S;if(m=mr(S),!m)return Ee?null:u?ae:""}m&&Oe&&de(m.firstChild);const W=br(ie?S:m);for(;z=W.nextNode();)vr(z),yr(z),z.content instanceof a&&fn(z.content);if(ie)return S;if(Ee){if(b)for(Z=ye.call(m.ownerDocument);m.firstChild;)Z.appendChild(m.firstChild);else Z=m;return(F.shadowroot||F.shadowrootmode)&&(Z=Ie.call(n,Z,!0)),Z}let se=fe?m.outerHTML:m.innerHTML;return fe&&G["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&Q(an,m.ownerDocument.doctype.name)&&(se="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+se),_e&&bt([Be,Fe,He],be=>{se=tt(se,be," ")}),P&&u?P.createHTML(se):se},e.setConfig=function(){let S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Nt(S),Ye=!0},e.clearConfig=function(){ze=null,Ye=!1},e.isValidAttribute=function(S,o,m){ze||Nt({});const T=j(S),z=j(o);return wr(T,z,m)},e.addHook=function(S,o){typeof o=="function"&&et(X[S],o)},e.removeHook=function(S,o){if(o!==void 0){const m=Ws(X[S],o);return m===-1?void 0:js(X[S],m,1)[0]}return Mr(X[S])},e.removeHooks=function(S){X[S]=[]},e.removeAllHooks=function(){X=$r()},e}var ia=on();const ca={class:"flex-1"},ua={class:"flex items-center"},da={class:"relative"},pa={key:1,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5 sticky top-[40px] z-10"},ha={key:0,class:"flex flex-col gap-2 text-gray-500 text-xs p-3"},fa={key:1,class:"flex flex-col gap-1"},ga=["onClick"],ma=["onClick"],ba={key:2,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5"},xa={class:"flex items-center gap-2"},va={class:"flex-1 overflow-y-auto overflow-x-hidden"},wa={class:"px-0.5"},ka={class:"mb-1"},ya={key:0},Ta={key:1,class:"text-gray-500"},_a={class:"flex items-center justify-between mt-0.5"},Ea={class:"flex items-center gap-1"},Sa={class:"flex flex-wrap gap-1"},Ca=["onClick"],Aa={key:0,class:"flex items-start gap-1.5 max-w-full"},Ra={class:"flex-1 bg-[var(--bg-color)] rounded-lg p-2 shadow-sm break-words"},Da={class:"text-[var(--text-color)] leading-tight text-[15px]"},Ma=["innerHTML"],Ia={key:1,class:"h-full flex items-center justify-center text-gray-500 text-[15px]"},La={class:"border-t border-[var(--border-color)] bg-[var(--bg-color)] w-full input-bar-container"},Na={class:"flex items-center justify-between relative z-10"},Oa={class:"flex items-center gap-2"},Pa={class:"text-sm font-medium"},za={class:"p-0.5 max-w-full"},$a=["placeholder","disabled"],Ua={class:"flex items-center justify-between"},Ba={class:"flex items-center gap-1"},Fa={class:"relative model-selector"},Ha={class:"max-h-[60vh] overflow-y-auto"},Ga=["onClick"],Wa={class:"flex items-center gap-2"},ja=["disabled"],qa={class:"flex-1 overflow-y-auto p-4"},Va={class:"flex-1 min-w-0"},Xa={class:"flex items-center gap-2"},Ya={class:"font-medium text-gray-900 truncate"},Za={class:"text-xs text-gray-400"},Ka={key:0,class:"text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full"},Qa={class:"text-xs text-gray-500 break-all"},Ja={class:"flex items-center gap-1"},eo=["onClick"],to=["onClick"],ro={key:0,class:"text-center text-gray-500 py-8"},no={key:4,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},so={class:"add-model-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 flex flex-col"},ao={class:"p-4 space-y-4"},oo={key:0,class:"mt-4"},lo={class:"flex items-center gap-2"},io={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},co={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},uo={class:"p-4 border-t border-[var(--border-color)] flex gap-2"},po=["disabled"],ho={key:0,class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},fo=Zt({__name:"Main",setup(r){const e=Wr(),t=Gr(),n=Et(),s=Kt(),a=Hr(),c=Ur(),l=Qt(),g=M(!1),i=M(!1),p=M(null),E=M(""),h=M({entryDisplayConfig:{skipJsCss:!0,excludedPathPatterns:[]}}),w=M(!1),x=M(""),_=M(!1),A=M(!1),B=M(!1),D=M({showName:"",baseUrl:"",modelName:"",apiKey:""}),V=M(null),P=M(!1);let ae=null;const ne=M(!0),ue=M(!0),ye=J;let he=0;const Ie=M(null),X=Ue(()=>!t.currentDatabase||t.currentDatabaseId===J?"请选择程序数据再对话":t.currentDatabase.dataId?"Type your message...":"请先录制程序数据然后再对话"),Be=Ue(()=>!t.currentDatabase||!t.currentDatabase.dataId||s.status==="waiting"),Fe=()=>{var b;return t.databases.length===1&&t.currentDatabaseId===J?"当前无可用的程序数据，请使用 Debug with XCodeMap 录制程序数据。":t.currentDatabaseId===J?(console.log("请先选择程序数据再对话"),"请先选择程序数据再对话"):(b=t.currentDatabase)!=null&&b.dataId?"你好！我是你的代码排查助手 XCodeMap。请告诉我具体是哪一个函数调用（CallID_Number）让你感到困惑，或者提供相关的类名、函数名等信息，我将尽力为你分析和解释。":(console.log("请先录制程序数据然后再对话"),"请先录制程序数据然后再对话")},He=async()=>{var u;const b=l.getText();if(b.trim()){if(!((u=t.currentDatabase)!=null&&u.dataId)){n.setError("当前没有数据，不可聊天。请先选择程序数据。或者使用 Debug with XCodeMap 创建新的程序数据。");return}if(!a.currentModel){n.setError("请先选择模型再发送消息"),_.value=!0;return}l.clearText(),await e.sendMessage(b)}},At=()=>{g.value=!g.value},Rt=async b=>{var U;const u=t.currentDatabase,Y=!u||u.id!==b;if(he=0,t.setCurrentDatabaseId(b),g.value=!1,Y){if(!await gt(b,((U=t.currentDatabase)==null?void 0:U.dataId)||"")){n.setError("Failed to switch process data");return}e.createNewChat(),p.value=null,ne.value=!0,i.value=!0}},Ge=async()=>{var b;if((b=t.currentDatabase)!=null&&b.dataId)try{const u=await Nn({processId:t.currentDatabase.dataId,first:ne.value,filterText:E.value}),Y=JSON.stringify(u),U=JSON.stringify(p.value);Y!==U&&(console.log("Data has changed, updating treeData",Y),p.value=u),ne.value=!1}catch(u){console.error("Failed to fetch tree data:",u)}},We=async()=>{const b=[...t.databases],u=t.currentDatabase;await t.getDatabase();const Y=t.databases,U=t.currentDatabase,v=new Set(b.map(H=>H.id)),oe=Y.filter(H=>!v.has(H.id));b.length===0||b.length===1&&b[0].id;const ie=oe.filter(H=>!H.id.toLowerCase().includes("local")&&!H.id.toLowerCase().includes("remote"));if(ie.length>0){const H=ie[0];if(t.setCurrentDatabaseId(H.id),!await gt(H.id,H.dataId||"")){n.setError("Failed to switch to new process data");return}e.createNewChat(),p.value=null,ne.value=!0,i.value=!0,g.value=!1,he=0;return}if(u&&u.id!==ye&&!U){console.log("Current database is no longer available, resetting to default"),t.setCurrentDatabaseId(ye),await gt(ye,"")||n.setError("Failed to switch process data"),g.value=!0,p.value=null,ne.value=!0,i.value=!1,e.createNewChat(),he=0;return}if(u&&U&&u.id===U.id&&u.dataId!==U.dataId&&!await gt(U.id,U.dataId||"")){n.setError("Failed to switch process data");return}U&&U.id!==ye&&(U.serverSelected?he=0:(he++,he>=3&&(console.log("Database consistency check failed 3 times, resetting to default"),t.setCurrentDatabaseId(ye),g.value=!0,p.value=null,ne.value=!0,i.value=!1,e.createNewChat(),he=0)))};_n(()=>{Ge()});const Dt=b=>{b.labelKey==="url"&&b.labelValue&&window.open(b.labelValue,"_blank"),console.log("Clicked tree node:",b)},it=b=>{const u=h.value.entryDisplayConfig.excludedPathPatterns.indexOf(b);u>-1&&(h.value.entryDisplayConfig.excludedPathPatterns.splice(u,1),F())},G=()=>{w.value=!0,$t(()=>{const b=document.querySelector(".input-new-tag input");b&&b.focus()})},je=()=>{x.value&&(h.value.entryDisplayConfig.excludedPathPatterns.push(x.value),F()),w.value=!1,x.value=""},F=async()=>{try{await On(h.value)||n.setError("Failed to update filter configuration")}catch(b){console.error("Failed to update filter configuration:",b)}},qe=b=>{const u=b.target;u.closest(".model-selector")||(_.value=!1),u.closest(".model-manager-modal")||(A.value=!1)},$=async()=>{_.value=!1;try{await a.getModelConfigData(),A.value=!0}catch(b){console.error("Failed to open model manager:",b),n.setError("Failed to open model manager")}},Te=()=>{A.value=!1},Ve=()=>{B.value=!0,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""}},Le=()=>{B.value=!1,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""},V.value=null},Xe=async()=>{const b={showName:D.value.showName.trim(),baseUrl:D.value.baseUrl.trim(),modelName:D.value.modelName.trim(),apiKey:D.value.apiKey.trim()};if(!b.showName||!b.baseUrl||!b.modelName||!b.apiKey){n.setError("请填写所有必填字段");return}P.value=!0,V.value=null;const u={uuid:crypto.randomUUID(),showName:b.showName,baseUrl:b.baseUrl,modelName:b.modelName,apiKey:b.apiKey},Y=await Pn(u);if(!Y.success){V.value={success:!1,message:Y.error||"模型连通性测试失败"},P.value=!1;return}const U=await zn(u);U.success?(Le(),await a.getModelConfigData()):V.value={success:!1,message:U.error||"添加模型失败"},P.value=!1},ct=async b=>{const u=await $n(b);u.success?await a.getModelConfigData():n.setError(u.error||"删除模型失败")},ut=async b=>{const u=await Un(b);u.success?await a.getModelConfigData():n.setError(u.error||"设置默认模型失败")},_e=async(b,u)=>{if(u.stopPropagation(),b.id===J){n.setError("不能删除默认程序数据");return}if(b.active){n.setError("不能删除正在运行的程序数据");return}await t.deleteDatabase(b.id)?t.currentDatabaseId===b.id&&t.setCurrentDatabaseId(J):n.setError("删除程序数据失败")};Fr(()=>{c.initTheme(),We(),Ge(),En().then(b=>{b.success&&b.data&&(h.value.entryDisplayConfig=b.data.entryDisplayConfig)}),a.getModelConfigData(),document.addEventListener("click",qe),window.addEventListener("textarea-content-changed",Oe),ae=window.setInterval(()=>{We(),Ge()},1e3)}),Sn(()=>{ae!==null&&(clearInterval(ae),ae=null),document.removeEventListener("click",qe),window.removeEventListener("textarea-content-changed",Oe)});const Ne=(b,u)=>u!=="tool"?b.replace(/`?CallID_(\d+)`?/g,"[CallID_$1](./findFunc?callId=$1)"):b,fe=b=>{I.setOptions({breaks:!0,gfm:!0,pedantic:!0});const u=I.parse(b);return ia.sanitize(u,{ALLOWED_TAGS:["p","br","strong","em","code","pre","blockquote","ul","ol","li","h1","h2","h3","h4","h5","h6","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title","class"]})},Ye=b=>{const u=b.target;u.style.height="auto",u.style.height=Math.min(u.scrollHeight,200)+"px"},Oe=()=>{$t(()=>{const b=document.querySelector(".input");b&&(b.style.height="auto",b.style.height=Math.min(b.scrollHeight,200)+"px")})},Ee=()=>{$t(()=>{Ie.value&&(Ie.value.scrollTop=Ie.value.scrollHeight)})};return kt(()=>{var b;return(b=e.currentChat)==null?void 0:b.messages},()=>{var b;Ee(),(b=e.currentChat)!=null&&b.messages&&e.currentChat.messages.length>0&&(i.value=!1)},{deep:!0}),kt(()=>l.message,()=>{l.message.trim()&&(i.value=!1)}),(b,u)=>{var Y,U;return k(),y("div",{class:C(["h-screen w-full flex flex-col overflow-x-hidden",(f(c).theme==="dark","bg-[var(--bg-color)]")])},[f(n).error?(k(),y("div",{key:0,class:C(["fixed top-0 left-0 right-0 px-4 py-3 z-[9999] flex items-center justify-between",f(c).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[d("span",ca,q(f(n).error.message),1),d("button",{onClick:u[0]||(u[0]=v=>f(n).clearError()),class:C(["ml-4 p-1 rounded-full hover:bg-opacity-20",f(c).theme==="dark"?"hover:bg-red-100":"hover:bg-red-200"])},u[18]||(u[18]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),2)],2)):O("",!0),d("div",{class:C(["border-b py-0.5 px-0.5 flex items-center justify-between sticky top-0 z-20 bg-[var(--bg-color)]",(f(c).theme==="dark","border-[var(--border-color)]")])},[d("div",ua,[d("div",da,[d("button",{onClick:At,class:C(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,q(f(t).currentDatabase?f(t).currentDatabase.name:"待选择程序数据(取消行号X图标)"),1),(k(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":g.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[19]||(u[19]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2)])]),d("button",{onClick:u[1]||(u[1]=()=>{f(e).createNewChat(),ue.value=!0}),class:C(["p-1 rounded-full",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"]),title:"新建聊天"},u[20]||(u[20]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)],2),g.value||f(t).currentDatabaseId===f(J)?(k(),y("div",pa,[f(t).databases.length===1&&f(t).currentDatabaseId===f(J)?(k(),y("div",ha,u[21]||(u[21]=[Cn('<div class="flex items-center" data-v-d4fb2280><span data-v-d4fb2280>当前无可用的程序数据，请使用 <img src="'+Hn+'" alt="XCodeMap Logo" class="w-3 h-3 inline align-text-bottom" data-v-d4fb2280> Debug with XCodeMap 录制程序数据。</span></div><div class="text-xs opacity-75 leading-relaxed space-y-1" data-v-d4fb2280><div class="font-medium" data-v-d4fb2280>使用步骤：</div><div class="space-y-1 pl-2" data-v-d4fb2280><div data-v-d4fb2280>1. 点击 Debug with XCodeMap 按钮</div><div data-v-d4fb2280>2. 开始录制</div><div data-v-d4fb2280>3. 查看请求列表或者点击行号栏 X 图标以回放现场</div><div data-v-d4fb2280>4. 关闭录制</div><div data-v-d4fb2280>5. 请尽量按需录制，避免长时间录制，造成内存占用过高</div><div data-v-d4fb2280>6. 更多功能查看配置页面</div></div></div>',2)]))):(k(),y("div",fa,[(k(!0),y(ve,null,Ce(f(t).databases,v=>(k(),y("div",{key:v.id,class:C(["flex items-center justify-between w-full px-2 py-0.5 rounded-lg text-xs font-medium text-left border-2 transition-all duration-150 focus:outline-none cursor-pointer",[v.id===f(t).currentDatabaseId?f(c).theme==="dark"?"button-selected-dark":"button-selected-light":f(c).theme==="dark"?"button-hover-dark":"button-hover-light"]])},[d("div",{class:"flex-1",onClick:oe=>Rt(v.id)},q(v.name),9,ga),v.id!==f(J)&&!v.active?(k(),y("button",{key:0,onClick:oe=>_e(v,oe),class:C(["ml-2 p-0.5 rounded-full transition-colors",f(c).theme==="dark"?"hover:bg-red-700 text-red-400 hover:text-red-200":"hover:bg-red-100 text-red-500 hover:text-red-700"]),title:"删除程序数据"},u[22]||(u[22]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)]),10,ma)):O("",!0)],2))),128))]))])):O("",!0),f(t).currentDatabase&&f(t).currentDatabase.active?(k(),y("div",ba,[d("div",xa,[d("span",{class:C(["px-1.5 py-0.5 text-xs rounded-full opacity-75",{"bg-green-50 text-green-600":f(t).currentDatabase.recordState==="recording","bg-gray-50 text-gray-500":f(t).currentDatabase.recordState==="idle","bg-yellow-50 text-yellow-600":f(t).currentDatabase.recordState==="paused"}])},q(f(t).currentDatabase.recordState),3),f(t).currentDatabase.recordState==="idle"?(k(),y("button",{key:0,onClick:u[2]||(u[2]=v=>f(t).startRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-green-50 text-green-600 hover:bg-green-200 hover:text-green-800 border border-green-200 hover:border-green-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"开始录制"}," 开始录制 ")):O("",!0),f(t).currentDatabase.recordState==="recording"?(k(),y("button",{key:1,onClick:u[3]||(u[3]=v=>f(t).endRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-red-50 text-red-600 hover:bg-red-200 hover:text-red-800 border border-red-200 hover:border-red-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"结束录制"}," 结束录制 ")):O("",!0),f(t).currentDatabase.recordState==="paused"?(k(),y("button",{key:2,onClick:u[4]||(u[4]=v=>f(t).restartRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-yellow-50 text-yellow-600 hover:bg-yellow-200 hover:text-yellow-800 border border-yellow-200 hover:border-yellow-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"重新录制"}," 重新录制 ")):O("",!0)])])):O("",!0),d("div",va,[f(t).currentDatabaseId!==f(J)?(k(),y("div",{key:0,class:C([(f(c).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]"),"border-b border-t"])},[d("div",wa,[d("div",{onClick:u[5]||(u[5]=v=>i.value=!i.value),class:C(["flex items-center cursor-pointer rounded-lg px-1 py-0.5 border-b border-[var(--border-color)] bg-[var(--undercaret-bg-color)] transition-colors duration-150",(f(c).theme==="dark","hover:bg-[var(--header-hover-bg-color)]")])},[(k(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-90":i.value,"text-[var(--text-color)]":!0}]),viewBox:"0 0 20 20",fill:"currentColor"},u[23]||(u[23]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),u[24]||(u[24]=d("span",{class:"text-xs font-medium px-1 text-[var(--text-color)]"},"请求与线程入口",-1))],2)]),i.value?(k(),y("div",{key:0,class:C(["p-1 bg-[var(--undercaret-bg-color)] overflow-y-auto",(f(c).theme==="dark","border-t border-[var(--border-color)]")])},[d("div",ka,[Se(d("input",{"onUpdate:modelValue":u[6]||(u[6]=v=>E.value=v),type:"text",placeholder:"搜索网络请求...",class:C(["w-full px-2 py-0.5 rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400":"bg-white border-gray-300 text-gray-700"])},null,2),[[Re,E.value]])]),p.value?(k(),y("div",ya,[An(ns,{nodes:p.value.rootNodes,onNodeClick:Dt},null,8,["nodes"])])):(k(),y("div",Ta,"Loading tree data...")),d("div",_a,[d("div",Ea,[d("span",{class:C(["text-xs opacity-50",f(c).theme==="dark"?"text-gray-400":"text-gray-600"])},"排除请求：",2),d("label",{class:C(["flex items-center gap-1 text-xs cursor-pointer opacity-50 hover:opacity-100 transition-opacity",f(c).theme==="dark"?"text-gray-500":"text-gray-400"])},[Se(d("input",{type:"checkbox","onUpdate:modelValue":u[7]||(u[7]=v=>h.value.entryDisplayConfig.skipJsCss=v),onChange:F,class:C(["rounded focus:ring-0 focus:ring-offset-0 h-2.5 w-2.5 cursor-pointer",f(c).theme==="dark"?"bg-gray-800 border-gray-600 text-gray-500":"border-gray-300 text-gray-400"])},null,34),[[Rn,h.value.entryDisplayConfig.skipJsCss]]),u[25]||(u[25]=d("span",{class:"text-[11px]"},"忽略css/js",-1))],2),d("div",Sa,[(k(!0),y(ve,null,Ce(h.value.entryDisplayConfig.excludedPathPatterns,v=>(k(),y("div",{key:v,class:C(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"])},[d("span",null,q(v),1),d("button",{onClick:oe=>it(v),class:C(f(c).theme==="dark"?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700")},u[26]||(u[26]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,Ca)],2))),128)),w.value?Se((k(),y("input",{key:0,"onUpdate:modelValue":u[8]||(u[8]=v=>x.value=v),class:C(["input-new-tag px-1 py-0 border rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200":"border-gray-300 text-gray-700"]),placeholder:"输入排除表达式，* 匹配一级路径，** 匹配多级路径",onKeyup:Dn(je,["enter"]),onBlur:je},null,34)),[[Re,x.value]]):(k(),y("button",{key:1,onClick:G,class:C(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-blue-400":"bg-gray-100 hover:bg-gray-200 text-blue-700"])},u[27]||(u[27]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),d("span",null,"New Filter",-1)]),2))])]),d("button",{onClick:u[9]||(u[9]=v=>i.value=!1),class:C(["rounded-full flex items-center",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"]),title:"收起网络请求"},u[28]||(u[28]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),2)])],2)):O("",!0)],2)):O("",!0),d("div",{ref_key:"messageContainerRef",ref:Ie,class:"p-0.5 space-y-0.5 w-full border-b border-[var(--border-color)]"},[f(e).currentChat?(k(),y(ve,{key:0},[f(e).currentChat.messages.length===0?(k(),y("div",Aa,[d("div",Ra,[d("p",Da,[(k(),Mn(ss,{key:(Y=f(e).currentChat)==null?void 0:Y.id,text:Fe(),speed:20,onComplete:u[10]||(u[10]=v=>ue.value=!1)},null,8,["text"]))])])])):O("",!0),(k(!0),y(ve,null,Ce(f(e).currentChat.messages,v=>(k(),y("div",{key:v.messageId,class:C(["flex items-start gap-1.5 max-w-full",{"justify-end":v.role==="user"}])},[d("div",{class:C(["flex-1 rounded-lg p-2 shadow-sm break-words min-w-0",v.role==="user"?(f(c).theme==="dark","bg-[var(--undercaret-bg-color)]"):(f(c).theme==="dark","bg-[var(--bg-color)]")])},[d("div",{class:C([v.role==="user"?(f(c).theme==="dark","text-[var(--text-color)]"):(f(c).theme==="dark","text-[var(--text-color)]"),"leading-tight text-[15px] markdown-content break-words whitespace-pre-wrap"]),innerHTML:fe(Ne(v.content,v.role))},null,10,Ma)],2)],2))),128))],64)):(k(),y("div",Ia," 请选择程序数据后再对话 "))],512)]),d("div",La,[f(s).status==="waiting"&&f(t).currentDatabase&&f(t).currentDatabaseId!==f(J)?(k(),y("div",{key:0,class:C(["border-t border-b py-2 px-3 relative overflow-hidden",f(c).theme==="dark"?"bg-gradient-to-r from-blue-900/30 to-purple-900/30 border-blue-500/50 text-blue-200":"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-300 text-blue-700"])},[u[30]||(u[30]=d("div",{class:"absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 animate-pulse"},null,-1)),d("div",Na,[d("div",Oa,[u[29]||(u[29]=d("div",{class:"flex space-x-1"},[d("div",{class:"w-1.5 h-1.5 bg-current rounded-full animate-bounce",style:{"animation-delay":"0ms"}}),d("div",{class:"w-1.5 h-1.5 bg-current rounded-full animate-bounce",style:{"animation-delay":"150ms"}}),d("div",{class:"w-1.5 h-1.5 bg-current rounded-full animate-bounce",style:{"animation-delay":"300ms"}})],-1)),d("span",Pa,q(f(s).statusMessage||"正在处理中..."),1)])])],2)):O("",!0),d("div",za,[Se(d("textarea",{"onUpdate:modelValue":u[11]||(u[11]=v=>f(l).message=v),class:"input w-full resize-none mb-0.5",rows:"1",placeholder:X.value,disabled:Be.value,onInput:Ye,style:{"min-height":"28px","max-height":"200px","overflow-y":"auto"}},null,40,$a),[[Re,f(l).message]]),d("div",Ua,[d("div",Ba,[d("span",{class:C(["text-xs",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])},"模型",2),d("div",Fa,[d("button",{onClick:u[12]||(u[12]=v=>_.value=!_.value),class:C(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,q(((U=f(a).currentModel)==null?void 0:U.showName)||"请选择模型"),1),(k(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:C(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":_.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[31]||(u[31]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2),_.value?(k(),y("div",{key:0,class:C(["absolute bottom-full left-0 mb-2 w-80 border rounded-lg shadow-lg z-50",f(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[d("div",{class:C(["p-2 border-b",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("span",{class:C(["text-sm font-medium",f(c).theme==="dark"?"text-gray-200":"text-gray-700"])},"选择模型",2)],2),d("div",Ha,[(k(!0),y(ve,null,Ce(f(a).availableModels,v=>(k(),y("button",{key:v.uuid,onClick:()=>{f(a).setCurrentModel(v.uuid),_.value=!1},class:C(["w-full px-3 py-2 text-left text-sm transition-all duration-150 border-2 focus:outline-none",{"button-selected-dark":f(a).currentModelUuid===v.uuid&&f(c).theme==="dark","button-selected-light":f(a).currentModelUuid===v.uuid&&f(c).theme==="light","button-hover-dark":f(c).theme==="dark"&&f(a).currentModelUuid!==v.uuid,"button-hover-light":f(c).theme==="light"&&f(a).currentModelUuid!==v.uuid}])},[d("div",Wa,[d("span",null,q(v.showName),1),d("span",{class:C(["text-xs opacity-75",[f(a).currentModelUuid===v.uuid?f(c).theme==="dark"?"text-gray-300":"text-blue-500":f(c).theme==="dark"?"text-gray-500":"text-gray-400"]])},"("+q(v.modelName)+")",3)])],10,Ga))),128)),f(a).availableModels.length===0?(k(),y("div",{key:0,class:C(["px-3 py-2 text-sm",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])}," 暂无可用模型 ",2)):O("",!0)]),d("div",{class:C(["border-t",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("button",{onClick:$,class:C(["w-full px-3 py-2 text-left text-sm transition-colors",f(c).theme==="dark"?"text-blue-400 hover:bg-gray-700":"text-blue-600 hover:bg-blue-50"])}," 管理模型 ",2)],2)],2)):O("",!0)])]),d("button",{onClick:He,class:C(["p-0.5 rounded-full transition-colors",[f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400 hover:text-gray-200":"hover:bg-gray-100 text-gray-600",{"opacity-50 cursor-not-allowed":!f(l).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting"}]]),disabled:!f(l).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting",title:"发送消息"},u[32]||(u[32]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),10,ja)])])]),A.value?(k(),y("div",{key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:Te},[d("div",{class:"model-manager-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 max-h-[80vh] flex flex-col",onClick:u[13]||(u[13]=Ln(()=>{},["stop"]))},[d("div",{class:"p-2 border-b border-[var(--border-color)] flex items-center justify-between"},[u[34]||(u[34]=d("h3",{class:"text-base font-medium text-[var(--text-color)]"},"模型管理",-1)),d("button",{onClick:Te,class:"text-gray-400 hover:text-gray-600"},u[33]||(u[33]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",qa,[(k(!0),y(ve,null,Ce(f(a).availableModels,v=>{var oe,ie;return k(),y("div",{key:v.uuid,class:"flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-1"},[d("div",Va,[d("div",Xa,[d("span",Ya,q(v.showName),1),d("span",Za,"("+q(v.modelName)+")",1),((oe=f(a).modelConfig)==null?void 0:oe.defaultModelId)===v.uuid?(k(),y("span",Ka," 默认 ")):O("",!0)]),d("div",Qa,q(v.baseUrl),1)]),d("div",Ja,[((ie=f(a).modelConfig)==null?void 0:ie.defaultModelId)!==v.uuid?(k(),y("button",{key:0,onClick:H=>ut(v),class:"flex-shrink-0 text-blue-500 hover:text-blue-700",title:"设为默认"},u[35]||(u[35]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]),8,eo)):O("",!0),d("button",{onClick:H=>ct(v),class:"flex-shrink-0 text-red-500 hover:text-red-700",title:"删除模型"},u[36]||(u[36]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,to)])])}),128)),f(a).availableModels.length===0?(k(),y("div",ro," 暂无已添加的模型 ")):O("",!0)]),d("div",{class:"p-4 border-t border-[var(--border-color)]"},[d("button",{onClick:Ve,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"},u[37]||(u[37]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),In(" 添加新模型 ")]))])])])):O("",!0),B.value?(k(),y("div",no,[d("div",so,[d("div",{class:"p-4 border-b border-[var(--border-color)] flex items-center justify-between"},[u[39]||(u[39]=d("h3",{class:"text-lg font-medium text-[var(--text-color)]"},"添加新模型",-1)),d("button",{onClick:Le,class:"text-gray-400 hover:text-gray-600"},u[38]||(u[38]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",ao,[d("div",null,[u[40]||(u[40]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型显示名称",-1)),Se(d("input",{"onUpdate:modelValue":u[14]||(u[14]=v=>D.value.showName=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入模型显示名称"},null,512),[[Re,D.value.showName]]),u[41]||(u[41]=d("p",{class:"mt-1 text-xs text-gray-500"},"模型需要兼容OpenAI的API，需要支持 FunctionCall。",-1))]),d("div",null,[u[42]||(u[42]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Base URL",-1)),Se(d("input",{"onUpdate:modelValue":u[15]||(u[15]=v=>D.value.baseUrl=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: https://api.openai.com/v1"},null,512),[[Re,D.value.baseUrl]]),u[43]||(u[43]=d("p",{class:"mt-1 text-xs text-gray-500"},"例如: https://dashscope.aliyuncs.com/compatible-mode/v1 或者 https://api.deepseek.com/v1 ",-1))]),d("div",null,[u[44]||(u[44]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型名称",-1)),Se(d("input",{"onUpdate:modelValue":u[16]||(u[16]=v=>D.value.modelName=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: gpt-3.5-turbo"},null,512),[[Re,D.value.modelName]]),u[45]||(u[45]=d("p",{class:"mt-1 text-xs text-gray-500"},"国内推荐阿里云qwen-max-latest 或者 deepseek-chat，国外推荐 Claude Sonnet 系列",-1))]),d("div",null,[u[46]||(u[46]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"API Key",-1)),Se(d("input",{"onUpdate:modelValue":u[17]||(u[17]=v=>D.value.apiKey=v),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入API Key"},null,512),[[Re,D.value.apiKey]])]),V.value?(k(),y("div",oo,[d("div",{class:C(["p-3 rounded-lg",{"bg-green-50 text-green-700":V.value.success,"bg-red-50 text-red-700":!V.value.success}])},[d("div",lo,[V.value.success?(k(),y("svg",io,u[47]||(u[47]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(k(),y("svg",co,u[48]||(u[48]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 00-1.414-1.414L10 10l-1.293-1.293z","clip-rule":"evenodd"},null,-1)]))),d("span",null,q(V.value.message),1)])],2)])):O("",!0)]),d("div",uo,[d("button",{onClick:Le,class:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),d("button",{onClick:Xe,class:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",disabled:P.value},[P.value?(k(),y("svg",ho,u[49]||(u[49]=[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):O("",!0),d("span",null,q(P.value?"测试中...":"保存"),1)],8,po)])])])):O("",!0)],2)}}}),go=Br(fo,[["__scopeId","data-v-d4fb2280"]]),ir=Bn(go);ir.use(Fn());ir.mount("#app");window.$vm=ir._instance;
