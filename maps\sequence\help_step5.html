<html>
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <title>help_step5</title>
    <link href="../assets/xcodemap/doc.css" rel="stylesheet">
    <style>
        /* cspell:disable-file */
        /* webkit printing magic: print all background colors */
        html {
            -webkit-print-color-adjust: exact;
        }

        * {
            box-sizing: border-box;
            -webkit-print-color-adjust: exact;
        }

        html,
        body {
            margin: 0;
            padding: 0;
        }

        @media only screen {
            body {
                margin: 2em auto;
                max-width: 900px;
                color: rgb(55, 53, 47);
            }
        }

        body {
            line-height: 1.5;
            /*
            white-space: pre-wrap;
            */
        }

        a,
        a.visited {
            color: inherit;
            text-decoration: underline;
        }

        .pdf-relative-link-path {
            font-size: 80%;
            color: #444;
        }

        h1,
        h2,
        h3 {
            letter-spacing: -0.01em;
            line-height: 1.2;
            font-weight: 600;
            margin-bottom: 0;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-top: 0;
            margin-bottom: 0.75em;
        }

        h1 {
            font-size: 1.875rem;
            margin-top: 1.875rem;
        }

        h2 {
            font-size: 1.5rem;
            margin-top: 1.5rem;
        }

        h3 {
            font-size: 1.25rem;
            margin-top: 1.25rem;
        }

        .source {
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 1.5em;
            word-break: break-all;
        }

        .callout {
            border-radius: 3px;
            padding: 1rem;
        }

        figure {
            margin: 1.25em 0;
            page-break-inside: avoid;
        }

        figcaption {
            opacity: 0.5;
            font-size: 85%;
            margin-top: 0.5em;
        }

        mark {
            background-color: transparent;
        }

        .indented {
            padding-left: 1.5em;
        }

        hr {
            background: transparent;
            display: block;
            width: 100%;
            height: 1px;
            visibility: visible;
            border: none;
            border-bottom: 1px solid rgba(55, 53, 47, 0.09);
        }

        img {
            max-width: 100%;
        }

        @media only print {
            img {
                max-height: 100vh;
                object-fit: contain;
            }
        }

        @page {
            margin: 1in;
        }

        .collection-content {
            font-size: 0.875rem;
        }

        .column-list {
            display: flex;
            justify-content: space-between;
        }

        .column {
            padding: 0 1em;
        }

        .column:first-child {
            padding-left: 0;
        }

        .column:last-child {
            padding-right: 0;
        }

        .table_of_contents-item {
            display: block;
            font-size: 0.875rem;
            line-height: 1.3;
            padding: 0.125rem;
        }

        .table_of_contents-indent-1 {
            margin-left: 1.5rem;
        }

        .table_of_contents-indent-2 {
            margin-left: 3rem;
        }

        .table_of_contents-indent-3 {
            margin-left: 4.5rem;
        }

        .table_of_contents-link {
            text-decoration: none;
            opacity: 0.7;
            border-bottom: 1px solid rgba(55, 53, 47, 0.18);
        }

        table,
        th,
        td {
            border: 1px solid rgba(55, 53, 47, 0.09);
            border-collapse: collapse;
        }

        table {
            border-left: none;
            border-right: none;
        }

        th,
        td {
            font-weight: normal;
            padding: 0.25em 0.5em;
            line-height: 1.5;
            min-height: 1.5em;
            text-align: left;
        }

        th {
            color: rgba(55, 53, 47, 0.6);
        }

        ol,
        ul {
            margin: 0;
            margin-block-start: 0.6em;
            margin-block-end: 0.6em;
        }

        li > ol:first-child,
        li > ul:first-child {
            margin-block-start: 0.6em;
        }

        ul > li {
            list-style: disc;
        }

        ul.to-do-list {
            padding-inline-start: 0;
        }

        ul.to-do-list > li {
            list-style: none;
        }

        .to-do-children-checked {
            text-decoration: line-through;
            opacity: 0.375;
        }

        ul.toggle > li {
            list-style: none;
        }

        ul {
            padding-inline-start: 1.7em;
        }

        ul > li {
            padding-left: 0.1em;
        }

        ol {
            padding-inline-start: 1.6em;
        }

        ol > li {
            padding-left: 0.2em;
        }

        .mono ol {
            padding-inline-start: 2em;
        }

        .mono ol > li {
            text-indent: -0.4em;
        }

        .toggle {
            padding-inline-start: 0em;
            list-style-type: none;
        }

        /* Indent toggle children */
        .toggle > li > details {
            padding-left: 1.7em;
        }

        .toggle > li > details > summary {
            margin-left: -1.1em;
        }

        .selected-value {
            display: inline-block;
            padding: 0 0.5em;
            background: rgba(206, 205, 202, 0.5);
            border-radius: 3px;
            margin-right: 0.5em;
            margin-top: 0.3em;
            margin-bottom: 0.3em;
            white-space: nowrap;
        }

        .collection-title {
            display: inline-block;
            margin-right: 1em;
        }

        .page-description {
            margin-bottom: 2em;
        }

        .simple-table {
            margin-top: 1em;
            font-size: 0.875rem;
            empty-cells: show;
        }

        .simple-table td {
            height: 29px;
            min-width: 120px;
        }

        .simple-table th {
            height: 29px;
            min-width: 120px;
        }

        .simple-table-header-color {
            background: rgb(247, 246, 243);
            color: black;
        }

        .simple-table-header {
            font-weight: 500;
        }

        time {
            opacity: 0.5;
        }

        .icon {
            display: inline-block;
            max-width: 1.2em;
            max-height: 1.2em;
            text-decoration: none;
            vertical-align: text-bottom;
            margin-right: 0.5em;
        }

        img.icon {
            border-radius: 3px;
        }

        .user-icon {
            width: 1.5em;
            height: 1.5em;
            border-radius: 100%;
            margin-right: 0.5rem;
        }

        .user-icon-inner {
            font-size: 0.8em;
        }

        .text-icon {
            border: 1px solid #000;
            text-align: center;
        }

        .page-cover-image {
            display: block;
            object-fit: cover;
            width: 100%;
            max-height: 30vh;
        }

        .page-header-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .page-header-icon-with-cover {
            margin-top: -0.72em;
            margin-left: 0.07em;
        }

        .page-header-icon img {
            border-radius: 3px;
        }

        .link-to-page {
            margin: 1em 0;
            padding: 0;
            border: none;
            font-weight: 500;
        }

        p > .user {
            opacity: 0.5;
        }

        td > .user,
        td > time {
            white-space: nowrap;
        }

        input[type="checkbox"] {
            transform: scale(1.5);
            margin-right: 0.6em;
            vertical-align: middle;
        }

        p {
            margin-top: 0.5em;
            margin-bottom: 0.5em;
        }

        .image {
            border: none;
            margin: 1.5em 0;
            padding: 0;
            border-radius: 0;
            text-align: center;
        }

        .code,
        code {
            background: rgba(135, 131, 120, 0.15);
            border-radius: 3px;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
            tab-size: 2;
        }

        code {
            color: #eb5757;
        }

        .code {
            padding: 1.5em 1em;
        }

        .code-wrap {
            white-space: pre-wrap;
            word-break: break-all;
        }

        .code > code {
            background: none;
            padding: 0;
            font-size: 100%;
            color: inherit;
        }

        blockquote {
            font-size: 1.25em;
            margin: 1em 0;
            padding-left: 1em;
            border-left: 3px solid rgb(55, 53, 47);
        }

        .bookmark {
            text-decoration: none;
            max-height: 8em;
            padding: 0;
            display: flex;
            width: 100%;
            align-items: stretch;
        }

        .bookmark-title {
            font-size: 0.85em;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 1.75em;
            white-space: nowrap;
        }

        .bookmark-text {
            display: flex;
            flex-direction: column;
        }

        .bookmark-info {
            flex: 4 1 180px;
            padding: 12px 14px 14px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .bookmark-image {
            width: 33%;
            flex: 1 1 180px;
            display: block;
            position: relative;
            object-fit: cover;
            border-radius: 1px;
        }

        .bookmark-description {
            color: rgba(55, 53, 47, 0.6);
            font-size: 0.75em;
            overflow: hidden;
            max-height: 4.5em;
            word-break: break-word;
        }

        .bookmark-href {
            font-size: 0.75em;
            margin-top: 0.25em;
        }

        .sans {
            font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
        }

        .code {
            font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace;
        }

        .serif {
            font-family: Lyon-Text, Georgia, ui-serif, serif;
        }

        .mono {
            font-family: iawriter-mono, Nitti, Menlo, Courier, monospace;
        }

        .pdf .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP';
        }

        .pdf:lang(zh-CN) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC';
        }

        .pdf:lang(zh-TW) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC';
        }

        .pdf:lang(ko-KR) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR';
        }

        .pdf .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP';
        }

        .pdf:lang(zh-CN) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC';
        }

        .pdf:lang(zh-TW) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC';
        }

        .pdf:lang(ko-KR) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR';
        }

        .pdf .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP';
        }

        .pdf:lang(zh-CN) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC';
        }

        .pdf:lang(zh-TW) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC';
        }

        .pdf:lang(ko-KR) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR';
        }

        .pdf .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP';
        }

        .pdf:lang(zh-CN) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC';
        }

        .pdf:lang(zh-TW) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC';
        }

        .pdf:lang(ko-KR) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR';
        }

        .highlight-default {
            color: rgba(55, 53, 47, 1);
        }

        .highlight-gray {
            color: rgba(120, 119, 116, 1);
            fill: rgba(120, 119, 116, 1);
        }

        .highlight-brown {
            color: rgba(159, 107, 83, 1);
            fill: rgba(159, 107, 83, 1);
        }

        .highlight-orange {
            color: rgba(217, 115, 13, 1);
            fill: rgba(217, 115, 13, 1);
        }

        .highlight-yellow {
            color: rgba(203, 145, 47, 1);
            fill: rgba(203, 145, 47, 1);
        }

        .highlight-teal {
            color: rgba(68, 131, 97, 1);
            fill: rgba(68, 131, 97, 1);
        }

        .highlight-blue {
            color: rgba(51, 126, 169, 1);
            fill: rgba(51, 126, 169, 1);
        }

        .highlight-purple {
            color: rgba(144, 101, 176, 1);
            fill: rgba(144, 101, 176, 1);
        }

        .highlight-pink {
            color: rgba(193, 76, 138, 1);
            fill: rgba(193, 76, 138, 1);
        }

        .highlight-red {
            color: rgba(212, 76, 71, 1);
            fill: rgba(212, 76, 71, 1);
        }

        .highlight-default_background {
            color: rgba(55, 53, 47, 1);
        }

        .highlight-gray_background {
            background: rgba(241, 241, 239, 1);
        }

        .highlight-brown_background {
            background: rgba(244, 238, 238, 1);
        }

        .highlight-orange_background {
            background: rgba(251, 236, 221, 1);
        }

        .highlight-yellow_background {
            background: rgba(251, 243, 219, 1);
        }

        .highlight-teal_background {
            background: rgba(237, 243, 236, 1);
        }

        .highlight-blue_background {
            background: rgba(231, 243, 248, 1);
        }

        .highlight-purple_background {
            background: rgba(244, 240, 247, 0.8);
        }

        .highlight-pink_background {
            background: rgba(249, 238, 243, 0.8);
        }

        .highlight-red_background {
            background: rgba(253, 235, 236, 1);
        }

        .block-color-default {
            color: inherit;
            fill: inherit;
        }

        .block-color-gray {
            color: rgba(120, 119, 116, 1);
            fill: rgba(120, 119, 116, 1);
        }

        .block-color-brown {
            color: rgba(159, 107, 83, 1);
            fill: rgba(159, 107, 83, 1);
        }

        .block-color-orange {
            color: rgba(217, 115, 13, 1);
            fill: rgba(217, 115, 13, 1);
        }

        .block-color-yellow {
            color: rgba(203, 145, 47, 1);
            fill: rgba(203, 145, 47, 1);
        }

        .block-color-teal {
            color: rgba(68, 131, 97, 1);
            fill: rgba(68, 131, 97, 1);
        }

        .block-color-blue {
            color: rgba(51, 126, 169, 1);
            fill: rgba(51, 126, 169, 1);
        }

        .block-color-purple {
            color: rgba(144, 101, 176, 1);
            fill: rgba(144, 101, 176, 1);
        }

        .block-color-pink {
            color: rgba(193, 76, 138, 1);
            fill: rgba(193, 76, 138, 1);
        }

        .block-color-red {
            color: rgba(212, 76, 71, 1);
            fill: rgba(212, 76, 71, 1);
        }

        .block-color-default_background {
            color: inherit;
            fill: inherit;
        }

        .block-color-gray_background {
            background: rgba(241, 241, 239, 1);
        }

        .block-color-brown_background {
            background: rgba(244, 238, 238, 1);
        }

        .block-color-orange_background {
            background: rgba(251, 236, 221, 1);
        }

        .block-color-yellow_background {
            background: rgba(251, 243, 219, 1);
        }

        .block-color-teal_background {
            background: rgba(237, 243, 236, 1);
        }

        .block-color-blue_background {
            background: rgba(231, 243, 248, 1);
        }

        .block-color-purple_background {
            background: rgba(244, 240, 247, 0.8);
        }

        .block-color-pink_background {
            background: rgba(249, 238, 243, 0.8);
        }

        .block-color-red_background {
            background: rgba(253, 235, 236, 1);
        }

        .select-value-color-uiBlue {
            background-color: rgba(35, 131, 226, .07);
        }

        .select-value-color-pink {
            background-color: rgba(245, 224, 233, 1);
        }

        .select-value-color-purple {
            background-color: rgba(232, 222, 238, 1);
        }

        .select-value-color-green {
            background-color: rgba(219, 237, 219, 1);
        }

        .select-value-color-gray {
            background-color: rgba(227, 226, 224, 1);
        }

        .select-value-color-transparentGray {
            background-color: rgba(227, 226, 224, 0);
        }

        .select-value-color-translucentGray {
            background-color: rgba(0, 0, 0, 0.06);
        }

        .select-value-color-orange {
            background-color: rgba(250, 222, 201, 1);
        }

        .select-value-color-brown {
            background-color: rgba(238, 224, 218, 1);
        }

        .select-value-color-red {
            background-color: rgba(255, 226, 221, 1);
        }

        .select-value-color-yellow {
            background-color: rgba(253, 236, 200, 1);
        }

        .select-value-color-blue {
            background-color: rgba(211, 229, 239, 1);
        }

        .select-value-color-pageGlass {
            background-color: undefined;
        }

        .select-value-color-washGlass {
            background-color: undefined;
        }

        .checkbox {
            display: inline-flex;
            vertical-align: text-bottom;
            width: 16;
            height: 16;
            background-size: 16px;
            margin-left: 2px;
            margin-right: 5px;
        }

        .checkbox-on {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
        }

        .checkbox-off {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
        }

    </style>
</head>
<body>
<article class="page sans" id="16b7eaa6-85d5-8094-b03a-f064136c5d68">
    <div class="page-body">
        <blockquote class="" id="684faf47-d4cb-4bdb-ab1b-0698863cdb28"><em>
            <mark class="highlight-red">
                当我们对源码有一定熟悉度的情况下，不需要从头开始走读，而是通过搜索快速到达指定位置，可以大大提高效率。
            </mark>
        </em></blockquote>
        <p class="" id="41bf5f9a-882f-4f58-b711-ab3c967f9102">第五步，通过搜索和回溯，实现时光穿梭。</p>
        <p class="" id="c2acc170-f206-41ff-8b08-bf8a2ab52bc9">
        </p>
        <p class="" id="316fa094-8aa8-43ef-9274-516b5078099f">本教程需要知道：</p>
        <ol class="numbered-list" id="744ea943-fcd8-47f3-96f1-874216fe9a33" start="1" type="1">
            <li>搜索界面</li>
        </ol>
        <ol class="numbered-list" id="16b7eaa6-85d5-80fa-8964-d892e6026557" start="2" type="1">
            <li>右键搜索</li>
        </ol>
        <ol class="numbered-list" id="16b7eaa6-85d5-80a7-bbe6-d032a553e641" start="3" type="1">
            <li>回溯与搜索结合使用</li>
        </ol>
        <h2 class="" id="75bcdb0e-05c3-4ab3-bda5-5f5e9b50fd01">1. 搜索界面</h2>
        <p class="" id="16b7eaa6-85d5-8032-b2e1-da8d407add22">序列图右上角找到搜索按钮🔍，或者请求列表找到“全局搜索”，
            可以调出搜索界面。</p>
        <figure class="image" id="16b7eaa6-85d5-8001-a9f8-c7b0d2d5dc03"><a
                href="help_step5%2016b7eaa685d58094b03af064136c5d68/image.png"><img src="help_step5%2016b7eaa685d58094b03af064136c5d68/image.png"
                                                                                    style="width:707.9765625px"/></a>
        </figure>
        <h2 class="" id="16b7eaa6-85d5-80fa-976e-eb76c685d6bf">2. 右键搜索</h2>
        <p class="" id="16b7eaa6-85d5-8002-9d8b-d9084aba8a29">右键搜索有点类似于地图应用的“附近搜索”。</p>
        <p class="" id="16b7eaa6-85d5-8066-b864-e50cc6eb1ac4">在上下文数据处，可以右键追踪相关数据。</p>
        <figure class="image" id="16b7eaa6-85d5-8080-8c69-e663047d948e"><a
                href="help_step5%2016b7eaa685d58094b03af064136c5d68/image%201.png"><img src="help_step5%2016b7eaa685d58094b03af064136c5d68/image%201.png"
                                                                                        style="width:707.9921875px"/></a>
        </figure>
        <p class="" id="16b7eaa6-85d5-804f-aab0-d88d02a0fc2c">在序列图的函数节点处，可以右键搜索相关函数。</p>
        <figure class="image" id="16b7eaa6-85d5-805b-afa5-e18889a440bc"><a
                href="help_step5%2016b7eaa685d58094b03af064136c5d68/image%202.png"><img src="help_step5%2016b7eaa685d58094b03af064136c5d68/image%202.png"
                                                                                        style="width:707.9921875px"/></a>
        </figure>
        <p class="" id="16b7eaa6-85d5-80b0-99d7-fb19c8e66ce4">在序列图的类上，右键可以搜索类。</p>
        <figure class="image" id="16b7eaa6-85d5-80ec-9322-eb6db8fdd544"><a
                href="help_step5%2016b7eaa685d58094b03af064136c5d68/image%203.png"><img src="help_step5%2016b7eaa685d58094b03af064136c5d68/image%203.png"
                                                                                        style="width:708px"/></a>
        </figure>
        <p class="" id="16b7eaa6-85d5-80ec-9d6d-f1574ae0c74c">
        </p>
        <h2 class="" id="16b7eaa6-85d5-8035-8fa5-fc8d6e57e932">3. 回溯与搜索结合使用</h2>
        <p class="" id="16b7eaa6-85d5-80d2-abb5-ed7231675f4b">
            当我们通过搜索跳到某个函数，都可以通过查看堆栈，看看这个函数是从哪里一步步执行来的，这样就可以搞清楚来龙去脉。</p>
        <figure class="image" id="16b7eaa6-85d5-804f-b4e2-ecb45fe23525"><a
                href="help_step5%2016b7eaa685d58094b03af064136c5d68/image%204.png"><img src="help_step5%2016b7eaa685d58094b03af064136c5d68/image%204.png"
                                                                                        style="width:707.984375px"/></a>
        </figure>
        <p class="" id="16b7eaa6-85d5-80e9-b864-d75d1d1f76e6">
        </p>
        <p class="" id="16b7eaa6-85d5-8013-b4d3-effa9de1c11c">
        </p>
        <p class="" id="16b7eaa6-85d5-80dc-bc47-cafcac6a17bd">
            从序列图、高亮执行源码、上下文数据，到时光穿梭，他们都是为了完成一个核心目标，重现源码，重现问题。只要能重现，就能轻松搞定。</p>
        <p class="" id="16b7eaa6-85d5-8024-9f02-e59da90e26f1">
        </p>
        <p class="" id="16b7eaa6-85d5-801f-a7e1-f1afe22e8141">那为什么说 “重现” 是搞定 “屎山代码”
            的良药呢？请看五步之外的加餐文档。</p>
        <p class="" id="16d7eaa6-85d5-8011-ae53-f8d80739ba9c">
        </p></div>
</article>
<span class="sans" style="font-size:14px;padding-top:2em"></span></body>
</html>