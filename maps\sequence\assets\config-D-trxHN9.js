import{h as K,u as Q,i as W,r as d,v as Y,z as Z,j as a,o as s,m as b,l as e,q as v,n as c,t as p,B as n,T as k,F as M,k as D,C as g,M as ee,A as U,y as te,_ as oe,R as re,S as le}from"./style-Iw_OziiJ.js";const ae={class:"block sm:inline"},se={class:"flex-1 overflow-y-auto overflow-x-hidden p-2"},ne={class:"max-w-4xl mx-auto space-y-1.5"},ie={class:"flex items-center gap-1.5 mb-1.5"},ue={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},de={class:"flex flex-col gap-2"},ve={class:"flex items-center gap-1.5 cursor-pointer"},xe={class:"flex items-center gap-1.5 cursor-pointer"},ce={class:"flex flex-col gap-2"},pe={class:"flex items-center gap-1.5 cursor-pointer"},be={class:"flex items-center gap-1.5 cursor-pointer"},ge={class:"flex items-center gap-1.5 mb-1.5"},me={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},fe={class:"flex flex-col gap-2"},he={class:"flex gap-3"},we={class:"flex items-center gap-1.5 cursor-pointer"},ye={class:"flex items-center gap-1.5 cursor-pointer"},Ce={key:0,class:"flex flex-wrap gap-1"},ke={class:"flex items-center gap-1.5 mb-1.5"},ze={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},Ne={class:"flex flex-col gap-1.5"},Se=["onClick"],Pe={class:"flex flex-col gap-1.5"},Ve={key:1,class:"flex gap-1 mt-1"},Fe={class:"flex items-center gap-1.5 mb-1.5"},Ie={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},Me={class:"flex flex-col gap-1.5"},De=["onClick"],He={key:0,class:"flex flex-col gap-1 w-full"},Be={class:"flex gap-1"},Ue={class:"flex items-center gap-1.5 mb-1.5"},Ee={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},_e={key:1,class:"space-y-3"},Ae={class:"grid grid-cols-2 gap-3"},Le={class:"flex flex-col gap-1"},Oe={class:"flex flex-col gap-1"},Te={class:"flex flex-col gap-1"},je={class:"flex flex-col gap-1"},Re={key:2,class:"space-y-3"},$e={class:"grid grid-cols-2 gap-3"},qe={class:"flex flex-col gap-1"},Xe={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},Je={class:"flex flex-col gap-1"},Ge={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},Ke={class:"flex flex-col gap-1"},Qe={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},We={class:"flex flex-col gap-1"},Ye={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},Ze={key:0,class:"space-y-3"},et={class:"grid grid-cols-2 gap-3"},tt={class:"flex flex-col gap-1"},ot={class:"flex flex-col gap-1"},rt={key:1,class:"space-y-3"},lt={class:"grid grid-cols-2 gap-3"},at={class:"flex flex-col gap-1"},st={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},nt={class:"flex flex-col gap-1"},it={class:"px-1 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},ut=K({__name:"Config",setup(vt){const w=Q(),x=W(),E=d(!0),o=d({startOnDebug:!1,recordMode:"all",enableAutoDetect:!0,autoDetectedPackages:[],includedPackagePrefixes:[],includedParentClasses:[],maxNumFirst:12,maxNumHash:3,maxNumFirstImportant:1024,maxNumHashImportant:256,maxColSize:32,maxStrSize:4096}),S=d(null),m=d({0:!1,1:!1,2:!1,3:!1,4:!1}),z=d(!1),y=d(""),P=d(!1),C=d(""),V=d(!1),i=d({maxNumFirst:12,maxNumHash:3,maxNumFirstImportant:1024,maxNumHashImportant:256}),F=d(!1),f=d({maxColSize:32,maxStrSize:4096}),N=l=>{m.value[l]=!m.value[l]},_=l=>{o.value.includedPackagePrefixes&&(o.value.includedPackagePrefixes.splice(l,1),u())},A=l=>{o.value.includedParentClasses&&(o.value.includedParentClasses.splice(l,1),u())},u=async()=>{try{const l=await ee(o.value);if(!l.success){console.error("Failed to update filter configuration:",l.error),w.setError(l.error||"Failed to update filter configuration");return}await I()}catch(l){console.error("Failed to update filter configuration:",l),w.setError("Failed to update filter configuration")}},L=()=>{z.value=!0,U(()=>{const l=document.querySelector(".input-new-package-prefix input");l&&l.focus()})},O=()=>{y.value&&(o.value.includedPackagePrefixes||(o.value.includedPackagePrefixes=[]),o.value.includedPackagePrefixes.push(y.value),u()),z.value=!1,y.value=""},T=()=>{P.value=!0,U(()=>{const l=document.querySelector(".input-new-parent-class input");l&&l.focus()})},j=()=>{C.value&&(o.value.includedParentClasses||(o.value.includedParentClasses=[]),o.value.includedParentClasses.push({type:C.value}),u()),P.value=!1,C.value=""},R=()=>{i.value={maxNumFirst:o.value.maxNumFirst||12,maxNumHash:o.value.maxNumHash||3,maxNumFirstImportant:o.value.maxNumFirstImportant||1024,maxNumHashImportant:o.value.maxNumHashImportant||256},V.value=!0},$=async()=>{o.value.maxNumFirst=i.value.maxNumFirst,o.value.maxNumHash=i.value.maxNumHash,o.value.maxNumFirstImportant=i.value.maxNumFirstImportant,o.value.maxNumHashImportant=i.value.maxNumHashImportant,await u(),V.value=!1},q=()=>{V.value=!1},X=()=>{f.value={maxColSize:o.value.maxColSize||32,maxStrSize:o.value.maxStrSize||4096},F.value=!0},J=async()=>{o.value.maxColSize=f.value.maxColSize,o.value.maxStrSize=f.value.maxStrSize,await u(),F.value=!1},G=()=>{F.value=!1},I=async()=>{try{const l=await te();l.success&&l.data?o.value=l.data:w.setError(l.error||"Failed to get configuration")}catch(l){console.error("Failed to get configuration:",l),w.setError("Failed to get configuration")}};return Y(async()=>{x.initTheme(),await I(),E.value=!1,S.value=window.setInterval(()=>{I()},3e4)}),Z(()=>{S.value&&(clearInterval(S.value),S.value=null)}),(l,t)=>{var B;return s(),a("div",{class:c(["h-screen min-w-full w-screen flex flex-col overflow-x-hidden",(v(x).theme==="dark","bg-[var(--bg-color)]")])},[(B=v(w))!=null&&B.error?(s(),a("div",{key:0,class:c(["fixed top-2 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300",v(x).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[e("span",ae,p(v(w).error.message),1)],2)):b("",!0),e("div",{class:c(["border-b py-2 px-4 shadow-sm",(v(x).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]")])},t[21]||(t[21]=[e("div",{class:"flex items-center"},[e("span",{class:"text-lg font-semibold text-[var(--text-color)]"},"录制配置")],-1)]),2),e("div",se,[e("div",ne,[e("div",{class:c(["rounded p-3 shadow-sm",(v(x).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",ie,[t[23]||(t[23]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制模式",-1)),e("button",{onClick:t[0]||(t[0]=r=>N("0")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[22]||(t[22]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),m.value[0]?(s(),a("p",ue," 全录模式，从程序启动就开始录制，会录制应用的完整启动过程。手工模式，默认不录制，需手工开启录制，可以跳过应用启动过程。 ")):b("",!0),e("div",de,[e("label",ve,[n(e("input",{type:"radio","onUpdate:modelValue":t[1]||(t[1]=r=>o.value.recordMode=r),value:"manual",onChange:u,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.recordMode]]),t[24]||(t[24]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"【推荐】手工模式，跳过应用启动过程，然后按需开启录制",-1))]),e("label",xe,[n(e("input",{type:"radio","onUpdate:modelValue":t[2]||(t[2]=r=>o.value.recordMode=r),value:"all",onChange:u,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.recordMode]]),t[25]||(t[25]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"全录模式，应用启动即开始录制，速度慢，但数据完整",-1))])])],2),e("div",{class:c(["rounded p-3 shadow-sm",(v(x).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[t[28]||(t[28]=e("h3",{class:"text-sm font-semibold mb-1.5 text-[var(--text-color)]"},"默认随着 Debug 启动",-1)),e("div",ce,[e("label",pe,[n(e("input",{type:"radio","onUpdate:modelValue":t[3]||(t[3]=r=>o.value.startOnDebug=r),value:!0,onChange:u,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.startOnDebug]]),t[26]||(t[26]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"是（支持使用 Jrebel/HotSwap 启动）",-1))]),e("label",be,[n(e("input",{type:"radio","onUpdate:modelValue":t[4]||(t[4]=r=>o.value.startOnDebug=r),value:!1,onChange:u,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.startOnDebug]]),t[27]||(t[27]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"否（需要使用 Debug with XCodeMap 启动）",-1))])])],2),e("div",{class:c(["rounded p-3 shadow-sm",(v(x).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",ge,[t[30]||(t[30]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制项目包",-1)),e("button",{onClick:t[5]||(t[5]=r=>N("1")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[29]||(t[29]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),m.value[1]?(s(),a("p",me," XCodeMap 默认检测并录制项目包。如果要录制依赖包，请在后面手工添加。如果发现项目包遗漏，也可在后面添加。 ")):b("",!0),e("div",fe,[e("div",he,[e("label",we,[n(e("input",{type:"radio","onUpdate:modelValue":t[6]||(t[6]=r=>o.value.enableAutoDetect=r),value:!0,onChange:u,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.enableAutoDetect]]),t[31]||(t[31]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"是",-1))]),e("label",ye,[n(e("input",{type:"radio","onUpdate:modelValue":t[7]||(t[7]=r=>o.value.enableAutoDetect=r),value:!1,onChange:u,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.enableAutoDetect]]),t[32]||(t[32]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"否",-1))])]),o.value.enableAutoDetect?(s(),a("div",Ce,[(s(!0),a(M,null,D(o.value.autoDetectedPackages,(r,h)=>(s(),a("div",{key:h,class:"px-1.5 py-0.5 rounded-full text-xs font-medium bg-[var(--bg-color)] text-[var(--text-color)]"},p(r),1))),128))])):b("",!0)])],2),e("div",{class:c(["rounded p-3 shadow-sm",(v(x).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",ke,[t[34]||(t[34]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制依赖包",-1)),e("button",{onClick:t[8]||(t[8]=r=>N("2")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[33]||(t[33]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),m.value[2]?(s(),a("p",ze," 配置包的前缀即可。例如配置 org.spring，将录制 spring 模块内的函数调用数据。如果函数被多次调用，则只录制最先出现的N次调用，然后按照不同的调用堆栈位置分别录制M次。具体次数由函数调用过滤配置中的普通包相关配置决定。 ")):b("",!0),e("div",Ne,[(s(!0),a(M,null,D(o.value.includedPackagePrefixes||[],(r,h)=>(s(),a("div",{key:h,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,p(r),1),e("button",{onClick:()=>{_(h)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},t[35]||(t[35]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,Se)]))),128)),e("div",Pe,[z.value?n((s(),a("input",{key:0,"onUpdate:modelValue":t[9]||(t[9]=r=>y.value=r),class:"input-new-package-prefix px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写包前缀，例如 org.spring"},null,512)),[[g,y.value]]):b("",!0),z.value?(s(),a("div",Ve,[e("button",{onClick:O,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:t[10]||(t[10]=()=>{z.value=!1,y.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])):(s(),a("button",{key:2,onClick:L,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[36]||(t[36]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加包前缀",-1)])))])])],2),e("div",{class:c(["rounded p-3 shadow-sm",(v(x).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Fe,[t[38]||(t[38]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制重点类",-1)),e("button",{onClick:t[11]||(t[11]=r=>N("3")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[37]||(t[37]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),m.value[3]?(s(),a("p",Ie," 配置类的完整名称。例如配置 org.springframework.beans.factory.BeanFactory，将重点录制该类的调用，其录制次数由函数调用过滤配置中的重点类相关配置决定。 ")):b("",!0),e("div",Me,[(s(!0),a(M,null,D(o.value.includedParentClasses,(r,h)=>(s(),a("div",{key:h,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,p(r.type),1),e("button",{onClick:()=>{A(h)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},t[39]||(t[39]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,De)]))),128)),P.value?(s(),a("div",He,[n(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>C.value=r),class:"input-new-parent-class px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写类完整名称，如 org.springframework.beans.factory.BeanFactory"},null,512),[[g,C.value]]),e("div",Be,[e("button",{onClick:j,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:t[13]||(t[13]=()=>{P.value=!1,C.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("button",{key:1,onClick:T,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[40]||(t[40]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加类",-1)])))])],2),e("div",{class:c(["rounded p-3 shadow-sm",(v(x).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Ue,[t[42]||(t[42]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"函数录制次数",-1)),e("button",{onClick:t[14]||(t[14]=r=>N("4")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[41]||(t[41]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),m.value[4]?(s(),a("p",Ee," 在每个请求中，每个函数会录制其最先出现的N次调用，然后按照不同的调用堆栈位置分别录制M次。这样可以既记录函数的早期行为，又能捕获不同上下文下的调用模式。 ")):b("",!0),V.value?(s(),a("div",_e,[e("div",Ae,[e("div",Le,[t[43]||(t[43]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数录制最先出现的N次",-1)),n(e("input",{"onUpdate:modelValue":t[15]||(t[15]=r=>i.value.maxNumFirst=r),type:"number",min:"1",max:"1000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[g,i.value.maxNumFirst,void 0,{number:!0}]])]),e("div",Oe,[t[44]||(t[44]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数每个堆栈位置录制M次",-1)),n(e("input",{"onUpdate:modelValue":t[16]||(t[16]=r=>i.value.maxNumHash=r),type:"number",min:"1",max:"100",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[g,i.value.maxNumHash,void 0,{number:!0}]])]),e("div",Te,[t[45]||(t[45]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数录制最先出现的N次",-1)),n(e("input",{"onUpdate:modelValue":t[17]||(t[17]=r=>i.value.maxNumFirstImportant=r),type:"number",min:"1",max:"10000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[g,i.value.maxNumFirstImportant,void 0,{number:!0}]])]),e("div",je,[t[46]||(t[46]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数每个堆栈位置录制M次",-1)),n(e("input",{"onUpdate:modelValue":t[18]||(t[18]=r=>i.value.maxNumHashImportant=r),type:"number",min:"1",max:"1000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[g,i.value.maxNumHashImportant,void 0,{number:!0}]])])]),e("div",{class:"flex gap-1"},[e("button",{onClick:$,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:q,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("div",Re,[e("div",$e,[e("div",qe,[t[47]||(t[47]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数录制最先出现的N次",-1)),e("div",Xe,p(o.value.maxNumFirst||12),1)]),e("div",Je,[t[48]||(t[48]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数每个堆栈位置录制M次",-1)),e("div",Ge,p(o.value.maxNumHash||3),1)]),e("div",Ke,[t[49]||(t[49]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数录制最先出现的N次",-1)),e("div",Qe,p(o.value.maxNumFirstImportant||1024),1)]),e("div",We,[t[50]||(t[50]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数每个堆栈位置录制M次",-1)),e("div",Ye,p(o.value.maxNumHashImportant||256),1)])]),e("button",{onClick:R,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[51]||(t[51]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1),e("span",null,"编辑配置",-1)]))]))],2),e("div",{class:c(["rounded p-3 shadow-sm",(v(x).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[t[57]||(t[57]=e("div",{class:"flex items-center gap-1.5 mb-1.5"},[e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"对象录制深度")],-1)),F.value?(s(),a("div",Ze,[e("div",et,[e("div",tt,[t[52]||(t[52]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"集合最大元素数",-1)),n(e("input",{"onUpdate:modelValue":t[19]||(t[19]=r=>f.value.maxColSize=r),type:"number",min:"1",max:"1000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[g,f.value.maxColSize,void 0,{number:!0}]])]),e("div",ot,[t[53]||(t[53]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"字符串最大长度",-1)),n(e("input",{"onUpdate:modelValue":t[20]||(t[20]=r=>f.value.maxStrSize=r),type:"number",min:"1",max:"10000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[g,f.value.maxStrSize,void 0,{number:!0}]])])]),e("div",{class:"flex gap-1"},[e("button",{onClick:J,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:G,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("div",rt,[e("div",lt,[e("div",at,[t[54]||(t[54]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"集合最大元素数",-1)),e("div",st,p(o.value.maxColSize||32),1)]),e("div",nt,[t[55]||(t[55]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"字符串最大长度",-1)),e("div",it,p(o.value.maxStrSize||4096),1)])]),e("button",{onClick:X,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[56]||(t[56]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1),e("span",null,"编辑配置",-1)]))]))],2)])])],2)}}}),dt=oe(ut,[["__scopeId","data-v-d14f3c3b"]]),H=re(dt);H.use(le());H.mount("#app");window.$vm=H._instance;
