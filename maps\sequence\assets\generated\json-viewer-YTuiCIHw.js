const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./jsoneditor.min-CWWzlBzm.js","./_commonjsHelpers-CqkleIqs.js"])))=>i.map(i=>d[i]);
import{i as R,u as j,r as y,w as O,x as A,j as p,o as b,m as w,l as s,q as h,n as N,t as k,a4 as F,_ as z,R as V,S as T}from"./style-CvhRCPHz.js";import{u as q}from"./error-Bhv9ZvWl.js";const B="modulepreload",L=function(J,c){return new URL(J,c).href},I={},U=function(c,d,g){let i=Promise.resolve();if(d&&d.length>0){let a=function(n){return Promise.all(n.map(v=>Promise.resolve(v).then(m=>({status:"fulfilled",value:m}),m=>({status:"rejected",reason:m}))))};const l=document.getElementsByTagName("link"),u=document.querySelector("meta[property=csp-nonce]"),S=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));i=a(d.map(n=>{if(n=L(n,g),n in I)return;I[n]=!0;const v=n.endsWith(".css"),m=v?'[rel="stylesheet"]':"";if(!!g)for(let o=l.length-1;o>=0;o--){const r=l[o];if(r.href===n&&(!v||r.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${n}"]${m}`))return;const e=document.createElement("link");if(e.rel=v?"stylesheet":B,v||(e.as="script"),e.crossOrigin="",e.href=n,S&&e.setAttribute("nonce",S),document.head.appendChild(e),v)return new Promise((o,r)=>{e.addEventListener("load",o),e.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${n}`)))})}))}function t(a){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=a,window.dispatchEvent(l),!l.defaultPrevented)throw a}return i.then(a=>{for(const l of a||[])l.status==="rejected"&&t(l.reason);return c().catch(t)})},D=["data-theme"],M={class:"block sm:inline"},K={class:"flex items-center"},$={class:"text-base font-semibold text-[var(--text-color)]"},W={key:0,class:"absolute top-16 left-1/2 transform -translate-x-1/2 z-50"},G={key:1,class:"flex-1 flex items-center justify-center"},H={key:2,class:"flex-1 p-2"},Q={key:0,class:"mt-4 p-4 border rounded bg-[var(--bg-color)] border-[var(--border-color)]"},X={class:"text-sm text-[var(--text-color)] overflow-auto max-h-96"},Y=R({__name:"JsonViewer",setup(J){const c=q(),d=j(),g=y(!0),i=y(""),t=y(null),a=y(),l=y(!1),u=()=>{const e=new URLSearchParams(window.location.search),o=e.get("processId"),r=e.get("labelKey"),f=e.get("callId"),x=e.get("maxVersion"),E=e.get("uniqId"),P=e.get("depth");return!o||!x||!E?(c.setError("Missing required parameters: processId, maxVersion, and uniqId"),null):{processId:o,labelKey:r||void 0,callId:f?parseInt(f):0,maxVersion:parseInt(x),uniqId:parseInt(E),depth:P?parseInt(P):1}},S=async()=>{const e=u();if(e)try{g.value=!0,console.log("Loading JSON data with params:",e);const o=await F(e);console.log("API response:",o),o.success&&o.data?(i.value=o.data,console.log("JSON data loaded:",i.value)):(console.error("API error:",o.error),c.setError(o.error||"Failed to load JSON data"))}catch(o){console.error("Failed to load JSON data:",o),c.setError("Failed to load JSON data")}finally{g.value=!1}},n=()=>{if(console.log("Initializing JSON editor..."),console.log("Container ref:",a.value),console.log("JSON data:",i.value),!a.value||!i.value){console.error("Missing container ref or JSON data"),setTimeout(()=>{a.value&&i.value&&(console.log("Retrying JSON editor initialization..."),n())},200);return}try{U(async()=>{const{default:e}=await import("./jsoneditor.min-CWWzlBzm.js").then(o=>o.j);return{default:e}},__vite__mapDeps([0,1]),import.meta.url).then(({default:e})=>{console.log("JSONEditor imported successfully");const o={mode:"view",modes:["view","tree","form","code","text"],search:!0,navigationBar:!0,statusBar:!0,colorPicker:!1,colorPickerOptions:{left:0,top:0},readOnly:!0,readonly:!0,theme:d.theme==="dark"?"ace/theme/monokai":"ace/theme/chrome",onError:r=>{console.error("JSONEditor error:",r)},onModeChange:(r,f)=>{console.log("Mode changed from",f,"to",r)}};if(a.value){console.log("Creating JSONEditor instance..."),t.value=new e(a.value,o);try{console.log("Parsing JSON data...");const r=JSON.parse(i.value);console.log("Parsed JSON:",r),t.value.set(r),t.value.expandAll(),t.value.setReadOnly&&t.value.setReadOnly(!0),t.value.setMode&&t.value.setMode("view"),console.log("JSON editor initialized successfully")}catch(r){console.error("Failed to parse JSON:",r),t.value.setMode("text"),t.value.setText(i.value),console.log("Set JSON editor to text mode with raw data")}}}).catch(e=>{console.error("Failed to load JSONEditor:",e),c.setError("Failed to load JSON editor")})}catch(e){console.error("Failed to initialize JSON editor:",e),c.setError("Failed to initialize JSON editor")}},v=async()=>{if(t.value)try{const e=t.value.get(),o=JSON.stringify(e,null,2);await navigator.clipboard.writeText(o),l.value=!0,setTimeout(()=>{l.value=!1},2e3),console.log("JSON copied to clipboard")}catch(e){console.error("Failed to copy JSON:",e);try{const o=t.value.get(),r=JSON.stringify(o,null,2),x=`clipboard://encoded?value=${encodeURIComponent(r)}`;console.log("Using clipboard protocol:",x),window.open(x,"_blank"),l.value=!0,setTimeout(()=>{l.value=!1},2e3),console.log("JSON copied to clipboard using fallback method")}catch(o){console.error("Failed to use fallback copy method:",o),c.setError("Failed to copy JSON to clipboard")}}},m=()=>{t.value&&t.value.expandAll()},_=()=>{t.value&&t.value.collapseAll()};return O(a,e=>{console.log("Container ref changed:",e),e&&i.value&&!t.value&&(console.log("Container ref is now available, initializing editor..."),setTimeout(()=>{n()},100))},{immediate:!0}),O(i,e=>{console.log("JSON data changed:",e?"has data":"no data"),e&&a.value&&!t.value&&(console.log("JSON data is now available, initializing editor..."),setTimeout(()=>{n()},100))},{immediate:!0}),O(()=>d.theme,e=>{if(console.log("Theme changed to:",e),t.value){const o=a.value;o&&(o.style.setProperty("--bg-color",e==="dark"?"#1f2937":"#ffffff"),o.style.setProperty("--text-color",e==="dark"?"#f9fafb":"#111827"),o.style.setProperty("--border-color",e==="dark"?"#374151":"#d1d5db"))}},{immediate:!0}),A(()=>{d.initTheme(),console.log("Component mounted, container ref:",a.value),S()}),(e,o)=>{var r,f;return b(),p("div",{class:N(["h-screen min-w-full w-screen flex flex-col overflow-x-hidden",(h(d).theme==="dark","bg-[var(--bg-color)]")]),"data-theme":h(d).theme},[(r=h(c))!=null&&r.error?(b(),p("div",{key:0,class:N(["fixed top-2 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300",h(d).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[s("span",M,k(h(c).error.message),1)],2)):w("",!0),s("div",{class:N(["border-b py-1 px-4 shadow-sm flex items-center justify-between",(h(d).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]")])},[s("div",K,[s("span",$,k(((f=u())==null?void 0:f.labelKey)||"JSON Viewer"),1)]),s("div",{class:"flex items-center gap-1"},[s("button",{onClick:m,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)] border border-[var(--border-color)]"}," 展开全部 "),s("button",{onClick:_,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)] border border-[var(--border-color)]"}," 折叠全部 "),s("button",{onClick:v,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)] border border-[var(--border-color)]"}," 复制JSON ")]),l.value?(b(),p("div",W,o[0]||(o[0]=[s("div",{class:"px-4 py-2 rounded shadow-lg bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300"},[s("p",{class:"text-sm font-medium"}," ✓ JSON已复制到剪贴板 ")],-1)]))):w("",!0)],2),g.value?(b(),p("div",G,o[1]||(o[1]=[s("div",{class:"text-center"},[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--text-color)] mx-auto mb-2"}),s("p",{class:"text-sm text-[var(--text-color-secondary)]"},"加载中...")],-1)]))):(b(),p("div",H,[s("div",{ref_key:"containerRef",ref:a,class:"w-full h-full min-h-[400px]"},null,512),i.value&&!t.value?(b(),p("div",Q,[o[2]||(o[2]=s("h3",{class:"text-lg font-semibold mb-2 text-[var(--text-color)]"},"JSON 数据",-1)),s("pre",X,k(i.value),1)])):w("",!0)]))],10,D)}}}),Z=z(Y,[["__scopeId","data-v-4ce43778"]]),C=V(Z);C.use(T());C.mount("#app");
