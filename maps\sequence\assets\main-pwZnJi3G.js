var fr=Object.defineProperty;var gr=(n,e,t)=>e in n?fr(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var N=(n,e,t)=>gr(n,typeof e!="symbol"?e+"":e,t);import{d as Ze,r as I,u as kt,c as Le,g as mr,a as br,b as xr,e as Lt,f as kr,p as yr,s as wr,h as Wt,i as Pn,w as zn,j as T,o as w,F as ge,k as ke,l as d,m as B,n as _,q as f,t as q,_ as $n,v as Un,x as vr,y as Tr,z as _r,A as xe,B as Te,C as Sr,D as Er,E as Ar,G as Cr,H as Rr,I as Dr,J as wn,K as Mr,L as Lr,M as Ir,N as Nr,O as Or,P as Pr,Q as zr,R as $r}from"./style-DhcuNy_7.js";const Ur=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href,qt=Ze("chatStatus",()=>{const n=I("idle"),e=I("");return{status:n,statusMessage:e,setStatus:s=>{n.value=s},setStatusMessage:s=>{e.value=s}}}),Bn=Ze("modelStatus",()=>{const n=I(null),e=I(null),t=kt(),r=Le(()=>!n.value||!e.value?null:e.value.modelDescList.find(o=>o.uuid===n.value)),s=Le(()=>{var o;return((o=e.value)==null?void 0:o.modelDescList)||[]});return{currentModelUuid:n,modelConfig:e,currentModel:r,availableModels:s,getModelConfigData:async()=>{try{const o=await mr();if(o.success&&o.data)e.value=o.data,!n.value&&o.data.modelDescList.length>0&&(n.value=o.data.modelDescList[0].uuid);else throw new Error(o.error||"Failed to get model config")}catch(o){console.error("Failed to get model config:",o),t.setError(o instanceof Error?o.message:"Failed to get model config","GET_MODEL_CONFIG_ERROR")}},setCurrentModel:o=>{var i;((i=e.value)==null?void 0:i.modelDescList.find(p=>p.uuid===o))&&(n.value=o)}}}),ce="EMPTY_PLACE_HOLDER",Fn=Ze("database",()=>{const n=I([{id:ce,name:"请选择数据源",recordState:"idle",dataId:null,active:!1,serverSelected:!1}]),e=I(ce),t=Le(()=>n.value.find(b=>b.id===e.value)||null),r=b=>{n.value.push(b)},s=b=>{e.value=b},a=async(b,g)=>{const A=n.value.find(S=>S.id===b);if(A)try{await br({executionId:b,cmd:"change",state:g}),A.recordState="preparing"}catch(S){console.error("Failed to change record state:",S)}};return{databases:n,currentDatabase:t,currentDatabaseId:e,addDatabase:r,setCurrentDatabase:s,changeState:a,queryState:b=>{var g;return(g=n.value.find(A=>A.id===b))==null?void 0:g.recordState},startRecord:b=>{const g=n.value.find(A=>A.id===b);g&&g.recordState==="idle"&&a(b,"start")},endRecord:b=>{const g=n.value.find(A=>A.id===b);g&&g.recordState==="recording"&&a(b,"stop")},restartRecord:b=>{const g=n.value.find(A=>A.id===b);g&&g.recordState==="paused"&&(a(b,"start"),Gn().createNewChat())},getDatabase:async()=>{try{const b=await xr();n.value=[n.value[0]],b.forEach(g=>{r(g)})}catch(b){console.error("Failed to fetch process data:",b)}}}}),Hn=Ze("inputBox",()=>{const n=I(""),e=kt(),t=qt(),r=Fn(),s=()=>!r.currentDatabase||!r.currentDatabase.dataId||t.status==="waiting"||r.currentDatabaseId===ce;return{message:n,appendText:i=>{if(s())return e.setError("当前状态不可添加文本到输入框"),!1;const p=n.value&&!n.value.endsWith(" ");return n.value+=(p?" ":"")+i,!0},setText:i=>{n.value=i},clearText:()=>{n.value=""},getText:()=>n.value,isDisabled:s}}),Br="SystemStatus",Fr="AddToChat",Gn=Ze("chat",()=>{const n=I([]),e=I(null),t=I(!1);let r=null;const s=kt(),a=qt(),c=Bn(),o=Hn(),h=Le(()=>n.value.find(E=>E.id===e.value)),i=async()=>{var E;try{e.value&&await Lt(e.value);const C={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await kr(C.id,((E=c.currentModel)==null?void 0:E.uuid)??""))return n.value=[C],e.value=C.id,t.value||p(),C;throw new Error("Failed to create chat channel")}catch(C){throw console.error("Failed to create chat channel:",C),s.setError(C instanceof Error?C.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),e.value=null,n.value=[],C}},p=()=>{r&&clearInterval(r),t.value=!0,r=window.setInterval(async()=>{if(t.value)try{const E=await yr();if(E&&E.length>0){a.setStatus("waiting");for(const C of E){if(C.messageId===Br){a.setStatusMessage(C.content);continue}if(C.messageId===Fr){o.appendText(C.content);continue}const $=n.value.find(D=>D.id===C.chatId);if($){const D=$.messages[$.messages.length-1];D&&D.role===C.role?D.content+=C.content:$.messages.push(C),$.updatedAt=Date.now()}}}else a.setStatus("sending")}catch(E){console.error("Failed to poll messages:",E),s.setError(E instanceof Error?E.message:"Failed to poll messages","POLL_ERROR")}},1e3)},b=()=>{r&&(clearInterval(r),r=null),t.value=!1};return{chats:n,currentChatId:e,currentChat:h,createNewChat:i,sendMessage:async E=>{e.value||await i();const C=h.value;if(!C)return;if(!c.currentModel){s.setError("请先选择模型再发送消息","SEND_MESSAGE_ERROR");return}a.setStatus("waiting");const $={messageId:crypto.randomUUID(),content:E,role:"user",timestamp:Date.now(),chatId:e.value,modelId:c.currentModel.uuid};try{if(!e.value)return;if(await wr($))C.messages.push($),C.updatedAt=Date.now();else throw new Error("会话可能已失效，请重新创建会话")}catch(D){throw console.error("Failed to send message:",D),s.setError(D instanceof Error?D.message:"会话可能已失效，请重新创建会话","SEND_MESSAGE_ERROR"),D}},removeChat:async E=>{try{if(await Lt(E))n.value=n.value.filter($=>$.id!==E),e.value===E&&(e.value=null);else throw new Error("Failed to remove chat channel")}catch(C){throw console.error("Failed to remove chat:",C),s.setError(C instanceof Error?C.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),C}},startPolling:p,stopPolling:b,cleanup:()=>{b(),e.value&&Lt(e.value).catch(console.error)}}}),Hr={class:"space-y-0.5"},Gr=["onClick"],Wr=["onClick"],qr={key:0,class:"pl-3 mt-0.5 space-y-0.5"},jr=["onClick"],Vr=["onClick"],Yr={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Zr=["onClick"],Xr=Wt({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(n,{emit:e}){const t=n,r=Pn(),s=I(t.nodes.map(h=>({...h,isExpanded:t.defaultExpanded,children:h.children?h.children.map(i=>({...i,isExpanded:t.defaultExpanded,children:i.children?i.children.map(p=>({...p,isExpanded:t.defaultExpanded})):[]})):[]})));zn(()=>t.nodes,h=>{s.value=h.map(i=>({...i,isExpanded:t.defaultExpanded,children:i.children?i.children.map(p=>({...p,isExpanded:t.defaultExpanded,children:p.children?p.children.map(b=>({...b,isExpanded:t.defaultExpanded})):[]})):[]}))},{deep:!0});const a=e,c=h=>{a("nodeClick",h)},o=h=>{h.isExpanded=!h.isExpanded};return(h,i)=>(w(),T("div",Hr,[(w(!0),T(ge,null,ke(s.value,p=>(w(),T("div",{key:p.nodeKey,class:"tree-node"},[d("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",f(r).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[p.children&&p.children.length>0?(w(),T("button",{key:0,onClick:b=>o(p),class:_(["p-0.5 rounded",f(r).theme==="dark"?"hover:bg-gray-600":"hover:bg-gray-100"])},[(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":p.isExpanded,"text-gray-600 hover:text-gray-800":f(r).theme==="light","text-gray-400 hover:text-gray-200":f(r).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},i[0]||(i[0]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],10,Gr)):B("",!0),d("span",{class:_({"text-gray-700 hover:text-gray-900":f(r).theme==="light","text-gray-300 hover:text-gray-100":f(r).theme==="dark","cursor-pointer flex-grow":!0}),onClick:b=>c(p)},q(p.label),11,Wr)],2),p.children&&p.children.length>0&&p.isExpanded?(w(),T("div",qr,[(w(!0),T(ge,null,ke(p.children,b=>(w(),T("div",{key:b.nodeKey,class:"tree-node"},[d("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",f(r).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[b.children&&b.children.length>0?(w(),T("button",{key:0,onClick:g=>o(b),class:_(["p-0.5 rounded",f(r).theme==="dark"?"hover:bg-gray-600":"hover:bg-gray-100"])},[(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":b.isExpanded,"text-gray-600 hover:text-gray-800":f(r).theme==="light","text-gray-400 hover:text-gray-200":f(r).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},i[1]||(i[1]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],10,jr)):B("",!0),d("span",{class:_({"text-gray-700 hover:text-gray-900":f(r).theme==="light","text-gray-300 hover:text-gray-100":f(r).theme==="dark","cursor-pointer flex-grow":!0}),onClick:g=>c(b)},q(b.label),11,Vr)],2),b.children&&b.children.length>0&&b.isExpanded?(w(),T("div",Yr,[(w(!0),T(ge,null,ke(b.children,g=>(w(),T("div",{key:g.nodeKey,class:"pl-3"},[d("div",{class:_(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200",f(r).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"])},[d("span",{class:_({"text-gray-700 hover:text-gray-900":f(r).theme==="light","text-gray-300 hover:text-gray-100":f(r).theme==="dark","cursor-pointer flex-grow":!0}),onClick:A=>c(g)},q(g.label),11,Zr)],2)]))),128))])):B("",!0)]))),128))])):B("",!0)]))),128))]))}}),Kr=$n(Xr,[["__scopeId","data-v-a8953f9c"]]),Qr=Wt({__name:"TypeWriter",props:{text:{},speed:{}},emits:["complete"],setup(n,{emit:e}){const t=n,r=e,s=I(""),a=I(0),c=I(!1),o=()=>{s.value="",a.value=0,c.value=!0;const h=setInterval(()=>{a.value<t.text.length?(s.value+=t.text[a.value],a.value++):(clearInterval(h),c.value=!1,r("complete"))},t.speed||50)};return zn(()=>t.text,()=>{o()}),Un(()=>{o()}),(h,i)=>(w(),T("span",null,q(s.value),1))}});function jt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var Se=jt();function Wn(n){Se=n}var Ye={exec:()=>null};function L(n,e=""){let t=typeof n=="string"?n:n.source;const r={replace:(s,a)=>{let c=typeof a=="string"?a:a.source;return c=c.replace(K.caret,"$1"),t=t.replace(s,c),r},getRegex:()=>new RegExp(t,e)};return r}var K={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:n=>new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}#`),htmlBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}<(?:[a-z].*>|!--)`,"i")},Jr=/^(?:[ \t]*(?:\n|$))+/,es=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,ts=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Xe=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,ns=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Vt=/(?:[*+-]|\d{1,9}[.)])/,qn=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,jn=L(qn).replace(/bull/g,Vt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),rs=L(qn).replace(/bull/g,Vt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Yt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,ss=/^[^\n]+/,Zt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,as=L(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Zt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ls=L(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Vt).getRegex(),yt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Xt=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,os=L("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Xt).replace("tag",yt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Vn=L(Yt).replace("hr",Xe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",yt).getRegex(),is=L(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Vn).getRegex(),Kt={blockquote:is,code:es,def:as,fences:ts,heading:ns,hr:Xe,html:os,lheading:jn,list:ls,newline:Jr,paragraph:Vn,table:Ye,text:ss},vn=L("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Xe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",yt).getRegex(),cs={...Kt,lheading:rs,table:vn,paragraph:L(Yt).replace("hr",Xe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",vn).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",yt).getRegex()},us={...Kt,html:L(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Xt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Ye,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:L(Yt).replace("hr",Xe).replace("heading",` *#{1,6} *[^
]`).replace("lheading",jn).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ds=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ps=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Yn=/^( {2,}|\\)\n(?!\s*$)/,hs=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,wt=/[\p{P}\p{S}]/u,Qt=/[\s\p{P}\p{S}]/u,Zn=/[^\s\p{P}\p{S}]/u,fs=L(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Qt).getRegex(),Xn=/(?!~)[\p{P}\p{S}]/u,gs=/(?!~)[\s\p{P}\p{S}]/u,ms=/(?:[^\s\p{P}\p{S}]|~)/u,bs=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Kn=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,xs=L(Kn,"u").replace(/punct/g,wt).getRegex(),ks=L(Kn,"u").replace(/punct/g,Xn).getRegex(),Qn="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",ys=L(Qn,"gu").replace(/notPunctSpace/g,Zn).replace(/punctSpace/g,Qt).replace(/punct/g,wt).getRegex(),ws=L(Qn,"gu").replace(/notPunctSpace/g,ms).replace(/punctSpace/g,gs).replace(/punct/g,Xn).getRegex(),vs=L("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Zn).replace(/punctSpace/g,Qt).replace(/punct/g,wt).getRegex(),Ts=L(/\\(punct)/,"gu").replace(/punct/g,wt).getRegex(),_s=L(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Ss=L(Xt).replace("(?:-->|$)","-->").getRegex(),Es=L("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Ss).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),mt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,As=L(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",mt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Jn=L(/^!?\[(label)\]\[(ref)\]/).replace("label",mt).replace("ref",Zt).getRegex(),er=L(/^!?\[(ref)\](?:\[\])?/).replace("ref",Zt).getRegex(),Cs=L("reflink|nolink(?!\\()","g").replace("reflink",Jn).replace("nolink",er).getRegex(),Jt={_backpedal:Ye,anyPunctuation:Ts,autolink:_s,blockSkip:bs,br:Yn,code:ps,del:Ye,emStrongLDelim:xs,emStrongRDelimAst:ys,emStrongRDelimUnd:vs,escape:ds,link:As,nolink:er,punctuation:fs,reflink:Jn,reflinkSearch:Cs,tag:Es,text:hs,url:Ye},Rs={...Jt,link:L(/^!?\[(label)\]\((.*?)\)/).replace("label",mt).getRegex(),reflink:L(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",mt).getRegex()},Ut={...Jt,emStrongRDelimAst:ws,emStrongLDelim:ks,url:L(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ds={...Ut,br:L(Yn).replace("{2,}","*").getRegex(),text:L(Ut.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},dt={normal:Kt,gfm:cs,pedantic:us},Fe={normal:Jt,gfm:Ut,breaks:Ds,pedantic:Rs},Ms={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Tn=n=>Ms[n];function ie(n,e){if(e){if(K.escapeTest.test(n))return n.replace(K.escapeReplace,Tn)}else if(K.escapeTestNoEncode.test(n))return n.replace(K.escapeReplaceNoEncode,Tn);return n}function _n(n){try{n=encodeURI(n).replace(K.percentDecode,"%")}catch{return null}return n}function Sn(n,e){var a;const t=n.replace(K.findPipe,(c,o,h)=>{let i=!1,p=o;for(;--p>=0&&h[p]==="\\";)i=!i;return i?"|":" |"}),r=t.split(K.splitPipe);let s=0;if(r[0].trim()||r.shift(),r.length>0&&!((a=r.at(-1))!=null&&a.trim())&&r.pop(),e)if(r.length>e)r.splice(e);else for(;r.length<e;)r.push("");for(;s<r.length;s++)r[s]=r[s].trim().replace(K.slashPipe,"|");return r}function He(n,e,t){const r=n.length;if(r===0)return"";let s=0;for(;s<r&&n.charAt(r-s-1)===e;)s++;return n.slice(0,r-s)}function Ls(n,e){if(n.indexOf(e[1])===-1)return-1;let t=0;for(let r=0;r<n.length;r++)if(n[r]==="\\")r++;else if(n[r]===e[0])t++;else if(n[r]===e[1]&&(t--,t<0))return r;return t>0?-2:-1}function En(n,e,t,r,s){const a=e.href,c=e.title||null,o=n[1].replace(s.other.outputLinkReplace,"$1");r.state.inLink=!0;const h={type:n[0].charAt(0)==="!"?"image":"link",raw:t,href:a,title:c,text:o,tokens:r.inlineTokens(o)};return r.state.inLink=!1,h}function Is(n,e,t){const r=n.match(t.other.indentCodeCompensation);if(r===null)return e;const s=r[1];return e.split(`
`).map(a=>{const c=a.match(t.other.beginningSpace);if(c===null)return a;const[o]=c;return o.length>=s.length?a.slice(s.length):a}).join(`
`)}var bt=class{constructor(n){N(this,"options");N(this,"rules");N(this,"lexer");this.options=n||Se}space(n){const e=this.rules.block.newline.exec(n);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(n){const e=this.rules.block.code.exec(n);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:He(t,`
`)}}}fences(n){const e=this.rules.block.fences.exec(n);if(e){const t=e[0],r=Is(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:r}}}heading(n){const e=this.rules.block.heading.exec(n);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const r=He(t,"#");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(t=r.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(n){const e=this.rules.block.hr.exec(n);if(e)return{type:"hr",raw:He(e[0],`
`)}}blockquote(n){const e=this.rules.block.blockquote.exec(n);if(e){let t=He(e[0],`
`).split(`
`),r="",s="";const a=[];for(;t.length>0;){let c=!1;const o=[];let h;for(h=0;h<t.length;h++)if(this.rules.other.blockquoteStart.test(t[h]))o.push(t[h]),c=!0;else if(!c)o.push(t[h]);else break;t=t.slice(h);const i=o.join(`
`),p=i.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}
${i}`:i,s=s?`${s}
${p}`:p;const b=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(p,a,!0),this.lexer.state.top=b,t.length===0)break;const g=a.at(-1);if((g==null?void 0:g.type)==="code")break;if((g==null?void 0:g.type)==="blockquote"){const A=g,S=A.raw+`
`+t.join(`
`),E=this.blockquote(S);a[a.length-1]=E,r=r.substring(0,r.length-A.raw.length)+E.raw,s=s.substring(0,s.length-A.text.length)+E.text;break}else if((g==null?void 0:g.type)==="list"){const A=g,S=A.raw+`
`+t.join(`
`),E=this.list(S);a[a.length-1]=E,r=r.substring(0,r.length-g.raw.length)+E.raw,s=s.substring(0,s.length-A.raw.length)+E.raw,t=S.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:r,tokens:a,text:s}}}list(n){let e=this.rules.block.list.exec(n);if(e){let t=e[1].trim();const r=t.length>1,s={type:"list",raw:"",ordered:r,start:r?+t.slice(0,-1):"",loose:!1,items:[]};t=r?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=r?t:"[*+-]");const a=this.rules.other.listItemRegex(t);let c=!1;for(;n;){let h=!1,i="",p="";if(!(e=a.exec(n))||this.rules.block.hr.test(n))break;i=e[0],n=n.substring(i.length);let b=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,$=>" ".repeat(3*$.length)),g=n.split(`
`,1)[0],A=!b.trim(),S=0;if(this.options.pedantic?(S=2,p=b.trimStart()):A?S=e[1].length+1:(S=e[2].search(this.rules.other.nonSpaceChar),S=S>4?1:S,p=b.slice(S),S+=e[1].length),A&&this.rules.other.blankLine.test(g)&&(i+=g+`
`,n=n.substring(g.length+1),h=!0),!h){const $=this.rules.other.nextBulletRegex(S),D=this.rules.other.hrRegex(S),j=this.rules.other.fencesBeginRegex(S),O=this.rules.other.headingBeginRegex(S),ne=this.rules.other.htmlBeginRegex(S);for(;n;){const re=n.split(`
`,1)[0];let le;if(g=re,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),le=g):le=g.replace(this.rules.other.tabCharGlobal,"    "),j.test(g)||O.test(g)||ne.test(g)||$.test(g)||D.test(g))break;if(le.search(this.rules.other.nonSpaceChar)>=S||!g.trim())p+=`
`+le.slice(S);else{if(A||b.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||j.test(b)||O.test(b)||D.test(b))break;p+=`
`+g}!A&&!g.trim()&&(A=!0),i+=re+`
`,n=n.substring(re.length+1),b=le.slice(S)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(i)&&(c=!0));let E=null,C;this.options.gfm&&(E=this.rules.other.listIsTask.exec(p),E&&(C=E[0]!=="[ ] ",p=p.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:i,task:!!E,checked:C,loose:!1,text:p,tokens:[]}),s.raw+=i}const o=s.items.at(-1);if(o)o.raw=o.raw.trimEnd(),o.text=o.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let h=0;h<s.items.length;h++)if(this.lexer.state.top=!1,s.items[h].tokens=this.lexer.blockTokens(s.items[h].text,[]),!s.loose){const i=s.items[h].tokens.filter(b=>b.type==="space"),p=i.length>0&&i.some(b=>this.rules.other.anyLine.test(b.raw));s.loose=p}if(s.loose)for(let h=0;h<s.items.length;h++)s.items[h].loose=!0;return s}}html(n){const e=this.rules.block.html.exec(n);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(n){const e=this.rules.block.def.exec(n);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:r,title:s}}}table(n){var c;const e=this.rules.block.table.exec(n);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=Sn(e[1]),r=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===r.length){for(const o of r)this.rules.other.tableAlignRight.test(o)?a.align.push("right"):this.rules.other.tableAlignCenter.test(o)?a.align.push("center"):this.rules.other.tableAlignLeft.test(o)?a.align.push("left"):a.align.push(null);for(let o=0;o<t.length;o++)a.header.push({text:t[o],tokens:this.lexer.inline(t[o]),header:!0,align:a.align[o]});for(const o of s)a.rows.push(Sn(o,a.header.length).map((h,i)=>({text:h,tokens:this.lexer.inline(h),header:!1,align:a.align[i]})));return a}}lheading(n){const e=this.rules.block.lheading.exec(n);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(n){const e=this.rules.block.paragraph.exec(n);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(n){const e=this.rules.block.text.exec(n);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(n){const e=this.rules.inline.escape.exec(n);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(n){const e=this.rules.inline.tag.exec(n);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(n){const e=this.rules.inline.link.exec(n);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const a=He(t.slice(0,-1),"\\");if((t.length-a.length)%2===0)return}else{const a=Ls(e[2],"()");if(a===-2)return;if(a>-1){const o=(e[0].indexOf("!")===0?5:4)+e[1].length+a;e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,o).trim(),e[3]=""}}let r=e[2],s="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(r);a&&(r=a[1],s=a[3])}else s=e[3]?e[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?r=r.slice(1):r=r.slice(1,-1)),En(e,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(n,e){let t;if((t=this.rules.inline.reflink.exec(n))||(t=this.rules.inline.nolink.exec(n))){const r=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[r.toLowerCase()];if(!s){const a=t[0].charAt(0);return{type:"text",raw:a,text:a}}return En(t,s,t[0],this.lexer,this.rules)}}emStrong(n,e,t=""){let r=this.rules.inline.emStrongLDelim.exec(n);if(!r||r[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(r[1]||r[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const a=[...r[0]].length-1;let c,o,h=a,i=0;const p=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,e=e.slice(-1*n.length+a);(r=p.exec(e))!=null;){if(c=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!c)continue;if(o=[...c].length,r[3]||r[4]){h+=o;continue}else if((r[5]||r[6])&&a%3&&!((a+o)%3)){i+=o;continue}if(h-=o,h>0)continue;o=Math.min(o,o+h+i);const b=[...r[0]][0].length,g=n.slice(0,a+r.index+b+o);if(Math.min(a,o)%2){const S=g.slice(1,-1);return{type:"em",raw:g,text:S,tokens:this.lexer.inlineTokens(S)}}const A=g.slice(2,-2);return{type:"strong",raw:g,text:A,tokens:this.lexer.inlineTokens(A)}}}}codespan(n){const e=this.rules.inline.code.exec(n);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const r=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return r&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(n){const e=this.rules.inline.br.exec(n);if(e)return{type:"br",raw:e[0]}}del(n){const e=this.rules.inline.del.exec(n);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(n){const e=this.rules.inline.autolink.exec(n);if(e){let t,r;return e[2]==="@"?(t=e[1],r="mailto:"+t):(t=e[1],r=t),{type:"link",raw:e[0],text:t,href:r,tokens:[{type:"text",raw:t,text:t}]}}}url(n){var t;let e;if(e=this.rules.inline.url.exec(n)){let r,s;if(e[2]==="@")r=e[0],s="mailto:"+r;else{let a;do a=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(a!==e[0]);r=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(n){const e=this.rules.inline.text.exec(n);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},me=class Bt{constructor(e){N(this,"tokens");N(this,"options");N(this,"state");N(this,"tokenizer");N(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Se,this.options.tokenizer=this.options.tokenizer||new bt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:K,block:dt.normal,inline:Fe.normal};this.options.pedantic?(t.block=dt.pedantic,t.inline=Fe.pedantic):this.options.gfm&&(t.block=dt.gfm,this.options.breaks?t.inline=Fe.breaks:t.inline=Fe.gfm),this.tokenizer.rules=t}static get rules(){return{block:dt,inline:Fe}}static lex(e,t){return new Bt(t).lex(e)}static lexInline(e,t){return new Bt(t).inlineTokens(e)}lex(e){e=e.replace(K.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const r=this.inlineQueue[t];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],r=!1){var s,a,c;for(this.options.pedantic&&(e=e.replace(K.tabCharGlobal,"    ").replace(K.spaceLine,""));e;){let o;if((a=(s=this.options.extensions)==null?void 0:s.block)!=null&&a.some(i=>(o=i.call({lexer:this},e,t))?(e=e.substring(o.raw.length),t.push(o),!0):!1))continue;if(o=this.tokenizer.space(e)){e=e.substring(o.raw.length);const i=t.at(-1);o.raw.length===1&&i!==void 0?i.raw+=`
`:t.push(o);continue}if(o=this.tokenizer.code(e)){e=e.substring(o.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+o.raw,i.text+=`
`+o.text,this.inlineQueue.at(-1).src=i.text):t.push(o);continue}if(o=this.tokenizer.fences(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.heading(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.hr(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.blockquote(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.list(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.html(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.def(e)){e=e.substring(o.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="paragraph"||(i==null?void 0:i.type)==="text"?(i.raw+=`
`+o.raw,i.text+=`
`+o.raw,this.inlineQueue.at(-1).src=i.text):this.tokens.links[o.tag]||(this.tokens.links[o.tag]={href:o.href,title:o.title});continue}if(o=this.tokenizer.table(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.lheading(e)){e=e.substring(o.raw.length),t.push(o);continue}let h=e;if((c=this.options.extensions)!=null&&c.startBlock){let i=1/0;const p=e.slice(1);let b;this.options.extensions.startBlock.forEach(g=>{b=g.call({lexer:this},p),typeof b=="number"&&b>=0&&(i=Math.min(i,b))}),i<1/0&&i>=0&&(h=e.substring(0,i+1))}if(this.state.top&&(o=this.tokenizer.paragraph(h))){const i=t.at(-1);r&&(i==null?void 0:i.type)==="paragraph"?(i.raw+=`
`+o.raw,i.text+=`
`+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(o),r=h.length!==e.length,e=e.substring(o.raw.length);continue}if(o=this.tokenizer.text(e)){e=e.substring(o.raw.length);const i=t.at(-1);(i==null?void 0:i.type)==="text"?(i.raw+=`
`+o.raw,i.text+=`
`+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=i.text):t.push(o);continue}if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}else throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var o,h,i;let r=e,s=null;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)p.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,s.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,c="";for(;e;){a||(c=""),a=!1;let p;if((h=(o=this.options.extensions)==null?void 0:o.inline)!=null&&h.some(g=>(p=g.call({lexer:this},e,t))?(e=e.substring(p.raw.length),t.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);const g=t.at(-1);p.type==="text"&&(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(p=this.tokenizer.emStrong(e,r,c)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),t.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),t.push(p);continue}let b=e;if((i=this.options.extensions)!=null&&i.startInline){let g=1/0;const A=e.slice(1);let S;this.options.extensions.startInline.forEach(E=>{S=E.call({lexer:this},A),typeof S=="number"&&S>=0&&(g=Math.min(g,S))}),g<1/0&&g>=0&&(b=e.substring(0,g+1))}if(p=this.tokenizer.inlineText(b)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(c=p.raw.slice(-1)),a=!0;const g=t.at(-1);(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(e){const g="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(g);break}else throw new Error(g)}}return t}},xt=class{constructor(n){N(this,"options");N(this,"parser");this.options=n||Se}space(n){return""}code({text:n,lang:e,escaped:t}){var a;const r=(a=(e||"").match(K.notSpaceStart))==null?void 0:a[0],s=n.replace(K.endingNewline,"")+`
`;return r?'<pre><code class="language-'+ie(r)+'">'+(t?s:ie(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:ie(s,!0))+`</code></pre>
`}blockquote({tokens:n}){return`<blockquote>
${this.parser.parse(n)}</blockquote>
`}html({text:n}){return n}heading({tokens:n,depth:e}){return`<h${e}>${this.parser.parseInline(n)}</h${e}>
`}hr(n){return`<hr>
`}list(n){const e=n.ordered,t=n.start;let r="";for(let c=0;c<n.items.length;c++){const o=n.items[c];r+=this.listitem(o)}const s=e?"ol":"ul",a=e&&t!==1?' start="'+t+'"':"";return"<"+s+a+`>
`+r+"</"+s+`>
`}listitem(n){var t;let e="";if(n.task){const r=this.checkbox({checked:!!n.checked});n.loose?((t=n.tokens[0])==null?void 0:t.type)==="paragraph"?(n.tokens[0].text=r+" "+n.tokens[0].text,n.tokens[0].tokens&&n.tokens[0].tokens.length>0&&n.tokens[0].tokens[0].type==="text"&&(n.tokens[0].tokens[0].text=r+" "+ie(n.tokens[0].tokens[0].text),n.tokens[0].tokens[0].escaped=!0)):n.tokens.unshift({type:"text",raw:r+" ",text:r+" ",escaped:!0}):e+=r+" "}return e+=this.parser.parse(n.tokens,!!n.loose),`<li>${e}</li>
`}checkbox({checked:n}){return"<input "+(n?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:n}){return`<p>${this.parser.parseInline(n)}</p>
`}table(n){let e="",t="";for(let s=0;s<n.header.length;s++)t+=this.tablecell(n.header[s]);e+=this.tablerow({text:t});let r="";for(let s=0;s<n.rows.length;s++){const a=n.rows[s];t="";for(let c=0;c<a.length;c++)t+=this.tablecell(a[c]);r+=this.tablerow({text:t})}return r&&(r=`<tbody>${r}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+r+`</table>
`}tablerow({text:n}){return`<tr>
${n}</tr>
`}tablecell(n){const e=this.parser.parseInline(n.tokens),t=n.header?"th":"td";return(n.align?`<${t} align="${n.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:n}){return`<strong>${this.parser.parseInline(n)}</strong>`}em({tokens:n}){return`<em>${this.parser.parseInline(n)}</em>`}codespan({text:n}){return`<code>${ie(n,!0)}</code>`}br(n){return"<br>"}del({tokens:n}){return`<del>${this.parser.parseInline(n)}</del>`}link({href:n,title:e,tokens:t}){const r=this.parser.parseInline(t),s=_n(n);if(s===null)return r;n=s;let a='<a href="'+n+'"';return e&&(a+=' title="'+ie(e)+'"'),a+=">"+r+"</a>",a}image({href:n,title:e,text:t,tokens:r}){r&&(t=this.parser.parseInline(r,this.parser.textRenderer));const s=_n(n);if(s===null)return ie(t);n=s;let a=`<img src="${n}" alt="${t}"`;return e&&(a+=` title="${ie(e)}"`),a+=">",a}text(n){return"tokens"in n&&n.tokens?this.parser.parseInline(n.tokens):"escaped"in n&&n.escaped?n.text:ie(n.text)}},en=class{strong({text:n}){return n}em({text:n}){return n}codespan({text:n}){return n}del({text:n}){return n}html({text:n}){return n}text({text:n}){return n}link({text:n}){return""+n}image({text:n}){return""+n}br(){return""}},be=class Ft{constructor(e){N(this,"options");N(this,"renderer");N(this,"textRenderer");this.options=e||Se,this.options.renderer=this.options.renderer||new xt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new en}static parse(e,t){return new Ft(t).parse(e)}static parseInline(e,t){return new Ft(t).parseInline(e)}parse(e,t=!0){var s,a;let r="";for(let c=0;c<e.length;c++){const o=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[o.type]){const i=o,p=this.options.extensions.renderers[i.type].call({parser:this},i);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){r+=p||"";continue}}const h=o;switch(h.type){case"space":{r+=this.renderer.space(h);continue}case"hr":{r+=this.renderer.hr(h);continue}case"heading":{r+=this.renderer.heading(h);continue}case"code":{r+=this.renderer.code(h);continue}case"table":{r+=this.renderer.table(h);continue}case"blockquote":{r+=this.renderer.blockquote(h);continue}case"list":{r+=this.renderer.list(h);continue}case"html":{r+=this.renderer.html(h);continue}case"paragraph":{r+=this.renderer.paragraph(h);continue}case"text":{let i=h,p=this.renderer.text(i);for(;c+1<e.length&&e[c+1].type==="text";)i=e[++c],p+=`
`+this.renderer.text(i);t?r+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):r+=p;continue}default:{const i='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return r}parseInline(e,t=this.renderer){var s,a;let r="";for(let c=0;c<e.length;c++){const o=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[o.type]){const i=this.options.extensions.renderers[o.type].call({parser:this},o);if(i!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type)){r+=i||"";continue}}const h=o;switch(h.type){case"escape":{r+=t.text(h);break}case"html":{r+=t.html(h);break}case"link":{r+=t.link(h);break}case"image":{r+=t.image(h);break}case"strong":{r+=t.strong(h);break}case"em":{r+=t.em(h);break}case"codespan":{r+=t.codespan(h);break}case"br":{r+=t.br(h);break}case"del":{r+=t.del(h);break}case"text":{r+=t.text(h);break}default:{const i='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return r}},$t,ft=($t=class{constructor(n){N(this,"options");N(this,"block");this.options=n||Se}preprocess(n){return n}postprocess(n){return n}processAllTokens(n){return n}provideLexer(){return this.block?me.lex:me.lexInline}provideParser(){return this.block?be.parse:be.parseInline}},N($t,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),$t),Ns=class{constructor(...n){N(this,"defaults",jt());N(this,"options",this.setOptions);N(this,"parse",this.parseMarkdown(!0));N(this,"parseInline",this.parseMarkdown(!1));N(this,"Parser",be);N(this,"Renderer",xt);N(this,"TextRenderer",en);N(this,"Lexer",me);N(this,"Tokenizer",bt);N(this,"Hooks",ft);this.use(...n)}walkTokens(n,e){var r,s;let t=[];for(const a of n)switch(t=t.concat(e.call(this,a)),a.type){case"table":{const c=a;for(const o of c.header)t=t.concat(this.walkTokens(o.tokens,e));for(const o of c.rows)for(const h of o)t=t.concat(this.walkTokens(h.tokens,e));break}case"list":{const c=a;t=t.concat(this.walkTokens(c.items,e));break}default:{const c=a;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(o=>{const h=c[o].flat(1/0);t=t.concat(this.walkTokens(h,e))}):c.tokens&&(t=t.concat(this.walkTokens(c.tokens,e)))}}return t}use(...n){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return n.forEach(t=>{const r={...t};if(r.async=this.defaults.async||r.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=e.renderers[s.name];a?e.renderers[s.name]=function(...c){let o=s.renderer.apply(this,c);return o===!1&&(o=a.apply(this,c)),o}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=e[s.level];a?a.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),r.extensions=e),t.renderer){const s=this.defaults.renderer||new xt(this.defaults);for(const a in t.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const c=a,o=t.renderer[c],h=s[c];s[c]=(...i)=>{let p=o.apply(s,i);return p===!1&&(p=h.apply(s,i)),p||""}}r.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new bt(this.defaults);for(const a in t.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const c=a,o=t.tokenizer[c],h=s[c];s[c]=(...i)=>{let p=o.apply(s,i);return p===!1&&(p=h.apply(s,i)),p}}r.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new ft;for(const a in t.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const c=a,o=t.hooks[c],h=s[c];ft.passThroughHooks.has(a)?s[c]=i=>{if(this.defaults.async)return Promise.resolve(o.call(s,i)).then(b=>h.call(s,b));const p=o.call(s,i);return h.call(s,p)}:s[c]=(...i)=>{let p=o.apply(s,i);return p===!1&&(p=h.apply(s,i)),p}}r.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,a=t.walkTokens;r.walkTokens=function(c){let o=[];return o.push(a.call(this,c)),s&&(o=o.concat(s.call(this,c))),o}}this.defaults={...this.defaults,...r}}),this}setOptions(n){return this.defaults={...this.defaults,...n},this}lexer(n,e){return me.lex(n,e??this.defaults)}parser(n,e){return be.parse(n,e??this.defaults)}parseMarkdown(n){return(t,r)=>{const s={...r},a={...this.defaults,...s},c=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&s.async===!1)return c(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return c(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return c(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=n);const o=a.hooks?a.hooks.provideLexer():n?me.lex:me.lexInline,h=a.hooks?a.hooks.provideParser():n?be.parse:be.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(i=>o(i,a)).then(i=>a.hooks?a.hooks.processAllTokens(i):i).then(i=>a.walkTokens?Promise.all(this.walkTokens(i,a.walkTokens)).then(()=>i):i).then(i=>h(i,a)).then(i=>a.hooks?a.hooks.postprocess(i):i).catch(c);try{a.hooks&&(t=a.hooks.preprocess(t));let i=o(t,a);a.hooks&&(i=a.hooks.processAllTokens(i)),a.walkTokens&&this.walkTokens(i,a.walkTokens);let p=h(i,a);return a.hooks&&(p=a.hooks.postprocess(p)),p}catch(i){return c(i)}}}onError(n,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,n){const r="<p>An error occurred:</p><pre>"+ie(t.message+"",!0)+"</pre>";return e?Promise.resolve(r):r}if(e)return Promise.reject(t);throw t}}},_e=new Ns;function M(n,e){return _e.parse(n,e)}M.options=M.setOptions=function(n){return _e.setOptions(n),M.defaults=_e.defaults,Wn(M.defaults),M};M.getDefaults=jt;M.defaults=Se;M.use=function(...n){return _e.use(...n),M.defaults=_e.defaults,Wn(M.defaults),M};M.walkTokens=function(n,e){return _e.walkTokens(n,e)};M.parseInline=_e.parseInline;M.Parser=be;M.parser=be.parse;M.Renderer=xt;M.TextRenderer=en;M.Lexer=me;M.lexer=me.lex;M.Tokenizer=bt;M.Hooks=ft;M.parse=M;M.options;M.setOptions;M.use;M.walkTokens;M.parseInline;be.parse;me.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:tr,setPrototypeOf:An,isFrozen:Os,getPrototypeOf:Ps,getOwnPropertyDescriptor:zs}=Object;let{freeze:Q,seal:se,create:nr}=Object,{apply:Ht,construct:Gt}=typeof Reflect<"u"&&Reflect;Q||(Q=function(e){return e});se||(se=function(e){return e});Ht||(Ht=function(e,t,r){return e.apply(t,r)});Gt||(Gt=function(e,t){return new e(...t)});const pt=J(Array.prototype.forEach),$s=J(Array.prototype.lastIndexOf),Cn=J(Array.prototype.pop),Ge=J(Array.prototype.push),Us=J(Array.prototype.splice),gt=J(String.prototype.toLowerCase),It=J(String.prototype.toString),Rn=J(String.prototype.match),We=J(String.prototype.replace),Bs=J(String.prototype.indexOf),Fs=J(String.prototype.trim),ae=J(Object.prototype.hasOwnProperty),X=J(RegExp.prototype.test),qe=Hs(TypeError);function J(n){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];return Ht(n,e,r)}}function Hs(n){return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Gt(n,t)}}function R(n,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:gt;An&&An(n,null);let r=e.length;for(;r--;){let s=e[r];if(typeof s=="string"){const a=t(s);a!==s&&(Os(e)||(e[r]=a),s=a)}n[s]=!0}return n}function Gs(n){for(let e=0;e<n.length;e++)ae(n,e)||(n[e]=null);return n}function fe(n){const e=nr(null);for(const[t,r]of tr(n))ae(n,t)&&(Array.isArray(r)?e[t]=Gs(r):r&&typeof r=="object"&&r.constructor===Object?e[t]=fe(r):e[t]=r);return e}function je(n,e){for(;n!==null;){const r=zs(n,e);if(r){if(r.get)return J(r.get);if(typeof r.value=="function")return J(r.value)}n=Ps(n)}function t(){return null}return t}const Dn=Q(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Nt=Q(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ot=Q(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ws=Q(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Pt=Q(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),qs=Q(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Mn=Q(["#text"]),Ln=Q(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),zt=Q(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),In=Q(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ht=Q(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),js=se(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Vs=se(/<%[\w\W]*|[\w\W]*%>/gm),Ys=se(/\$\{[\w\W]*/gm),Zs=se(/^data-[\-\w.\u00B7-\uFFFF]+$/),Xs=se(/^aria-[\-\w]+$/),rr=se(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ks=se(/^(?:\w+script|data):/i),Qs=se(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),sr=se(/^html$/i),Js=se(/^[a-z][.\w]*(-[.\w]+)+$/i);var Nn=Object.freeze({__proto__:null,ARIA_ATTR:Xs,ATTR_WHITESPACE:Qs,CUSTOM_ELEMENT:Js,DATA_ATTR:Zs,DOCTYPE_NAME:sr,ERB_EXPR:Vs,IS_ALLOWED_URI:rr,IS_SCRIPT_OR_DATA:Ks,MUSTACHE_EXPR:js,TMPLIT_EXPR:Ys});const Ve={element:1,text:3,progressingInstruction:7,comment:8,document:9},ea=function(){return typeof window>"u"?null:window},ta=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let r=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(r=t.getAttribute(s));const a="dompurify"+(r?"#"+r:"");try{return e.createPolicy(a,{createHTML(c){return c},createScriptURL(c){return c}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},On=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function ar(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ea();const e=v=>ar(v);if(e.version="3.2.6",e.removed=[],!n||!n.document||n.document.nodeType!==Ve.document||!n.Element)return e.isSupported=!1,e;let{document:t}=n;const r=t,s=r.currentScript,{DocumentFragment:a,HTMLTemplateElement:c,Node:o,Element:h,NodeFilter:i,NamedNodeMap:p=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:b,DOMParser:g,trustedTypes:A}=n,S=h.prototype,E=je(S,"cloneNode"),C=je(S,"remove"),$=je(S,"nextSibling"),D=je(S,"childNodes"),j=je(S,"parentNode");if(typeof c=="function"){const v=t.createElement("template");v.content&&v.content.ownerDocument&&(t=v.content.ownerDocument)}let O,ne="";const{implementation:re,createNodeIterator:le,createDocumentFragment:Ke,getElementsByTagName:ye}=t,{importNode:vt}=r;let V=On();e.isSupported=typeof tr=="function"&&typeof j=="function"&&re&&re.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Ie,ERB_EXPR:Ne,TMPLIT_EXPR:Oe,DATA_ATTR:Tt,ARIA_ATTR:Pe,IS_SCRIPT_OR_DATA:Qe,ATTR_WHITESPACE:Je,CUSTOM_ELEMENT:_t}=Nn;let{IS_ALLOWED_URI:et}=Nn,U=null;const Ee=R({},[...Dn,...Nt,...Ot,...Pt,...Mn]);let F=null;const tt=R({},[...Ln,...zt,...In,...ht]);let P=Object.seal(nr(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),we=null,ve=null,nt=!0,ze=!0,rt=!1,st=!0,x=!1,u=!0,H=!1,ee=!1,y=!1,ue=!1,at=!1,lt=!1,nn=!0,rn=!1;const lr="user-content-";let St=!0,$e=!1,Ae={},Ce=null;const sn=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let an=null;const ln=R({},["audio","video","img","source","image","track"]);let Et=null;const on=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ot="http://www.w3.org/1998/Math/MathML",it="http://www.w3.org/2000/svg",de="http://www.w3.org/1999/xhtml";let Re=de,At=!1,Ct=null;const or=R({},[ot,it,de],It);let ct=R({},["mi","mo","mn","ms","mtext"]),ut=R({},["annotation-xml"]);const ir=R({},["title","style","font","a","script"]);let Ue=null;const cr=["application/xhtml+xml","text/html"],ur="text/html";let W=null,De=null;const dr=t.createElement("form"),cn=function(l){return l instanceof RegExp||l instanceof Function},Rt=function(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(De&&De===l)){if((!l||typeof l!="object")&&(l={}),l=fe(l),Ue=cr.indexOf(l.PARSER_MEDIA_TYPE)===-1?ur:l.PARSER_MEDIA_TYPE,W=Ue==="application/xhtml+xml"?It:gt,U=ae(l,"ALLOWED_TAGS")?R({},l.ALLOWED_TAGS,W):Ee,F=ae(l,"ALLOWED_ATTR")?R({},l.ALLOWED_ATTR,W):tt,Ct=ae(l,"ALLOWED_NAMESPACES")?R({},l.ALLOWED_NAMESPACES,It):or,Et=ae(l,"ADD_URI_SAFE_ATTR")?R(fe(on),l.ADD_URI_SAFE_ATTR,W):on,an=ae(l,"ADD_DATA_URI_TAGS")?R(fe(ln),l.ADD_DATA_URI_TAGS,W):ln,Ce=ae(l,"FORBID_CONTENTS")?R({},l.FORBID_CONTENTS,W):sn,we=ae(l,"FORBID_TAGS")?R({},l.FORBID_TAGS,W):fe({}),ve=ae(l,"FORBID_ATTR")?R({},l.FORBID_ATTR,W):fe({}),Ae=ae(l,"USE_PROFILES")?l.USE_PROFILES:!1,nt=l.ALLOW_ARIA_ATTR!==!1,ze=l.ALLOW_DATA_ATTR!==!1,rt=l.ALLOW_UNKNOWN_PROTOCOLS||!1,st=l.ALLOW_SELF_CLOSE_IN_ATTR!==!1,x=l.SAFE_FOR_TEMPLATES||!1,u=l.SAFE_FOR_XML!==!1,H=l.WHOLE_DOCUMENT||!1,ue=l.RETURN_DOM||!1,at=l.RETURN_DOM_FRAGMENT||!1,lt=l.RETURN_TRUSTED_TYPE||!1,y=l.FORCE_BODY||!1,nn=l.SANITIZE_DOM!==!1,rn=l.SANITIZE_NAMED_PROPS||!1,St=l.KEEP_CONTENT!==!1,$e=l.IN_PLACE||!1,et=l.ALLOWED_URI_REGEXP||rr,Re=l.NAMESPACE||de,ct=l.MATHML_TEXT_INTEGRATION_POINTS||ct,ut=l.HTML_INTEGRATION_POINTS||ut,P=l.CUSTOM_ELEMENT_HANDLING||{},l.CUSTOM_ELEMENT_HANDLING&&cn(l.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(P.tagNameCheck=l.CUSTOM_ELEMENT_HANDLING.tagNameCheck),l.CUSTOM_ELEMENT_HANDLING&&cn(l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(P.attributeNameCheck=l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),l.CUSTOM_ELEMENT_HANDLING&&typeof l.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(P.allowCustomizedBuiltInElements=l.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),x&&(ze=!1),at&&(ue=!0),Ae&&(U=R({},Mn),F=[],Ae.html===!0&&(R(U,Dn),R(F,Ln)),Ae.svg===!0&&(R(U,Nt),R(F,zt),R(F,ht)),Ae.svgFilters===!0&&(R(U,Ot),R(F,zt),R(F,ht)),Ae.mathMl===!0&&(R(U,Pt),R(F,In),R(F,ht))),l.ADD_TAGS&&(U===Ee&&(U=fe(U)),R(U,l.ADD_TAGS,W)),l.ADD_ATTR&&(F===tt&&(F=fe(F)),R(F,l.ADD_ATTR,W)),l.ADD_URI_SAFE_ATTR&&R(Et,l.ADD_URI_SAFE_ATTR,W),l.FORBID_CONTENTS&&(Ce===sn&&(Ce=fe(Ce)),R(Ce,l.FORBID_CONTENTS,W)),St&&(U["#text"]=!0),H&&R(U,["html","head","body"]),U.table&&(R(U,["tbody"]),delete we.tbody),l.TRUSTED_TYPES_POLICY){if(typeof l.TRUSTED_TYPES_POLICY.createHTML!="function")throw qe('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof l.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw qe('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');O=l.TRUSTED_TYPES_POLICY,ne=O.createHTML("")}else O===void 0&&(O=ta(A,s)),O!==null&&typeof ne=="string"&&(ne=O.createHTML(""));Q&&Q(l),De=l}},un=R({},[...Nt,...Ot,...Ws]),dn=R({},[...Pt,...qs]),pr=function(l){let m=j(l);(!m||!m.tagName)&&(m={namespaceURI:Re,tagName:"template"});const k=gt(l.tagName),z=gt(m.tagName);return Ct[l.namespaceURI]?l.namespaceURI===it?m.namespaceURI===de?k==="svg":m.namespaceURI===ot?k==="svg"&&(z==="annotation-xml"||ct[z]):!!un[k]:l.namespaceURI===ot?m.namespaceURI===de?k==="math":m.namespaceURI===it?k==="math"&&ut[z]:!!dn[k]:l.namespaceURI===de?m.namespaceURI===it&&!ut[z]||m.namespaceURI===ot&&!ct[z]?!1:!dn[k]&&(ir[k]||!un[k]):!!(Ue==="application/xhtml+xml"&&Ct[l.namespaceURI]):!1},oe=function(l){Ge(e.removed,{element:l});try{j(l).removeChild(l)}catch{C(l)}},Me=function(l,m){try{Ge(e.removed,{attribute:m.getAttributeNode(l),from:m})}catch{Ge(e.removed,{attribute:null,from:m})}if(m.removeAttribute(l),l==="is")if(ue||at)try{oe(m)}catch{}else try{m.setAttribute(l,"")}catch{}},pn=function(l){let m=null,k=null;if(y)l="<remove></remove>"+l;else{const G=Rn(l,/^[\r\n\t ]+/);k=G&&G[0]}Ue==="application/xhtml+xml"&&Re===de&&(l='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+l+"</body></html>");const z=O?O.createHTML(l):l;if(Re===de)try{m=new g().parseFromString(z,Ue)}catch{}if(!m||!m.documentElement){m=re.createDocument(Re,"template",null);try{m.documentElement.innerHTML=At?ne:z}catch{}}const Y=m.body||m.documentElement;return l&&k&&Y.insertBefore(t.createTextNode(k),Y.childNodes[0]||null),Re===de?ye.call(m,H?"html":"body")[0]:H?m.documentElement:Y},hn=function(l){return le.call(l.ownerDocument||l,l,i.SHOW_ELEMENT|i.SHOW_COMMENT|i.SHOW_TEXT|i.SHOW_PROCESSING_INSTRUCTION|i.SHOW_CDATA_SECTION,null)},Dt=function(l){return l instanceof b&&(typeof l.nodeName!="string"||typeof l.textContent!="string"||typeof l.removeChild!="function"||!(l.attributes instanceof p)||typeof l.removeAttribute!="function"||typeof l.setAttribute!="function"||typeof l.namespaceURI!="string"||typeof l.insertBefore!="function"||typeof l.hasChildNodes!="function")},fn=function(l){return typeof o=="function"&&l instanceof o};function pe(v,l,m){pt(v,k=>{k.call(e,l,m,De)})}const gn=function(l){let m=null;if(pe(V.beforeSanitizeElements,l,null),Dt(l))return oe(l),!0;const k=W(l.nodeName);if(pe(V.uponSanitizeElement,l,{tagName:k,allowedTags:U}),u&&l.hasChildNodes()&&!fn(l.firstElementChild)&&X(/<[/\w!]/g,l.innerHTML)&&X(/<[/\w!]/g,l.textContent)||l.nodeType===Ve.progressingInstruction||u&&l.nodeType===Ve.comment&&X(/<[/\w]/g,l.data))return oe(l),!0;if(!U[k]||we[k]){if(!we[k]&&bn(k)&&(P.tagNameCheck instanceof RegExp&&X(P.tagNameCheck,k)||P.tagNameCheck instanceof Function&&P.tagNameCheck(k)))return!1;if(St&&!Ce[k]){const z=j(l)||l.parentNode,Y=D(l)||l.childNodes;if(Y&&z){const G=Y.length;for(let te=G-1;te>=0;--te){const he=E(Y[te],!0);he.__removalCount=(l.__removalCount||0)+1,z.insertBefore(he,$(l))}}}return oe(l),!0}return l instanceof h&&!pr(l)||(k==="noscript"||k==="noembed"||k==="noframes")&&X(/<\/no(script|embed|frames)/i,l.innerHTML)?(oe(l),!0):(x&&l.nodeType===Ve.text&&(m=l.textContent,pt([Ie,Ne,Oe],z=>{m=We(m,z," ")}),l.textContent!==m&&(Ge(e.removed,{element:l.cloneNode()}),l.textContent=m)),pe(V.afterSanitizeElements,l,null),!1)},mn=function(l,m,k){if(nn&&(m==="id"||m==="name")&&(k in t||k in dr))return!1;if(!(ze&&!ve[m]&&X(Tt,m))){if(!(nt&&X(Pe,m))){if(!F[m]||ve[m]){if(!(bn(l)&&(P.tagNameCheck instanceof RegExp&&X(P.tagNameCheck,l)||P.tagNameCheck instanceof Function&&P.tagNameCheck(l))&&(P.attributeNameCheck instanceof RegExp&&X(P.attributeNameCheck,m)||P.attributeNameCheck instanceof Function&&P.attributeNameCheck(m))||m==="is"&&P.allowCustomizedBuiltInElements&&(P.tagNameCheck instanceof RegExp&&X(P.tagNameCheck,k)||P.tagNameCheck instanceof Function&&P.tagNameCheck(k))))return!1}else if(!Et[m]){if(!X(et,We(k,Je,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&l!=="script"&&Bs(k,"data:")===0&&an[l])){if(!(rt&&!X(Qe,We(k,Je,"")))){if(k)return!1}}}}}}return!0},bn=function(l){return l!=="annotation-xml"&&Rn(l,_t)},xn=function(l){pe(V.beforeSanitizeAttributes,l,null);const{attributes:m}=l;if(!m||Dt(l))return;const k={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let z=m.length;for(;z--;){const Y=m[z],{name:G,namespaceURI:te,value:he}=Y,Be=W(G),Mt=he;let Z=G==="value"?Mt:Fs(Mt);if(k.attrName=Be,k.attrValue=Z,k.keepAttr=!0,k.forceKeepAttr=void 0,pe(V.uponSanitizeAttribute,l,k),Z=k.attrValue,rn&&(Be==="id"||Be==="name")&&(Me(G,l),Z=lr+Z),u&&X(/((--!?|])>)|<\/(style|title)/i,Z)){Me(G,l);continue}if(k.forceKeepAttr)continue;if(!k.keepAttr){Me(G,l);continue}if(!st&&X(/\/>/i,Z)){Me(G,l);continue}x&&pt([Ie,Ne,Oe],yn=>{Z=We(Z,yn," ")});const kn=W(l.nodeName);if(!mn(kn,Be,Z)){Me(G,l);continue}if(O&&typeof A=="object"&&typeof A.getAttributeType=="function"&&!te)switch(A.getAttributeType(kn,Be)){case"TrustedHTML":{Z=O.createHTML(Z);break}case"TrustedScriptURL":{Z=O.createScriptURL(Z);break}}if(Z!==Mt)try{te?l.setAttributeNS(te,G,Z):l.setAttribute(G,Z),Dt(l)?oe(l):Cn(e.removed)}catch{Me(G,l)}}pe(V.afterSanitizeAttributes,l,null)},hr=function v(l){let m=null;const k=hn(l);for(pe(V.beforeSanitizeShadowDOM,l,null);m=k.nextNode();)pe(V.uponSanitizeShadowNode,m,null),gn(m),xn(m),m.content instanceof a&&v(m.content);pe(V.afterSanitizeShadowDOM,l,null)};return e.sanitize=function(v){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,k=null,z=null,Y=null;if(At=!v,At&&(v="<!-->"),typeof v!="string"&&!fn(v))if(typeof v.toString=="function"){if(v=v.toString(),typeof v!="string")throw qe("dirty is not a string, aborting")}else throw qe("toString is not a function");if(!e.isSupported)return v;if(ee||Rt(l),e.removed=[],typeof v=="string"&&($e=!1),$e){if(v.nodeName){const he=W(v.nodeName);if(!U[he]||we[he])throw qe("root node is forbidden and cannot be sanitized in-place")}}else if(v instanceof o)m=pn("<!---->"),k=m.ownerDocument.importNode(v,!0),k.nodeType===Ve.element&&k.nodeName==="BODY"||k.nodeName==="HTML"?m=k:m.appendChild(k);else{if(!ue&&!x&&!H&&v.indexOf("<")===-1)return O&&lt?O.createHTML(v):v;if(m=pn(v),!m)return ue?null:lt?ne:""}m&&y&&oe(m.firstChild);const G=hn($e?v:m);for(;z=G.nextNode();)gn(z),xn(z),z.content instanceof a&&hr(z.content);if($e)return v;if(ue){if(at)for(Y=Ke.call(m.ownerDocument);m.firstChild;)Y.appendChild(m.firstChild);else Y=m;return(F.shadowroot||F.shadowrootmode)&&(Y=vt.call(r,Y,!0)),Y}let te=H?m.outerHTML:m.innerHTML;return H&&U["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&X(sr,m.ownerDocument.doctype.name)&&(te="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+te),x&&pt([Ie,Ne,Oe],he=>{te=We(te,he," ")}),O&&lt?O.createHTML(te):te},e.setConfig=function(){let v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Rt(v),ee=!0},e.clearConfig=function(){De=null,ee=!1},e.isValidAttribute=function(v,l,m){De||Rt({});const k=W(v),z=W(l);return mn(k,z,m)},e.addHook=function(v,l){typeof l=="function"&&Ge(V[v],l)},e.removeHook=function(v,l){if(l!==void 0){const m=$s(V[v],l);return m===-1?void 0:Us(V[v],m,1)[0]}return Cn(V[v])},e.removeHooks=function(v){V[v]=[]},e.removeAllHooks=function(){V=On()},e}var na=ar();const ra={class:"block sm:inline"},sa={class:"flex items-center"},aa={class:"relative"},la={key:1,class:"bg-white border-b border-gray-200 py-1 px-4"},oa={key:0,class:"flex items-center gap-2 text-gray-500 text-sm"},ia={key:1,class:"flex flex-col gap-2"},ca=["onClick"],ua={key:2,class:"bg-white border-b border-gray-200 py-1 px-4"},da={class:"flex items-center justify-between"},pa={class:"flex gap-2"},ha={class:"px-1"},fa={class:"mb-1"},ga={key:0},ma={key:1,class:"text-gray-500"},ba={class:"flex items-center justify-between mt-0.5"},xa={class:"flex items-center gap-1"},ka={class:"flex flex-wrap gap-1"},ya=["onClick"],wa={class:"flex-1 overflow-y-auto overflow-x-hidden p-1.5 space-y-1 w-full"},va={key:0,class:"flex items-start gap-1.5 max-w-full"},Ta={class:"flex-1 bg-white rounded-lg p-2 shadow-sm break-words"},_a={class:"text-gray-800 leading-tight text-[15px]"},Sa=["innerHTML"],Ea={key:1,class:"h-full flex items-center justify-center text-gray-500 text-[15px]"},Aa={class:"flex items-center justify-between"},Ca={class:"text-sm"},Ra={class:"border-t border-gray-200 bg-white w-full"},Da={class:"p-2 max-w-full"},Ma=["placeholder","disabled"],La={class:"flex items-center justify-between"},Ia={class:"flex items-center gap-2"},Na={class:"relative model-selector"},Oa={class:"max-h-[60vh] overflow-y-auto"},Pa=["onClick"],za={class:"flex items-center gap-2"},$a=["disabled"],Ua={class:"flex-1 overflow-y-auto p-4"},Ba={class:"flex-1 min-w-0"},Fa={class:"flex items-center gap-2"},Ha={class:"font-medium text-gray-900 truncate"},Ga={class:"text-xs text-gray-400"},Wa={class:"text-xs text-gray-500 break-all"},qa=["onClick"],ja={key:0,class:"text-center text-gray-500 py-8"},Va={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ya={class:"add-model-modal bg-white rounded-lg shadow-lg w-96 flex flex-col"},Za={class:"p-4 space-y-4"},Xa={key:0,class:"mt-4"},Ka={class:"flex items-center gap-2"},Qa={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},Ja={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},el={class:"p-4 border-t border-gray-200 flex gap-2"},tl=["disabled"],nl={key:0,class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},rl=Wt({__name:"Main",setup(n){const e=Gn(),t=Fn(),r=kt(),s=qt(),a=Bn(),c=Pn(),o=Hn(),h=I(!1),i=I(!1),p=I(null),b=I(""),g=I({entryDisplayConfig:{skipJsCss:!0,excludedPathPatterns:[]}}),A=I(!1),S=I(""),E=I(!1),C=I(!1),$=I(!1),D=I({showName:"",baseUrl:"",modelName:"",apiKey:""}),j=I(null),O=I(!1);let ne=null;const re=I(!0),le=I(!0),Ke=ce;let ye=0;const vt=Le(()=>!t.currentDatabase||t.currentDatabaseId===ce?"请选择数据源再对话":t.currentDatabase.dataId?"Type your message...":"请先录制程序数据然后再对话"),V=Le(()=>!t.currentDatabase||!t.currentDatabase.dataId||s.status==="waiting"),Ie=()=>{var x;return t.databases.length===1&&t.currentDatabaseId===ce?"当前无可用的数据源，请使用 Debug with XCodeMap 录制程序数据。":t.currentDatabaseId===ce?"请先选择数据源再对话":(x=t.currentDatabase)!=null&&x.dataId?"你好！我是你的代码排查助手 XCodeMap。请告诉我具体是哪一个函数调用（CallID_Number）让你感到困惑，或者提供相关的类名、函数名等信息，我将尽力为你分析和解释。":"请先录制程序数据然后再对话"},Ne=async()=>{var u;const x=o.getText();if(x.trim()){if(!((u=t.currentDatabase)!=null&&u.dataId)){r.setError("当前没有数据，不可聊天。请先选择数据源。或者使用 Debug with XCodeMap 创建新的数据源。");return}if(!a.currentModel){r.setError("请先选择模型再发送消息"),E.value=!0;return}o.clearText(),await e.sendMessage(x)}},Oe=()=>{h.value=!h.value},Tt=async x=>{var ee;const u=t.currentDatabase,H=!u||u.id!==x;if(ye=0,t.setCurrentDatabase(x),h.value=!1,H){if(!await wn(x,((ee=t.currentDatabase)==null?void 0:ee.dataId)||"")){r.setError("Failed to switch process data");return}e.createNewChat(),p.value=null,re.value=!0,i.value=!0}},Pe=async()=>{var x;if((x=t.currentDatabase)!=null&&x.dataId)try{const u=await Mr({processId:t.currentDatabase.dataId,first:re.value,filterText:b.value}),H=JSON.stringify(u),ee=JSON.stringify(p.value);H!==ee&&(console.log("Data has changed, updating treeData",H),p.value=u),re.value=!1}catch(u){console.error("Failed to fetch tree data:",u)}},Qe=async()=>{const x=t.currentDatabase;await t.getDatabase();const u=t.currentDatabase;if(x&&u&&x.id===u.id&&x.dataId!==u.dataId&&!await wn(u.id,u.dataId||"")){r.setError("Failed to switch process data");return}u&&u.id!==Ke&&(u.serverSelected?ye=0:(ye++,ye>=3&&(console.log("Database consistency check failed 3 times, resetting to default"),t.setCurrentDatabase(Ke),h.value=!0,p.value=null,re.value=!0,i.value=!1,e.createNewChat(),ye=0)))};vr(()=>{Pe()});const Je=x=>{x.labelKey==="url"&&x.labelValue&&window.open(x.labelValue,"_blank"),console.log("Clicked tree node:",x)},_t=x=>{const u=g.value.entryDisplayConfig.excludedPathPatterns.indexOf(x);u>-1&&(g.value.entryDisplayConfig.excludedPathPatterns.splice(u,1),Ee())},et=()=>{A.value=!0,Lr(()=>{const x=document.querySelector(".input-new-tag input");x&&x.focus()})},U=()=>{S.value&&(g.value.entryDisplayConfig.excludedPathPatterns.push(S.value),Ee()),A.value=!1,S.value=""},Ee=async()=>{try{await Ir(g.value)||r.setError("Failed to update filter configuration")}catch(x){console.error("Failed to update filter configuration:",x)}},F=x=>{const u=x.target;u.closest(".model-selector")||(E.value=!1),u.closest(".model-manager-modal")||(C.value=!1)},tt=async()=>{E.value=!1;try{await a.getModelConfigData(),C.value=!0}catch(x){console.error("Failed to open model manager:",x),r.setError("Failed to open model manager")}},P=()=>{C.value=!1},we=()=>{$.value=!0,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""}},ve=()=>{$.value=!1,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""},j.value=null},nt=async()=>{const x={showName:D.value.showName.trim(),baseUrl:D.value.baseUrl.trim(),modelName:D.value.modelName.trim(),apiKey:D.value.apiKey.trim()};if(!x.showName||!x.baseUrl||!x.modelName||!x.apiKey){r.setError("请填写所有必填字段");return}O.value=!0,j.value=null;const u={uuid:crypto.randomUUID(),showName:x.showName,baseUrl:x.baseUrl,modelName:x.modelName,apiKey:x.apiKey},H=await Nr(u);if(!H.success){j.value={success:!1,message:H.error||"模型连通性测试失败"},O.value=!1;return}const ee=await Or(u);ee.success?(ve(),await a.getModelConfigData()):j.value={success:!1,message:ee.error||"添加模型失败"},O.value=!1},ze=async x=>{const u=await Pr(x);u.success?await a.getModelConfigData():r.setError(u.error||"删除模型失败")};Un(()=>{c.initTheme(),Qe(),Pe(),Tr().then(x=>{x.success&&x.data&&(g.value.entryDisplayConfig=x.data.entryDisplayConfig)}),a.getModelConfigData(),document.addEventListener("click",F),ne=window.setInterval(()=>{Qe(),Pe()},1e3)}),_r(()=>{ne!==null&&(clearInterval(ne),ne=null),document.removeEventListener("click",F),C.value=!1,$.value=!1});const rt=(x,u)=>u!=="tool"?x.replace(/`?CallID_(\d+)`?/g,"[CallID_$1](./findFunc?callId=$1)"):x,st=x=>{M.setOptions({breaks:!0,gfm:!0,pedantic:!0});const u=M.parse(x);return na.sanitize(u,{ALLOWED_TAGS:["p","br","strong","em","code","pre","blockquote","ul","ol","li","h1","h2","h3","h4","h5","h6","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title","class"]})};return(x,u)=>{var H,ee;return w(),T("div",{class:_(["h-screen w-full flex flex-col overflow-x-hidden",f(c).theme==="dark"?"bg-gray-900":"bg-gray-50"])},[f(r).error?(w(),T("div",{key:0,class:_(["fixed top-4 left-1/2 transform -translate-x-1/2 px-4 py-3 rounded z-[9999]",f(c).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[d("span",ra,q(f(r).error.message),1)],2)):B("",!0),d("div",{class:_(["border-b py-1 px-1 flex items-center justify-between",f(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[d("div",sa,[d("div",aa,[d("button",{onClick:Oe,class:_(["flex items-center gap-1 px-2 py-1 text-sm rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,q(f(t).currentDatabase?f(t).currentDatabase.name:"请选择数据源"),1),(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-180":h.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[17]||(u[17]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2)])]),d("button",{onClick:u[0]||(u[0]=()=>{f(e).createNewChat(),le.value=!0}),class:_(["p-2 rounded-full",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"]),title:"新建聊天"},u[18]||(u[18]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)],2),h.value||f(t).currentDatabaseId===f(ce)?(w(),T("div",la,[f(t).databases.length===1?(w(),T("div",oa,u[19]||(u[19]=[d("span",null,"当前无可用的数据源，请使用",-1),d("img",{src:Ur,alt:"XCodeMap Logo",class:"w-6 h-6"},null,-1),d("span",null,"Debug with XCodeMap 录制程序数据。",-1)]))):(w(),T("div",ia,[(w(!0),T(ge,null,ke(f(t).databases,y=>(w(),T("button",{key:y.id,onClick:ue=>Tt(y.id),class:_(["w-full px-4 py-2 rounded-lg text-sm font-medium text-left border-2 transition-all duration-150 focus:outline-none",[y.id===f(t).currentDatabaseId?f(c).theme==="dark"?"button-selected-dark":"button-selected-light":f(c).theme==="dark"?"button-hover-dark":"button-hover-light"]])},q(y.name),11,ca))),128))]))])):B("",!0),f(t).currentDatabase&&f(t).currentDatabase.active?(w(),T("div",ua,[d("div",da,[d("span",{class:_(["px-3 py-1 text-sm rounded-full",{"bg-green-100 text-green-800":f(t).currentDatabase.recordState==="recording","bg-gray-100 text-gray-800":f(t).currentDatabase.recordState==="idle","bg-yellow-100 text-yellow-800":f(t).currentDatabase.recordState==="paused"}])},q(f(t).currentDatabase.recordState),3),d("div",pa,[f(t).currentDatabase.recordState==="idle"?(w(),T("button",{key:0,onClick:u[1]||(u[1]=y=>f(t).startRecord(f(t).currentDatabase.id)),class:"p-2 hover:bg-gray-100 rounded-full",title:"开始录制"},u[20]||(u[20]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z","clip-rule":"evenodd"})],-1)]))):B("",!0),f(t).currentDatabase.recordState==="recording"?(w(),T("button",{key:1,onClick:u[2]||(u[2]=y=>f(t).endRecord(f(t).currentDatabase.id)),class:"p-2 hover:bg-gray-100 rounded-full",title:"结束录制"},u[21]||(u[21]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z","clip-rule":"evenodd"})],-1)]))):B("",!0),f(t).currentDatabase.recordState==="paused"?(w(),T("button",{key:2,onClick:u[3]||(u[3]=y=>f(t).restartRecord(f(t).currentDatabase.id)),class:"p-2 hover:bg-gray-100 rounded-full",title:"重新录制"},u[22]||(u[22]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"})],-1)]))):B("",!0)])])])):B("",!0),f(t).currentDatabaseId!==f(ce)?(w(),T("div",{key:3,class:_([f(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200","border-b"])},[d("div",ha,[d("div",{onClick:u[4]||(u[4]=y=>i.value=!i.value),class:_(["flex items-center cursor-pointer rounded-lg px-2 py-1",f(c).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-100"])},[(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":i.value,"text-gray-400 hover:text-gray-300":f(c).theme==="dark","text-gray-600 hover:text-gray-800":f(c).theme==="light"}]),viewBox:"0 0 20 20",fill:"currentColor"},u[23]||(u[23]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),d("span",{class:_(["text-base font-medium px-2",f(c).theme==="dark"?"text-gray-200":"text-gray-900"])},"网络请求",2)],2)]),i.value?(w(),T("div",{key:0,class:_(["p-1",f(c).theme==="dark"?"border-t border-gray-700":"border-t border-gray-200"])},[d("div",fa,[xe(d("input",{"onUpdate:modelValue":u[5]||(u[5]=y=>b.value=y),type:"text",placeholder:"搜索网络请求...",class:_(["w-full px-2 py-0.5 rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400":"bg-white border-gray-300 text-gray-700"])},null,2),[[Te,b.value]])]),p.value?(w(),T("div",ga,[Sr(Kr,{nodes:p.value.rootNodes,onNodeClick:Je},null,8,["nodes"])])):(w(),T("div",ma,"Loading tree data...")),d("div",ba,[d("div",xa,[d("span",{class:_(["text-xs opacity-50",f(c).theme==="dark"?"text-gray-400":"text-gray-600"])},"排除请求：",2),d("label",{class:_(["flex items-center gap-1 text-xs cursor-pointer opacity-50 hover:opacity-100 transition-opacity",f(c).theme==="dark"?"text-gray-500":"text-gray-400"])},[xe(d("input",{type:"checkbox","onUpdate:modelValue":u[6]||(u[6]=y=>g.value.entryDisplayConfig.skipJsCss=y),onChange:Ee,class:_(["rounded focus:ring-0 focus:ring-offset-0 h-2.5 w-2.5 cursor-pointer",f(c).theme==="dark"?"bg-gray-800 border-gray-600 text-gray-500":"border-gray-300 text-gray-400"])},null,34),[[Er,g.value.entryDisplayConfig.skipJsCss]]),u[24]||(u[24]=d("span",{class:"text-[11px]"},"忽略css/js",-1))],2),d("div",ka,[(w(!0),T(ge,null,ke(g.value.entryDisplayConfig.excludedPathPatterns,y=>(w(),T("div",{key:y,class:_(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"])},[d("span",null,q(y),1),d("button",{onClick:ue=>_t(y),class:_(f(c).theme==="dark"?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700")},u[25]||(u[25]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,ya)],2))),128)),A.value?xe((w(),T("input",{key:0,"onUpdate:modelValue":u[7]||(u[7]=y=>S.value=y),class:_(["input-new-tag px-1 py-0 border rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200":"border-gray-300 text-gray-700"]),placeholder:"输入排除表达式，* 匹配一级路径，** 匹配多级路径",onKeyup:Ar(U,["enter"]),onBlur:U},null,34)),[[Te,S.value]]):(w(),T("button",{key:1,onClick:et,class:_(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-blue-400":"bg-gray-100 hover:bg-gray-200 text-blue-700"])},u[26]||(u[26]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),d("span",null,"New Filter",-1)]),2))])]),d("button",{onClick:u[8]||(u[8]=y=>i.value=!1),class:_(["rounded-full flex items-center",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"]),title:"收起网络请求"},u[27]||(u[27]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),2)])],2)):B("",!0)],2)):B("",!0),d("div",wa,[f(e).currentChat?(w(),T(ge,{key:0},[f(e).currentChat.messages.length===0?(w(),T("div",va,[d("div",Ta,[d("p",_a,[(w(),Cr(Qr,{key:(H=f(e).currentChat)==null?void 0:H.id,text:Ie(),speed:20,onComplete:u[9]||(u[9]=y=>le.value=!1)},null,8,["text"]))])])])):B("",!0),(w(!0),T(ge,null,ke(f(e).currentChat.messages,y=>(w(),T("div",{key:y.messageId,class:_(["flex items-start gap-1.5 max-w-full",{"justify-end":y.role==="user"}])},[d("div",{class:_(["flex-1 rounded-lg p-2 shadow-sm break-words min-w-0",y.role==="user"?"bg-gray-100":"bg-white"])},[d("div",{class:_([(y.role==="user","text-gray-800"),"leading-tight text-[15px] markdown-content break-words whitespace-pre-wrap"]),innerHTML:st(rt(y.content,y.role))},null,10,Sa)],2)],2))),128))],64)):(w(),T("div",Ea," 请选择数据源后再对话 "))]),f(s).status==="waiting"&&f(t).currentDatabase&&f(t).currentDatabaseId!==f(ce)?(w(),T("div",{key:4,class:_(["border-t py-2 px-4",f(c).theme==="dark"?"bg-blue-900 border-blue-800 text-blue-200":"bg-blue-50 border-blue-200 text-blue-700"])},[d("div",Aa,[d("span",Ca,q(f(s).statusMessage||"未知状态..."),1)])],2)):B("",!0),d("div",Ra,[d("div",Da,[xe(d("textarea",{"onUpdate:modelValue":u[10]||(u[10]=y=>f(o).message=y),class:"input w-full resize-none mb-1",rows:"3",placeholder:vt.value,disabled:V.value},null,8,Ma),[[Te,f(o).message]]),d("div",La,[d("div",Ia,[d("span",{class:_(["text-sm",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])},"模型",2),d("div",Na,[d("button",{onClick:u[11]||(u[11]=y=>E.value=!E.value),class:_(["flex items-center gap-1 px-2 py-1 text-sm rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,q(((ee=f(a).currentModel)==null?void 0:ee.showName)||"请选择模型"),1),(w(),T("svg",{xmlns:"http://www.w3.org/2000/svg",class:_(["h-4 w-4 transition-transform duration-200",{"transform rotate-180":E.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[28]||(u[28]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2),E.value?(w(),T("div",{key:0,class:_(["absolute bottom-full left-0 mb-2 w-80 border rounded-lg shadow-lg z-50",f(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[d("div",{class:_(["p-2 border-b",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("span",{class:_(["text-sm font-medium",f(c).theme==="dark"?"text-gray-200":"text-gray-700"])},"选择模型",2)],2),d("div",Oa,[(w(!0),T(ge,null,ke(f(a).availableModels,y=>(w(),T("button",{key:y.uuid,onClick:()=>{f(a).setCurrentModel(y.uuid),E.value=!1},class:_(["w-full px-3 py-2 text-left text-sm transition-all duration-150 border-2 focus:outline-none",{"button-selected-dark":f(a).currentModelUuid===y.uuid&&f(c).theme==="dark","button-selected-light":f(a).currentModelUuid===y.uuid&&f(c).theme==="light","button-hover-dark":f(c).theme==="dark"&&f(a).currentModelUuid!==y.uuid,"button-hover-light":f(c).theme==="light"&&f(a).currentModelUuid!==y.uuid}])},[d("div",za,[d("span",null,q(y.showName),1),d("span",{class:_(["text-xs opacity-75",[f(a).currentModelUuid===y.uuid?f(c).theme==="dark"?"text-gray-300":"text-blue-500":f(c).theme==="dark"?"text-gray-500":"text-gray-400"]])},"("+q(y.modelName)+")",3)])],10,Pa))),128)),f(a).availableModels.length===0?(w(),T("div",{key:0,class:_(["px-3 py-2 text-sm",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])}," 暂无可用模型 ",2)):B("",!0)]),d("div",{class:_(["border-t",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("button",{onClick:tt,class:_(["w-full px-3 py-2 text-left text-sm transition-colors",f(c).theme==="dark"?"text-blue-400 hover:bg-gray-700":"text-blue-600 hover:bg-blue-50"])}," 管理模型 ",2)],2)],2)):B("",!0)])]),d("button",{onClick:Ne,class:_(["p-2 rounded-full transition-colors",[f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400 hover:text-gray-200":"hover:bg-gray-100 text-gray-600",{"opacity-50 cursor-not-allowed":!f(o).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting"}]]),disabled:!f(o).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting",title:"发送消息"},u[29]||(u[29]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),10,$a)])])]),C.value?(w(),T("div",{key:5,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:P},[d("div",{class:"model-manager-modal bg-white rounded-lg shadow-lg w-96 max-h-[80vh] flex flex-col",onClick:u[12]||(u[12]=Dr(()=>{},["stop"]))},[d("div",{class:"p-2 border-b border-gray-200 flex items-center justify-between"},[u[31]||(u[31]=d("h3",{class:"text-base font-medium text-gray-900"},"模型管理",-1)),d("button",{onClick:P,class:"text-gray-400 hover:text-gray-600"},u[30]||(u[30]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",Ua,[(w(!0),T(ge,null,ke(f(a).availableModels,y=>(w(),T("div",{key:y.uuid,class:"flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-1"},[d("div",Ba,[d("div",Fa,[d("span",Ha,q(y.showName),1),d("span",Ga,"("+q(y.modelName)+")",1)]),d("div",Wa,q(y.baseUrl),1)]),d("button",{onClick:ue=>ze(y),class:"ml-2 flex-shrink-0 text-red-500 hover:text-red-700",title:"删除模型"},u[32]||(u[32]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,qa)]))),128)),f(a).availableModels.length===0?(w(),T("div",ja," 暂无已添加的模型 ")):B("",!0)]),d("div",{class:"p-4 border-t border-gray-200"},[d("button",{onClick:we,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"},u[33]||(u[33]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Rr(" 添加新模型 ")]))])])])):B("",!0),$.value?(w(),T("div",Va,[d("div",Ya,[d("div",{class:"p-4 border-b border-gray-200 flex items-center justify-between"},[u[35]||(u[35]=d("h3",{class:"text-lg font-medium text-gray-900"},"添加新模型",-1)),d("button",{onClick:ve,class:"text-gray-400 hover:text-gray-600"},u[34]||(u[34]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",Za,[d("div",null,[u[36]||(u[36]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型显示名称",-1)),xe(d("input",{"onUpdate:modelValue":u[13]||(u[13]=y=>D.value.showName=y),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入模型显示名称"},null,512),[[Te,D.value.showName]]),u[37]||(u[37]=d("p",{class:"mt-1 text-xs text-gray-500"},"模型需要兼容OpenAI的API，需要支持 FunctionCall。",-1))]),d("div",null,[u[38]||(u[38]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Base URL",-1)),xe(d("input",{"onUpdate:modelValue":u[14]||(u[14]=y=>D.value.baseUrl=y),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: https://api.openai.com/v1"},null,512),[[Te,D.value.baseUrl]]),u[39]||(u[39]=d("p",{class:"mt-1 text-xs text-gray-500"},"例如: https://dashscope.aliyuncs.com/compatible-mode/v1",-1))]),d("div",null,[u[40]||(u[40]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型名称",-1)),xe(d("input",{"onUpdate:modelValue":u[15]||(u[15]=y=>D.value.modelName=y),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: gpt-3.5-turbo"},null,512),[[Te,D.value.modelName]]),u[41]||(u[41]=d("p",{class:"mt-1 text-xs text-gray-500"},"国内推荐阿里云qwen-max-latest 或者 deepseek-chat，国外推荐 Claude Sonnet 系列",-1))]),d("div",null,[u[42]||(u[42]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"API Key",-1)),xe(d("input",{"onUpdate:modelValue":u[16]||(u[16]=y=>D.value.apiKey=y),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入API Key"},null,512),[[Te,D.value.apiKey]])]),j.value?(w(),T("div",Xa,[d("div",{class:_(["p-3 rounded-lg",{"bg-green-50 text-green-700":j.value.success,"bg-red-50 text-red-700":!j.value.success}])},[d("div",Ka,[j.value.success?(w(),T("svg",Qa,u[43]||(u[43]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(w(),T("svg",Ja,u[44]||(u[44]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 00-1.414-1.414L10 10l-1.293-1.293z","clip-rule":"evenodd"},null,-1)]))),d("span",null,q(j.value.message),1)])],2)])):B("",!0)]),d("div",el,[d("button",{onClick:ve,class:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),d("button",{onClick:nt,class:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",disabled:O.value},[O.value?(w(),T("svg",nl,u[45]||(u[45]=[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):B("",!0),d("span",null,q(O.value?"测试中...":"保存"),1)],8,tl)])])])):B("",!0)],2)}}}),sl=$n(rl,[["__scopeId","data-v-850b2c1e"]]),tn=zr(sl);tn.use($r());tn.mount("#app");window.$vm=tn._instance;
