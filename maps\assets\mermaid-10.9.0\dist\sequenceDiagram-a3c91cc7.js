import { g as we, D as _e, C as ke, c as st, s as $t, b as Pe, a as Le, E as Ie, l as X, d as Nt, e as te, f as _, Y as ee, a4 as at, r as se, _ as Bt, a5 as Ae, a6 as wt, h as Ne, j as It, k as ve, A as B } from "./mermaid-00886c59.js";
import { d as Se, a as Me, b as Re, c as Ce, e as zt, g as vt } from "./svgDrawCommon-a14ead8e.js";
var Yt = function() {
  var t = function(ht, w, P, I) {
    for (P = P || {}, I = ht.length; I--; P[ht[I]] = w)
      ;
    return P;
  }, e = [1, 2], c = [1, 3], s = [1, 4], r = [2, 4], i = [1, 9], o = [1, 11], l = [1, 13], u = [1, 14], n = [1, 16], x = [1, 17], T = [1, 18], p = [1, 24], g = [1, 25], m = [1, 26], k = [1, 27], A = [1, 28], V = [1, 29], S = [1, 30], O = [1, 31], R = [1, 32], q = [1, 33], z = [1, 34], J = [1, 35], $ = [1, 36], H = [1, 37], U = [1, 38], F = [1, 39], W = [1, 41], Z = [1, 42], K = [1, 43], Q = [1, 44], tt = [1, 45], v = [1, 46], y = [1, 4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], L = [4, 5, 16, 50, 52, 53], j = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], nt = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], N = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 48, 50, 52, 53, 54, 59, 60, 61, 62, 70], Gt = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 50, 52, 53, 54, 59, 60, 61, 62, 70], dt = [68, 69, 70], ot = [1, 120], Mt = {
    trace: function() {
    },
    yy: {},
    symbols_: { error: 2, start: 3, SPACE: 4, NEWLINE: 5, SD: 6, document: 7, line: 8, statement: 9, box_section: 10, box_line: 11, participant_statement: 12, create: 13, box: 14, restOfLine: 15, end: 16, signal: 17, autonumber: 18, NUM: 19, off: 20, activate: 21, actor: 22, deactivate: 23, note_statement: 24, links_statement: 25, link_statement: 26, properties_statement: 27, details_statement: 28, title: 29, legacy_title: 30, acc_title: 31, acc_title_value: 32, acc_descr: 33, acc_descr_value: 34, acc_descr_multiline_value: 35, loop: 36, rect: 37, opt: 38, alt: 39, else_sections: 40, par: 41, par_sections: 42, par_over: 43, critical: 44, option_sections: 45, break: 46, option: 47, and: 48, else: 49, participant: 50, AS: 51, participant_actor: 52, destroy: 53, note: 54, placement: 55, text2: 56, over: 57, actor_pair: 58, links: 59, link: 60, properties: 61, details: 62, spaceList: 63, ",": 64, left_of: 65, right_of: 66, signaltype: 67, "+": 68, "-": 69, ACTOR: 70, SOLID_OPEN_ARROW: 71, DOTTED_OPEN_ARROW: 72, SOLID_ARROW: 73, DOTTED_ARROW: 74, SOLID_CROSS: 75, DOTTED_CROSS: 76, SOLID_POINT: 77, DOTTED_POINT: 78, TXT: 79, $accept: 0, $end: 1 },
    terminals_: { 2: "error", 4: "SPACE", 5: "NEWLINE", 6: "SD", 13: "create", 14: "box", 15: "restOfLine", 16: "end", 18: "autonumber", 19: "NUM", 20: "off", 21: "activate", 23: "deactivate", 29: "title", 30: "legacy_title", 31: "acc_title", 32: "acc_title_value", 33: "acc_descr", 34: "acc_descr_value", 35: "acc_descr_multiline_value", 36: "loop", 37: "rect", 38: "opt", 39: "alt", 41: "par", 43: "par_over", 44: "critical", 46: "break", 47: "option", 48: "and", 49: "else", 50: "participant", 51: "AS", 52: "participant_actor", 53: "destroy", 54: "note", 57: "over", 59: "links", 60: "link", 61: "properties", 62: "details", 64: ",", 65: "left_of", 66: "right_of", 68: "+", 69: "-", 70: "ACTOR", 71: "SOLID_OPEN_ARROW", 72: "DOTTED_OPEN_ARROW", 73: "SOLID_ARROW", 74: "DOTTED_ARROW", 75: "SOLID_CROSS", 76: "DOTTED_CROSS", 77: "SOLID_POINT", 78: "DOTTED_POINT", 79: "TXT" },
    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [10, 0], [10, 2], [11, 2], [11, 1], [11, 1], [9, 1], [9, 2], [9, 4], [9, 2], [9, 4], [9, 3], [9, 3], [9, 2], [9, 3], [9, 3], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [45, 1], [45, 4], [42, 1], [42, 4], [40, 1], [40, 4], [12, 5], [12, 3], [12, 5], [12, 3], [12, 3], [24, 4], [24, 4], [25, 3], [26, 3], [27, 3], [28, 3], [63, 2], [63, 1], [58, 3], [58, 1], [55, 1], [55, 1], [17, 5], [17, 5], [17, 4], [22, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [56, 1]],
    performAction: function(w, P, I, b, M, d, Et) {
      var h = d.length - 1;
      switch (M) {
        case 3:
          return b.apply(d[h]), d[h];
        case 4:
        case 9:
          this.$ = [];
          break;
        case 5:
        case 10:
          d[h - 1].push(d[h]), this.$ = d[h - 1];
          break;
        case 6:
        case 7:
        case 11:
        case 12:
          this.$ = d[h];
          break;
        case 8:
        case 13:
          this.$ = [];
          break;
        case 15:
          d[h].type = "createParticipant", this.$ = d[h];
          break;
        case 16:
          d[h - 1].unshift({ type: "boxStart", boxData: b.parseBoxData(d[h - 2]) }), d[h - 1].push({ type: "boxEnd", boxText: d[h - 2] }), this.$ = d[h - 1];
          break;
        case 18:
          this.$ = { type: "sequenceIndex", sequenceIndex: Number(d[h - 2]), sequenceIndexStep: Number(d[h - 1]), sequenceVisible: !0, signalType: b.LINETYPE.AUTONUMBER };
          break;
        case 19:
          this.$ = { type: "sequenceIndex", sequenceIndex: Number(d[h - 1]), sequenceIndexStep: 1, sequenceVisible: !0, signalType: b.LINETYPE.AUTONUMBER };
          break;
        case 20:
          this.$ = { type: "sequenceIndex", sequenceVisible: !1, signalType: b.LINETYPE.AUTONUMBER };
          break;
        case 21:
          this.$ = { type: "sequenceIndex", sequenceVisible: !0, signalType: b.LINETYPE.AUTONUMBER };
          break;
        case 22:
          this.$ = { type: "activeStart", signalType: b.LINETYPE.ACTIVE_START, actor: d[h - 1] };
          break;
        case 23:
          this.$ = { type: "activeEnd", signalType: b.LINETYPE.ACTIVE_END, actor: d[h - 1] };
          break;
        case 29:
          b.setDiagramTitle(d[h].substring(6)), this.$ = d[h].substring(6);
          break;
        case 30:
          b.setDiagramTitle(d[h].substring(7)), this.$ = d[h].substring(7);
          break;
        case 31:
          this.$ = d[h].trim(), b.setAccTitle(this.$);
          break;
        case 32:
        case 33:
          this.$ = d[h].trim(), b.setAccDescription(this.$);
          break;
        case 34:
          d[h - 1].unshift({ type: "loopStart", loopText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.LOOP_START }), d[h - 1].push({ type: "loopEnd", loopText: d[h - 2], signalType: b.LINETYPE.LOOP_END }), this.$ = d[h - 1];
          break;
        case 35:
          d[h - 1].unshift({ type: "rectStart", color: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.RECT_START }), d[h - 1].push({ type: "rectEnd", color: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.RECT_END }), this.$ = d[h - 1];
          break;
        case 36:
          d[h - 1].unshift({ type: "optStart", optText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.OPT_START }), d[h - 1].push({ type: "optEnd", optText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.OPT_END }), this.$ = d[h - 1];
          break;
        case 37:
          d[h - 1].unshift({ type: "altStart", altText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.ALT_START }), d[h - 1].push({ type: "altEnd", signalType: b.LINETYPE.ALT_END }), this.$ = d[h - 1];
          break;
        case 38:
          d[h - 1].unshift({ type: "parStart", parText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.PAR_START }), d[h - 1].push({ type: "parEnd", signalType: b.LINETYPE.PAR_END }), this.$ = d[h - 1];
          break;
        case 39:
          d[h - 1].unshift({ type: "parStart", parText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.PAR_OVER_START }), d[h - 1].push({ type: "parEnd", signalType: b.LINETYPE.PAR_END }), this.$ = d[h - 1];
          break;
        case 40:
          d[h - 1].unshift({ type: "criticalStart", criticalText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.CRITICAL_START }), d[h - 1].push({ type: "criticalEnd", signalType: b.LINETYPE.CRITICAL_END }), this.$ = d[h - 1];
          break;
        case 41:
          d[h - 1].unshift({ type: "breakStart", breakText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.BREAK_START }), d[h - 1].push({ type: "breakEnd", optText: b.parseMessage(d[h - 2]), signalType: b.LINETYPE.BREAK_END }), this.$ = d[h - 1];
          break;
        case 43:
          this.$ = d[h - 3].concat([{ type: "option", optionText: b.parseMessage(d[h - 1]), signalType: b.LINETYPE.CRITICAL_OPTION }, d[h]]);
          break;
        case 45:
          this.$ = d[h - 3].concat([{ type: "and", parText: b.parseMessage(d[h - 1]), signalType: b.LINETYPE.PAR_AND }, d[h]]);
          break;
        case 47:
          this.$ = d[h - 3].concat([{ type: "else", altText: b.parseMessage(d[h - 1]), signalType: b.LINETYPE.ALT_ELSE }, d[h]]);
          break;
        case 48:
          d[h - 3].draw = "participant", d[h - 3].type = "addParticipant", d[h - 3].description = b.parseMessage(d[h - 1]), this.$ = d[h - 3];
          break;
        case 49:
          d[h - 1].draw = "participant", d[h - 1].type = "addParticipant", this.$ = d[h - 1];
          break;
        case 50:
          d[h - 3].draw = "actor", d[h - 3].type = "addParticipant", d[h - 3].description = b.parseMessage(d[h - 1]), this.$ = d[h - 3];
          break;
        case 51:
          d[h - 1].draw = "actor", d[h - 1].type = "addParticipant", this.$ = d[h - 1];
          break;
        case 52:
          d[h - 1].type = "destroyParticipant", this.$ = d[h - 1];
          break;
        case 53:
          this.$ = [d[h - 1], { type: "addNote", placement: d[h - 2], actor: d[h - 1].actor, text: d[h] }];
          break;
        case 54:
          d[h - 2] = [].concat(d[h - 1], d[h - 1]).slice(0, 2), d[h - 2][0] = d[h - 2][0].actor, d[h - 2][1] = d[h - 2][1].actor, this.$ = [d[h - 1], { type: "addNote", placement: b.PLACEMENT.OVER, actor: d[h - 2].slice(0, 2), text: d[h] }];
          break;
        case 55:
          this.$ = [d[h - 1], { type: "addLinks", actor: d[h - 1].actor, text: d[h] }];
          break;
        case 56:
          this.$ = [d[h - 1], { type: "addALink", actor: d[h - 1].actor, text: d[h] }];
          break;
        case 57:
          this.$ = [d[h - 1], { type: "addProperties", actor: d[h - 1].actor, text: d[h] }];
          break;
        case 58:
          this.$ = [d[h - 1], { type: "addDetails", actor: d[h - 1].actor, text: d[h] }];
          break;
        case 61:
          this.$ = [d[h - 2], d[h]];
          break;
        case 62:
          this.$ = d[h];
          break;
        case 63:
          this.$ = b.PLACEMENT.LEFTOF;
          break;
        case 64:
          this.$ = b.PLACEMENT.RIGHTOF;
          break;
        case 65:
          this.$ = [
            d[h - 4],
            d[h - 1],
            { type: "addMessage", from: d[h - 4].actor, to: d[h - 1].actor, signalType: d[h - 3], msg: d[h], activate: !0 },
            { type: "activeStart", signalType: b.LINETYPE.ACTIVE_START, actor: d[h - 1] }
          ];
          break;
        case 66:
          this.$ = [
            d[h - 4],
            d[h - 1],
            { type: "addMessage", from: d[h - 4].actor, to: d[h - 1].actor, signalType: d[h - 3], msg: d[h] },
            { type: "activeEnd", signalType: b.LINETYPE.ACTIVE_END, actor: d[h - 4] }
          ];
          break;
        case 67:
          this.$ = [d[h - 3], d[h - 1], { type: "addMessage", from: d[h - 3].actor, to: d[h - 1].actor, signalType: d[h - 2], msg: d[h] }];
          break;
        case 68:
          this.$ = { type: "addParticipant", actor: d[h] };
          break;
        case 69:
          this.$ = b.LINETYPE.SOLID_OPEN;
          break;
        case 70:
          this.$ = b.LINETYPE.DOTTED_OPEN;
          break;
        case 71:
          this.$ = b.LINETYPE.SOLID;
          break;
        case 72:
          this.$ = b.LINETYPE.DOTTED;
          break;
        case 73:
          this.$ = b.LINETYPE.SOLID_CROSS;
          break;
        case 74:
          this.$ = b.LINETYPE.DOTTED_CROSS;
          break;
        case 75:
          this.$ = b.LINETYPE.SOLID_POINT;
          break;
        case 76:
          this.$ = b.LINETYPE.DOTTED_POINT;
          break;
        case 77:
          this.$ = b.parseMessage(d[h].trim().substring(1));
          break;
      }
    },
    table: [{ 3: 1, 4: e, 5: c, 6: s }, { 1: [3] }, { 3: 5, 4: e, 5: c, 6: s }, { 3: 6, 4: e, 5: c, 6: s }, t([1, 4, 5, 13, 14, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], r, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, t(y, [2, 5]), { 9: 47, 12: 12, 13: l, 14: u, 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, t(y, [2, 7]), t(y, [2, 8]), t(y, [2, 14]), { 12: 48, 50: H, 52: U, 53: F }, { 15: [1, 49] }, { 5: [1, 50] }, { 5: [1, 53], 19: [1, 51], 20: [1, 52] }, { 22: 54, 70: v }, { 22: 55, 70: v }, { 5: [1, 56] }, { 5: [1, 57] }, { 5: [1, 58] }, { 5: [1, 59] }, { 5: [1, 60] }, t(y, [2, 29]), t(y, [2, 30]), { 32: [1, 61] }, { 34: [1, 62] }, t(y, [2, 33]), { 15: [1, 63] }, { 15: [1, 64] }, { 15: [1, 65] }, { 15: [1, 66] }, { 15: [1, 67] }, { 15: [1, 68] }, { 15: [1, 69] }, { 15: [1, 70] }, { 22: 71, 70: v }, { 22: 72, 70: v }, { 22: 73, 70: v }, { 67: 74, 71: [1, 75], 72: [1, 76], 73: [1, 77], 74: [1, 78], 75: [1, 79], 76: [1, 80], 77: [1, 81], 78: [1, 82] }, { 55: 83, 57: [1, 84], 65: [1, 85], 66: [1, 86] }, { 22: 87, 70: v }, { 22: 88, 70: v }, { 22: 89, 70: v }, { 22: 90, 70: v }, t([5, 51, 64, 71, 72, 73, 74, 75, 76, 77, 78, 79], [2, 68]), t(y, [2, 6]), t(y, [2, 15]), t(L, [2, 9], { 10: 91 }), t(y, [2, 17]), { 5: [1, 93], 19: [1, 92] }, { 5: [1, 94] }, t(y, [2, 21]), { 5: [1, 95] }, { 5: [1, 96] }, t(y, [2, 24]), t(y, [2, 25]), t(y, [2, 26]), t(y, [2, 27]), t(y, [2, 28]), t(y, [2, 31]), t(y, [2, 32]), t(j, r, { 7: 97 }), t(j, r, { 7: 98 }), t(j, r, { 7: 99 }), t(nt, r, { 40: 100, 7: 101 }), t(N, r, { 42: 102, 7: 103 }), t(N, r, { 7: 103, 42: 104 }), t(Gt, r, { 45: 105, 7: 106 }), t(j, r, { 7: 107 }), { 5: [1, 109], 51: [1, 108] }, { 5: [1, 111], 51: [1, 110] }, { 5: [1, 112] }, { 22: 115, 68: [1, 113], 69: [1, 114], 70: v }, t(dt, [2, 69]), t(dt, [2, 70]), t(dt, [2, 71]), t(dt, [2, 72]), t(dt, [2, 73]), t(dt, [2, 74]), t(dt, [2, 75]), t(dt, [2, 76]), { 22: 116, 70: v }, { 22: 118, 58: 117, 70: v }, { 70: [2, 63] }, { 70: [2, 64] }, { 56: 119, 79: ot }, { 56: 121, 79: ot }, { 56: 122, 79: ot }, { 56: 123, 79: ot }, { 4: [1, 126], 5: [1, 128], 11: 125, 12: 127, 16: [1, 124], 50: H, 52: U, 53: F }, { 5: [1, 129] }, t(y, [2, 19]), t(y, [2, 20]), t(y, [2, 22]), t(y, [2, 23]), { 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 16: [1, 130], 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, { 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 16: [1, 131], 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, { 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 16: [1, 132], 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, { 16: [1, 133] }, { 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 16: [2, 46], 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 49: [1, 134], 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, { 16: [1, 135] }, { 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 16: [2, 44], 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 48: [1, 136], 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, { 16: [1, 137] }, { 16: [1, 138] }, { 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 16: [2, 42], 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 47: [1, 139], 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, { 4: i, 5: o, 8: 8, 9: 10, 12: 12, 13: l, 14: u, 16: [1, 140], 17: 15, 18: n, 21: x, 22: 40, 23: T, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: p, 30: g, 31: m, 33: k, 35: A, 36: V, 37: S, 38: O, 39: R, 41: q, 43: z, 44: J, 46: $, 50: H, 52: U, 53: F, 54: W, 59: Z, 60: K, 61: Q, 62: tt, 70: v }, { 15: [1, 141] }, t(y, [2, 49]), { 15: [1, 142] }, t(y, [2, 51]), t(y, [2, 52]), { 22: 143, 70: v }, { 22: 144, 70: v }, { 56: 145, 79: ot }, { 56: 146, 79: ot }, { 56: 147, 79: ot }, { 64: [1, 148], 79: [2, 62] }, { 5: [2, 55] }, { 5: [2, 77] }, { 5: [2, 56] }, { 5: [2, 57] }, { 5: [2, 58] }, t(y, [2, 16]), t(L, [2, 10]), { 12: 149, 50: H, 52: U, 53: F }, t(L, [2, 12]), t(L, [2, 13]), t(y, [2, 18]), t(y, [2, 34]), t(y, [2, 35]), t(y, [2, 36]), t(y, [2, 37]), { 15: [1, 150] }, t(y, [2, 38]), { 15: [1, 151] }, t(y, [2, 39]), t(y, [2, 40]), { 15: [1, 152] }, t(y, [2, 41]), { 5: [1, 153] }, { 5: [1, 154] }, { 56: 155, 79: ot }, { 56: 156, 79: ot }, { 5: [2, 67] }, { 5: [2, 53] }, { 5: [2, 54] }, { 22: 157, 70: v }, t(L, [2, 11]), t(nt, r, { 7: 101, 40: 158 }), t(N, r, { 7: 103, 42: 159 }), t(Gt, r, { 7: 106, 45: 160 }), t(y, [2, 48]), t(y, [2, 50]), { 5: [2, 65] }, { 5: [2, 66] }, { 79: [2, 61] }, { 16: [2, 47] }, { 16: [2, 45] }, { 16: [2, 43] }],
    defaultActions: { 5: [2, 1], 6: [2, 2], 85: [2, 63], 86: [2, 64], 119: [2, 55], 120: [2, 77], 121: [2, 56], 122: [2, 57], 123: [2, 58], 145: [2, 67], 146: [2, 53], 147: [2, 54], 155: [2, 65], 156: [2, 66], 157: [2, 61], 158: [2, 47], 159: [2, 45], 160: [2, 43] },
    parseError: function(w, P) {
      if (P.recoverable)
        this.trace(w);
      else {
        var I = new Error(w);
        throw I.hash = P, I;
      }
    },
    parse: function(w) {
      var P = this, I = [0], b = [], M = [null], d = [], Et = this.table, h = "", kt = 0, Xt = 0, Te = 2, Jt = 1, be = d.slice.call(arguments, 1), Y = Object.create(this.lexer), ut = { yy: {} };
      for (var Ct in this.yy)
        Object.prototype.hasOwnProperty.call(this.yy, Ct) && (ut.yy[Ct] = this.yy[Ct]);
      Y.setInput(w, ut.yy), ut.yy.lexer = Y, ut.yy.parser = this, typeof Y.yylloc > "u" && (Y.yylloc = {});
      var Dt = Y.yylloc;
      d.push(Dt);
      var Ee = Y.options && Y.options.ranges;
      typeof ut.yy.parseError == "function" ? this.parseError = ut.yy.parseError : this.parseError = Object.getPrototypeOf(this).parseError;
      function me() {
        var lt;
        return lt = b.pop() || Y.lex() || Jt, typeof lt != "number" && (lt instanceof Array && (b = lt, lt = b.pop()), lt = P.symbols_[lt] || lt), lt;
      }
      for (var G, pt, et, Vt, yt = {}, Pt, ct, Zt, Lt; ; ) {
        if (pt = I[I.length - 1], this.defaultActions[pt] ? et = this.defaultActions[pt] : ((G === null || typeof G > "u") && (G = me()), et = Et[pt] && Et[pt][G]), typeof et > "u" || !et.length || !et[0]) {
          var Ot = "";
          Lt = [];
          for (Pt in Et[pt])
            this.terminals_[Pt] && Pt > Te && Lt.push("'" + this.terminals_[Pt] + "'");
          Y.showPosition ? Ot = "Parse error on line " + (kt + 1) + `:
` + Y.showPosition() + `
Expecting ` + Lt.join(", ") + ", got '" + (this.terminals_[G] || G) + "'" : Ot = "Parse error on line " + (kt + 1) + ": Unexpected " + (G == Jt ? "end of input" : "'" + (this.terminals_[G] || G) + "'"), this.parseError(Ot, {
            text: Y.match,
            token: this.terminals_[G] || G,
            line: Y.yylineno,
            loc: Dt,
            expected: Lt
          });
        }
        if (et[0] instanceof Array && et.length > 1)
          throw new Error("Parse Error: multiple actions possible at state: " + pt + ", token: " + G);
        switch (et[0]) {
          case 1:
            I.push(G), M.push(Y.yytext), d.push(Y.yylloc), I.push(et[1]), G = null, Xt = Y.yyleng, h = Y.yytext, kt = Y.yylineno, Dt = Y.yylloc;
            break;
          case 2:
            if (ct = this.productions_[et[1]][1], yt.$ = M[M.length - ct], yt._$ = {
              first_line: d[d.length - (ct || 1)].first_line,
              last_line: d[d.length - 1].last_line,
              first_column: d[d.length - (ct || 1)].first_column,
              last_column: d[d.length - 1].last_column
            }, Ee && (yt._$.range = [
              d[d.length - (ct || 1)].range[0],
              d[d.length - 1].range[1]
            ]), Vt = this.performAction.apply(yt, [
              h,
              Xt,
              kt,
              ut.yy,
              et[1],
              M,
              d
            ].concat(be)), typeof Vt < "u")
              return Vt;
            ct && (I = I.slice(0, -1 * ct * 2), M = M.slice(0, -1 * ct), d = d.slice(0, -1 * ct)), I.push(this.productions_[et[1]][0]), M.push(yt.$), d.push(yt._$), Zt = Et[I[I.length - 2]][I[I.length - 1]], I.push(Zt);
            break;
          case 3:
            return !0;
        }
      }
      return !0;
    }
  }, ye = function() {
    var ht = {
      EOF: 1,
      parseError: function(P, I) {
        if (this.yy.parser)
          this.yy.parser.parseError(P, I);
        else
          throw new Error(P);
      },
      // resets the lexer, sets new input
      setInput: function(w, P) {
        return this.yy = P || this.yy || {}, this._input = w, this._more = this._backtrack = this.done = !1, this.yylineno = this.yyleng = 0, this.yytext = this.matched = this.match = "", this.conditionStack = ["INITIAL"], this.yylloc = {
          first_line: 1,
          first_column: 0,
          last_line: 1,
          last_column: 0
        }, this.options.ranges && (this.yylloc.range = [0, 0]), this.offset = 0, this;
      },
      // consumes and returns one char from the input
      input: function() {
        var w = this._input[0];
        this.yytext += w, this.yyleng++, this.offset++, this.match += w, this.matched += w;
        var P = w.match(/(?:\r\n?|\n).*/g);
        return P ? (this.yylineno++, this.yylloc.last_line++) : this.yylloc.last_column++, this.options.ranges && this.yylloc.range[1]++, this._input = this._input.slice(1), w;
      },
      // unshifts one char (or a string) into the input
      unput: function(w) {
        var P = w.length, I = w.split(/(?:\r\n?|\n)/g);
        this._input = w + this._input, this.yytext = this.yytext.substr(0, this.yytext.length - P), this.offset -= P;
        var b = this.match.split(/(?:\r\n?|\n)/g);
        this.match = this.match.substr(0, this.match.length - 1), this.matched = this.matched.substr(0, this.matched.length - 1), I.length - 1 && (this.yylineno -= I.length - 1);
        var M = this.yylloc.range;
        return this.yylloc = {
          first_line: this.yylloc.first_line,
          last_line: this.yylineno + 1,
          first_column: this.yylloc.first_column,
          last_column: I ? (I.length === b.length ? this.yylloc.first_column : 0) + b[b.length - I.length].length - I[0].length : this.yylloc.first_column - P
        }, this.options.ranges && (this.yylloc.range = [M[0], M[0] + this.yyleng - P]), this.yyleng = this.yytext.length, this;
      },
      // When called from action, caches matched text and appends it on next action
      more: function() {
        return this._more = !0, this;
      },
      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
      reject: function() {
        if (this.options.backtrack_lexer)
          this._backtrack = !0;
        else
          return this.parseError("Lexical error on line " + (this.yylineno + 1) + `. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
` + this.showPosition(), {
            text: "",
            token: null,
            line: this.yylineno
          });
        return this;
      },
      // retain first n characters of the match
      less: function(w) {
        this.unput(this.match.slice(w));
      },
      // displays already matched input, i.e. for error messages
      pastInput: function() {
        var w = this.matched.substr(0, this.matched.length - this.match.length);
        return (w.length > 20 ? "..." : "") + w.substr(-20).replace(/\n/g, "");
      },
      // displays upcoming input, i.e. for error messages
      upcomingInput: function() {
        var w = this.match;
        return w.length < 20 && (w += this._input.substr(0, 20 - w.length)), (w.substr(0, 20) + (w.length > 20 ? "..." : "")).replace(/\n/g, "");
      },
      // displays the character position where the lexing error occurred, i.e. for error messages
      showPosition: function() {
        var w = this.pastInput(), P = new Array(w.length + 1).join("-");
        return w + this.upcomingInput() + `
` + P + "^";
      },
      // test the lexed token: return FALSE when not a match, otherwise return token
      test_match: function(w, P) {
        var I, b, M;
        if (this.options.backtrack_lexer && (M = {
          yylineno: this.yylineno,
          yylloc: {
            first_line: this.yylloc.first_line,
            last_line: this.last_line,
            first_column: this.yylloc.first_column,
            last_column: this.yylloc.last_column
          },
          yytext: this.yytext,
          match: this.match,
          matches: this.matches,
          matched: this.matched,
          yyleng: this.yyleng,
          offset: this.offset,
          _more: this._more,
          _input: this._input,
          yy: this.yy,
          conditionStack: this.conditionStack.slice(0),
          done: this.done
        }, this.options.ranges && (M.yylloc.range = this.yylloc.range.slice(0))), b = w[0].match(/(?:\r\n?|\n).*/g), b && (this.yylineno += b.length), this.yylloc = {
          first_line: this.yylloc.last_line,
          last_line: this.yylineno + 1,
          first_column: this.yylloc.last_column,
          last_column: b ? b[b.length - 1].length - b[b.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + w[0].length
        }, this.yytext += w[0], this.match += w[0], this.matches = w, this.yyleng = this.yytext.length, this.options.ranges && (this.yylloc.range = [this.offset, this.offset += this.yyleng]), this._more = !1, this._backtrack = !1, this._input = this._input.slice(w[0].length), this.matched += w[0], I = this.performAction.call(this, this.yy, this, P, this.conditionStack[this.conditionStack.length - 1]), this.done && this._input && (this.done = !1), I)
          return I;
        if (this._backtrack) {
          for (var d in M)
            this[d] = M[d];
          return !1;
        }
        return !1;
      },
      // return next match in input
      next: function() {
        if (this.done)
          return this.EOF;
        this._input || (this.done = !0);
        var w, P, I, b;
        this._more || (this.yytext = "", this.match = "");
        for (var M = this._currentRules(), d = 0; d < M.length; d++)
          if (I = this._input.match(this.rules[M[d]]), I && (!P || I[0].length > P[0].length)) {
            if (P = I, b = d, this.options.backtrack_lexer) {
              if (w = this.test_match(I, M[d]), w !== !1)
                return w;
              if (this._backtrack) {
                P = !1;
                continue;
              } else
                return !1;
            } else if (!this.options.flex)
              break;
          }
        return P ? (w = this.test_match(P, M[b]), w !== !1 ? w : !1) : this._input === "" ? this.EOF : this.parseError("Lexical error on line " + (this.yylineno + 1) + `. Unrecognized text.
` + this.showPosition(), {
          text: "",
          token: null,
          line: this.yylineno
        });
      },
      // return next match that has a token
      lex: function() {
        var P = this.next();
        return P || this.lex();
      },
      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
      begin: function(P) {
        this.conditionStack.push(P);
      },
      // pop the previously active lexer condition state off the condition stack
      popState: function() {
        var P = this.conditionStack.length - 1;
        return P > 0 ? this.conditionStack.pop() : this.conditionStack[0];
      },
      // produce the lexer rule set which is active for the currently active lexer condition state
      _currentRules: function() {
        return this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1] ? this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules : this.conditions.INITIAL.rules;
      },
      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
      topState: function(P) {
        return P = this.conditionStack.length - 1 - Math.abs(P || 0), P >= 0 ? this.conditionStack[P] : "INITIAL";
      },
      // alias for begin(condition)
      pushState: function(P) {
        this.begin(P);
      },
      // return the number of states currently on the stack
      stateStackSize: function() {
        return this.conditionStack.length;
      },
      options: { "case-insensitive": !0 },
      performAction: function(P, I, b, M) {
        switch (b) {
          case 0:
            return 5;
          case 1:
            break;
          case 2:
            break;
          case 3:
            break;
          case 4:
            break;
          case 5:
            break;
          case 6:
            return 19;
          case 7:
            return this.begin("LINE"), 14;
          case 8:
            return this.begin("ID"), 50;
          case 9:
            return this.begin("ID"), 52;
          case 10:
            return 13;
          case 11:
            return this.begin("ID"), 53;
          case 12:
            return I.yytext = I.yytext.trim(), this.begin("ALIAS"), 70;
          case 13:
            return this.popState(), this.popState(), this.begin("LINE"), 51;
          case 14:
            return this.popState(), this.popState(), 5;
          case 15:
            return this.begin("LINE"), 36;
          case 16:
            return this.begin("LINE"), 37;
          case 17:
            return this.begin("LINE"), 38;
          case 18:
            return this.begin("LINE"), 39;
          case 19:
            return this.begin("LINE"), 49;
          case 20:
            return this.begin("LINE"), 41;
          case 21:
            return this.begin("LINE"), 43;
          case 22:
            return this.begin("LINE"), 48;
          case 23:
            return this.begin("LINE"), 44;
          case 24:
            return this.begin("LINE"), 47;
          case 25:
            return this.begin("LINE"), 46;
          case 26:
            return this.popState(), 15;
          case 27:
            return 16;
          case 28:
            return 65;
          case 29:
            return 66;
          case 30:
            return 59;
          case 31:
            return 60;
          case 32:
            return 61;
          case 33:
            return 62;
          case 34:
            return 57;
          case 35:
            return 54;
          case 36:
            return this.begin("ID"), 21;
          case 37:
            return this.begin("ID"), 23;
          case 38:
            return 29;
          case 39:
            return 30;
          case 40:
            return this.begin("acc_title"), 31;
          case 41:
            return this.popState(), "acc_title_value";
          case 42:
            return this.begin("acc_descr"), 33;
          case 43:
            return this.popState(), "acc_descr_value";
          case 44:
            this.begin("acc_descr_multiline");
            break;
          case 45:
            this.popState();
            break;
          case 46:
            return "acc_descr_multiline_value";
          case 47:
            return 6;
          case 48:
            return 18;
          case 49:
            return 20;
          case 50:
            return 64;
          case 51:
            return 5;
          case 52:
            return I.yytext = I.yytext.trim(), 70;
          case 53:
            return 73;
          case 54:
            return 74;
          case 55:
            return 71;
          case 56:
            return 72;
          case 57:
            return 75;
          case 58:
            return 76;
          case 59:
            return 77;
          case 60:
            return 78;
          case 61:
            return 79;
          case 62:
            return 68;
          case 63:
            return 69;
          case 64:
            return 5;
          case 65:
            return "INVALID";
        }
      },
      rules: [/^(?:[\n]+)/i, /^(?:\s+)/i, /^(?:((?!\n)\s)+)/i, /^(?:#[^\n]*)/i, /^(?:%(?!\{)[^\n]*)/i, /^(?:[^\}]%%[^\n]*)/i, /^(?:[0-9]+(?=[ \n]+))/i, /^(?:box\b)/i, /^(?:participant\b)/i, /^(?:actor\b)/i, /^(?:create\b)/i, /^(?:destroy\b)/i, /^(?:[^\->:\n,;]+?([\-]*[^\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i, /^(?:as\b)/i, /^(?:(?:))/i, /^(?:loop\b)/i, /^(?:rect\b)/i, /^(?:opt\b)/i, /^(?:alt\b)/i, /^(?:else\b)/i, /^(?:par\b)/i, /^(?:par_over\b)/i, /^(?:and\b)/i, /^(?:critical\b)/i, /^(?:option\b)/i, /^(?:break\b)/i, /^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i, /^(?:end\b)/i, /^(?:left of\b)/i, /^(?:right of\b)/i, /^(?:links\b)/i, /^(?:link\b)/i, /^(?:properties\b)/i, /^(?:details\b)/i, /^(?:over\b)/i, /^(?:note\b)/i, /^(?:activate\b)/i, /^(?:deactivate\b)/i, /^(?:title\s[^#\n;]+)/i, /^(?:title:\s[^#\n;]+)/i, /^(?:accTitle\s*:\s*)/i, /^(?:(?!\n||)*[^\n]*)/i, /^(?:accDescr\s*:\s*)/i, /^(?:(?!\n||)*[^\n]*)/i, /^(?:accDescr\s*\{\s*)/i, /^(?:[\}])/i, /^(?:[^\}]*)/i, /^(?:sequenceDiagram\b)/i, /^(?:autonumber\b)/i, /^(?:off\b)/i, /^(?:,)/i, /^(?:;)/i, /^(?:[^\+\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\->:\n,;]+)*)/i, /^(?:->>)/i, /^(?:-->>)/i, /^(?:->)/i, /^(?:-->)/i, /^(?:-[x])/i, /^(?:--[x])/i, /^(?:-[\)])/i, /^(?:--[\)])/i, /^(?::(?:(?:no)?wrap)?[^#\n;]+)/i, /^(?:\+)/i, /^(?:-)/i, /^(?:$)/i, /^(?:.)/i],
      conditions: { acc_descr_multiline: { rules: [45, 46], inclusive: !1 }, acc_descr: { rules: [43], inclusive: !1 }, acc_title: { rules: [41], inclusive: !1 }, ID: { rules: [2, 3, 12], inclusive: !1 }, ALIAS: { rules: [2, 3, 13, 14], inclusive: !1 }, LINE: { rules: [2, 3, 26], inclusive: !1 }, INITIAL: { rules: [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], inclusive: !0 } }
    };
    return ht;
  }();
  Mt.lexer = ye;
  function Rt() {
    this.yy = {};
  }
  return Rt.prototype = Mt, Mt.Parser = Rt, new Rt();
}();
Yt.parser = Yt;
const De = Yt;
class Ve {
  /**
   * @param init - Function that creates the default state.
   */
  constructor(e) {
    this.init = e, this.records = this.init();
  }
  reset() {
    this.records = this.init();
  }
}
const E = new Ve(() => ({
  prevActor: void 0,
  actors: {},
  createdActors: {},
  destroyedActors: {},
  boxes: [],
  messages: [],
  notes: [],
  sequenceNumbersEnabled: !1,
  wrapEnabled: void 0,
  currentBox: void 0,
  lastCreated: void 0,
  lastDestroyed: void 0
})), Oe = function(t) {
  E.records.boxes.push({
    name: t.text,
    wrap: t.wrap === void 0 && gt() || !!t.wrap,
    fill: t.color,
    actorKeys: []
  }), E.records.currentBox = E.records.boxes.slice(-1)[0];
}, Ft = function(t, e, c, s) {
  let r = E.records.currentBox;
  const i = E.records.actors[t];
  if (i) {
    if (E.records.currentBox && i.box && E.records.currentBox !== i.box)
      throw new Error(
        "A same participant should only be defined in one Box: " + i.name + " can't be in '" + i.box.name + "' and in '" + E.records.currentBox.name + "' at the same time."
      );
    if (r = i.box ? i.box : E.records.currentBox, i.box = r, i && e === i.name && c == null)
      return;
  }
  (c == null || c.text == null) && (c = { text: e, wrap: null, type: s }), (s == null || c.text == null) && (c = { text: e, wrap: null, type: s }), E.records.actors[t] = {
    box: r,
    name: e,
    description: c.text,
    wrap: c.wrap === void 0 && gt() || !!c.wrap,
    prevActor: E.records.prevActor,
    links: {},
    properties: {},
    actorCnt: null,
    rectData: null,
    type: s || "participant"
  }, E.records.prevActor && E.records.actors[E.records.prevActor] && (E.records.actors[E.records.prevActor].nextActor = t), E.records.currentBox && E.records.currentBox.actorKeys.push(t), E.records.prevActor = t;
}, Be = (t) => {
  let e, c = 0;
  for (e = 0; e < E.records.messages.length; e++)
    E.records.messages[e].type === mt.ACTIVE_START && E.records.messages[e].from.actor === t && c++, E.records.messages[e].type === mt.ACTIVE_END && E.records.messages[e].from.actor === t && c--;
  return c;
}, Ye = function(t, e, c, s) {
  E.records.messages.push({
    from: t,
    to: e,
    message: c.text,
    wrap: c.wrap === void 0 && gt() || !!c.wrap,
    answer: s
  });
}, C = function(t, e, c = { text: void 0, wrap: void 0 }, s, r = !1) {
  if (s === mt.ACTIVE_END && Be(t.actor) < 1) {
    let o = new Error("Trying to inactivate an inactive participant (" + t.actor + ")");
    throw o.hash = {
      text: "->>-",
      token: "->>-",
      line: "1",
      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },
      expected: ["'ACTIVE_PARTICIPANT'"]
    }, o;
  }
  return E.records.messages.push({
    from: t,
    to: e,
    message: c.text,
    wrap: c.wrap === void 0 && gt() || !!c.wrap,
    type: s,
    activate: r
  }), !0;
}, Fe = function() {
  return E.records.boxes.length > 0;
}, We = function() {
  return E.records.boxes.some((t) => t.name);
}, qe = function() {
  return E.records.messages;
}, ze = function() {
  return E.records.boxes;
}, He = function() {
  return E.records.actors;
}, Ue = function() {
  return E.records.createdActors;
}, Ke = function() {
  return E.records.destroyedActors;
}, _t = function(t) {
  return E.records.actors[t];
}, Ge = function() {
  return Object.keys(E.records.actors);
}, Xe = function() {
  E.records.sequenceNumbersEnabled = !0;
}, Je = function() {
  E.records.sequenceNumbersEnabled = !1;
}, Ze = () => E.records.sequenceNumbersEnabled, Qe = function(t) {
  E.records.wrapEnabled = t;
}, gt = () => E.records.wrapEnabled !== void 0 ? E.records.wrapEnabled : st().sequence.wrap, je = function() {
  E.reset(), Ie();
}, $e = function(t) {
  const e = t.trim(), c = {
    text: e.replace(/^:?(?:no)?wrap:/, "").trim(),
    wrap: e.match(/^:?wrap:/) !== null ? !0 : e.match(/^:?nowrap:/) !== null ? !1 : void 0
  };
  return X.debug("parseMessage:", c), c;
}, t0 = function(t) {
  const e = t.match(/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/);
  let c = e != null && e[1] ? e[1].trim() : "transparent", s = e != null && e[2] ? e[2].trim() : void 0;
  if (window && window.CSS)
    window.CSS.supports("color", c) || (c = "transparent", s = t.trim());
  else {
    const r = new Option().style;
    r.color = c, r.color !== c && (c = "transparent", s = t.trim());
  }
  return {
    color: c,
    text: s !== void 0 ? Nt(s.replace(/^:?(?:no)?wrap:/, ""), st()) : void 0,
    wrap: s !== void 0 ? s.match(/^:?wrap:/) !== null ? !0 : s.match(/^:?nowrap:/) !== null ? !1 : void 0 : void 0
  };
}, mt = {
  SOLID: 0,
  DOTTED: 1,
  NOTE: 2,
  SOLID_CROSS: 3,
  DOTTED_CROSS: 4,
  SOLID_OPEN: 5,
  DOTTED_OPEN: 6,
  LOOP_START: 10,
  LOOP_END: 11,
  ALT_START: 12,
  ALT_ELSE: 13,
  ALT_END: 14,
  OPT_START: 15,
  OPT_END: 16,
  ACTIVE_START: 17,
  ACTIVE_END: 18,
  PAR_START: 19,
  PAR_AND: 20,
  PAR_END: 21,
  RECT_START: 22,
  RECT_END: 23,
  SOLID_POINT: 24,
  DOTTED_POINT: 25,
  AUTONUMBER: 26,
  CRITICAL_START: 27,
  CRITICAL_OPTION: 28,
  CRITICAL_END: 29,
  BREAK_START: 30,
  BREAK_END: 31,
  PAR_OVER_START: 32
}, e0 = {
  FILLED: 0,
  OPEN: 1
}, s0 = {
  LEFTOF: 0,
  RIGHTOF: 1,
  OVER: 2
}, ne = function(t, e, c) {
  const s = {
    actor: t,
    placement: e,
    message: c.text,
    wrap: c.wrap === void 0 && gt() || !!c.wrap
  }, r = [].concat(t, t);
  E.records.notes.push(s), E.records.messages.push({
    from: r[0],
    to: r[1],
    message: c.text,
    wrap: c.wrap === void 0 && gt() || !!c.wrap,
    type: mt.NOTE,
    placement: e
  });
}, re = function(t, e) {
  const c = _t(t);
  try {
    let s = Nt(e.text, st());
    s = s.replace(/&amp;/g, "&"), s = s.replace(/&equals;/g, "=");
    const r = JSON.parse(s);
    Ht(c, r);
  } catch (s) {
    X.error("error while parsing actor link text", s);
  }
}, n0 = function(t, e) {
  const c = _t(t);
  try {
    const o = {};
    let l = Nt(e.text, st());
    var s = l.indexOf("@");
    l = l.replace(/&amp;/g, "&"), l = l.replace(/&equals;/g, "=");
    var r = l.slice(0, s - 1).trim(), i = l.slice(s + 1).trim();
    o[r] = i, Ht(c, o);
  } catch (o) {
    X.error("error while parsing actor link text", o);
  }
};
function Ht(t, e) {
  if (t.links == null)
    t.links = e;
  else
    for (let c in e)
      t.links[c] = e[c];
}
const ie = function(t, e) {
  const c = _t(t);
  try {
    let s = Nt(e.text, st());
    const r = JSON.parse(s);
    ae(c, r);
  } catch (s) {
    X.error("error while parsing actor properties text", s);
  }
};
function ae(t, e) {
  if (t.properties == null)
    t.properties = e;
  else
    for (let c in e)
      t.properties[c] = e[c];
}
function r0() {
  E.records.currentBox = void 0;
}
const oe = function(t, e) {
  const c = _t(t), s = document.getElementById(e.text);
  try {
    const r = s.innerHTML, i = JSON.parse(r);
    i.properties && ae(c, i.properties), i.links && Ht(c, i.links);
  } catch (r) {
    X.error("error while parsing actor details text", r);
  }
}, i0 = function(t, e) {
  if (t !== void 0 && t.properties !== void 0)
    return t.properties[e];
}, ce = function(t) {
  if (Array.isArray(t))
    t.forEach(function(e) {
      ce(e);
    });
  else
    switch (t.type) {
      case "sequenceIndex":
        E.records.messages.push({
          from: void 0,
          to: void 0,
          message: {
            start: t.sequenceIndex,
            step: t.sequenceIndexStep,
            visible: t.sequenceVisible
          },
          wrap: !1,
          type: t.signalType
        });
        break;
      case "addParticipant":
        Ft(t.actor, t.actor, t.description, t.draw);
        break;
      case "createParticipant":
        if (E.records.actors[t.actor])
          throw new Error(
            "It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior"
          );
        E.records.lastCreated = t.actor, Ft(t.actor, t.actor, t.description, t.draw), E.records.createdActors[t.actor] = E.records.messages.length;
        break;
      case "destroyParticipant":
        E.records.lastDestroyed = t.actor, E.records.destroyedActors[t.actor] = E.records.messages.length;
        break;
      case "activeStart":
        C(t.actor, void 0, void 0, t.signalType);
        break;
      case "activeEnd":
        C(t.actor, void 0, void 0, t.signalType);
        break;
      case "addNote":
        ne(t.actor, t.placement, t.text);
        break;
      case "addLinks":
        re(t.actor, t.text);
        break;
      case "addALink":
        n0(t.actor, t.text);
        break;
      case "addProperties":
        ie(t.actor, t.text);
        break;
      case "addDetails":
        oe(t.actor, t.text);
        break;
      case "addMessage":
        if (E.records.lastCreated) {
          if (t.to !== E.records.lastCreated)
            throw new Error(
              "The created participant " + E.records.lastCreated + " does not have an associated creating message after its declaration. Please check the sequence diagram."
            );
          E.records.lastCreated = void 0;
        } else if (E.records.lastDestroyed) {
          if (t.to !== E.records.lastDestroyed && t.from !== E.records.lastDestroyed)
            throw new Error(
              "The destroyed participant " + E.records.lastDestroyed + " does not have an associated destroying message after its declaration. Please check the sequence diagram."
            );
          E.records.lastDestroyed = void 0;
        }
        C(t.from, t.to, t.msg, t.signalType, t.activate);
        break;
      case "boxStart":
        Oe(t.boxData);
        break;
      case "boxEnd":
        r0();
        break;
      case "loopStart":
        C(void 0, void 0, t.loopText, t.signalType);
        break;
      case "loopEnd":
        C(void 0, void 0, void 0, t.signalType);
        break;
      case "rectStart":
        C(void 0, void 0, t.color, t.signalType);
        break;
      case "rectEnd":
        C(void 0, void 0, void 0, t.signalType);
        break;
      case "optStart":
        C(void 0, void 0, t.optText, t.signalType);
        break;
      case "optEnd":
        C(void 0, void 0, void 0, t.signalType);
        break;
      case "altStart":
        C(void 0, void 0, t.altText, t.signalType);
        break;
      case "else":
        C(void 0, void 0, t.altText, t.signalType);
        break;
      case "altEnd":
        C(void 0, void 0, void 0, t.signalType);
        break;
      case "setAccTitle":
        $t(t.text);
        break;
      case "parStart":
        C(void 0, void 0, t.parText, t.signalType);
        break;
      case "and":
        C(void 0, void 0, t.parText, t.signalType);
        break;
      case "parEnd":
        C(void 0, void 0, void 0, t.signalType);
        break;
      case "criticalStart":
        C(void 0, void 0, t.criticalText, t.signalType);
        break;
      case "option":
        C(void 0, void 0, t.optionText, t.signalType);
        break;
      case "criticalEnd":
        C(void 0, void 0, void 0, t.signalType);
        break;
      case "breakStart":
        C(void 0, void 0, t.breakText, t.signalType);
        break;
      case "breakEnd":
        C(void 0, void 0, void 0, t.signalType);
        break;
    }
}, Qt = {
  addActor: Ft,
  addMessage: Ye,
  addSignal: C,
  addLinks: re,
  addDetails: oe,
  addProperties: ie,
  autoWrap: gt,
  setWrap: Qe,
  enableSequenceNumbers: Xe,
  disableSequenceNumbers: Je,
  showSequenceNumbers: Ze,
  getMessages: qe,
  getActors: He,
  getCreatedActors: Ue,
  getDestroyedActors: Ke,
  getActor: _t,
  getActorKeys: Ge,
  getActorProperty: i0,
  getAccTitle: we,
  getBoxes: ze,
  getDiagramTitle: _e,
  setDiagramTitle: ke,
  getConfig: () => st().sequence,
  clear: je,
  parseMessage: $e,
  parseBoxData: t0,
  LINETYPE: mt,
  ARROWTYPE: e0,
  PLACEMENT: s0,
  addNote: ne,
  setAccTitle: $t,
  apply: ce,
  setAccDescription: Pe,
  getAccDescription: Le,
  hasAtLeastOneBox: Fe,
  hasAtLeastOneBoxWithTitle: We
}, a0 = (t) => `.actor {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }

  text.actor > tspan {
    fill: ${t.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${t.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${t.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${t.signalColor};
  }

  #arrowhead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .sequenceNumber {
    fill: ${t.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${t.signalColor};
  }

  #crosshead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .messageText {
    fill: ${t.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${t.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${t.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${t.noteBorderColor};
    fill: ${t.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${t.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation1 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation2 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${t.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
    stroke-width: 2px;
  }
`, o0 = a0, ft = 18 * 2, le = "actor-top", de = "actor-bottom", Ut = function(t, e) {
  return Se(t, e);
}, c0 = function(t, e, c, s, r) {
  if (e.links === void 0 || e.links === null || Object.keys(e.links).length === 0)
    return { height: 0, width: 0 };
  const i = e.links, o = e.actorCnt, l = e.rectData;
  var u = "none";
  r && (u = "block !important");
  const n = t.append("g");
  n.attr("id", "actor" + o + "_popup"), n.attr("class", "actorPopupMenu"), n.attr("display", u);
  var x = "";
  l.class !== void 0 && (x = " " + l.class);
  let T = l.width > c ? l.width : c;
  const p = n.append("rect");
  if (p.attr("class", "actorPopupMenuPanel" + x), p.attr("x", l.x), p.attr("y", l.height), p.attr("fill", l.fill), p.attr("stroke", l.stroke), p.attr("width", T), p.attr("height", l.height), p.attr("rx", l.rx), p.attr("ry", l.ry), i != null) {
    var g = 20;
    for (let A in i) {
      var m = n.append("a"), k = te.sanitizeUrl(i[A]);
      m.attr("xlink:href", k), m.attr("target", "_blank"), P0(s)(
        A,
        m,
        l.x + 10,
        l.height + g,
        T,
        20,
        { class: "actor" },
        s
      ), g += 30;
    }
  }
  return p.attr("height", g), { height: l.height + g, width: T };
}, l0 = function(t) {
  return "var pu = document.getElementById('" + t + "'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }";
}, At = async function(t, e, c = null) {
  let s = t.append("foreignObject");
  const r = await se(e.text, Bt()), o = s.append("xhtml:div").attr("style", "width: fit-content;").attr("xmlns", "http://www.w3.org/1999/xhtml").html(r).node().getBoundingClientRect();
  if (s.attr("height", Math.round(o.height)).attr("width", Math.round(o.width)), e.class === "noteText") {
    const l = t.node().firstChild;
    l.setAttribute("height", o.height + 2 * e.textMargin);
    const u = l.getBBox();
    s.attr("x", Math.round(u.x + u.width / 2 - o.width / 2)).attr("y", Math.round(u.y + u.height / 2 - o.height / 2));
  } else if (c) {
    let { startx: l, stopx: u, starty: n } = c;
    if (l > u) {
      const x = l;
      l = u, u = x;
    }
    s.attr("x", Math.round(l + Math.abs(l - u) / 2 - o.width / 2)), e.class === "loopText" ? s.attr("y", Math.round(n)) : s.attr("y", Math.round(n - o.height));
  }
  return [s];
}, bt = function(t, e) {
  let c = 0, s = 0;
  const r = e.text.split(_.lineBreakRegex), [i, o] = ee(e.fontSize);
  let l = [], u = 0, n = () => e.y;
  if (e.valign !== void 0 && e.textMargin !== void 0 && e.textMargin > 0)
    switch (e.valign) {
      case "top":
      case "start":
        n = () => Math.round(e.y + e.textMargin);
        break;
      case "middle":
      case "center":
        n = () => Math.round(e.y + (c + s + e.textMargin) / 2);
        break;
      case "bottom":
      case "end":
        n = () => Math.round(
          e.y + (c + s + 2 * e.textMargin) - e.textMargin
        );
        break;
    }
  if (e.anchor !== void 0 && e.textMargin !== void 0 && e.width !== void 0)
    switch (e.anchor) {
      case "left":
      case "start":
        e.x = Math.round(e.x + e.textMargin), e.anchor = "start", e.dominantBaseline = "middle", e.alignmentBaseline = "middle";
        break;
      case "middle":
      case "center":
        e.x = Math.round(e.x + e.width / 2), e.anchor = "middle", e.dominantBaseline = "middle", e.alignmentBaseline = "middle";
        break;
      case "right":
      case "end":
        e.x = Math.round(e.x + e.width - e.textMargin), e.anchor = "end", e.dominantBaseline = "middle", e.alignmentBaseline = "middle";
        break;
    }
  for (let [x, T] of r.entries()) {
    e.textMargin !== void 0 && e.textMargin === 0 && i !== void 0 && (u = x * i);
    const p = t.append("text");
    p.attr("x", e.x), p.attr("y", n()), e.anchor !== void 0 && p.attr("text-anchor", e.anchor).attr("dominant-baseline", e.dominantBaseline).attr("alignment-baseline", e.alignmentBaseline), e.fontFamily !== void 0 && p.style("font-family", e.fontFamily), o !== void 0 && p.style("font-size", o), e.fontWeight !== void 0 && p.style("font-weight", e.fontWeight), e.fill !== void 0 && p.attr("fill", e.fill), e.class !== void 0 && p.attr("class", e.class), e.dy !== void 0 ? p.attr("dy", e.dy) : u !== 0 && p.attr("dy", u);
    const g = T || Ae;
    if (e.tspan) {
      const m = p.append("tspan");
      m.attr("x", e.x), e.fill !== void 0 && m.attr("fill", e.fill), m.text(g);
    } else
      p.text(g);
    e.valign !== void 0 && e.textMargin !== void 0 && e.textMargin > 0 && (s += (p._groups || p)[0][0].getBBox().height, c = s), l.push(p);
  }
  return l;
}, he = function(t, e) {
  function c(r, i, o, l, u) {
    return r + "," + i + " " + (r + o) + "," + i + " " + (r + o) + "," + (i + l - u) + " " + (r + o - u * 1.2) + "," + (i + l) + " " + r + "," + (i + l);
  }
  const s = t.append("polygon");
  return s.attr("points", c(e.x, e.y, e.width, e.height, 7)), s.attr("class", "labelBox"), e.y = e.y + e.height / 2, bt(t, e), s;
};
let it = -1;
const ue = (t, e, c, s) => {
  t.select && c.forEach((r) => {
    const i = e[r], o = t.select("#actor" + i.actorCnt);
    !s.mirrorActors && i.stopy ? o.attr("y2", i.stopy + i.height / 2) : s.mirrorActors && o.attr("y2", i.stopy);
  });
}, d0 = async function(t, e, c, s) {
  const r = s ? e.stopy : e.starty, i = e.x + e.width / 2, o = r + 5, l = t.append("g").lower();
  var u = l;
  s || (it++, Object.keys(e.links || {}).length && !c.forceMenus && u.attr("onclick", l0(`actor${it}_popup`)).attr("cursor", "pointer"), u.append("line").attr("id", "actor" + it).attr("x1", i).attr("y1", o).attr("x2", i).attr("y2", 2e3).attr("class", "actor-line").attr("class", "200").attr("stroke-width", "0.5px").attr("stroke", "#999"), u = l.append("g"), e.actorCnt = it, e.links != null && u.attr("id", "root-" + it));
  const n = vt();
  var x = "actor";
  e.properties != null && e.properties.class ? x = e.properties.class : n.fill = "#eaeaea", s ? x += ` ${de}` : x += ` ${le}`, n.x = e.x, n.y = r, n.width = e.width, n.height = e.height, n.class = x, n.rx = 3, n.ry = 3, n.name = e.name;
  const T = Ut(u, n);
  if (e.rectData = n, e.properties != null && e.properties.icon) {
    const g = e.properties.icon.trim();
    g.charAt(0) === "@" ? Re(u, n.x + n.width - 20, n.y + 10, g.substr(1)) : Ce(u, n.x + n.width - 20, n.y + 10, g);
  }
  await Kt(c, at(e.description))(
    e.description,
    u,
    n.x,
    n.y,
    n.width,
    n.height,
    { class: "actor" },
    c
  );
  let p = e.height;
  if (T.node) {
    const g = T.node().getBBox();
    e.height = g.height, p = g.height;
  }
  return p;
}, h0 = async function(t, e, c, s) {
  const r = s ? e.stopy : e.starty, i = e.x + e.width / 2, o = r + 80;
  t.lower(), s || (it++, t.append("line").attr("id", "actor" + it).attr("x1", i).attr("y1", o).attr("x2", i).attr("y2", 2e3).attr("class", "actor-line").attr("class", "200").attr("stroke-width", "0.5px").attr("stroke", "#999"), e.actorCnt = it);
  const l = t.append("g");
  let u = "actor-man";
  s ? u += ` ${de}` : u += ` ${le}`, l.attr("class", u), l.attr("name", e.name);
  const n = vt();
  n.x = e.x, n.y = r, n.fill = "#eaeaea", n.width = e.width, n.height = e.height, n.class = "actor", n.rx = 3, n.ry = 3, l.append("line").attr("id", "actor-man-torso" + it).attr("x1", i).attr("y1", r + 25).attr("x2", i).attr("y2", r + 45), l.append("line").attr("id", "actor-man-arms" + it).attr("x1", i - ft / 2).attr("y1", r + 33).attr("x2", i + ft / 2).attr("y2", r + 33), l.append("line").attr("x1", i - ft / 2).attr("y1", r + 60).attr("x2", i).attr("y2", r + 45), l.append("line").attr("x1", i).attr("y1", r + 45).attr("x2", i + ft / 2 - 2).attr("y2", r + 60);
  const x = l.append("circle");
  x.attr("cx", e.x + e.width / 2), x.attr("cy", r + 10), x.attr("r", 15), x.attr("width", e.width), x.attr("height", e.height);
  const T = l.node().getBBox();
  return e.height = T.height, await Kt(c, at(e.description))(
    e.description,
    l,
    n.x,
    n.y + 35,
    n.width,
    n.height,
    { class: "actor" },
    c
  ), e.height;
}, u0 = async function(t, e, c, s) {
  switch (e.type) {
    case "actor":
      return await h0(t, e, c, s);
    case "participant":
      return await d0(t, e, c, s);
  }
}, p0 = async function(t, e, c) {
  const r = t.append("g");
  pe(r, e), e.name && await Kt(c)(
    e.name,
    r,
    e.x,
    e.y + (e.textMaxHeight || 0) / 2,
    e.width,
    0,
    { class: "text" },
    c
  ), r.lower();
}, f0 = function(t) {
  return t.append("g");
}, g0 = function(t, e, c, s, r) {
  const i = vt(), o = e.anchored;
  i.x = e.startx, i.y = e.starty, i.class = "activation" + r % 3, i.width = e.stopx - e.startx, i.height = c - e.starty, Ut(o, i);
}, x0 = async function(t, e, c, s) {
  const {
    boxMargin: r,
    boxTextMargin: i,
    labelBoxHeight: o,
    labelBoxWidth: l,
    messageFontFamily: u,
    messageFontSize: n,
    messageFontWeight: x
  } = s, T = t.append("g"), p = function(k, A, V, S) {
    return T.append("line").attr("x1", k).attr("y1", A).attr("x2", V).attr("y2", S).attr("class", "loopLine");
  };
  p(e.startx, e.starty, e.stopx, e.starty), p(e.stopx, e.starty, e.stopx, e.stopy), p(e.startx, e.stopy, e.stopx, e.stopy), p(e.startx, e.starty, e.startx, e.stopy), e.sections !== void 0 && e.sections.forEach(function(k) {
    p(e.startx, k.y, e.stopx, k.y).style(
      "stroke-dasharray",
      "3, 3"
    );
  });
  let g = zt();
  g.text = c, g.x = e.startx, g.y = e.starty, g.fontFamily = u, g.fontSize = n, g.fontWeight = x, g.anchor = "middle", g.valign = "middle", g.tspan = !1, g.width = l || 50, g.height = o || 20, g.textMargin = i, g.class = "labelText", he(T, g), g = fe(), g.text = e.title, g.x = e.startx + l / 2 + (e.stopx - e.startx) / 2, g.y = e.starty + r + i, g.anchor = "middle", g.valign = "middle", g.textMargin = i, g.class = "loopText", g.fontFamily = u, g.fontSize = n, g.fontWeight = x, g.wrap = !0;
  let m = at(g.text) ? await At(T, g, e) : bt(T, g);
  if (e.sectionTitles !== void 0) {
    for (const [k, A] of Object.entries(e.sectionTitles))
      if (A.message) {
        g.text = A.message, g.x = e.startx + (e.stopx - e.startx) / 2, g.y = e.sections[k].y + r + i, g.class = "loopText", g.anchor = "middle", g.valign = "middle", g.tspan = !1, g.fontFamily = u, g.fontSize = n, g.fontWeight = x, g.wrap = e.wrap, at(g.text) ? (e.starty = e.sections[k].y, await At(T, g, e)) : bt(T, g);
        let V = Math.round(
          m.map((S) => (S._groups || S)[0][0].getBBox().height).reduce((S, O) => S + O)
        );
        e.sections[k].height += V - (r + i);
      }
  }
  return e.height = Math.round(e.stopy - e.starty), T;
}, pe = function(t, e) {
  Me(t, e);
}, y0 = function(t) {
  t.append("defs").append("symbol").attr("id", "database").attr("fill-rule", "evenodd").attr("clip-rule", "evenodd").append("path").attr("transform", "scale(.5)").attr(
    "d",
    "M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z"
  );
}, T0 = function(t) {
  t.append("defs").append("symbol").attr("id", "computer").attr("width", "24").attr("height", "24").append("path").attr("transform", "scale(.5)").attr(
    "d",
    "M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z"
  );
}, b0 = function(t) {
  t.append("defs").append("symbol").attr("id", "clock").attr("width", "24").attr("height", "24").append("path").attr("transform", "scale(.5)").attr(
    "d",
    "M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z"
  );
}, E0 = function(t) {
  t.append("defs").append("marker").attr("id", "arrowhead").attr("refX", 7.9).attr("refY", 5).attr("markerUnits", "userSpaceOnUse").attr("markerWidth", 12).attr("markerHeight", 12).attr("orient", "auto").append("path").attr("d", "M 0 0 L 10 5 L 0 10 z");
}, m0 = function(t) {
  t.append("defs").append("marker").attr("id", "filled-head").attr("refX", 15.5).attr("refY", 7).attr("markerWidth", 20).attr("markerHeight", 28).attr("orient", "auto").append("path").attr("d", "M 18,7 L9,13 L14,7 L9,1 Z");
}, w0 = function(t) {
  t.append("defs").append("marker").attr("id", "sequencenumber").attr("refX", 15).attr("refY", 15).attr("markerWidth", 60).attr("markerHeight", 40).attr("orient", "auto").append("circle").attr("cx", 15).attr("cy", 15).attr("r", 6);
}, _0 = function(t) {
  t.append("defs").append("marker").attr("id", "crosshead").attr("markerWidth", 15).attr("markerHeight", 8).attr("orient", "auto").attr("refX", 4).attr("refY", 4.5).append("path").attr("fill", "none").attr("stroke", "#000000").style("stroke-dasharray", "0, 0").attr("stroke-width", "1pt").attr("d", "M 1,2 L 6,7 M 6,2 L 1,7");
}, fe = function() {
  return {
    x: 0,
    y: 0,
    fill: void 0,
    anchor: void 0,
    style: "#666",
    width: void 0,
    height: void 0,
    textMargin: 0,
    rx: 0,
    ry: 0,
    tspan: !0,
    valign: void 0
  };
}, k0 = function() {
  return {
    x: 0,
    y: 0,
    fill: "#EDF2AE",
    stroke: "#666",
    width: 100,
    anchor: "start",
    height: 100,
    rx: 0,
    ry: 0
  };
}, Kt = function() {
  function t(i, o, l, u, n, x, T) {
    const p = o.append("text").attr("x", l + n / 2).attr("y", u + x / 2 + 5).style("text-anchor", "middle").text(i);
    r(p, T);
  }
  function e(i, o, l, u, n, x, T, p) {
    const { actorFontSize: g, actorFontFamily: m, actorFontWeight: k } = p, [A, V] = ee(g), S = i.split(_.lineBreakRegex);
    for (let O = 0; O < S.length; O++) {
      const R = O * A - A * (S.length - 1) / 2, q = o.append("text").attr("x", l + n / 2).attr("y", u).style("text-anchor", "middle").style("font-size", V).style("font-weight", k).style("font-family", m);
      q.append("tspan").attr("x", l + n / 2).attr("dy", R).text(S[O]), q.attr("y", u + x / 2).attr("dominant-baseline", "central").attr("alignment-baseline", "central"), r(q, T);
    }
  }
  function c(i, o, l, u, n, x, T, p) {
    const g = o.append("switch"), k = g.append("foreignObject").attr("x", l).attr("y", u).attr("width", n).attr("height", x).append("xhtml:div").style("display", "table").style("height", "100%").style("width", "100%");
    k.append("div").style("display", "table-cell").style("text-align", "center").style("vertical-align", "middle").text(i), e(i, g, l, u, n, x, T, p), r(k, T);
  }
  async function s(i, o, l, u, n, x, T, p) {
    const g = await wt(i, Bt()), m = o.append("switch"), A = m.append("foreignObject").attr("x", l + n / 2 - g.width / 2).attr("y", u + x / 2 - g.height / 2).attr("width", g.width).attr("height", g.height).append("xhtml:div").style("height", "100%").style("width", "100%");
    A.append("div").style("text-align", "center").style("vertical-align", "middle").html(await se(i, Bt())), e(i, m, l, u, n, x, T, p), r(A, T);
  }
  function r(i, o) {
    for (const l in o)
      o.hasOwnProperty(l) && i.attr(l, o[l]);
  }
  return function(i, o = !1) {
    return o ? s : i.textPlacement === "fo" ? c : i.textPlacement === "old" ? t : e;
  };
}(), P0 = function() {
  function t(r, i, o, l, u, n, x) {
    const T = i.append("text").attr("x", o).attr("y", l).style("text-anchor", "start").text(r);
    s(T, x);
  }
  function e(r, i, o, l, u, n, x, T) {
    const { actorFontSize: p, actorFontFamily: g, actorFontWeight: m } = T, k = r.split(_.lineBreakRegex);
    for (let A = 0; A < k.length; A++) {
      const V = A * p - p * (k.length - 1) / 2, S = i.append("text").attr("x", o).attr("y", l).style("text-anchor", "start").style("font-size", p).style("font-weight", m).style("font-family", g);
      S.append("tspan").attr("x", o).attr("dy", V).text(k[A]), S.attr("y", l + n / 2).attr("dominant-baseline", "central").attr("alignment-baseline", "central"), s(S, x);
    }
  }
  function c(r, i, o, l, u, n, x, T) {
    const p = i.append("switch"), m = p.append("foreignObject").attr("x", o).attr("y", l).attr("width", u).attr("height", n).append("xhtml:div").style("display", "table").style("height", "100%").style("width", "100%");
    m.append("div").style("display", "table-cell").style("text-align", "center").style("vertical-align", "middle").text(r), e(r, p, o, l, u, n, x, T), s(m, x);
  }
  function s(r, i) {
    for (const o in i)
      i.hasOwnProperty(o) && r.attr(o, i[o]);
  }
  return function(r) {
    return r.textPlacement === "fo" ? c : r.textPlacement === "old" ? t : e;
  };
}(), D = {
  drawRect: Ut,
  drawText: bt,
  drawLabel: he,
  drawActor: u0,
  drawBox: p0,
  drawPopup: c0,
  anchorElement: f0,
  drawActivation: g0,
  drawLoop: x0,
  drawBackgroundRect: pe,
  insertArrowHead: E0,
  insertArrowFilledHead: m0,
  insertSequenceNumber: w0,
  insertArrowCrossHead: _0,
  insertDatabaseIcon: y0,
  insertComputerIcon: T0,
  insertClockIcon: b0,
  getTextObj: fe,
  getNoteRect: k0,
  fixLifeLineHeights: ue,
  sanitizeUrl: te.sanitizeUrl
};
let a = {};
const f = {
  data: {
    startx: void 0,
    stopx: void 0,
    starty: void 0,
    stopy: void 0
  },
  verticalPos: 0,
  sequenceItems: [],
  activations: [],
  models: {
    getHeight: function() {
      return Math.max.apply(
        null,
        this.actors.length === 0 ? [0] : this.actors.map((t) => t.height || 0)
      ) + (this.loops.length === 0 ? 0 : this.loops.map((t) => t.height || 0).reduce((t, e) => t + e)) + (this.messages.length === 0 ? 0 : this.messages.map((t) => t.height || 0).reduce((t, e) => t + e)) + (this.notes.length === 0 ? 0 : this.notes.map((t) => t.height || 0).reduce((t, e) => t + e));
    },
    clear: function() {
      this.actors = [], this.boxes = [], this.loops = [], this.messages = [], this.notes = [];
    },
    addBox: function(t) {
      this.boxes.push(t);
    },
    addActor: function(t) {
      this.actors.push(t);
    },
    addLoop: function(t) {
      this.loops.push(t);
    },
    addMessage: function(t) {
      this.messages.push(t);
    },
    addNote: function(t) {
      this.notes.push(t);
    },
    lastActor: function() {
      return this.actors[this.actors.length - 1];
    },
    lastLoop: function() {
      return this.loops[this.loops.length - 1];
    },
    lastMessage: function() {
      return this.messages[this.messages.length - 1];
    },
    lastNote: function() {
      return this.notes[this.notes.length - 1];
    },
    actors: [],
    boxes: [],
    loops: [],
    messages: [],
    notes: []
  },
  init: function() {
    this.sequenceItems = [], this.activations = [], this.models.clear(), this.data = {
      startx: void 0,
      stopx: void 0,
      starty: void 0,
      stopy: void 0
    }, this.verticalPos = 0, xe(st());
  },
  updateVal: function(t, e, c, s) {
    t[e] === void 0 ? t[e] = c : t[e] = s(c, t[e]);
  },
  updateBounds: function(t, e, c, s) {
    const r = this;
    let i = 0;
    function o(l) {
      return function(n) {
        i++;
        const x = r.sequenceItems.length - i + 1;
        r.updateVal(n, "starty", e - x * a.boxMargin, Math.min), r.updateVal(n, "stopy", s + x * a.boxMargin, Math.max), r.updateVal(f.data, "startx", t - x * a.boxMargin, Math.min), r.updateVal(f.data, "stopx", c + x * a.boxMargin, Math.max), l !== "activation" && (r.updateVal(n, "startx", t - x * a.boxMargin, Math.min), r.updateVal(n, "stopx", c + x * a.boxMargin, Math.max), r.updateVal(f.data, "starty", e - x * a.boxMargin, Math.min), r.updateVal(f.data, "stopy", s + x * a.boxMargin, Math.max));
      };
    }
    this.sequenceItems.forEach(o()), this.activations.forEach(o("activation"));
  },
  insert: function(t, e, c, s) {
    const r = _.getMin(t, c), i = _.getMax(t, c), o = _.getMin(e, s), l = _.getMax(e, s);
    this.updateVal(f.data, "startx", r, Math.min), this.updateVal(f.data, "starty", o, Math.min), this.updateVal(f.data, "stopx", i, Math.max), this.updateVal(f.data, "stopy", l, Math.max), this.updateBounds(r, o, i, l);
  },
  newActivation: function(t, e, c) {
    const s = c[t.from.actor], r = St(t.from.actor).length || 0, i = s.x + s.width / 2 + (r - 1) * a.activationWidth / 2;
    this.activations.push({
      startx: i,
      starty: this.verticalPos + 2,
      stopx: i + a.activationWidth,
      stopy: void 0,
      actor: t.from.actor,
      anchored: D.anchorElement(e)
    });
  },
  endActivation: function(t) {
    const e = this.activations.map(function(c) {
      return c.actor;
    }).lastIndexOf(t.from.actor);
    return this.activations.splice(e, 1)[0];
  },
  createLoop: function(t = { message: void 0, wrap: !1, width: void 0 }, e) {
    return {
      startx: void 0,
      starty: this.verticalPos,
      stopx: void 0,
      stopy: void 0,
      title: t.message,
      wrap: t.wrap,
      width: t.width,
      height: 0,
      fill: e
    };
  },
  newLoop: function(t = { message: void 0, wrap: !1, width: void 0 }, e) {
    this.sequenceItems.push(this.createLoop(t, e));
  },
  endLoop: function() {
    return this.sequenceItems.pop();
  },
  isLoopOverlap: function() {
    return this.sequenceItems.length ? this.sequenceItems[this.sequenceItems.length - 1].overlap : !1;
  },
  addSectionToLoop: function(t) {
    const e = this.sequenceItems.pop();
    e.sections = e.sections || [], e.sectionTitles = e.sectionTitles || [], e.sections.push({ y: f.getVerticalPos(), height: 0 }), e.sectionTitles.push(t), this.sequenceItems.push(e);
  },
  saveVerticalPos: function() {
    this.isLoopOverlap() && (this.savedVerticalPos = this.verticalPos);
  },
  resetVerticalPos: function() {
    this.isLoopOverlap() && (this.verticalPos = this.savedVerticalPos);
  },
  bumpVerticalPos: function(t) {
    this.verticalPos = this.verticalPos + t, this.data.stopy = _.getMax(this.data.stopy, this.verticalPos);
  },
  getVerticalPos: function() {
    return this.verticalPos;
  },
  getBounds: function() {
    return { bounds: this.data, models: this.models };
  }
}, L0 = async function(t, e) {
  f.bumpVerticalPos(a.boxMargin), e.height = a.boxMargin, e.starty = f.getVerticalPos();
  const c = vt();
  c.x = e.startx, c.y = e.starty, c.width = e.width || a.width, c.class = "note";
  const s = t.append("g"), r = D.drawRect(s, c), i = zt();
  i.x = e.startx, i.y = e.starty, i.width = c.width, i.dy = "1em", i.text = e.message, i.class = "noteText", i.fontFamily = a.noteFontFamily, i.fontSize = a.noteFontSize, i.fontWeight = a.noteFontWeight, i.anchor = a.noteAlign, i.textMargin = a.noteMargin, i.valign = "center";
  const o = at(i.text) ? await At(s, i) : bt(s, i), l = Math.round(
    o.map((u) => (u._groups || u)[0][0].getBBox().height).reduce((u, n) => u + n)
  );
  r.attr("height", l + 2 * a.noteMargin), e.height += l + 2 * a.noteMargin, f.bumpVerticalPos(l + 2 * a.noteMargin), e.stopy = e.starty + l + 2 * a.noteMargin, e.stopx = e.startx + c.width, f.insert(e.startx, e.starty, e.stopx, e.stopy), f.models.addNote(e);
}, xt = (t) => ({
  fontFamily: t.messageFontFamily,
  fontSize: t.messageFontSize,
  fontWeight: t.messageFontWeight
}), Tt = (t) => ({
  fontFamily: t.noteFontFamily,
  fontSize: t.noteFontSize,
  fontWeight: t.noteFontWeight
}), Wt = (t) => ({
  fontFamily: t.actorFontFamily,
  fontSize: t.actorFontSize,
  fontWeight: t.actorFontWeight
});
async function I0(t, e) {
  f.bumpVerticalPos(10);
  const { startx: c, stopx: s, message: r } = e, i = _.splitBreaks(r).length, o = at(r), l = o ? await wt(r, st()) : B.calculateTextDimensions(r, xt(a));
  if (!o) {
    const T = l.height / i;
    e.height += T, f.bumpVerticalPos(T);
  }
  let u, n = l.height - 10;
  const x = l.width;
  if (c === s) {
    u = f.getVerticalPos() + n, a.rightAngles || (n += a.boxMargin, u = f.getVerticalPos() + n), n += 30;
    const T = _.getMax(x / 2, a.width / 2);
    f.insert(
      c - T,
      f.getVerticalPos() - 10 + n,
      s + T,
      f.getVerticalPos() + 30 + n
    );
  } else
    n += a.boxMargin, u = f.getVerticalPos() + n, f.insert(c, u - 10, s, u);
  return f.bumpVerticalPos(n), e.height += n, e.stopy = e.starty + e.height, f.insert(e.fromBounds, e.starty, e.toBounds, e.stopy), u;
}
const A0 = async function(t, e, c, s) {
  const { startx: r, stopx: i, starty: o, message: l, type: u, sequenceIndex: n, sequenceVisible: x } = e, T = B.calculateTextDimensions(l, xt(a)), p = zt();
  p.x = r, p.y = o + 10, p.width = i - r, p.class = "messageText", p.dy = "1em", p.text = l, p.fontFamily = a.messageFontFamily, p.fontSize = a.messageFontSize, p.fontWeight = a.messageFontWeight, p.anchor = a.messageAlign, p.valign = "center", p.textMargin = a.wrapPadding, p.tspan = !1, at(p.text) ? await At(t, p, { startx: r, stopx: i, starty: c }) : bt(t, p);
  const g = T.width;
  let m;
  r === i ? a.rightAngles ? m = t.append("path").attr(
    "d",
    `M  ${r},${c} H ${r + _.getMax(a.width / 2, g / 2)} V ${c + 25} H ${r}`
  ) : m = t.append("path").attr(
    "d",
    "M " + r + "," + c + " C " + (r + 60) + "," + (c - 10) + " " + (r + 60) + "," + (c + 30) + " " + r + "," + (c + 20)
  ) : (m = t.append("line"), m.attr("x1", r), m.attr("y1", c), m.attr("x2", i), m.attr("y2", c)), u === s.db.LINETYPE.DOTTED || u === s.db.LINETYPE.DOTTED_CROSS || u === s.db.LINETYPE.DOTTED_POINT || u === s.db.LINETYPE.DOTTED_OPEN ? (m.style("stroke-dasharray", "3, 3"), m.attr("class", "messageLine1")) : m.attr("class", "messageLine0");
  let k = "";
  a.arrowMarkerAbsolute && (k = window.location.protocol + "//" + window.location.host + window.location.pathname + window.location.search, k = k.replace(/\(/g, "\\("), k = k.replace(/\)/g, "\\)")), m.attr("stroke-width", 2), m.attr("stroke", "none"), m.style("fill", "none"), (u === s.db.LINETYPE.SOLID || u === s.db.LINETYPE.DOTTED) && m.attr("marker-end", "url(" + k + "#arrowhead)"), (u === s.db.LINETYPE.SOLID_POINT || u === s.db.LINETYPE.DOTTED_POINT) && m.attr("marker-end", "url(" + k + "#filled-head)"), (u === s.db.LINETYPE.SOLID_CROSS || u === s.db.LINETYPE.DOTTED_CROSS) && m.attr("marker-end", "url(" + k + "#crosshead)"), (x || a.showSequenceNumbers) && (m.attr("marker-start", "url(" + k + "#sequencenumber)"), t.append("text").attr("x", r).attr("y", c + 4).attr("font-family", "sans-serif").attr("font-size", "12px").attr("text-anchor", "middle").attr("class", "sequenceNumber").text(n));
}, N0 = async function(t, e, c, s, r, i, o) {
  let l = 0, u = 0, n, x = 0;
  for (const T of s) {
    const p = e[T], g = p.box;
    n && n != g && (o || f.models.addBox(n), u += a.boxMargin + n.margin), g && g != n && (o || (g.x = l + u, g.y = r), u += g.margin), p.width = p.width || a.width, p.height = _.getMax(p.height || a.height, a.height), p.margin = p.margin || a.actorMargin, x = _.getMax(x, p.height), c[p.name] && (u += p.width / 2), p.x = l + u, p.starty = f.getVerticalPos(), f.insert(p.x, r, p.x + p.width, p.height), l += p.width + u, p.box && (p.box.width = l + g.margin - p.box.x), u = p.margin, n = p.box, f.models.addActor(p);
  }
  n && !o && f.models.addBox(n), f.bumpVerticalPos(x);
}, qt = async function(t, e, c, s) {
  if (s) {
    let r = 0;
    f.bumpVerticalPos(a.boxMargin * 2);
    for (const i of c) {
      const o = e[i];
      o.stopy || (o.stopy = f.getVerticalPos());
      const l = await D.drawActor(t, o, a, !0);
      r = _.getMax(r, l);
    }
    f.bumpVerticalPos(r + a.boxMargin);
  } else
    for (const r of c) {
      const i = e[r];
      await D.drawActor(t, i, a, !1);
    }
}, ge = function(t, e, c, s) {
  let r = 0, i = 0;
  for (const o of c) {
    const l = e[o], u = R0(l), n = D.drawPopup(
      t,
      l,
      u,
      a,
      a.forceMenus,
      s
    );
    n.height > r && (r = n.height), n.width + l.x > i && (i = n.width + l.x);
  }
  return { maxHeight: r, maxWidth: i };
}, xe = function(t) {
  Ne(a, t), t.fontFamily && (a.actorFontFamily = a.noteFontFamily = a.messageFontFamily = t.fontFamily), t.fontSize && (a.actorFontSize = a.noteFontSize = a.messageFontSize = t.fontSize), t.fontWeight && (a.actorFontWeight = a.noteFontWeight = a.messageFontWeight = t.fontWeight);
}, St = function(t) {
  return f.activations.filter(function(e) {
    return e.actor === t;
  });
}, jt = function(t, e) {
  const c = e[t], s = St(t), r = s.reduce(function(o, l) {
    return _.getMin(o, l.startx);
  }, c.x + c.width / 2 - 1), i = s.reduce(function(o, l) {
    return _.getMax(o, l.stopx);
  }, c.x + c.width / 2 + 1);
  return [r, i];
};
function rt(t, e, c, s, r) {
  f.bumpVerticalPos(c);
  let i = s;
  if (e.id && e.message && t[e.id]) {
    const o = t[e.id].width, l = xt(a);
    e.message = B.wrapLabel(`[${e.message}]`, o - 2 * a.wrapPadding, l), e.width = o, e.wrap = !0;
    const u = B.calculateTextDimensions(e.message, l), n = _.getMax(u.height, a.labelBoxHeight);
    i = s + n, X.debug(`${n} - ${e.message}`);
  }
  r(e), f.bumpVerticalPos(i);
}
function v0(t, e, c, s, r, i, o) {
  function l(n, x) {
    n.x < r[t.from].x ? (f.insert(
      e.stopx - x,
      e.starty,
      e.startx,
      e.stopy + n.height / 2 + a.noteMargin
    ), e.stopx = e.stopx + x) : (f.insert(
      e.startx,
      e.starty,
      e.stopx + x,
      e.stopy + n.height / 2 + a.noteMargin
    ), e.stopx = e.stopx - x);
  }
  function u(n, x) {
    n.x < r[t.to].x ? (f.insert(
      e.startx - x,
      e.starty,
      e.stopx,
      e.stopy + n.height / 2 + a.noteMargin
    ), e.startx = e.startx + x) : (f.insert(
      e.stopx,
      e.starty,
      e.startx + x,
      e.stopy + n.height / 2 + a.noteMargin
    ), e.startx = e.startx - x);
  }
  if (i[t.to] == s) {
    const n = r[t.to], x = n.type == "actor" ? ft / 2 + 3 : n.width / 2 + 3;
    l(n, x), n.starty = c - n.height / 2, f.bumpVerticalPos(n.height / 2);
  } else if (o[t.from] == s) {
    const n = r[t.from];
    if (a.mirrorActors) {
      const x = n.type == "actor" ? ft / 2 : n.width / 2;
      u(n, x);
    }
    n.stopy = c - n.height / 2, f.bumpVerticalPos(n.height / 2);
  } else if (o[t.to] == s) {
    const n = r[t.to];
    if (a.mirrorActors) {
      const x = n.type == "actor" ? ft / 2 + 3 : n.width / 2 + 3;
      l(n, x);
    }
    n.stopy = c - n.height / 2, f.bumpVerticalPos(n.height / 2);
  }
}
const S0 = async function(t, e, c, s) {
  const { securityLevel: r, sequence: i } = st();
  a = i;
  let o;
  r === "sandbox" && (o = It("#i" + e));
  const l = r === "sandbox" ? It(o.nodes()[0].contentDocument.body) : It("body"), u = r === "sandbox" ? o.nodes()[0].contentDocument : document;
  f.init(), X.debug(s.db);
  const n = r === "sandbox" ? l.select(`[id="${e}"]`) : It(`[id="${e}"]`), x = s.db.getActors(), T = s.db.getCreatedActors(), p = s.db.getDestroyedActors(), g = s.db.getBoxes();
  let m = s.db.getActorKeys();
  const k = s.db.getMessages(), A = s.db.getDiagramTitle(), V = s.db.hasAtLeastOneBox(), S = s.db.hasAtLeastOneBoxWithTitle(), O = await M0(x, k, s);
  if (a.height = await C0(x, O, g), D.insertComputerIcon(n), D.insertDatabaseIcon(n), D.insertClockIcon(n), V && (f.bumpVerticalPos(a.boxMargin), S && f.bumpVerticalPos(g[0].textMaxHeight)), a.hideUnusedParticipants === !0) {
    const y = /* @__PURE__ */ new Set();
    k.forEach((L) => {
      y.add(L.from), y.add(L.to);
    }), m = m.filter((L) => y.has(L));
  }
  await N0(n, x, T, m, 0, k, !1);
  const R = await O0(k, x, O, s);
  D.insertArrowHead(n), D.insertArrowCrossHead(n), D.insertArrowFilledHead(n), D.insertSequenceNumber(n);
  function q(y, L) {
    const j = f.endActivation(y);
    j.starty + 18 > L && (j.starty = L - 6, L += 12), D.drawActivation(
      n,
      j,
      L,
      a,
      St(y.from.actor).length
    ), f.insert(j.startx, L - 10, j.stopx, L);
  }
  let z = 1, J = 1;
  const $ = [], H = [];
  let U = 0;
  for (const y of k) {
    let L, j, nt;
    switch (y.type) {
      case s.db.LINETYPE.NOTE:
        f.resetVerticalPos(), j = y.noteModel, await L0(n, j);
        break;
      case s.db.LINETYPE.ACTIVE_START:
        f.newActivation(y, n, x);
        break;
      case s.db.LINETYPE.ACTIVE_END:
        q(y, f.getVerticalPos());
        break;
      case s.db.LINETYPE.LOOP_START:
        rt(
          R,
          y,
          a.boxMargin,
          a.boxMargin + a.boxTextMargin,
          (N) => f.newLoop(N)
        );
        break;
      case s.db.LINETYPE.LOOP_END:
        L = f.endLoop(), await D.drawLoop(n, L, "loop", a), f.bumpVerticalPos(L.stopy - f.getVerticalPos()), f.models.addLoop(L);
        break;
      case s.db.LINETYPE.RECT_START:
        rt(
          R,
          y,
          a.boxMargin,
          a.boxMargin,
          (N) => f.newLoop(void 0, N.message)
        );
        break;
      case s.db.LINETYPE.RECT_END:
        L = f.endLoop(), H.push(L), f.models.addLoop(L), f.bumpVerticalPos(L.stopy - f.getVerticalPos());
        break;
      case s.db.LINETYPE.OPT_START:
        rt(
          R,
          y,
          a.boxMargin,
          a.boxMargin + a.boxTextMargin,
          (N) => f.newLoop(N)
        );
        break;
      case s.db.LINETYPE.OPT_END:
        L = f.endLoop(), await D.drawLoop(n, L, "opt", a), f.bumpVerticalPos(L.stopy - f.getVerticalPos()), f.models.addLoop(L);
        break;
      case s.db.LINETYPE.ALT_START:
        rt(
          R,
          y,
          a.boxMargin,
          a.boxMargin + a.boxTextMargin,
          (N) => f.newLoop(N)
        );
        break;
      case s.db.LINETYPE.ALT_ELSE:
        rt(
          R,
          y,
          a.boxMargin + a.boxTextMargin,
          a.boxMargin,
          (N) => f.addSectionToLoop(N)
        );
        break;
      case s.db.LINETYPE.ALT_END:
        L = f.endLoop(), await D.drawLoop(n, L, "alt", a), f.bumpVerticalPos(L.stopy - f.getVerticalPos()), f.models.addLoop(L);
        break;
      case s.db.LINETYPE.PAR_START:
      case s.db.LINETYPE.PAR_OVER_START:
        rt(
          R,
          y,
          a.boxMargin,
          a.boxMargin + a.boxTextMargin,
          (N) => f.newLoop(N)
        ), f.saveVerticalPos();
        break;
      case s.db.LINETYPE.PAR_AND:
        rt(
          R,
          y,
          a.boxMargin + a.boxTextMargin,
          a.boxMargin,
          (N) => f.addSectionToLoop(N)
        );
        break;
      case s.db.LINETYPE.PAR_END:
        L = f.endLoop(), await D.drawLoop(n, L, "par", a), f.bumpVerticalPos(L.stopy - f.getVerticalPos()), f.models.addLoop(L);
        break;
      case s.db.LINETYPE.AUTONUMBER:
        z = y.message.start || z, J = y.message.step || J, y.message.visible ? s.db.enableSequenceNumbers() : s.db.disableSequenceNumbers();
        break;
      case s.db.LINETYPE.CRITICAL_START:
        rt(
          R,
          y,
          a.boxMargin,
          a.boxMargin + a.boxTextMargin,
          (N) => f.newLoop(N)
        );
        break;
      case s.db.LINETYPE.CRITICAL_OPTION:
        rt(
          R,
          y,
          a.boxMargin + a.boxTextMargin,
          a.boxMargin,
          (N) => f.addSectionToLoop(N)
        );
        break;
      case s.db.LINETYPE.CRITICAL_END:
        L = f.endLoop(), await D.drawLoop(n, L, "critical", a), f.bumpVerticalPos(L.stopy - f.getVerticalPos()), f.models.addLoop(L);
        break;
      case s.db.LINETYPE.BREAK_START:
        rt(
          R,
          y,
          a.boxMargin,
          a.boxMargin + a.boxTextMargin,
          (N) => f.newLoop(N)
        );
        break;
      case s.db.LINETYPE.BREAK_END:
        L = f.endLoop(), await D.drawLoop(n, L, "break", a), f.bumpVerticalPos(L.stopy - f.getVerticalPos()), f.models.addLoop(L);
        break;
      default:
        try {
          nt = y.msgModel, nt.starty = f.getVerticalPos(), nt.sequenceIndex = z, nt.sequenceVisible = s.db.showSequenceNumbers();
          const N = await I0(n, nt);
          v0(
            y,
            nt,
            N,
            U,
            x,
            T,
            p
          ), $.push({ messageModel: nt, lineStartY: N }), f.models.addMessage(nt);
        } catch (N) {
          X.error("error while drawing message", N);
        }
    }
    [
      s.db.LINETYPE.SOLID_OPEN,
      s.db.LINETYPE.DOTTED_OPEN,
      s.db.LINETYPE.SOLID,
      s.db.LINETYPE.DOTTED,
      s.db.LINETYPE.SOLID_CROSS,
      s.db.LINETYPE.DOTTED_CROSS,
      s.db.LINETYPE.SOLID_POINT,
      s.db.LINETYPE.DOTTED_POINT
    ].includes(y.type) && (z = z + J), U++;
  }
  X.debug("createdActors", T), X.debug("destroyedActors", p), await qt(n, x, m, !1);
  for (const y of $)
    await A0(n, y.messageModel, y.lineStartY, s);
  a.mirrorActors && await qt(n, x, m, !0), H.forEach((y) => D.drawBackgroundRect(n, y)), ue(n, x, m, a);
  for (const y of f.models.boxes)
    y.height = f.getVerticalPos() - y.y, f.insert(y.x, y.y, y.x + y.width, y.height), y.startx = y.x, y.starty = y.y, y.stopx = y.startx + y.width, y.stopy = y.starty + y.height, y.stroke = "rgb(0,0,0, 0.5)", await D.drawBox(n, y, a);
  V && f.bumpVerticalPos(a.boxMargin);
  const F = ge(n, x, m, u), { bounds: W } = f.getBounds();
  let Z = W.stopy - W.starty;
  Z < F.maxHeight && (Z = F.maxHeight);
  let K = Z + 2 * a.diagramMarginY;
  a.mirrorActors && (K = K - a.boxMargin + a.bottomMarginAdj);
  let Q = W.stopx - W.startx;
  Q < F.maxWidth && (Q = F.maxWidth);
  const tt = Q + 2 * a.diagramMarginX;
  A && n.append("text").text(A).attr("x", (W.stopx - W.startx) / 2 - 2 * a.diagramMarginX).attr("y", -25), ve(n, K, tt, a.useMaxWidth);
  const v = A ? 40 : 0;
  n.attr(
    "viewBox",
    W.startx - a.diagramMarginX + " -" + (a.diagramMarginY + v) + " " + tt + " " + (K + v)
  ), X.debug("models:", f.models);
};
async function M0(t, e, c) {
  const s = {};
  for (const r of e)
    if (t[r.to] && t[r.from]) {
      const i = t[r.to];
      if (r.placement === c.db.PLACEMENT.LEFTOF && !i.prevActor || r.placement === c.db.PLACEMENT.RIGHTOF && !i.nextActor)
        continue;
      const o = r.placement !== void 0, l = !o, u = o ? Tt(a) : xt(a), n = r.wrap ? B.wrapLabel(r.message, a.width - 2 * a.wrapPadding, u) : r.message, T = (at(n) ? await wt(r.message, st()) : B.calculateTextDimensions(n, u)).width + 2 * a.wrapPadding;
      l && r.from === i.nextActor ? s[r.to] = _.getMax(
        s[r.to] || 0,
        T
      ) : l && r.from === i.prevActor ? s[r.from] = _.getMax(
        s[r.from] || 0,
        T
      ) : l && r.from === r.to ? (s[r.from] = _.getMax(
        s[r.from] || 0,
        T / 2
      ), s[r.to] = _.getMax(
        s[r.to] || 0,
        T / 2
      )) : r.placement === c.db.PLACEMENT.RIGHTOF ? s[r.from] = _.getMax(
        s[r.from] || 0,
        T
      ) : r.placement === c.db.PLACEMENT.LEFTOF ? s[i.prevActor] = _.getMax(
        s[i.prevActor] || 0,
        T
      ) : r.placement === c.db.PLACEMENT.OVER && (i.prevActor && (s[i.prevActor] = _.getMax(
        s[i.prevActor] || 0,
        T / 2
      )), i.nextActor && (s[r.from] = _.getMax(
        s[r.from] || 0,
        T / 2
      )));
    }
  return X.debug("maxMessageWidthPerActor:", s), s;
}
const R0 = function(t) {
  let e = 0;
  const c = Wt(a);
  for (const s in t.links) {
    const i = B.calculateTextDimensions(s, c).width + 2 * a.wrapPadding + 2 * a.boxMargin;
    e < i && (e = i);
  }
  return e;
};
async function C0(t, e, c) {
  let s = 0;
  for (const i of Object.keys(t)) {
    const o = t[i];
    o.wrap && (o.description = B.wrapLabel(
      o.description,
      a.width - 2 * a.wrapPadding,
      Wt(a)
    ));
    const l = at(o.description) ? await wt(o.description, st()) : B.calculateTextDimensions(o.description, Wt(a));
    o.width = o.wrap ? a.width : _.getMax(a.width, l.width + 2 * a.wrapPadding), o.height = o.wrap ? _.getMax(l.height, a.height) : a.height, s = _.getMax(s, o.height);
  }
  for (const i in e) {
    const o = t[i];
    if (!o)
      continue;
    const l = t[o.nextActor];
    if (!l) {
      const T = e[i] + a.actorMargin - o.width / 2;
      o.margin = _.getMax(T, a.actorMargin);
      continue;
    }
    const n = e[i] + a.actorMargin - o.width / 2 - l.width / 2;
    o.margin = _.getMax(n, a.actorMargin);
  }
  let r = 0;
  return c.forEach((i) => {
    const o = xt(a);
    let l = i.actorKeys.reduce((x, T) => x += t[T].width + (t[T].margin || 0), 0);
    l -= 2 * a.boxTextMargin, i.wrap && (i.name = B.wrapLabel(i.name, l - 2 * a.wrapPadding, o));
    const u = B.calculateTextDimensions(i.name, o);
    r = _.getMax(u.height, r);
    const n = _.getMax(l, u.width + 2 * a.wrapPadding);
    if (i.margin = a.boxTextMargin, l < n) {
      const x = (n - l) / 2;
      i.margin += x;
    }
  }), c.forEach((i) => i.textMaxHeight = r), _.getMax(s, a.height);
}
const D0 = async function(t, e, c) {
  const s = e[t.from].x, r = e[t.to].x, i = t.wrap && t.message;
  let o = at(t.message) ? await wt(t.message, st()) : B.calculateTextDimensions(
    i ? B.wrapLabel(t.message, a.width, Tt(a)) : t.message,
    Tt(a)
  );
  const l = {
    width: i ? a.width : _.getMax(a.width, o.width + 2 * a.noteMargin),
    height: 0,
    startx: e[t.from].x,
    stopx: 0,
    starty: 0,
    stopy: 0,
    message: t.message
  };
  return t.placement === c.db.PLACEMENT.RIGHTOF ? (l.width = i ? _.getMax(a.width, o.width) : _.getMax(
    e[t.from].width / 2 + e[t.to].width / 2,
    o.width + 2 * a.noteMargin
  ), l.startx = s + (e[t.from].width + a.actorMargin) / 2) : t.placement === c.db.PLACEMENT.LEFTOF ? (l.width = i ? _.getMax(a.width, o.width + 2 * a.noteMargin) : _.getMax(
    e[t.from].width / 2 + e[t.to].width / 2,
    o.width + 2 * a.noteMargin
  ), l.startx = s - l.width + (e[t.from].width - a.actorMargin) / 2) : t.to === t.from ? (o = B.calculateTextDimensions(
    i ? B.wrapLabel(
      t.message,
      _.getMax(a.width, e[t.from].width),
      Tt(a)
    ) : t.message,
    Tt(a)
  ), l.width = i ? _.getMax(a.width, e[t.from].width) : _.getMax(
    e[t.from].width,
    a.width,
    o.width + 2 * a.noteMargin
  ), l.startx = s + (e[t.from].width - l.width) / 2) : (l.width = Math.abs(s + e[t.from].width / 2 - (r + e[t.to].width / 2)) + a.actorMargin, l.startx = s < r ? s + e[t.from].width / 2 - a.actorMargin / 2 : r + e[t.to].width / 2 - a.actorMargin / 2), i && (l.message = B.wrapLabel(
    t.message,
    l.width - 2 * a.wrapPadding,
    Tt(a)
  )), X.debug(
    `NM:[${l.startx},${l.stopx},${l.starty},${l.stopy}:${l.width},${l.height}=${t.message}]`
  ), l;
}, V0 = function(t, e, c) {
  if (![
    c.db.LINETYPE.SOLID_OPEN,
    c.db.LINETYPE.DOTTED_OPEN,
    c.db.LINETYPE.SOLID,
    c.db.LINETYPE.DOTTED,
    c.db.LINETYPE.SOLID_CROSS,
    c.db.LINETYPE.DOTTED_CROSS,
    c.db.LINETYPE.SOLID_POINT,
    c.db.LINETYPE.DOTTED_POINT
  ].includes(t.type))
    return {};
  const [s, r] = jt(t.from, e), [i, o] = jt(t.to, e), l = s <= i, u = l ? r : s;
  let n = l ? i : o;
  const x = Math.abs(i - o) > 2, T = (k) => l ? -k : k;
  t.from === t.to ? n = u : (t.activate && !x && (n += T(a.activationWidth / 2 - 1)), [c.db.LINETYPE.SOLID_OPEN, c.db.LINETYPE.DOTTED_OPEN].includes(t.type) || (n += T(3)));
  const p = [s, r, i, o], g = Math.abs(u - n);
  t.wrap && t.message && (t.message = B.wrapLabel(
    t.message,
    _.getMax(g + 2 * a.wrapPadding, a.width),
    xt(a)
  ));
  const m = B.calculateTextDimensions(t.message, xt(a));
  return {
    width: _.getMax(
      t.wrap ? 0 : m.width + 2 * a.wrapPadding,
      g + 2 * a.wrapPadding,
      a.width
    ),
    height: 0,
    startx: u,
    stopx: n,
    starty: 0,
    stopy: 0,
    message: t.message,
    type: t.type,
    wrap: t.wrap,
    fromBounds: Math.min.apply(null, p),
    toBounds: Math.max.apply(null, p)
  };
}, O0 = async function(t, e, c, s) {
  const r = {}, i = [];
  let o, l, u;
  for (const n of t) {
    switch (n.id = B.random({ length: 10 }), n.type) {
      case s.db.LINETYPE.LOOP_START:
      case s.db.LINETYPE.ALT_START:
      case s.db.LINETYPE.OPT_START:
      case s.db.LINETYPE.PAR_START:
      case s.db.LINETYPE.PAR_OVER_START:
      case s.db.LINETYPE.CRITICAL_START:
      case s.db.LINETYPE.BREAK_START:
        i.push({
          id: n.id,
          msg: n.message,
          from: Number.MAX_SAFE_INTEGER,
          to: Number.MIN_SAFE_INTEGER,
          width: 0
        });
        break;
      case s.db.LINETYPE.ALT_ELSE:
      case s.db.LINETYPE.PAR_AND:
      case s.db.LINETYPE.CRITICAL_OPTION:
        n.message && (o = i.pop(), r[o.id] = o, r[n.id] = o, i.push(o));
        break;
      case s.db.LINETYPE.LOOP_END:
      case s.db.LINETYPE.ALT_END:
      case s.db.LINETYPE.OPT_END:
      case s.db.LINETYPE.PAR_END:
      case s.db.LINETYPE.CRITICAL_END:
      case s.db.LINETYPE.BREAK_END:
        o = i.pop(), r[o.id] = o;
        break;
      case s.db.LINETYPE.ACTIVE_START:
        {
          const T = e[n.from ? n.from.actor : n.to.actor], p = St(n.from ? n.from.actor : n.to.actor).length, g = T.x + T.width / 2 + (p - 1) * a.activationWidth / 2, m = {
            startx: g,
            stopx: g + a.activationWidth,
            actor: n.from.actor,
            enabled: !0
          };
          f.activations.push(m);
        }
        break;
      case s.db.LINETYPE.ACTIVE_END:
        {
          const T = f.activations.map((p) => p.actor).lastIndexOf(n.from.actor);
          delete f.activations.splice(T, 1)[0];
        }
        break;
    }
    n.placement !== void 0 ? (l = await D0(n, e, s), n.noteModel = l, i.forEach((T) => {
      o = T, o.from = _.getMin(o.from, l.startx), o.to = _.getMax(o.to, l.startx + l.width), o.width = _.getMax(o.width, Math.abs(o.from - o.to)) - a.labelBoxWidth;
    })) : (u = V0(n, e, s), n.msgModel = u, u.startx && u.stopx && i.length > 0 && i.forEach((T) => {
      if (o = T, u.startx === u.stopx) {
        const p = e[n.from], g = e[n.to];
        o.from = _.getMin(
          p.x - u.width / 2,
          p.x - p.width / 2,
          o.from
        ), o.to = _.getMax(
          g.x + u.width / 2,
          g.x + p.width / 2,
          o.to
        ), o.width = _.getMax(o.width, Math.abs(o.to - o.from)) - a.labelBoxWidth;
      } else
        o.from = _.getMin(u.startx, o.from), o.to = _.getMax(u.stopx, o.to), o.width = _.getMax(o.width, u.width) - a.labelBoxWidth;
    }));
  }
  return f.activations = [], X.debug("Loop type widths:", r), r;
}, B0 = {
  bounds: f,
  drawActors: qt,
  drawActorsPopup: ge,
  setConf: xe,
  draw: S0
}, W0 = {
  parser: De,
  db: Qt,
  renderer: B0,
  styles: o0,
  init: ({ wrap: t }) => {
    Qt.setWrap(t);
  }
};
export {
  W0 as diagram
};
