export interface Point {
    x: number;
    y: number;
}
export interface TextDimensionConfig {
    fontSize?: number;
    fontWeight?: number;
    fontFamily?: string;
}
export interface TextDimensions {
    width: number;
    height: number;
    lineHeight?: number;
}
export interface EdgeData {
    arrowheadStyle?: string;
    labelpos?: string;
    labelType?: string;
    label?: string;
    classes: string;
    pattern: string;
    id: string;
    arrowhead: string;
    startLabelRight: string;
    endLabelLeft: string;
    arrowTypeStart: string;
    arrowTypeEnd: string;
    style: string;
    labelStyle: string;
    curve: any;
}
