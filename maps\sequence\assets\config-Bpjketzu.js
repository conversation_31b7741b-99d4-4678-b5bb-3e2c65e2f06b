import{i as Y,u as Z,j as ee,r as u,x as te,A as oe,k as a,o as s,n as b,m as e,t as d,q as x,v as g,D as n,V as k,F as H,l as E,E as m,H as re,O as le,W as ae,B as A,z as se,X as ne,_ as ie,T as ue,U as de}from"./style-Bh4pv2ug.js";const ve={class:"block sm:inline"},ce={class:"flex-1 overflow-y-auto overflow-x-hidden p-2"},xe={class:"max-w-4xl mx-auto space-y-1.5"},pe={class:"flex items-center gap-1.5 mb-1.5"},ge={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},be={class:"flex flex-col gap-2"},me={class:"flex items-center gap-1.5 cursor-pointer"},fe={class:"flex items-center gap-1.5 cursor-pointer"},he={class:"flex flex-col gap-2"},we={class:"flex items-center gap-1.5 cursor-pointer"},ye={class:"flex items-center gap-1.5 cursor-pointer"},Ce={class:"flex items-center gap-1.5 mb-1.5"},ke={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},ze={class:"flex flex-col gap-2"},Ne={class:"flex gap-3"},Pe={class:"flex items-center gap-1.5 cursor-pointer"},Fe={class:"flex items-center gap-1.5 cursor-pointer"},Se={key:0,class:"flex flex-wrap gap-1"},Ve={class:"flex items-center gap-1.5 mb-1.5"},Me={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},Ie={class:"flex flex-col gap-1.5"},De=["onClick"],He={class:"flex flex-col gap-1.5"},Ee={key:1,class:"flex gap-1 mt-1"},Be={class:"flex items-center gap-1.5 mb-1.5"},Ue={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},Ae={class:"flex flex-col gap-1.5"},Le=["onClick"],_e={key:0,class:"flex flex-col gap-1 w-full"},Oe={class:"flex gap-1"},Te={class:"flex items-center gap-1.5 mb-1.5"},je={key:0,class:"text-xs mb-1.5 p-1.5 rounded text-[var(--text-color-secondary)] bg-[var(--bg-color)]"},$e={key:1,class:"space-y-3"},Re={class:"grid grid-cols-2 gap-3"},Ge={class:"flex flex-col gap-1"},Xe={class:"flex flex-col gap-1"},qe={class:"flex flex-col gap-1"},Je={class:"flex flex-col gap-1"},We={key:2,class:"space-y-3"},Ke={class:"grid grid-cols-2 gap-3"},Qe={class:"flex flex-col gap-1"},Ye={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},Ze={class:"flex flex-col gap-1"},et={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},tt={class:"flex flex-col gap-1"},ot={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},rt={class:"flex flex-col gap-1"},lt={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},at={key:0,class:"space-y-3"},st={class:"grid grid-cols-2 gap-3"},nt={class:"flex flex-col gap-1"},it={class:"flex flex-col gap-1"},ut={key:1,class:"space-y-3"},dt={class:"grid grid-cols-2 gap-3"},vt={class:"flex flex-col gap-1"},ct={class:"px-2 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},xt={class:"flex flex-col gap-1"},pt={class:"px-1 py-1 rounded text-xs bg-[var(--bg-color)] text-[var(--text-color)] border border-[var(--input-border-color)]"},gt={class:"flex flex-col gap-2"},bt={class:"flex items-center gap-1.5 cursor-pointer"},mt=Y({__name:"Config",setup(ht){const p=Z(),v=ee(),L=u(!0),o=u({startOnDebug:!1,recordMode:"all",enableAutoDetect:!0,autoDetectedPackages:[],includedPackagePrefixes:[],includedParentClasses:[],maxNumFirst:12,maxNumHash:3,maxNumFirstImportant:1024,maxNumHashImportant:256,maxColSize:32,maxStrSize:4096}),P=u({improvePlan:!0}),F=u(null),f=u({0:!1,1:!1,2:!1,3:!1,4:!1}),z=u(!1),y=u(""),S=u(!1),C=u(""),V=u(!1),i=u({maxNumFirst:12,maxNumHash:3,maxNumFirstImportant:1024,maxNumHashImportant:256}),M=u(!1),h=u({maxColSize:32,maxStrSize:4096}),N=l=>{f.value[l]=!f.value[l]},_=l=>{o.value.includedPackagePrefixes&&(o.value.includedPackagePrefixes.splice(l,1),c())},O=l=>{o.value.includedParentClasses&&(o.value.includedParentClasses.splice(l,1),c())},c=async()=>{try{const l=await le(o.value);if(!l.success){console.error("Failed to update filter configuration:",l.error),p.setError(l.error||"Failed to update filter configuration");return}await I()}catch(l){console.error("Failed to update filter configuration:",l),p.setError("Failed to update filter configuration")}},T=async()=>{try{const l=await ae(P.value);if(!l.success){console.error("Failed to update global configuration:",l.error),p.setError(l.error||"Failed to update global configuration");return}await D()}catch(l){console.error("Failed to update global configuration:",l),p.setError("Failed to update global configuration")}},j=()=>{z.value=!0,A(()=>{const l=document.querySelector(".input-new-package-prefix input");l&&l.focus()})},$=()=>{y.value&&(o.value.includedPackagePrefixes||(o.value.includedPackagePrefixes=[]),o.value.includedPackagePrefixes.push(y.value),c()),z.value=!1,y.value=""},R=()=>{S.value=!0,A(()=>{const l=document.querySelector(".input-new-parent-class input");l&&l.focus()})},G=()=>{C.value&&(o.value.includedParentClasses||(o.value.includedParentClasses=[]),o.value.includedParentClasses.push({type:C.value}),c()),S.value=!1,C.value=""},X=()=>{i.value={maxNumFirst:o.value.maxNumFirst||12,maxNumHash:o.value.maxNumHash||3,maxNumFirstImportant:o.value.maxNumFirstImportant||1024,maxNumHashImportant:o.value.maxNumHashImportant||256},V.value=!0},q=async()=>{o.value.maxNumFirst=i.value.maxNumFirst,o.value.maxNumHash=i.value.maxNumHash,o.value.maxNumFirstImportant=i.value.maxNumFirstImportant,o.value.maxNumHashImportant=i.value.maxNumHashImportant,await c(),V.value=!1},J=()=>{V.value=!1},W=()=>{h.value={maxColSize:o.value.maxColSize||32,maxStrSize:o.value.maxStrSize||4096},M.value=!0},K=async()=>{o.value.maxColSize=h.value.maxColSize,o.value.maxStrSize=h.value.maxStrSize,await c(),M.value=!1},Q=()=>{M.value=!1},I=async()=>{try{const l=await se();l.success&&l.data?o.value=l.data:p.setError(l.error||"Failed to get configuration")}catch(l){console.error("Failed to get configuration:",l),p.setError("Failed to get configuration")}},D=async()=>{try{const l=await ne();l.success&&l.data?P.value=l.data:p.setError(l.error||"Failed to get global configuration")}catch(l){console.error("Failed to get global configuration:",l),p.setError("Failed to get global configuration")}};return te(async()=>{v.initTheme(),await Promise.all([I(),D()]),L.value=!1,F.value=window.setInterval(()=>{I(),D()},3e4)}),oe(()=>{F.value&&(clearInterval(F.value),F.value=null)}),(l,t)=>{var U;return s(),a("div",{class:x(["h-screen min-w-full w-screen flex flex-col overflow-x-hidden",(d(v).theme==="dark","bg-[var(--bg-color)]")])},[(U=d(p))!=null&&U.error?(s(),a("div",{key:0,class:x(["fixed top-2 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300",d(v).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[e("span",ve,g(d(p).error.message),1)],2)):b("",!0),e("div",{class:x(["border-b py-2 px-4 shadow-sm",(d(v).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]")])},t[22]||(t[22]=[e("div",{class:"flex items-center"},[e("span",{class:"text-lg font-semibold text-[var(--text-color)]"},"录制配置")],-1)]),2),e("div",ce,[e("div",xe,[e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",pe,[t[24]||(t[24]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制模式",-1)),e("button",{onClick:t[0]||(t[0]=r=>N("0")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[23]||(t[23]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[0]?(s(),a("p",ge," 全录模式，从程序启动就开始录制，会录制应用的完整启动过程。手工模式，默认不录制，需手工开启录制，可以跳过应用启动过程。 ")):b("",!0),e("div",be,[e("label",me,[n(e("input",{type:"radio","onUpdate:modelValue":t[1]||(t[1]=r=>o.value.recordMode=r),value:"manual",onChange:c,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.recordMode]]),t[25]||(t[25]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"【推荐】手工模式，跳过应用启动过程，然后按需开启录制",-1))]),e("label",fe,[n(e("input",{type:"radio","onUpdate:modelValue":t[2]||(t[2]=r=>o.value.recordMode=r),value:"all",onChange:c,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.recordMode]]),t[26]||(t[26]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"全录模式，应用启动即开始录制，速度慢，但数据完整",-1))])])],2),e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[t[29]||(t[29]=e("h3",{class:"text-sm font-semibold mb-1.5 text-[var(--text-color)]"},"默认随着 Debug 启动",-1)),e("div",he,[e("label",we,[n(e("input",{type:"radio","onUpdate:modelValue":t[3]||(t[3]=r=>o.value.startOnDebug=r),value:!0,onChange:c,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.startOnDebug]]),t[27]||(t[27]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"是（支持使用 Jrebel/HotSwap 启动）",-1))]),e("label",ye,[n(e("input",{type:"radio","onUpdate:modelValue":t[4]||(t[4]=r=>o.value.startOnDebug=r),value:!1,onChange:c,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.startOnDebug]]),t[28]||(t[28]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"否（需要使用 Debug with XCodeMap 启动）",-1))])])],2),e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Ce,[t[31]||(t[31]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制项目包",-1)),e("button",{onClick:t[5]||(t[5]=r=>N("1")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[30]||(t[30]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[1]?(s(),a("p",ke," XCodeMap 默认检测并录制项目包。如果要录制依赖包，请在后面手工添加。如果发现项目包遗漏，也可在后面添加。 ")):b("",!0),e("div",ze,[e("div",Ne,[e("label",Pe,[n(e("input",{type:"radio","onUpdate:modelValue":t[6]||(t[6]=r=>o.value.enableAutoDetect=r),value:!0,onChange:c,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.enableAutoDetect]]),t[32]||(t[32]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"是",-1))]),e("label",Fe,[n(e("input",{type:"radio","onUpdate:modelValue":t[7]||(t[7]=r=>o.value.enableAutoDetect=r),value:!1,onChange:c,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)]"},null,544),[[k,o.value.enableAutoDetect]]),t[33]||(t[33]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"否",-1))])]),o.value.enableAutoDetect?(s(),a("div",Se,[(s(!0),a(H,null,E(o.value.autoDetectedPackages,(r,w)=>(s(),a("div",{key:w,class:"px-1.5 py-0.5 rounded-full text-xs font-medium bg-[var(--bg-color)] text-[var(--text-color)]"},g(r),1))),128))])):b("",!0)])],2),e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Ve,[t[35]||(t[35]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制依赖包",-1)),e("button",{onClick:t[8]||(t[8]=r=>N("2")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[34]||(t[34]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[2]?(s(),a("p",Me," 配置包的前缀即可。例如配置 org.spring，将录制 spring 模块内的函数调用数据。如果函数被多次调用，则只录制最先出现的N次调用，然后按照不同的调用堆栈位置分别录制M次。具体次数由函数调用过滤配置中的普通包相关配置决定。 ")):b("",!0),e("div",Ie,[(s(!0),a(H,null,E(o.value.includedPackagePrefixes||[],(r,w)=>(s(),a("div",{key:w,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,g(r),1),e("button",{onClick:()=>{_(w)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},t[36]||(t[36]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,De)]))),128)),e("div",He,[z.value?n((s(),a("input",{key:0,"onUpdate:modelValue":t[9]||(t[9]=r=>y.value=r),class:"input-new-package-prefix px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写包前缀，例如 org.spring"},null,512)),[[m,y.value]]):b("",!0),z.value?(s(),a("div",Ee,[e("button",{onClick:$,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:t[10]||(t[10]=()=>{z.value=!1,y.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])):(s(),a("button",{key:2,onClick:j,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[37]||(t[37]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加包前缀",-1)])))])])],2),e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Be,[t[39]||(t[39]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"录制重点类",-1)),e("button",{onClick:t[11]||(t[11]=r=>N("3")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[38]||(t[38]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[3]?(s(),a("p",Ue," 配置类的完整名称。例如配置 org.springframework.beans.factory.BeanFactory，将重点录制该类的调用，其录制次数由函数调用过滤配置中的重点类相关配置决定。 ")):b("",!0),e("div",Ae,[(s(!0),a(H,null,E(o.value.includedParentClasses,(r,w)=>(s(),a("div",{key:w,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs font-medium w-fit bg-[var(--bg-color)] text-[var(--text-color)]"},[e("span",null,g(r.type),1),e("button",{onClick:()=>{O(w)},class:"inline-flex items-center p-0.5 rounded-full hover:bg-[var(--header-hover-bg-color)] transition-colors"},t[40]||(t[40]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5 text-[var(--text-color-secondary)]",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,Le)]))),128)),S.value?(s(),a("div",_e,[n(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>C.value=r),class:"input-new-parent-class px-1.5 py-0.5 border rounded text-xs focus:outline-none focus:ring-2 w-full bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]",placeholder:"填写类完整名称，如 org.springframework.beans.factory.BeanFactory"},null,512),[[m,C.value]]),e("div",Oe,[e("button",{onClick:G,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:t[13]||(t[13]=()=>{S.value=!1,C.value=""}),class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("button",{key:1,onClick:R,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[41]||(t[41]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),e("span",null,"添加类",-1)])))])],2),e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[e("div",Te,[t[43]||(t[43]=e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"函数录制次数",-1)),e("button",{onClick:t[14]||(t[14]=r=>N("4")),class:"text-[var(--text-color-secondary)] hover:text-[var(--text-color)] transition-colors duration-200"},t[42]||(t[42]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})],-1)]))]),f.value[4]?(s(),a("p",je," 在每个请求中，每个函数会录制其最先出现的N次调用，然后按照不同的调用堆栈位置分别录制M次。这样可以既记录函数的早期行为，又能捕获不同上下文下的调用模式。 ")):b("",!0),V.value?(s(),a("div",$e,[e("div",Re,[e("div",Ge,[t[44]||(t[44]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数录制最先出现的N次",-1)),n(e("input",{"onUpdate:modelValue":t[15]||(t[15]=r=>i.value.maxNumFirst=r),type:"number",min:"1",max:"1000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[m,i.value.maxNumFirst,void 0,{number:!0}]])]),e("div",Xe,[t[45]||(t[45]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数每个堆栈位置录制M次",-1)),n(e("input",{"onUpdate:modelValue":t[16]||(t[16]=r=>i.value.maxNumHash=r),type:"number",min:"1",max:"100",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[m,i.value.maxNumHash,void 0,{number:!0}]])]),e("div",qe,[t[46]||(t[46]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数录制最先出现的N次",-1)),n(e("input",{"onUpdate:modelValue":t[17]||(t[17]=r=>i.value.maxNumFirstImportant=r),type:"number",min:"1",max:"10000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[m,i.value.maxNumFirstImportant,void 0,{number:!0}]])]),e("div",Je,[t[47]||(t[47]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数每个堆栈位置录制M次",-1)),n(e("input",{"onUpdate:modelValue":t[18]||(t[18]=r=>i.value.maxNumHashImportant=r),type:"number",min:"1",max:"1000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[m,i.value.maxNumHashImportant,void 0,{number:!0}]])])]),e("div",{class:"flex gap-1"},[e("button",{onClick:q,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:J,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("div",We,[e("div",Ke,[e("div",Qe,[t[48]||(t[48]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数录制最先出现的N次",-1)),e("div",Ye,g(o.value.maxNumFirst||12),1)]),e("div",Ze,[t[49]||(t[49]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"普通包内的函数每个堆栈位置录制M次",-1)),e("div",et,g(o.value.maxNumHash||3),1)]),e("div",tt,[t[50]||(t[50]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数录制最先出现的N次",-1)),e("div",ot,g(o.value.maxNumFirstImportant||1024),1)]),e("div",rt,[t[51]||(t[51]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"重点类函数每个堆栈位置录制M次",-1)),e("div",lt,g(o.value.maxNumHashImportant||256),1)])]),e("button",{onClick:X,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[52]||(t[52]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1),e("span",null,"编辑配置",-1)]))]))],2),e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[t[58]||(t[58]=e("div",{class:"flex items-center gap-1.5 mb-1.5"},[e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"对象录制深度")],-1)),M.value?(s(),a("div",at,[e("div",st,[e("div",nt,[t[53]||(t[53]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"集合最大元素数",-1)),n(e("input",{"onUpdate:modelValue":t[19]||(t[19]=r=>h.value.maxColSize=r),type:"number",min:"1",max:"1000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[m,h.value.maxColSize,void 0,{number:!0}]])]),e("div",it,[t[54]||(t[54]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"字符串最大长度",-1)),n(e("input",{"onUpdate:modelValue":t[20]||(t[20]=r=>h.value.maxStrSize=r),type:"number",min:"1",max:"10000",class:"px-2 py-1 border rounded text-xs focus:outline-none focus:ring-2 bg-[var(--input-bg-color)] border-[var(--input-border-color)] text-[var(--text-color)]"},null,512),[[m,h.value.maxStrSize,void 0,{number:!0}]])])]),e("div",{class:"flex gap-1"},[e("button",{onClick:K,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--button-hover-color)] text-white hover:bg-[var(--header-hover-bg-color)]"}," 保存 "),e("button",{onClick:Q,class:"px-2 py-0.5 rounded text-xs transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"}," 取消 ")])])):(s(),a("div",ut,[e("div",dt,[e("div",vt,[t[55]||(t[55]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"集合最大元素数",-1)),e("div",ct,g(o.value.maxColSize||32),1)]),e("div",xt,[t[56]||(t[56]=e("label",{class:"text-xs font-medium text-[var(--text-color)]"},"字符串最大长度",-1)),e("div",pt,g(o.value.maxStrSize||4096),1)])]),e("button",{onClick:W,class:"inline-flex items-center gap-0.5 px-1.5 py-0.5 rounded-full text-xs w-fit transition-colors bg-[var(--bg-color)] text-[var(--text-color)] hover:bg-[var(--header-hover-bg-color)]"},t[57]||(t[57]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1),e("span",null,"编辑配置",-1)]))]))],2),e("div",{class:x(["rounded p-3 shadow-sm",(d(v).theme==="dark","bg-[var(--undercaret-bg-color)]")])},[t[61]||(t[61]=e("div",{class:"flex items-center gap-1.5 mb-1.5"},[e("h3",{class:"text-sm font-semibold text-[var(--text-color)]"},"全局配置")],-1)),e("div",gt,[e("label",bt,[n(e("input",{type:"checkbox","onUpdate:modelValue":t[21]||(t[21]=r=>P.value.improvePlan=r),onChange:T,class:"w-3.5 h-3.5 text-[var(--button-hover-color)] focus:ring-[var(--button-hover-color)] border-[var(--input-border-color)] bg-[var(--input-bg-color)] rounded"},null,544),[[re,P.value.improvePlan]]),t[59]||(t[59]=e("span",{class:"text-xs font-medium text-[var(--text-color)]"},"参与改进计划（发送点击Metrics）",-1))]),t[60]||(t[60]=e("p",{class:"text-xs text-[var(--text-color-secondary)]"}," 帮助改进 XCodeMap 产品，我们会收集匿名的使用数据来优化功能。 ",-1))])],2)])])],2)}}}),ft=ie(mt,[["__scopeId","data-v-96fe74fa"]]),B=ue(ft);B.use(de());B.mount("#app");window.$vm=B._instance;
