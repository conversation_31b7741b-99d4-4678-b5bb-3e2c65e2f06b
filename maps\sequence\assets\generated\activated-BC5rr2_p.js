import{i as ee,u as te,r as l,x as se,z as re,W as ae,X as oe,j as r,o as a,l as e,m as i,t as d,F as le,k as de,n as Q,B as X,Y as ne,Z as ie,$ as ce,_ as ue,R as xe,S as ve}from"./style-BxxvIZDh.js";import{u as ge}from"./activation-BAR1_29T.js";import{Q as be}from"./browser-BtZzEF0O.js";const ye={class:"h-screen w-full flex flex-col overflow-x-hidden bg-gray-50 dark:bg-gray-900"},me={class:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4"},pe={class:"max-w-4xl mx-auto flex justify-between items-center"},fe={class:"flex items-center space-x-4"},ke={key:0,class:"flex items-center space-x-2"},he={key:1,class:"flex items-center space-x-4"},we={class:"text-sm"},_e={class:"ml-1 font-medium text-gray-900 dark:text-white"},Ce={class:"text-sm"},Le={class:"ml-1 font-medium text-gray-900 dark:text-white"},je={key:2,class:"text-sm text-gray-500 dark:text-gray-400"},Fe={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},Me={class:"max-w-4xl mx-auto"},Pe={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},Se={class:"ml-11"},Ie={class:"bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900 dark:to-red-900 border border-orange-200 dark:border-orange-700 rounded-lg p-4 mb-4"},Te={class:"flex items-start space-x-3"},Ae={class:"flex-1"},Oe={class:"flex items-center space-x-2"},ze=["disabled"],Be={key:0,class:"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg"},Ee={key:1,class:"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg"},Re={class:"flex items-center space-x-2"},Ue={class:"text-sm text-red-700 dark:text-red-300"},De={key:2,class:"text-center py-8"},$e={key:3,class:"space-y-4"},Qe=["onClick"],Xe={class:"flex items-center space-x-3"},Ne={class:"flex-shrink-0"},Ge={key:0,class:"w-2 h-2 rounded-full bg-white"},Ve={class:"flex-1 flex justify-between items-center"},qe={class:"font-medium text-gray-900 dark:text-white"},We={class:"text-sm text-gray-500 dark:text-gray-400"},Ye={class:"text-right"},Ze={class:"text-lg font-semibold text-gray-900 dark:text-white"},He={class:"text-xs text-gray-500 dark:text-gray-400"},Je={key:4,class:"text-center py-8"},Ke={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},et={class:"ml-11"},tt={class:"text-center mb-4"},st=["disabled"],rt={key:0,class:"text-center"},at={class:"text-sm text-gray-500 dark:text-gray-400"},ot={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},lt={class:"ml-11"},dt={key:0,class:"mb-4 p-4 border border-blue-500 bg-blue-50 dark:bg-blue-900 rounded-lg"},nt={class:"flex items-center space-x-3"},it={class:"flex-1 flex justify-between items-center"},ct={class:"font-medium text-gray-900 dark:text-white"},ut={class:"text-sm text-gray-500 dark:text-gray-400"},xt={class:"text-right"},vt={class:"text-lg font-semibold text-gray-900 dark:text-white"},gt={class:"text-xs text-gray-500 dark:text-gray-400"},bt={key:1,class:"text-center py-8"},yt={key:2,class:"space-y-4"},mt={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600"},pt={key:0,class:"text-center"},ft={key:0,class:"text-sm text-green-600 dark:text-green-400 font-medium"},kt={key:1,class:"text-sm text-red-600 dark:text-red-400 font-medium"},ht={key:1,class:"text-center"},wt={key:3,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},_t={key:4,class:"text-center py-8"},Ct={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Lt={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},jt={class:"flex justify-between items-center mb-4"},Ft={class:"space-y-4"},Mt={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},Pt=["disabled"],St={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},It=["disabled"],Tt={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},At={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},Ot={class:"flex justify-between items-center mb-4"},zt={class:"text-center space-y-4"},Bt={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Et={class:"font-medium text-gray-900 dark:text-white mb-2"},Rt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ut={class:"text-sm text-gray-500 dark:text-gray-400"},Dt={class:"flex justify-center"},$t={key:0,class:"flex items-center space-x-2"},Qt={key:1,class:"bg-white p-4 rounded-lg"},Xt=["src"],Nt={class:"text-sm text-gray-600 dark:text-gray-300 space-y-2"},Gt={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg"},N=300,Vt=ee({__name:"Activated",setup(Wt){const V=te(),q=ge(),A=l(""),x=l(""),v=l(!1),g=l(""),k=l(null),M=l(!1),P=l([]),n=l(null),h=l(!1),W=l(!1),b=l(null),S=l(""),w=l(!1),I=l(!1),_=l(!1),C=l(!1),L=l(!1),p=l(null),y=l(0),j=l(!1),f=l(""),u=l(!1),O=l(null);se(()=>{V.initTheme();const s=new URLSearchParams(window.location.search),t=s.get("deviceCode"),o=s.get("utype");t?A.value=t:g.value="未找到设备码参数",o!==null&&(O.value=parseInt(o)),F()}),re(()=>{m()});const Y=async()=>{if(!n.value){g.value="请先选择一个许可证";return}try{v.value=!0,g.value="";const s=await q.generateActivateCode(A.value,n.value.orderId);s&&(x.value=s,u.value=!0)}catch(s){g.value=s instanceof Error?s.message:"生成激活码失败",console.error("Failed to generate activate code:",s)}finally{v.value=!1}},F=async()=>{try{M.value=!0;const[s,t]=await Promise.all([ae(),oe()]);s.success&&s.data?k.value=s.data:console.error("Failed to load user info:",s.error),t.success?P.value=t.data||[]:console.error("Failed to load licenses:",t.error)}catch(s){console.error("Failed to load user info:",s)}finally{M.value=!1}},Z=s=>{n.value=s},z=async s=>{s===1?_.value=!0:s===2&&(C.value=!0);try{const o=await ne({licenseType:s,payMethod:"wechat",payPrice:s===0?0:s===1?6900:16900});o.success?(m(),b.value=o.data,h.value=!1,w.value=!0,await H(o.data.wxCodeUrl),J(o.data.orderId)):console.error("Failed to create order:",o.error)}catch(t){console.error("Failed to create order:",t)}finally{s===1?_.value=!1:s===2&&(C.value=!1)}},H=async s=>{try{I.value=!0;const t=await be.toDataURL(s,{width:256,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});S.value=t}catch(t){console.error("Failed to generate QR code:",t)}finally{I.value=!1}},J=s=>{if(L.value)return;L.value=!0,y.value=0;const t=async()=>{try{y.value++,console.log(`Polling order status, attempt ${y.value}`),(await ce({orderId:s})).success?(console.log("Order payment successful!"),m(),w.value=!1,await F(),alert("支付成功! 您现在可以使用许可证生成激活码了！")):y.value>=N?(console.log("Max polling count reached, stopping polling"),m(),alert("支付超时，请重新创建订单。")):p.value=setTimeout(t,1e3)}catch(o){console.error("Error polling order status:",o),y.value>=N?(m(),alert("检查订单状态失败，请手动刷新页面。")):p.value=setTimeout(t,1e3)}};t()},m=()=>{p.value&&(clearTimeout(p.value),p.value=null),L.value=!1,y.value=0},c=l(""),B=async()=>{try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(x.value),c.value="复制成功!",console.log("Activate code copied to clipboard");else{const s=document.createElement("textarea");s.value=x.value,s.style.position="fixed",s.style.left="-999999px",s.style.top="-999999px",document.body.appendChild(s),s.focus(),s.select();const t=document.execCommand("copy");document.body.removeChild(s),t?(c.value="复制成功!",console.log("Activate code copied to clipboard")):(c.value="复制失败，请手动复制",console.error("Failed to copy activate code"))}setTimeout(()=>{c.value=""},3e3)}catch(s){c.value="复制失败，请手动复制",console.error("Failed to copy activate code:",s)}},K=async()=>{try{j.value=!0,f.value="";const s=await ie();s.success?(await F(),alert("试用许可证申请成功！您现在可以使用许可证生成激活码了！")):f.value=s.error||"申请试用许可证失败"}catch(s){f.value=s instanceof Error?s.message:"申请试用许可证失败",console.error("Failed to apply trial license:",s)}finally{j.value=!1}},E=s=>s===0?"试用许可证":s===1?"一年有效期":s===2?"永久有效期":"未知类型",T=s=>s===0?"免费":(s/100).toFixed(0)+"元",R=s=>new Date(s).toLocaleDateString("zh-CN");return(s,t)=>(a(),r("div",ye,[e("div",me,[e("div",pe,[e("div",fe,[t[8]||(t[8]=e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})]),e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"用户信息")],-1)),M.value?(a(),r("div",ke,t[5]||(t[5]=[e("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1),e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"加载中...",-1)]))):k.value?(a(),r("div",he,[e("div",we,[t[6]||(t[6]=e("span",{class:"text-gray-500 dark:text-gray-400"},"ID:",-1)),e("span",_e,d(k.value.uniqueId),1)]),e("div",Ce,[t[7]||(t[7]=e("span",{class:"text-gray-500 dark:text-gray-400"},"昵称:",-1)),e("span",Le,d(k.value.username),1)])])):(a(),r("div",je," 未获取到用户信息 "))]),e("div",{class:"flex items-center space-x-2"},[e("button",{onClick:F,class:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200"}," 刷新 ")])])]),e("div",Fe,[e("div",Me,[t[25]||(t[25]=e("div",{class:"text-center mb-8"},[e("h1",{class:"text-3xl font-bold mb-4 text-gray-900 dark:text-white"}," XCodeMap 激活码生成 ")],-1)),e("div",Pe,[t[17]||(t[17]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 1 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第一步 选择许可证 ")],-1)),e("div",Se,[e("div",Ie,[e("div",Te,[t[11]||(t[11]=e("div",{class:"flex-shrink-0 mt-1"},[e("svg",{class:"w-5 h-5 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",Ae,[t[9]||(t[9]=e("h4",{class:"font-medium text-orange-800 dark:text-orange-200 mb-2"}," 🚨 项目需要您的支持！ ",-1)),t[10]||(t[10]=e("p",{class:"text-sm text-orange-700 dark:text-orange-300 mb-3 leading-relaxed"}," 再不收费，项目就要黄了(╥﹏╥)，请购买许可证，以支持 XCodeMap 的持续发展，为开发者创造颠覆性的源码调试体验。一个许可证可以激活 2 台设备，但同一时间只能有一台设备在线使用，也支持解绑，请放心使用。 ",-1)),e("div",Oe,[e("button",{onClick:t[0]||(t[0]=o=>h.value=!0),class:"px-6 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md transition-colors duration-200 shadow-sm"}," 💝 69 元购买许可证，一个月后将恢复原价 "),O.value===0?(a(),r("button",{key:0,onClick:K,disabled:j.value,class:"px-4 py-2 bg-gray-800 hover:bg-gray-900 disabled:bg-gray-400 text-white text-xs font-medium rounded-md transition-colors duration-200 shadow-sm"},d(j.value?"申请中...":"申请试用许可证"),9,ze)):i("",!0)])])])]),t[16]||(t[16]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," 请选择一个可用的许可证来生成激活码 ",-1)),u.value?(a(),r("div",Be,t[12]||(t[12]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})]),e("span",{class:"text-sm text-yellow-700 dark:text-yellow-300 font-medium"}," 已生成激活码，每个许可证仅可激活一次 ")],-1)]))):i("",!0),f.value?(a(),r("div",Ee,[e("div",Re,[t[13]||(t[13]=e("svg",{class:"w-4 h-4 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("span",Ue,d(f.value),1)])])):i("",!0),W.value?(a(),r("div",De,t[14]||(t[14]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在加载许可证...",-1)]))):P.value.length>0?(a(),r("div",$e,[(a(!0),r(le,null,de(P.value,o=>{var U,D,$;return a(),r("div",{key:o.id,onClick:Yt=>u.value?null:Z(o),class:Q(["p-4 border rounded-lg transition-colors duration-200",u.value?"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-60":((U=n.value)==null?void 0:U.id)===o.id?"border-blue-500 bg-blue-50 dark:bg-blue-900 cursor-pointer":"border-gray-300 dark:border-gray-600 hover:border-blue-300 cursor-pointer"])},[e("div",Xe,[e("div",Ne,[e("div",{class:Q(["w-5 h-5 rounded-full border-2 flex items-center justify-center",((D=n.value)==null?void 0:D.id)===o.id?"border-blue-600 bg-blue-600":"border-gray-400 dark:border-gray-500"])},[(($=n.value)==null?void 0:$.id)===o.id?(a(),r("div",Ge)):i("",!0)],2)]),e("div",Ve,[e("div",null,[e("h3",qe,d(E(o.licenseType)),1),e("p",We," 过期时间: "+d(R(o.activationExpiredTime)),1)]),e("div",Ye,[e("p",Ze,d(T(o.payPrice)),1),e("p",He," 订单号: "+d(o.orderId),1)])])])],10,Qe)}),128))])):(a(),r("div",Je,t[15]||(t[15]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"暂无可用许可证",-1)])))])]),e("div",Ke,[t[18]||(t[18]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 2 "),e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第二步 生成激活码 ")],-1)),e("div",et,[e("div",tt,[e("button",{onClick:Y,disabled:v.value||!n.value||u.value,class:"px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-md transition-colors duration-200"},d(v.value?"正在生成激活码...":u.value?"已生成激活码":"生成激活码"),9,st)]),n.value?i("",!0):(a(),r("div",rt,[e("p",at,d(u.value?"已生成激活码，无法再次生成":"请先选择许可证"),1)]))])]),e("div",ot,[t[24]||(t[24]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 3 "),e("div",null,[e("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第三步 获取激活码 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mt-1"}," 请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ")])],-1)),e("div",lt,[n.value&&x.value?(a(),r("div",dt,[e("div",nt,[t[19]||(t[19]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-5 h-5 rounded-full border-2 border-blue-600 bg-blue-600 flex items-center justify-center"},[e("div",{class:"w-2 h-2 rounded-full bg-white"})])],-1)),e("div",it,[e("div",null,[e("h3",ct,d(E(n.value.licenseType)),1),e("p",ut," 过期时间: "+d(R(n.value.activationExpiredTime)),1)]),e("div",xt,[e("p",vt,d(T(n.value.payPrice)),1),e("p",gt," 订单号: "+d(n.value.orderId),1)])])])])):i("",!0),v.value?(a(),r("div",bt,t[20]||(t[20]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在生成激活码...",-1)]))):x.value&&!v.value?(a(),r("div",yt,[e("div",mt,[e("code",{class:"activate-code-text text-sm font-mono break-all text-gray-900 dark:text-gray-100 font-medium select-all cursor-pointer",onClick:B,title:"点击复制激活码"},d(x.value),1)]),e("div",{class:"flex justify-center"},[e("button",{onClick:B,class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 复制激活码 ")]),c.value?(a(),r("div",pt,[c.value==="复制成功!"?(a(),r("span",ft," ✓ "+d(c.value),1)):(a(),r("span",kt," ✗ "+d(c.value),1))])):i("",!0),c.value==="复制失败，请手动复制"?(a(),r("div",ht,t[21]||(t[21]=[e("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 您可以点击上方的激活码文本进行手动复制 ",-1)]))):i("",!0),t[22]||(t[22]=e("div",{class:"text-center"},[e("p",{class:"text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-700"}," ✨ 激活码已生成，请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ✨ ")],-1))])):g.value?(a(),r("div",wt,d(g.value),1)):(a(),r("div",_t,t[23]||(t[23]=[e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请先完成前两步操作 ",-1)])))])])])]),h.value?(a(),r("div",Ct,[e("div",Lt,[e("div",jt,[t[27]||(t[27]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 购买许可证 ",-1)),e("button",{onClick:t[1]||(t[1]=o=>h.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[26]||(t[26]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ft,[e("div",Mt,[t[28]||(t[28]=X('<div class="flex justify-between items-center" data-v-5c1c4573><div data-v-5c1c4573><h4 class="font-medium text-gray-900 dark:text-white" data-v-5c1c4573>一年有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-5c1c4573>适合短期使用</p></div><div class="text-right" data-v-5c1c4573><div class="flex items-center space-x-2" data-v-5c1c4573><span class="text-xl font-semibold text-red-600 dark:text-red-400" data-v-5c1c4573>69元</span><span class="text-sm text-gray-500 dark:text-gray-400 line-through" data-v-5c1c4573>原价119元</span></div></div></div>',1)),e("button",{onClick:t[2]||(t[2]=o=>z(1)),disabled:_.value,class:"w-full mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},d(_.value?"创建订单中...":"限时购买"),9,Pt)]),e("div",St,[t[29]||(t[29]=X('<div class="flex justify-between items-center" data-v-5c1c4573><div data-v-5c1c4573><h4 class="font-medium text-gray-900 dark:text-white" data-v-5c1c4573>永久有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-5c1c4573>一次购买，永久使用</p></div><div class="text-right" data-v-5c1c4573><div class="flex items-center space-x-2" data-v-5c1c4573><span class="text-xl font-semibold text-red-600 dark:text-red-400" data-v-5c1c4573>169元</span><span class="text-sm text-gray-500 dark:text-gray-400 line-through" data-v-5c1c4573>原价299元</span></div></div></div>',1)),e("button",{onClick:t[3]||(t[3]=o=>z(2)),disabled:C.value,class:"w-full mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},d(C.value?"创建订单中...":"限时购买"),9,It)])])])])):i("",!0),w.value?(a(),r("div",Tt,[e("div",At,[e("div",Ot,[t[31]||(t[31]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 微信支付 ",-1)),e("button",{onClick:t[4]||(t[4]=()=>{w.value=!1,m()}),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[30]||(t[30]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",zt,[b.value?(a(),r("div",Bt,[e("h4",Et,d(b.value.licenseType===0?"试用许可证":b.value.licenseType===1?"一年有效期许可证":"永久许可证"),1),e("p",Rt,d(T(b.value.payPrice)),1),e("p",Ut," 订单号: "+d(b.value.orderId),1)])):i("",!0),e("div",Dt,[I.value?(a(),r("div",$t,t[32]||(t[32]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"text-sm text-gray-600 dark:text-gray-300"},"生成二维码中...",-1)]))):S.value?(a(),r("div",Qt,[e("img",{src:S.value,alt:"微信支付二维码",class:"w-64 h-64"},null,8,Xt)])):i("",!0)]),e("div",Nt,[t[34]||(t[34]=e("p",null,"请使用微信扫描二维码完成支付",-1)),t[35]||(t[35]=e("p",null,"支付成功后，您可以使用许可证生成激活码",-1)),L.value?(a(),r("div",Gt,t[33]||(t[33]=[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e("span",{class:"text-sm text-blue-600 dark:text-blue-400"}," 正在等待支付完成...请尽快完成支付 ")],-1)]))):i("",!0)])])])])):i("",!0)]))}}),qt=ue(Vt,[["__scopeId","data-v-5c1c4573"]]),G=xe(qt);G.use(ve());G.mount("#app");
