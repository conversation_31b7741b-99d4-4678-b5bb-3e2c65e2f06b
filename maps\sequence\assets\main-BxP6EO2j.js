import{d as Ee,r as k,c as ze,a as Re,w as Ve,b as R,o as E,F as I,e as W,f as y,g as N,n as X,t as U,_ as Je,h as Ke,i as xt,j as Et,u as S,k as Ae,v as ke,l as Rt,m as St,p as Ct,q as _t,s as Tt,x as Ot}from"./style-DLRB9IET.js";const At=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href;function We(e,t){return function(){return e.apply(t,arguments)}}const{toString:kt}=Object.prototype,{getPrototypeOf:Se}=Object,{iterator:oe,toStringTag:Xe}=Symbol,ae=(e=>t=>{const r=kt.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),L=e=>(e=e.toLowerCase(),t=>ae(t)===e),ie=e=>t=>typeof t===e,{isArray:V}=Array,G=ie("undefined");function Dt(e){return e!==null&&!G(e)&&e.constructor!==null&&!G(e.constructor)&&F(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ge=L("ArrayBuffer");function Nt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ge(e.buffer),t}const Ft=ie("string"),F=ie("function"),Qe=ie("number"),le=e=>e!==null&&typeof e=="object",Pt=e=>e===!0||e===!1,ee=e=>{if(ae(e)!=="object")return!1;const t=Se(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Xe in e)&&!(oe in e)},Lt=L("Date"),Bt=L("File"),Ut=L("Blob"),Mt=L("FileList"),It=e=>le(e)&&F(e.pipe),jt=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||F(e.append)&&((t=ae(e))==="formdata"||t==="object"&&F(e.toString)&&e.toString()==="[object FormData]"))},qt=L("URLSearchParams"),[$t,Ht,zt,Vt]=["ReadableStream","Request","Response","Headers"].map(L),Jt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Q(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),V(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),a=o.length;let l;for(n=0;n<a;n++)l=o[n],t.call(null,e[l],l,e)}}function Ye(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const q=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ze=e=>!G(e)&&e!==q;function ye(){const{caseless:e}=Ze(this)&&this||{},t={},r=(n,s)=>{const o=e&&Ye(t,s)||s;ee(t[o])&&ee(n)?t[o]=ye(t[o],n):ee(n)?t[o]=ye({},n):V(n)?t[o]=n.slice():t[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&Q(arguments[n],r);return t}const Kt=(e,t,r,{allOwnKeys:n}={})=>(Q(t,(s,o)=>{r&&F(s)?e[o]=We(s,r):e[o]=s},{allOwnKeys:n}),e),Wt=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Xt=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Gt=(e,t,r,n)=>{let s,o,a;const l={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)a=s[o],(!n||n(a,e,t))&&!l[a]&&(t[a]=e[a],l[a]=!0);e=r!==!1&&Se(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Qt=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Yt=e=>{if(!e)return null;if(V(e))return e;let t=e.length;if(!Qe(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Zt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Se(Uint8Array)),er=(e,t)=>{const n=(e&&e[oe]).call(e);let s;for(;(s=n.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},tr=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},rr=L("HTMLFormElement"),nr=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),De=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),sr=L("RegExp"),et=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};Q(r,(s,o)=>{let a;(a=t(s,o,e))!==!1&&(n[o]=a||s)}),Object.defineProperties(e,n)},or=e=>{et(e,(t,r)=>{if(F(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(F(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},ar=(e,t)=>{const r={},n=s=>{s.forEach(o=>{r[o]=!0})};return V(e)?n(e):n(String(e).split(t)),r},ir=()=>{},lr=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function cr(e){return!!(e&&F(e.append)&&e[Xe]==="FormData"&&e[oe])}const ur=e=>{const t=new Array(10),r=(n,s)=>{if(le(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[s]=n;const o=V(n)?[]:{};return Q(n,(a,l)=>{const f=r(a,s+1);!G(f)&&(o[l]=f)}),t[s]=void 0,o}}return n};return r(e,0)},dr=L("AsyncFunction"),fr=e=>e&&(le(e)||F(e))&&F(e.then)&&F(e.catch),tt=((e,t)=>e?setImmediate:t?((r,n)=>(q.addEventListener("message",({source:s,data:o})=>{s===q&&o===r&&n.length&&n.shift()()},!1),s=>{n.push(s),q.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",F(q.postMessage)),pr=typeof queueMicrotask<"u"?queueMicrotask.bind(q):typeof process<"u"&&process.nextTick||tt,hr=e=>e!=null&&F(e[oe]),i={isArray:V,isArrayBuffer:Ge,isBuffer:Dt,isFormData:jt,isArrayBufferView:Nt,isString:Ft,isNumber:Qe,isBoolean:Pt,isObject:le,isPlainObject:ee,isReadableStream:$t,isRequest:Ht,isResponse:zt,isHeaders:Vt,isUndefined:G,isDate:Lt,isFile:Bt,isBlob:Ut,isRegExp:sr,isFunction:F,isStream:It,isURLSearchParams:qt,isTypedArray:Zt,isFileList:Mt,forEach:Q,merge:ye,extend:Kt,trim:Jt,stripBOM:Wt,inherits:Xt,toFlatObject:Gt,kindOf:ae,kindOfTest:L,endsWith:Qt,toArray:Yt,forEachEntry:er,matchAll:tr,isHTMLForm:rr,hasOwnProperty:De,hasOwnProp:De,reduceDescriptors:et,freezeMethods:or,toObjectSet:ar,toCamelCase:nr,noop:ir,toFiniteNumber:lr,findKey:Ye,global:q,isContextDefined:Ze,isSpecCompliantForm:cr,toJSONObject:ur,isAsyncFn:dr,isThenable:fr,setImmediate:tt,asap:pr,isIterable:hr};function v(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}i.inherits(v,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:i.toJSONObject(this.config),code:this.code,status:this.status}}});const rt=v.prototype,nt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{nt[e]={value:e}});Object.defineProperties(v,nt);Object.defineProperty(rt,"isAxiosError",{value:!0});v.from=(e,t,r,n,s,o)=>{const a=Object.create(rt);return i.toFlatObject(e,a,function(f){return f!==Error.prototype},l=>l!=="isAxiosError"),v.call(a,e.message,t,r,n,s),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};const mr=null;function ge(e){return i.isPlainObject(e)||i.isArray(e)}function st(e){return i.endsWith(e,"[]")?e.slice(0,-2):e}function Ne(e,t,r){return e?e.concat(t).map(function(s,o){return s=st(s),!r&&o?"["+s+"]":s}).join(r?".":""):t}function yr(e){return i.isArray(e)&&!e.some(ge)}const gr=i.toFlatObject(i,{},null,function(t){return/^is[A-Z]/.test(t)});function ce(e,t,r){if(!i.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=i.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(b,g){return!i.isUndefined(g[b])});const n=r.metaTokens,s=r.visitor||u,o=r.dots,a=r.indexes,f=(r.Blob||typeof Blob<"u"&&Blob)&&i.isSpecCompliantForm(t);if(!i.isFunction(s))throw new TypeError("visitor must be a function");function c(m){if(m===null)return"";if(i.isDate(m))return m.toISOString();if(!f&&i.isBlob(m))throw new v("Blob is not supported. Use a Buffer instead.");return i.isArrayBuffer(m)||i.isTypedArray(m)?f&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function u(m,b,g){let _=m;if(m&&!g&&typeof m=="object"){if(i.endsWith(b,"{}"))b=n?b:b.slice(0,-2),m=JSON.stringify(m);else if(i.isArray(m)&&yr(m)||(i.isFileList(m)||i.endsWith(b,"[]"))&&(_=i.toArray(m)))return b=st(b),_.forEach(function(x,h){!(i.isUndefined(x)||x===null)&&t.append(a===!0?Ne([b],h,o):a===null?b:b+"[]",c(x))}),!1}return ge(m)?!0:(t.append(Ne(g,b,o),c(m)),!1)}const d=[],p=Object.assign(gr,{defaultVisitor:u,convertValue:c,isVisitable:ge});function w(m,b){if(!i.isUndefined(m)){if(d.indexOf(m)!==-1)throw Error("Circular reference detected in "+b.join("."));d.push(m),i.forEach(m,function(_,O){(!(i.isUndefined(_)||_===null)&&s.call(t,_,i.isString(O)?O.trim():O,b,p))===!0&&w(_,b?b.concat(O):[O])}),d.pop()}}if(!i.isObject(e))throw new TypeError("data must be an object");return w(e),t}function Fe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function Ce(e,t){this._pairs=[],e&&ce(e,this,t)}const ot=Ce.prototype;ot.append=function(t,r){this._pairs.push([t,r])};ot.toString=function(t){const r=t?function(n){return t.call(this,n,Fe)}:Fe;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function wr(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function at(e,t,r){if(!t)return e;const n=r&&r.encode||wr;i.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let o;if(s?o=s(t,r):o=i.isURLSearchParams(t)?t.toString():new Ce(t,r).toString(n),o){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Pe{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){i.forEach(this.handlers,function(n){n!==null&&t(n)})}}const it={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},br=typeof URLSearchParams<"u"?URLSearchParams:Ce,vr=typeof FormData<"u"?FormData:null,xr=typeof Blob<"u"?Blob:null,Er={isBrowser:!0,classes:{URLSearchParams:br,FormData:vr,Blob:xr},protocols:["http","https","file","blob","url","data"]},_e=typeof window<"u"&&typeof document<"u",we=typeof navigator=="object"&&navigator||void 0,Rr=_e&&(!we||["ReactNative","NativeScript","NS"].indexOf(we.product)<0),Sr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Cr=_e&&window.location.href||"http://localhost",_r=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:_e,hasStandardBrowserEnv:Rr,hasStandardBrowserWebWorkerEnv:Sr,navigator:we,origin:Cr},Symbol.toStringTag,{value:"Module"})),D={..._r,...Er};function Tr(e,t){return ce(e,new D.classes.URLSearchParams,Object.assign({visitor:function(r,n,s,o){return D.isNode&&i.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Or(e){return i.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Ar(e){const t={},r=Object.keys(e);let n;const s=r.length;let o;for(n=0;n<s;n++)o=r[n],t[o]=e[o];return t}function lt(e){function t(r,n,s,o){let a=r[o++];if(a==="__proto__")return!0;const l=Number.isFinite(+a),f=o>=r.length;return a=!a&&i.isArray(s)?s.length:a,f?(i.hasOwnProp(s,a)?s[a]=[s[a],n]:s[a]=n,!l):((!s[a]||!i.isObject(s[a]))&&(s[a]=[]),t(r,n,s[a],o)&&i.isArray(s[a])&&(s[a]=Ar(s[a])),!l)}if(i.isFormData(e)&&i.isFunction(e.entries)){const r={};return i.forEachEntry(e,(n,s)=>{t(Or(n),s,r,0)}),r}return null}function kr(e,t,r){if(i.isString(e))try{return(t||JSON.parse)(e),i.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Y={transitional:it,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,o=i.isObject(t);if(o&&i.isHTMLForm(t)&&(t=new FormData(t)),i.isFormData(t))return s?JSON.stringify(lt(t)):t;if(i.isArrayBuffer(t)||i.isBuffer(t)||i.isStream(t)||i.isFile(t)||i.isBlob(t)||i.isReadableStream(t))return t;if(i.isArrayBufferView(t))return t.buffer;if(i.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Tr(t,this.formSerializer).toString();if((l=i.isFileList(t))||n.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return ce(l?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(r.setContentType("application/json",!1),kr(t)):t}],transformResponse:[function(t){const r=this.transitional||Y.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(i.isResponse(t)||i.isReadableStream(t))return t;if(t&&i.isString(t)&&(n&&!this.responseType||s)){const a=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(l){if(a)throw l.name==="SyntaxError"?v.from(l,v.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:D.classes.FormData,Blob:D.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};i.forEach(["delete","get","head","post","put","patch"],e=>{Y.headers[e]={}});const Dr=i.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Nr=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(a){s=a.indexOf(":"),r=a.substring(0,s).trim().toLowerCase(),n=a.substring(s+1).trim(),!(!r||t[r]&&Dr[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Le=Symbol("internals");function K(e){return e&&String(e).trim().toLowerCase()}function te(e){return e===!1||e==null?e:i.isArray(e)?e.map(te):String(e)}function Fr(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Pr=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function fe(e,t,r,n,s){if(i.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!i.isString(t)){if(i.isString(n))return t.indexOf(n)!==-1;if(i.isRegExp(n))return n.test(t)}}function Lr(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function Br(e,t){const r=i.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,o,a){return this[n].call(this,t,s,o,a)},configurable:!0})})}let P=class{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function o(l,f,c){const u=K(f);if(!u)throw new Error("header name must be a non-empty string");const d=i.findKey(s,u);(!d||s[d]===void 0||c===!0||c===void 0&&s[d]!==!1)&&(s[d||f]=te(l))}const a=(l,f)=>i.forEach(l,(c,u)=>o(c,u,f));if(i.isPlainObject(t)||t instanceof this.constructor)a(t,r);else if(i.isString(t)&&(t=t.trim())&&!Pr(t))a(Nr(t),r);else if(i.isObject(t)&&i.isIterable(t)){let l={},f,c;for(const u of t){if(!i.isArray(u))throw TypeError("Object iterator must return a key-value pair");l[c=u[0]]=(f=l[c])?i.isArray(f)?[...f,u[1]]:[f,u[1]]:u[1]}a(l,r)}else t!=null&&o(r,t,n);return this}get(t,r){if(t=K(t),t){const n=i.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return Fr(s);if(i.isFunction(r))return r.call(this,s,n);if(i.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=K(t),t){const n=i.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||fe(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function o(a){if(a=K(a),a){const l=i.findKey(n,a);l&&(!r||fe(n,n[l],l,r))&&(delete n[l],s=!0)}}return i.isArray(t)?t.forEach(o):o(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const o=r[n];(!t||fe(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const r=this,n={};return i.forEach(this,(s,o)=>{const a=i.findKey(n,o);if(a){r[a]=te(s),delete r[o];return}const l=t?Lr(o):String(o).trim();l!==o&&delete r[o],r[l]=te(s),n[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return i.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&i.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[Le]=this[Le]={accessors:{}}).accessors,s=this.prototype;function o(a){const l=K(a);n[l]||(Br(s,a),n[l]=!0)}return i.isArray(t)?t.forEach(o):o(t),this}};P.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);i.reduceDescriptors(P.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});i.freezeMethods(P);function pe(e,t){const r=this||Y,n=t||r,s=P.from(n.headers);let o=n.data;return i.forEach(e,function(l){o=l.call(r,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function ct(e){return!!(e&&e.__CANCEL__)}function J(e,t,r){v.call(this,e??"canceled",v.ERR_CANCELED,t,r),this.name="CanceledError"}i.inherits(J,v,{__CANCEL__:!0});function ut(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new v("Request failed with status code "+r.status,[v.ERR_BAD_REQUEST,v.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Ur(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Mr(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,o=0,a;return t=t!==void 0?t:1e3,function(f){const c=Date.now(),u=n[o];a||(a=c),r[s]=f,n[s]=c;let d=o,p=0;for(;d!==s;)p+=r[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),c-a<t)return;const w=u&&c-u;return w?Math.round(p*1e3/w):void 0}}function Ir(e,t){let r=0,n=1e3/t,s,o;const a=(c,u=Date.now())=>{r=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const u=Date.now(),d=u-r;d>=n?a(c,u):(s=c,o||(o=setTimeout(()=>{o=null,a(s)},n-d)))},()=>s&&a(s)]}const ne=(e,t,r=3)=>{let n=0;const s=Mr(50,250);return Ir(o=>{const a=o.loaded,l=o.lengthComputable?o.total:void 0,f=a-n,c=s(f),u=a<=l;n=a;const d={loaded:a,total:l,progress:l?a/l:void 0,bytes:f,rate:c||void 0,estimated:c&&l&&u?(l-a)/c:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(d)},r)},Be=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Ue=e=>(...t)=>i.asap(()=>e(...t)),jr=D.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,D.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(D.origin),D.navigator&&/(msie|trident)/i.test(D.navigator.userAgent)):()=>!0,qr=D.hasStandardBrowserEnv?{write(e,t,r,n,s,o){const a=[e+"="+encodeURIComponent(t)];i.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),i.isString(n)&&a.push("path="+n),i.isString(s)&&a.push("domain="+s),o===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function $r(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Hr(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function dt(e,t,r){let n=!$r(t);return e&&(n||r==!1)?Hr(e,t):t}const Me=e=>e instanceof P?{...e}:e;function H(e,t){t=t||{};const r={};function n(c,u,d,p){return i.isPlainObject(c)&&i.isPlainObject(u)?i.merge.call({caseless:p},c,u):i.isPlainObject(u)?i.merge({},u):i.isArray(u)?u.slice():u}function s(c,u,d,p){if(i.isUndefined(u)){if(!i.isUndefined(c))return n(void 0,c,d,p)}else return n(c,u,d,p)}function o(c,u){if(!i.isUndefined(u))return n(void 0,u)}function a(c,u){if(i.isUndefined(u)){if(!i.isUndefined(c))return n(void 0,c)}else return n(void 0,u)}function l(c,u,d){if(d in t)return n(c,u);if(d in e)return n(void 0,c)}const f={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:l,headers:(c,u,d)=>s(Me(c),Me(u),d,!0)};return i.forEach(Object.keys(Object.assign({},e,t)),function(u){const d=f[u]||s,p=d(e[u],t[u],u);i.isUndefined(p)&&d!==l||(r[u]=p)}),r}const ft=e=>{const t=H({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:l}=t;t.headers=a=P.from(a),t.url=at(dt(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let f;if(i.isFormData(r)){if(D.hasStandardBrowserEnv||D.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((f=a.getContentType())!==!1){const[c,...u]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];a.setContentType([c||"multipart/form-data",...u].join("; "))}}if(D.hasStandardBrowserEnv&&(n&&i.isFunction(n)&&(n=n(t)),n||n!==!1&&jr(t.url))){const c=s&&o&&qr.read(o);c&&a.set(s,c)}return t},zr=typeof XMLHttpRequest<"u",Vr=zr&&function(e){return new Promise(function(r,n){const s=ft(e);let o=s.data;const a=P.from(s.headers).normalize();let{responseType:l,onUploadProgress:f,onDownloadProgress:c}=s,u,d,p,w,m;function b(){w&&w(),m&&m(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let g=new XMLHttpRequest;g.open(s.method.toUpperCase(),s.url,!0),g.timeout=s.timeout;function _(){if(!g)return;const x=P.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),T={data:!l||l==="text"||l==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:x,config:e,request:g};ut(function(M){r(M),b()},function(M){n(M),b()},T),g=null}"onloadend"in g?g.onloadend=_:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(_)},g.onabort=function(){g&&(n(new v("Request aborted",v.ECONNABORTED,e,g)),g=null)},g.onerror=function(){n(new v("Network Error",v.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let h=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const T=s.transitional||it;s.timeoutErrorMessage&&(h=s.timeoutErrorMessage),n(new v(h,T.clarifyTimeoutError?v.ETIMEDOUT:v.ECONNABORTED,e,g)),g=null},o===void 0&&a.setContentType(null),"setRequestHeader"in g&&i.forEach(a.toJSON(),function(h,T){g.setRequestHeader(T,h)}),i.isUndefined(s.withCredentials)||(g.withCredentials=!!s.withCredentials),l&&l!=="json"&&(g.responseType=s.responseType),c&&([p,m]=ne(c,!0),g.addEventListener("progress",p)),f&&g.upload&&([d,w]=ne(f),g.upload.addEventListener("progress",d),g.upload.addEventListener("loadend",w)),(s.cancelToken||s.signal)&&(u=x=>{g&&(n(!x||x.type?new J(null,e,g):x),g.abort(),g=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const O=Ur(s.url);if(O&&D.protocols.indexOf(O)===-1){n(new v("Unsupported protocol "+O+":",v.ERR_BAD_REQUEST,e));return}g.send(o||null)})},Jr=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const o=function(c){if(!s){s=!0,l();const u=c instanceof Error?c:this.reason;n.abort(u instanceof v?u:new J(u instanceof Error?u.message:u))}};let a=t&&setTimeout(()=>{a=null,o(new v(`timeout ${t} of ms exceeded`,v.ETIMEDOUT))},t);const l=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:f}=n;return f.unsubscribe=()=>i.asap(l),f}},Kr=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},Wr=async function*(e,t){for await(const r of Xr(e))yield*Kr(r,t)},Xr=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},Ie=(e,t,r,n)=>{const s=Wr(e,t);let o=0,a,l=f=>{a||(a=!0,n&&n(f))};return new ReadableStream({async pull(f){try{const{done:c,value:u}=await s.next();if(c){l(),f.close();return}let d=u.byteLength;if(r){let p=o+=d;r(p)}f.enqueue(new Uint8Array(u))}catch(c){throw l(c),c}},cancel(f){return l(f),s.return()}},{highWaterMark:2})},ue=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",pt=ue&&typeof ReadableStream=="function",Gr=ue&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ht=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Qr=pt&&ht(()=>{let e=!1;const t=new Request(D.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),je=64*1024,be=pt&&ht(()=>i.isReadableStream(new Response("").body)),se={stream:be&&(e=>e.body)};ue&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!se[t]&&(se[t]=i.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new v(`Response type '${t}' is not supported`,v.ERR_NOT_SUPPORT,n)})})})(new Response);const Yr=async e=>{if(e==null)return 0;if(i.isBlob(e))return e.size;if(i.isSpecCompliantForm(e))return(await new Request(D.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(i.isArrayBufferView(e)||i.isArrayBuffer(e))return e.byteLength;if(i.isURLSearchParams(e)&&(e=e+""),i.isString(e))return(await Gr(e)).byteLength},Zr=async(e,t)=>{const r=i.toFiniteNumber(e.getContentLength());return r??Yr(t)},en=ue&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:o,timeout:a,onDownloadProgress:l,onUploadProgress:f,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:p}=ft(e);c=c?(c+"").toLowerCase():"text";let w=Jr([s,o&&o.toAbortSignal()],a),m;const b=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let g;try{if(f&&Qr&&r!=="get"&&r!=="head"&&(g=await Zr(u,n))!==0){let T=new Request(t,{method:"POST",body:n,duplex:"half"}),C;if(i.isFormData(n)&&(C=T.headers.get("content-type"))&&u.setContentType(C),T.body){const[M,Z]=Be(g,ne(Ue(f)));n=Ie(T.body,je,M,Z)}}i.isString(d)||(d=d?"include":"omit");const _="credentials"in Request.prototype;m=new Request(t,{...p,signal:w,method:r.toUpperCase(),headers:u.normalize().toJSON(),body:n,duplex:"half",credentials:_?d:void 0});let O=await fetch(m);const x=be&&(c==="stream"||c==="response");if(be&&(l||x&&b)){const T={};["status","statusText","headers"].forEach(Oe=>{T[Oe]=O[Oe]});const C=i.toFiniteNumber(O.headers.get("content-length")),[M,Z]=l&&Be(C,ne(Ue(l),!0))||[];O=new Response(Ie(O.body,je,M,()=>{Z&&Z(),b&&b()}),T)}c=c||"text";let h=await se[i.findKey(se,c)||"text"](O,e);return!x&&b&&b(),await new Promise((T,C)=>{ut(T,C,{data:h,headers:P.from(O.headers),status:O.status,statusText:O.statusText,config:e,request:m})})}catch(_){throw b&&b(),_&&_.name==="TypeError"&&/Load failed|fetch/i.test(_.message)?Object.assign(new v("Network Error",v.ERR_NETWORK,e,m),{cause:_.cause||_}):v.from(_,_&&_.code,e,m)}}),ve={http:mr,xhr:Vr,fetch:en};i.forEach(ve,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const qe=e=>`- ${e}`,tn=e=>i.isFunction(e)||e===null||e===!1,mt={getAdapter:e=>{e=i.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let o=0;o<t;o++){r=e[o];let a;if(n=r,!tn(r)&&(n=ve[(a=String(r)).toLowerCase()],n===void 0))throw new v(`Unknown adapter '${a}'`);if(n)break;s[a||"#"+o]=n}if(!n){const o=Object.entries(s).map(([l,f])=>`adapter ${l} `+(f===!1?"is not supported by the environment":"is not available in the build"));let a=t?o.length>1?`since :
`+o.map(qe).join(`
`):" "+qe(o[0]):"as no adapter specified";throw new v("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return n},adapters:ve};function he(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new J(null,e)}function $e(e){return he(e),e.headers=P.from(e.headers),e.data=pe.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),mt.getAdapter(e.adapter||Y.adapter)(e).then(function(n){return he(e),n.data=pe.call(e,e.transformResponse,n),n.headers=P.from(n.headers),n},function(n){return ct(n)||(he(e),n&&n.response&&(n.response.data=pe.call(e,e.transformResponse,n.response),n.response.headers=P.from(n.response.headers))),Promise.reject(n)})}const yt="1.9.0",de={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{de[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const He={};de.transitional=function(t,r,n){function s(o,a){return"[Axios v"+yt+"] Transitional option '"+o+"'"+a+(n?". "+n:"")}return(o,a,l)=>{if(t===!1)throw new v(s(a," has been removed"+(r?" in "+r:"")),v.ERR_DEPRECATED);return r&&!He[a]&&(He[a]=!0,console.warn(s(a," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,a,l):!0}};de.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function rn(e,t,r){if(typeof e!="object")throw new v("options must be an object",v.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const o=n[s],a=t[o];if(a){const l=e[o],f=l===void 0||a(l,o,e);if(f!==!0)throw new v("option "+o+" must be "+f,v.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new v("Unknown option "+o,v.ERR_BAD_OPTION)}}const re={assertOptions:rn,validators:de},B=re.validators;let $=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Pe,response:new Pe}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=H(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:o}=r;n!==void 0&&re.assertOptions(n,{silentJSONParsing:B.transitional(B.boolean),forcedJSONParsing:B.transitional(B.boolean),clarifyTimeoutError:B.transitional(B.boolean)},!1),s!=null&&(i.isFunction(s)?r.paramsSerializer={serialize:s}:re.assertOptions(s,{encode:B.function,serialize:B.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),re.assertOptions(r,{baseUrl:B.spelling("baseURL"),withXsrfToken:B.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let a=o&&i.merge(o.common,o[r.method]);o&&i.forEach(["delete","get","head","post","put","patch","common"],m=>{delete o[m]}),r.headers=P.concat(a,o);const l=[];let f=!0;this.interceptors.request.forEach(function(b){typeof b.runWhen=="function"&&b.runWhen(r)===!1||(f=f&&b.synchronous,l.unshift(b.fulfilled,b.rejected))});const c=[];this.interceptors.response.forEach(function(b){c.push(b.fulfilled,b.rejected)});let u,d=0,p;if(!f){const m=[$e.bind(this),void 0];for(m.unshift.apply(m,l),m.push.apply(m,c),p=m.length,u=Promise.resolve(r);d<p;)u=u.then(m[d++],m[d++]);return u}p=l.length;let w=r;for(d=0;d<p;){const m=l[d++],b=l[d++];try{w=m(w)}catch(g){b.call(this,g);break}}try{u=$e.call(this,w)}catch(m){return Promise.reject(m)}for(d=0,p=c.length;d<p;)u=u.then(c[d++],c[d++]);return u}getUri(t){t=H(this.defaults,t);const r=dt(t.baseURL,t.url,t.allowAbsoluteUrls);return at(r,t.params,t.paramsSerializer)}};i.forEach(["delete","get","head","options"],function(t){$.prototype[t]=function(r,n){return this.request(H(n||{},{method:t,url:r,data:(n||{}).data}))}});i.forEach(["post","put","patch"],function(t){function r(n){return function(o,a,l){return this.request(H(l||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:a}))}}$.prototype[t]=r(),$.prototype[t+"Form"]=r(!0)});let nn=class gt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o;const a=new Promise(l=>{n.subscribe(l),o=l}).then(s);return a.cancel=function(){n.unsubscribe(o)},a},t(function(o,a,l){n.reason||(n.reason=new J(o,a,l),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new gt(function(s){t=s}),cancel:t}}};function sn(e){return function(r){return e.apply(null,r)}}function on(e){return i.isObject(e)&&e.isAxiosError===!0}const xe={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(xe).forEach(([e,t])=>{xe[t]=e});function wt(e){const t=new $(e),r=We($.prototype.request,t);return i.extend(r,$.prototype,t,{allOwnKeys:!0}),i.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return wt(H(e,s))},r}const A=wt(Y);A.Axios=$;A.CanceledError=J;A.CancelToken=nn;A.isCancel=ct;A.VERSION=yt;A.toFormData=ce;A.AxiosError=v;A.Cancel=A.CanceledError;A.all=function(t){return Promise.all(t)};A.spread=sn;A.isAxiosError=on;A.mergeConfig=H;A.AxiosHeaders=P;A.formToJSON=e=>lt(i.isHTMLForm(e)?new FormData(e):e);A.getAdapter=mt.getAdapter;A.HttpStatusCode=xe;A.default=A;const{Axios:ms,AxiosError:ys,CanceledError:gs,isCancel:ws,CancelToken:bs,VERSION:vs,all:xs,Cancel:Es,isAxiosError:Rs,spread:Ss,toFormData:Cs,AxiosHeaders:_s,HttpStatusCode:Ts,formToJSON:Os,getAdapter:As,mergeConfig:ks}=A,j=A.create({baseURL:"./",headers:{"Content-Type":"application/json"}}),an=async()=>{try{const e=await j.post("/getProcessDataList");return e.data?e.data.errorCode==="200"?e.data.data:(console.error("Error getting process data list:",e.data.errorMsg),[]):(console.error("Invalid response format:",e.data),[])}catch(e){return console.error("Failed to fetch process data list:",e),[]}},ln=async e=>{try{const t=await j.post("/changeRecordState",e);return t.data?t.data.errorCode==="200"?t.data.data:(console.error("Error changing record state:",t.data.errorMsg),!1):(console.error("Invalid response format:",t.data),!1)}catch(t){return console.error("Failed to change record state:",t),!1}},cn=async e=>{try{const t=await j.post("/getDataRequests",e);return t.data?t.data.errorCode==="200"?t.data.data:(console.error("Error getting tree data:",t.data.errorMsg),null):(console.error("Invalid response format:",t.data),null)}catch(t){return console.error("Failed to fetch tree data:",t),null}},un=async e=>{try{const t=await j.post("/createChatChannel",{chatId:e});return t.data?t.data.errorCode==="200"?t.data.data:(console.error("Error creating chat channel:",t.data.errorMsg),!1):(console.error("Invalid response format:",t.data),!1)}catch(t){return console.error("Failed to create chat channel:",t),!1}},dn=async e=>{try{const t=await j.post("/sinkChatMessage",e);return t.data?t.data.errorCode==="200"?t.data.data:(console.error("Error sinking chat message:",t.data.errorMsg),!1):(console.error("Invalid response format:",t.data),!1)}catch(t){return console.error("Failed to sink chat message:",t),!1}},fn=async()=>{try{const e=await j.post("/pollChatMessage");return e.data?e.data.errorCode==="200"?e.data.data:(console.error("Error polling chat message:",e.data.errorMsg),null):(console.error("Invalid response format:",e.data),null)}catch(e){return console.error("Failed to poll chat message:",e),null}},me=async e=>{try{const t=await j.post("/removeChatChannel",{chatId:e});return t.data?t.data.errorCode==="200"?t.data.data:(console.error("Error removing chat channel:",t.data.errorMsg),!1):(console.error("Invalid response format:",t.data),!1)}catch(t){return console.error("Failed to remove chat channel:",t),!1}},pn=async e=>{try{const t=await j.post("/switchProcessData",{processDataId:e});return t.data?t.data.errorCode==="200"?t.data.data:(console.error("Error switching process data:",t.data.errorMsg),!1):(console.error("Invalid response format:",t.data),!1)}catch(t){return console.error("Failed to switch process data:",t),!1}},bt=Ee("error",()=>{const e=k(null),t=k(null),r=(s,o)=>{t.value&&clearTimeout(t.value),e.value={message:s,code:o,timestamp:Date.now()},t.value=window.setTimeout(()=>{n()},5e3)},n=()=>{e.value=null,t.value&&(clearTimeout(t.value),t.value=null)};return{error:e,setError:r,clearError:n}}),vt=Ee("chat",()=>{const e=k([]),t=k(null),r=k(!1);let n=null;const s=bt(),o=ze(()=>e.value.find(p=>p.id===t.value)),a=async()=>{try{t.value&&await me(t.value);const p={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await un(p.id))return e.value=[p],t.value=p.id,r.value||l(),p;throw new Error("Failed to create chat channel")}catch(p){throw console.error("Failed to create chat channel:",p),s.setError(p instanceof Error?p.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),t.value=null,e.value=[],p}},l=()=>{n&&clearInterval(n),r.value=!0,n=window.setInterval(async()=>{if(r.value)try{const p=await fn();if(p){const w=e.value.find(m=>m.id===p.chatId);w&&(w.messages.push(p),w.updatedAt=Date.now())}}catch(p){console.error("Failed to poll messages:",p),s.setError(p instanceof Error?p.message:"Failed to poll messages","POLL_ERROR")}},1e3)},f=()=>{n&&(clearInterval(n),n=null),r.value=!1};return{chats:e,currentChatId:t,currentChat:o,createNewChat:a,sendMessage:async p=>{t.value||await a();const w=o.value;if(!w)return;const m={id:crypto.randomUUID(),content:p,role:"user",timestamp:Date.now(),chatId:t.value};try{if(!t.value)return;if(await dn(m))w.messages.push(m),w.updatedAt=Date.now();else throw new Error("Failed to send message")}catch(b){throw console.error("Failed to send message:",b),s.setError(b instanceof Error?b.message:"Failed to send message","SEND_MESSAGE_ERROR"),b}},removeChat:async p=>{try{if(await me(p))e.value=e.value.filter(m=>m.id!==p),t.value===p&&(t.value=null);else throw new Error("Failed to remove chat channel")}catch(w){throw console.error("Failed to remove chat:",w),s.setError(w instanceof Error?w.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),w}},startPolling:l,stopPolling:f,cleanup:()=>{f(),t.value&&me(t.value).catch(console.error)}}}),z="EMPTY_PLACE_HOLDER",hn=Ee("database",()=>{const e=k([{id:z,name:"请选择数据源",recordState:"idle",dataId:null,active:!1}]),t=k(z),r=ze(()=>e.value.find(d=>d.id===t.value)||null),n=d=>{e.value.push(d)},s=d=>{t.value=d},o=async(d,p)=>{const w=e.value.find(m=>m.id===d);if(w)try{await ln({executionId:d,cmd:"change",state:p}),w.recordState="preparing"}catch(m){console.error("Failed to change record state:",m)}};return{databases:e,currentDatabase:r,currentDatabaseId:t,addDatabase:n,setCurrentDatabase:s,changeState:o,queryState:d=>{var p;return(p=e.value.find(w=>w.id===d))==null?void 0:p.recordState},startRecord:d=>{const p=e.value.find(w=>w.id===d);p&&p.recordState==="idle"&&o(d,"start")},endRecord:d=>{const p=e.value.find(w=>w.id===d);p&&p.recordState==="recording"&&o(d,"stop")},restartRecord:d=>{const p=e.value.find(w=>w.id===d);p&&p.recordState==="paused"&&(o(d,"start"),vt().createNewChat())},getDatabase:async()=>{try{const d=await an();e.value=[e.value[0]],d.forEach(p=>{n(p)})}catch(d){console.error("Failed to fetch process data:",d)}}}}),mn={class:"space-y-0.5"},yn={class:"flex items-center gap-0.5 hover:bg-gray-50 rounded px-1 py-0.5 transition-all duration-200"},gn=["onClick"],wn=["onClick"],bn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},vn={class:"flex items-center gap-0.5 hover:bg-gray-50 rounded px-1 py-0.5 transition-all duration-200"},xn=["onClick"],En=["onClick"],Rn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Sn={class:"flex items-center gap-0.5 hover:bg-gray-50 rounded px-1 py-0.5 transition-all duration-200"},Cn=["onClick"],_n=Re({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(e,{emit:t}){const r=e,n=k(r.nodes.map(l=>({...l,isExpanded:r.defaultExpanded,children:l.children?l.children.map(f=>({...f,isExpanded:r.defaultExpanded,children:f.children?f.children.map(c=>({...c,isExpanded:r.defaultExpanded})):[]})):[]})));Ve(()=>r.nodes,l=>{n.value=l.map(f=>({...f,isExpanded:r.defaultExpanded,children:f.children?f.children.map(c=>({...c,isExpanded:r.defaultExpanded,children:c.children?c.children.map(u=>({...u,isExpanded:r.defaultExpanded})):[]})):[]}))},{deep:!0});const s=t,o=l=>{s("nodeClick",l)},a=l=>{l.isExpanded=!l.isExpanded};return(l,f)=>(E(),R("div",mn,[(E(!0),R(I,null,W(n.value,c=>(E(),R("div",{key:c.nodeKey,class:"tree-node"},[y("div",yn,[c.children&&c.children.length>0?(E(),R("button",{key:0,onClick:u=>a(c),class:"p-0.5 hover:bg-gray-100 rounded"},[(E(),R("svg",{xmlns:"http://www.w3.org/2000/svg",class:X(["h-4 w-4 text-gray-600 transition-transform duration-200",{"transform rotate-90":c.isExpanded}]),viewBox:"0 0 20 20",fill:"currentColor"},f[0]||(f[0]=[y("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],8,gn)):N("",!0),y("span",{class:"text-gray-700 cursor-pointer hover:text-gray-900 flex-grow",onClick:u=>o(c)},U(c.label),9,wn)]),c.children&&c.children.length>0&&c.isExpanded?(E(),R("div",bn,[(E(!0),R(I,null,W(c.children,u=>(E(),R("div",{key:u.nodeKey,class:"tree-node"},[y("div",vn,[u.children&&u.children.length>0?(E(),R("button",{key:0,onClick:d=>a(u),class:"p-0.5 hover:bg-gray-100 rounded"},[(E(),R("svg",{xmlns:"http://www.w3.org/2000/svg",class:X(["h-4 w-4 text-gray-600 transition-transform duration-200",{"transform rotate-90":u.isExpanded}]),viewBox:"0 0 20 20",fill:"currentColor"},f[1]||(f[1]=[y("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))],8,xn)):N("",!0),y("span",{class:"text-gray-700 cursor-pointer hover:text-gray-900 flex-grow",onClick:d=>o(u)},U(u.label),9,En)]),u.children&&u.children.length>0&&u.isExpanded?(E(),R("div",Rn,[(E(!0),R(I,null,W(u.children,d=>(E(),R("div",{key:d.nodeKey,class:"pl-3"},[y("div",Sn,[y("span",{class:"text-gray-700 cursor-pointer hover:text-gray-900 flex-grow",onClick:p=>o(d)},U(d.label),9,Cn)])]))),128))])):N("",!0)]))),128))])):N("",!0)]))),128))]))}}),Tn=Je(_n,[["__scopeId","data-v-8440f299"]]),On=Re({__name:"TypeWriter",props:{text:{},speed:{}},emits:["complete"],setup(e,{emit:t}){const r=e,n=t,s=k(""),o=k(0),a=k(!1),l=()=>{s.value="",o.value=0,a.value=!0;const f=setInterval(()=>{o.value<r.text.length?(s.value+=r.text[o.value],o.value++):(clearInterval(f),a.value=!1,n("complete"))},r.speed||50)};return Ve(()=>r.text,()=>{l()}),Ke(()=>{l()}),(f,c)=>(E(),R("span",null,U(s.value),1))}}),An={class:"h-screen min-w-full w-screen flex flex-col bg-gray-50 overflow-x-hidden"},kn={key:0,class:"fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50",role:"alert"},Dn={class:"block sm:inline"},Nn={class:"bg-white border-b border-gray-200 py-1 px-1 flex items-center justify-between"},Fn={class:"flex items-center"},Pn={class:"text-lg font-medium text-gray-900 px-2"},Ln={key:1,class:"bg-white border-b border-gray-200 py-1 px-4"},Bn={key:0,class:"flex items-center gap-2 text-gray-500 text-sm"},Un={key:1,class:"flex flex-col gap-2"},Mn=["onClick"],In={key:2,class:"bg-white border-b border-gray-200 py-1 px-4"},jn={class:"flex items-center justify-between"},qn={class:"flex gap-2"},$n={key:3,class:"bg-white border-b border-gray-200"},Hn={class:"px-1"},zn={key:0,class:"p-1 border-t border-gray-200"},Vn={class:"mb-2"},Jn={key:0},Kn={key:1,class:"text-gray-500"},Wn={class:"flex justify-center mt-2"},Xn={class:"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 max-w-full"},Gn={key:0,class:"flex items-start gap-3 max-w-full"},Qn={key:0,class:"w-8 h-8 flex-shrink-0 rounded-full bg-primary-100 flex items-center justify-center"},Yn={class:"flex-1 bg-white rounded-lg p-4 shadow-sm break-words"},Zn={class:"text-gray-800"},es={class:"flex-1 bg-white rounded-lg p-4 shadow-sm break-words"},ts={class:"text-gray-800"},rs={class:"flex-1 bg-primary-600 rounded-lg p-4 shadow-sm break-words"},ns={class:"text-white"},ss={key:1,class:"h-full flex items-center justify-center text-gray-500"},os={class:"border-t border-gray-200 bg-white w-full"},as={class:"p-4 max-w-full"},is=["placeholder","disabled","onKeydown"],ls={class:"flex items-center justify-between"},cs=["disabled"],us=Re({__name:"Main",setup(e){const t=vt(),r=hn(),n=bt(),s=k(""),o=k(!1),a=k(!1),l=k(null),f=k("");let c=null;const u=k(!0),d=k(!0),p=()=>{var x;return r.databases.length===1&&r.currentDatabaseId===z?"当前无可用的数据源，请使用 Debug with XCodeMap 录制程序数据。":r.currentDatabaseId===z?"请先选择数据源再对话":(x=r.currentDatabase)!=null&&x.dataId?"您好，有什么可以帮您的吗？":"请先录制程序数据然后再对话"},w=x=>{x.shiftKey||m()},m=async()=>{var h;if(!s.value.trim())return;if(!((h=r.currentDatabase)!=null&&h.dataId)){n.setError("当前没有数据，不可聊天。请先选择数据源。或者使用 Debug with XCodeMap 创建新的数据源。");return}const x=s.value;s.value="",await t.sendMessage(x)},b=()=>{o.value=!o.value},g=async x=>{const h=r.currentDatabase,T=!h||h.id!==x||h.dataId!==x;if(r.setCurrentDatabase(x),o.value=!1,T){if(!await pn(x)){n.setError("Failed to switch process data");return}t.createNewChat(),l.value=null,u.value=!0}},_=async()=>{var x;if((x=r.currentDatabase)!=null&&x.dataId)try{const h=await cn({processId:r.currentDatabase.dataId,first:u.value,filterText:f.value}),T=JSON.stringify(h),C=JSON.stringify(l.value);T!==C&&(console.log("Data has changed, updating treeData",T),l.value=h),u.value=!1}catch(h){console.error("Failed to fetch tree data:",h)}};xt(()=>{_()});const O=x=>{x.labelKey==="url"&&x.labelValue&&window.open(x.labelValue,"_blank"),console.log("Clicked tree node:",x)};return Ke(()=>{r.getDatabase(),_(),c=window.setInterval(()=>{r.getDatabase(),_()},1e3)}),Et(()=>{c!==null&&(clearInterval(c),c=null)}),(x,h)=>{var T;return E(),R("div",An,[S(n).error?(E(),R("div",kn,[y("span",Dn,U(S(n).error.message),1)])):N("",!0),y("div",Nn,[y("div",Fn,[y("div",{onClick:b,class:"flex items-center cursor-pointer hover:bg-gray-100 rounded-lg px-2 py-1"},[h[9]||(h[9]=y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),y("span",Pn,U(S(r).currentDatabase?S(r).currentDatabase.name:"请选择数据源"),1)])]),y("button",{onClick:h[0]||(h[0]=()=>{S(t).createNewChat(),d.value=!0}),class:"p-2 hover:bg-gray-100 rounded-full",title:"新建聊天"},h[10]||(h[10]=[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]))]),o.value||S(r).currentDatabaseId===S(z)?(E(),R("div",Ln,[S(r).databases.length===1?(E(),R("div",Bn,h[11]||(h[11]=[y("span",null,"当前无可用的数据源，请使用",-1),y("img",{src:At,alt:"XCodeMap Logo",class:"w-6 h-6"},null,-1),y("span",null,"Debug with XCodeMap 录制程序数据。",-1)]))):(E(),R("div",Un,[(E(!0),R(I,null,W(S(r).databases,C=>(E(),R("button",{key:C.id,onClick:M=>g(C.id),class:"w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-700 text-left"},U(C.name),9,Mn))),128))]))])):N("",!0),S(r).currentDatabase&&S(r).currentDatabase.active?(E(),R("div",In,[y("div",jn,[y("span",{class:X(["px-3 py-1 text-sm rounded-full",{"bg-green-100 text-green-800":S(r).currentDatabase.recordState==="recording","bg-gray-100 text-gray-800":S(r).currentDatabase.recordState==="idle","bg-yellow-100 text-yellow-800":S(r).currentDatabase.recordState==="paused"}])},U(S(r).currentDatabase.recordState),3),y("div",qn,[S(r).currentDatabase.recordState==="idle"?(E(),R("button",{key:0,onClick:h[1]||(h[1]=C=>S(r).startRecord(S(r).currentDatabase.id)),class:"p-2 hover:bg-gray-100 rounded-full",title:"开始录制"},h[12]||(h[12]=[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z","clip-rule":"evenodd"})],-1)]))):N("",!0),S(r).currentDatabase.recordState==="recording"?(E(),R("button",{key:1,onClick:h[2]||(h[2]=C=>S(r).endRecord(S(r).currentDatabase.id)),class:"p-2 hover:bg-gray-100 rounded-full",title:"结束录制"},h[13]||(h[13]=[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z","clip-rule":"evenodd"})],-1)]))):N("",!0),S(r).currentDatabase.recordState==="paused"?(E(),R("button",{key:2,onClick:h[3]||(h[3]=C=>S(r).restartRecord(S(r).currentDatabase.id)),class:"p-2 hover:bg-gray-100 rounded-full",title:"重新录制"},h[14]||(h[14]=[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"})],-1)]))):N("",!0)])])])):N("",!0),S(r).currentDatabaseId!==S(z)?(E(),R("div",$n,[y("div",Hn,[y("div",{onClick:h[4]||(h[4]=C=>a.value=!a.value),class:"flex items-center cursor-pointer hover:bg-gray-100 rounded-lg px-2 py-1"},[(E(),R("svg",{xmlns:"http://www.w3.org/2000/svg",class:X(["h-4 w-4 text-gray-600 transition-transform duration-200 hover:text-gray-800",{"transform rotate-90":a.value}]),viewBox:"0 0 20 20",fill:"currentColor"},h[15]||(h[15]=[y("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),h[16]||(h[16]=y("span",{class:"text-base font-medium text-gray-900 px-2"},"网络请求",-1))])]),a.value?(E(),R("div",zn,[y("div",Vn,[Ae(y("input",{"onUpdate:modelValue":h[5]||(h[5]=C=>f.value=C),type:"text",placeholder:"搜索网络请求...",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[ke,f.value]])]),l.value?(E(),R("div",Jn,[Rt(Tn,{nodes:l.value.rootNodes,onNodeClick:O},null,8,["nodes"])])):(E(),R("div",Kn,"Loading tree data...")),y("div",Wn,[y("button",{onClick:h[6]||(h[6]=C=>a.value=!1),class:"p-2 hover:bg-gray-100 rounded-full",title:"收起网络请求"},h[17]||(h[17]=[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]))])])):N("",!0)])):N("",!0),y("div",Xn,[S(t).currentChat?(E(),R(I,{key:0},[S(t).currentChat.messages.length===0?(E(),R("div",Gn,[d.value?N("",!0):(E(),R("div",Qn,h[18]||(h[18]=[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-primary-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 100-12 6 6 0 000 12z","clip-rule":"evenodd"})],-1)]))),y("div",Yn,[y("p",Zn,[(E(),St(On,{key:(T=S(t).currentChat)==null?void 0:T.id,text:p(),speed:50,onComplete:h[7]||(h[7]=C=>d.value=!1)},null,8,["text"]))])])])):N("",!0),(E(!0),R(I,null,W(S(t).currentChat.messages,C=>(E(),R("div",{key:C.id,class:X(["flex items-start gap-3 max-w-full",{"justify-end":C.role==="user"}])},[C.role==="assistant"?(E(),R(I,{key:0},[h[19]||(h[19]=y("div",{class:"w-8 h-8 flex-shrink-0 rounded-full bg-primary-100 flex items-center justify-center"},[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-primary-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 100-12 6 6 0 000 12z","clip-rule":"evenodd"})])],-1)),y("div",es,[y("p",ts,U(C.content),1)])],64)):(E(),R(I,{key:1},[y("div",rs,[y("p",ns,U(C.content),1)]),h[20]||(h[20]=y("div",{class:"w-8 h-8 flex-shrink-0 rounded-full bg-gray-200 flex items-center justify-center"},[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1))],64))],2))),128))],64)):(E(),R("div",ss," 请选择数据源后再对话 "))]),y("div",os,[y("div",as,[Ae(y("textarea",{"onUpdate:modelValue":h[8]||(h[8]=C=>s.value=C),class:"input w-full resize-none mb-2",rows:"3",placeholder:S(r).currentDatabase?S(r).currentDatabase.dataId?"Type your message...":"请先录制程序数据然后再对话":"请选择数据源再对话",disabled:!S(r).currentDatabase||!S(r).currentDatabase.dataId,onKeydown:Ct(_t(w,["prevent"]),["enter"])},null,40,is),[[ke,s.value]]),y("div",ls,[h[22]||(h[22]=y("span",{class:"text-sm text-gray-500"},"模型 deepseek",-1)),y("button",{onClick:m,class:"p-2 hover:bg-gray-100 rounded-full",disabled:!s.value.trim()||!S(r).currentDatabase||!S(r).currentDatabase.dataId,title:"发送消息"},h[21]||(h[21]=[y("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-600",viewBox:"0 0 20 20",fill:"currentColor"},[y("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),8,cs)])])])])}}}),ds=Je(us,[["__scopeId","data-v-73f6d2c8"]]),Te=Tt(ds);Te.use(Ot());Te.mount("#app");window.$vm=Te._instance;
