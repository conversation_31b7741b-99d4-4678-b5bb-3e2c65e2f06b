import{i as ft,u as gt,r as D,x as ht,z as mt,W as yt,X as vt,j as S,o as N,l as a,m as q,t as U,F as pt,k as bt,n as Pe,B as Te,Y as wt,Z as xt,$ as kt,_ as Ct,R as Et,S as _t}from"./style-DTQfQXkH.js";import{u as Bt}from"./activation-DDWghb3T.js";function At(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var W={},ie,Ie;function Rt(){return Ie||(Ie=1,ie=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),ie}var ae={},Y={},Se;function Q(){if(Se)return Y;Se=1;let n;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return Y.getSymbolSize=function(r){if(!r)throw new Error('"version" cannot be null or undefined');if(r<1||r>40)throw new Error('"version" should be in range from 1 to 40');return r*4+17},Y.getSymbolTotalCodewords=function(r){return o[r]},Y.getBCHDigit=function(s){let r=0;for(;s!==0;)r++,s>>>=1;return r},Y.setToSJISFunction=function(r){if(typeof r!="function")throw new Error('"toSJISFunc" is not a valid function.');n=r},Y.isKanjiModeEnabled=function(){return typeof n<"u"},Y.toSJIS=function(r){return n(r)},Y}var le={},Ne;function Me(){return Ne||(Ne=1,function(n){n.L={bit:1},n.M={bit:0},n.Q={bit:3},n.H={bit:2};function o(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"l":case"low":return n.L;case"m":case"medium":return n.M;case"q":case"quartile":return n.Q;case"h":case"high":return n.H;default:throw new Error("Unknown EC Level: "+s)}}n.isValid=function(r){return r&&typeof r.bit<"u"&&r.bit>=0&&r.bit<4},n.from=function(r,e){if(n.isValid(r))return r;try{return o(r)}catch{return e}}}(le)),le}var de,Le;function Mt(){if(Le)return de;Le=1;function n(){this.buffer=[],this.length=0}return n.prototype={get:function(o){const s=Math.floor(o/8);return(this.buffer[s]>>>7-o%8&1)===1},put:function(o,s){for(let r=0;r<s;r++)this.putBit((o>>>s-r-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const s=Math.floor(this.length/8);this.buffer.length<=s&&this.buffer.push(0),o&&(this.buffer[s]|=128>>>this.length%8),this.length++}},de=n,de}var ue,De;function Pt(){if(De)return ue;De=1;function n(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return n.prototype.set=function(o,s,r,e){const t=o*this.size+s;this.data[t]=r,e&&(this.reservedBit[t]=!0)},n.prototype.get=function(o,s){return this.data[o*this.size+s]},n.prototype.xor=function(o,s,r){this.data[o*this.size+s]^=r},n.prototype.isReserved=function(o,s){return this.reservedBit[o*this.size+s]},ue=n,ue}var ce={},Fe;function Tt(){return Fe||(Fe=1,function(n){const o=Q().getSymbolSize;n.getRowColCoords=function(r){if(r===1)return[];const e=Math.floor(r/7)+2,t=o(r),i=t===145?26:Math.ceil((t-13)/(2*e-2))*2,d=[t-7];for(let l=1;l<e-1;l++)d[l]=d[l-1]-i;return d.push(6),d.reverse()},n.getPositions=function(r){const e=[],t=n.getRowColCoords(r),i=t.length;for(let d=0;d<i;d++)for(let l=0;l<i;l++)d===0&&l===0||d===0&&l===i-1||d===i-1&&l===0||e.push([t[d],t[l]]);return e}}(ce)),ce}var fe={},Ue;function It(){if(Ue)return fe;Ue=1;const n=Q().getSymbolSize,o=7;return fe.getPositions=function(r){const e=n(r);return[[0,0],[e-o,0],[0,e-o]]},fe}var ge={},qe;function St(){return qe||(qe=1,function(n){n.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};n.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},n.from=function(e){return n.isValid(e)?parseInt(e,10):void 0},n.getPenaltyN1=function(e){const t=e.size;let i=0,d=0,l=0,f=null,c=null;for(let C=0;C<t;C++){d=l=0,f=c=null;for(let x=0;x<t;x++){let g=e.get(C,x);g===f?d++:(d>=5&&(i+=o.N1+(d-5)),f=g,d=1),g=e.get(x,C),g===c?l++:(l>=5&&(i+=o.N1+(l-5)),c=g,l=1)}d>=5&&(i+=o.N1+(d-5)),l>=5&&(i+=o.N1+(l-5))}return i},n.getPenaltyN2=function(e){const t=e.size;let i=0;for(let d=0;d<t-1;d++)for(let l=0;l<t-1;l++){const f=e.get(d,l)+e.get(d,l+1)+e.get(d+1,l)+e.get(d+1,l+1);(f===4||f===0)&&i++}return i*o.N2},n.getPenaltyN3=function(e){const t=e.size;let i=0,d=0,l=0;for(let f=0;f<t;f++){d=l=0;for(let c=0;c<t;c++)d=d<<1&2047|e.get(f,c),c>=10&&(d===1488||d===93)&&i++,l=l<<1&2047|e.get(c,f),c>=10&&(l===1488||l===93)&&i++}return i*o.N3},n.getPenaltyN4=function(e){let t=0;const i=e.data.length;for(let l=0;l<i;l++)t+=e.data[l];return Math.abs(Math.ceil(t*100/i/5)-10)*o.N4};function s(r,e,t){switch(r){case n.Patterns.PATTERN000:return(e+t)%2===0;case n.Patterns.PATTERN001:return e%2===0;case n.Patterns.PATTERN010:return t%3===0;case n.Patterns.PATTERN011:return(e+t)%3===0;case n.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(t/3))%2===0;case n.Patterns.PATTERN101:return e*t%2+e*t%3===0;case n.Patterns.PATTERN110:return(e*t%2+e*t%3)%2===0;case n.Patterns.PATTERN111:return(e*t%3+(e+t)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}n.applyMask=function(e,t){const i=t.size;for(let d=0;d<i;d++)for(let l=0;l<i;l++)t.isReserved(l,d)||t.xor(l,d,s(e,l,d))},n.getBestMask=function(e,t){const i=Object.keys(n.Patterns).length;let d=0,l=1/0;for(let f=0;f<i;f++){t(f),n.applyMask(f,e);const c=n.getPenaltyN1(e)+n.getPenaltyN2(e)+n.getPenaltyN3(e)+n.getPenaltyN4(e);n.applyMask(f,e),c<l&&(l=c,d=f)}return d}}(ge)),ge}var ne={},je;function at(){if(je)return ne;je=1;const n=Me(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return ne.getBlocksCount=function(e,t){switch(t){case n.L:return o[(e-1)*4+0];case n.M:return o[(e-1)*4+1];case n.Q:return o[(e-1)*4+2];case n.H:return o[(e-1)*4+3];default:return}},ne.getTotalCodewordsCount=function(e,t){switch(t){case n.L:return s[(e-1)*4+0];case n.M:return s[(e-1)*4+1];case n.Q:return s[(e-1)*4+2];case n.H:return s[(e-1)*4+3];default:return}},ne}var he={},re={},ze;function Nt(){if(ze)return re;ze=1;const n=new Uint8Array(512),o=new Uint8Array(256);return function(){let r=1;for(let e=0;e<255;e++)n[e]=r,o[r]=e,r<<=1,r&256&&(r^=285);for(let e=255;e<512;e++)n[e]=n[e-255]}(),re.log=function(r){if(r<1)throw new Error("log("+r+")");return o[r]},re.exp=function(r){return n[r]},re.mul=function(r,e){return r===0||e===0?0:n[o[r]+o[e]]},re}var Ve;function Lt(){return Ve||(Ve=1,function(n){const o=Nt();n.mul=function(r,e){const t=new Uint8Array(r.length+e.length-1);for(let i=0;i<r.length;i++)for(let d=0;d<e.length;d++)t[i+d]^=o.mul(r[i],e[d]);return t},n.mod=function(r,e){let t=new Uint8Array(r);for(;t.length-e.length>=0;){const i=t[0];for(let l=0;l<e.length;l++)t[l]^=o.mul(e[l],i);let d=0;for(;d<t.length&&t[d]===0;)d++;t=t.slice(d)}return t},n.generateECPolynomial=function(r){let e=new Uint8Array([1]);for(let t=0;t<r;t++)e=n.mul(e,new Uint8Array([1,o.exp(t)]));return e}}(he)),he}var me,Oe;function Dt(){if(Oe)return me;Oe=1;const n=Lt();function o(s){this.genPoly=void 0,this.degree=s,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(r){this.degree=r,this.genPoly=n.generateECPolynomial(this.degree)},o.prototype.encode=function(r){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(r.length+this.degree);e.set(r);const t=n.mod(e,this.genPoly),i=this.degree-t.length;if(i>0){const d=new Uint8Array(this.degree);return d.set(t,i),d}return t},me=o,me}var ye={},ve={},pe={},Ke;function lt(){return Ke||(Ke=1,pe.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),pe}var H={},He;function dt(){if(He)return H;He=1;const n="[0-9]+",o="[A-Z $%*+\\-./:]+";let s="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";s=s.replace(/u/g,"\\u");const r="(?:(?![A-Z0-9 $%*+\\-./:]|"+s+`)(?:.|[\r
]))+`;H.KANJI=new RegExp(s,"g"),H.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),H.BYTE=new RegExp(r,"g"),H.NUMERIC=new RegExp(n,"g"),H.ALPHANUMERIC=new RegExp(o,"g");const e=new RegExp("^"+s+"$"),t=new RegExp("^"+n+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return H.testKanji=function(l){return e.test(l)},H.testNumeric=function(l){return t.test(l)},H.testAlphanumeric=function(l){return i.test(l)},H}var Je;function $(){return Je||(Je=1,function(n){const o=lt(),s=dt();n.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},n.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},n.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},n.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},n.MIXED={bit:-1},n.getCharCountIndicator=function(t,i){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!o.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?t.ccBits[0]:i<27?t.ccBits[1]:t.ccBits[2]},n.getBestModeForData=function(t){return s.testNumeric(t)?n.NUMERIC:s.testAlphanumeric(t)?n.ALPHANUMERIC:s.testKanji(t)?n.KANJI:n.BYTE},n.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},n.isValid=function(t){return t&&t.bit&&t.ccBits};function r(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return n.NUMERIC;case"alphanumeric":return n.ALPHANUMERIC;case"kanji":return n.KANJI;case"byte":return n.BYTE;default:throw new Error("Unknown mode: "+e)}}n.from=function(t,i){if(n.isValid(t))return t;try{return r(t)}catch{return i}}}(ve)),ve}var Ye;function Ft(){return Ye||(Ye=1,function(n){const o=Q(),s=at(),r=Me(),e=$(),t=lt(),i=7973,d=o.getBCHDigit(i);function l(x,g,R){for(let M=1;M<=40;M++)if(g<=n.getCapacity(M,R,x))return M}function f(x,g){return e.getCharCountIndicator(x,g)+4}function c(x,g){let R=0;return x.forEach(function(M){const F=f(M.mode,g);R+=F+M.getBitsLength()}),R}function C(x,g){for(let R=1;R<=40;R++)if(c(x,R)<=n.getCapacity(R,g,e.MIXED))return R}n.from=function(g,R){return t.isValid(g)?parseInt(g,10):R},n.getCapacity=function(g,R,M){if(!t.isValid(g))throw new Error("Invalid QR Code version");typeof M>"u"&&(M=e.BYTE);const F=o.getSymbolTotalCodewords(g),_=s.getTotalCodewordsCount(g,R),T=(F-_)*8;if(M===e.MIXED)return T;const B=T-f(M,g);switch(M){case e.NUMERIC:return Math.floor(B/10*3);case e.ALPHANUMERIC:return Math.floor(B/11*2);case e.KANJI:return Math.floor(B/13);case e.BYTE:default:return Math.floor(B/8)}},n.getBestVersionForData=function(g,R){let M;const F=r.from(R,r.M);if(Array.isArray(g)){if(g.length>1)return C(g,F);if(g.length===0)return 1;M=g[0]}else M=g;return l(M.mode,M.getLength(),F)},n.getEncodedBits=function(g){if(!t.isValid(g)||g<7)throw new Error("Invalid QR Code version");let R=g<<12;for(;o.getBCHDigit(R)-d>=0;)R^=i<<o.getBCHDigit(R)-d;return g<<12|R}}(ye)),ye}var be={},Ge;function Ut(){if(Ge)return be;Ge=1;const n=Q(),o=1335,s=21522,r=n.getBCHDigit(o);return be.getEncodedBits=function(t,i){const d=t.bit<<3|i;let l=d<<10;for(;n.getBCHDigit(l)-r>=0;)l^=o<<n.getBCHDigit(l)-r;return(d<<10|l)^s},be}var we={},xe,Qe;function qt(){if(Qe)return xe;Qe=1;const n=$();function o(s){this.mode=n.NUMERIC,this.data=s.toString()}return o.getBitsLength=function(r){return 10*Math.floor(r/3)+(r%3?r%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(r){let e,t,i;for(e=0;e+3<=this.data.length;e+=3)t=this.data.substr(e,3),i=parseInt(t,10),r.put(i,10);const d=this.data.length-e;d>0&&(t=this.data.substr(e),i=parseInt(t,10),r.put(i,d*3+1))},xe=o,xe}var ke,$e;function jt(){if($e)return ke;$e=1;const n=$(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(r){this.mode=n.ALPHANUMERIC,this.data=r}return s.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let i=o.indexOf(this.data[t])*45;i+=o.indexOf(this.data[t+1]),e.put(i,11)}this.data.length%2&&e.put(o.indexOf(this.data[t]),6)},ke=s,ke}var Ce,Xe;function zt(){if(Xe)return Ce;Xe=1;const n=$();function o(s){this.mode=n.BYTE,typeof s=="string"?this.data=new TextEncoder().encode(s):this.data=new Uint8Array(s)}return o.getBitsLength=function(r){return r*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(s){for(let r=0,e=this.data.length;r<e;r++)s.put(this.data[r],8)},Ce=o,Ce}var Ee,We;function Vt(){if(We)return Ee;We=1;const n=$(),o=Q();function s(r){this.mode=n.KANJI,this.data=r}return s.getBitsLength=function(e){return e*13},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(r){let e;for(e=0;e<this.data.length;e++){let t=o.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else if(t>=57408&&t<=60351)t-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);t=(t>>>8&255)*192+(t&255),r.put(t,13)}},Ee=s,Ee}var _e={exports:{}},Ze;function Ot(){return Ze||(Ze=1,function(n){var o={single_source_shortest_paths:function(s,r,e){var t={},i={};i[r]=0;var d=o.PriorityQueue.make();d.push(r,0);for(var l,f,c,C,x,g,R,M,F;!d.empty();){l=d.pop(),f=l.value,C=l.cost,x=s[f]||{};for(c in x)x.hasOwnProperty(c)&&(g=x[c],R=C+g,M=i[c],F=typeof i[c]>"u",(F||M>R)&&(i[c]=R,d.push(c,R),t[c]=f))}if(typeof e<"u"&&typeof i[e]>"u"){var _=["Could not find a path from ",r," to ",e,"."].join("");throw new Error(_)}return t},extract_shortest_path_from_predecessor_list:function(s,r){for(var e=[],t=r;t;)e.push(t),s[t],t=s[t];return e.reverse(),e},find_path:function(s,r,e){var t=o.single_source_shortest_paths(s,r,e);return o.extract_shortest_path_from_predecessor_list(t,e)},PriorityQueue:{make:function(s){var r=o.PriorityQueue,e={},t;s=s||{};for(t in r)r.hasOwnProperty(t)&&(e[t]=r[t]);return e.queue=[],e.sorter=s.sorter||r.default_sorter,e},default_sorter:function(s,r){return s.cost-r.cost},push:function(s,r){var e={value:s,cost:r};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};n.exports=o}(_e)),_e.exports}var et;function Kt(){return et||(et=1,function(n){const o=$(),s=qt(),r=jt(),e=zt(),t=Vt(),i=dt(),d=Q(),l=Ot();function f(_){return unescape(encodeURIComponent(_)).length}function c(_,T,B){const k=[];let L;for(;(L=_.exec(B))!==null;)k.push({data:L[0],index:L.index,mode:T,length:L[0].length});return k}function C(_){const T=c(i.NUMERIC,o.NUMERIC,_),B=c(i.ALPHANUMERIC,o.ALPHANUMERIC,_);let k,L;return d.isKanjiModeEnabled()?(k=c(i.BYTE,o.BYTE,_),L=c(i.KANJI,o.KANJI,_)):(k=c(i.BYTE_KANJI,o.BYTE,_),L=[]),T.concat(B,k,L).sort(function(b,v){return b.index-v.index}).map(function(b){return{data:b.data,mode:b.mode,length:b.length}})}function x(_,T){switch(T){case o.NUMERIC:return s.getBitsLength(_);case o.ALPHANUMERIC:return r.getBitsLength(_);case o.KANJI:return t.getBitsLength(_);case o.BYTE:return e.getBitsLength(_)}}function g(_){return _.reduce(function(T,B){const k=T.length-1>=0?T[T.length-1]:null;return k&&k.mode===B.mode?(T[T.length-1].data+=B.data,T):(T.push(B),T)},[])}function R(_){const T=[];for(let B=0;B<_.length;B++){const k=_[B];switch(k.mode){case o.NUMERIC:T.push([k,{data:k.data,mode:o.ALPHANUMERIC,length:k.length},{data:k.data,mode:o.BYTE,length:k.length}]);break;case o.ALPHANUMERIC:T.push([k,{data:k.data,mode:o.BYTE,length:k.length}]);break;case o.KANJI:T.push([k,{data:k.data,mode:o.BYTE,length:f(k.data)}]);break;case o.BYTE:T.push([{data:k.data,mode:o.BYTE,length:f(k.data)}])}}return T}function M(_,T){const B={},k={start:{}};let L=["start"];for(let m=0;m<_.length;m++){const b=_[m],v=[];for(let y=0;y<b.length;y++){const A=b[y],p=""+m+y;v.push(p),B[p]={node:A,lastCount:0},k[p]={};for(let E=0;E<L.length;E++){const w=L[E];B[w]&&B[w].node.mode===A.mode?(k[w][p]=x(B[w].lastCount+A.length,A.mode)-x(B[w].lastCount,A.mode),B[w].lastCount+=A.length):(B[w]&&(B[w].lastCount=A.length),k[w][p]=x(A.length,A.mode)+4+o.getCharCountIndicator(A.mode,T))}}L=v}for(let m=0;m<L.length;m++)k[L[m]].end=0;return{map:k,table:B}}function F(_,T){let B;const k=o.getBestModeForData(_);if(B=o.from(T,k),B!==o.BYTE&&B.bit<k.bit)throw new Error('"'+_+'" cannot be encoded with mode '+o.toString(B)+`.
 Suggested mode is: `+o.toString(k));switch(B===o.KANJI&&!d.isKanjiModeEnabled()&&(B=o.BYTE),B){case o.NUMERIC:return new s(_);case o.ALPHANUMERIC:return new r(_);case o.KANJI:return new t(_);case o.BYTE:return new e(_)}}n.fromArray=function(T){return T.reduce(function(B,k){return typeof k=="string"?B.push(F(k,null)):k.data&&B.push(F(k.data,k.mode)),B},[])},n.fromString=function(T,B){const k=C(T,d.isKanjiModeEnabled()),L=R(k),m=M(L,B),b=l.find_path(m.map,"start","end"),v=[];for(let y=1;y<b.length-1;y++)v.push(m.table[b[y]].node);return n.fromArray(g(v))},n.rawSplit=function(T){return n.fromArray(C(T,d.isKanjiModeEnabled()))}}(we)),we}var tt;function Ht(){if(tt)return ae;tt=1;const n=Q(),o=Me(),s=Mt(),r=Pt(),e=Tt(),t=It(),i=St(),d=at(),l=Dt(),f=Ft(),c=Ut(),C=$(),x=Kt();function g(m,b){const v=m.size,y=t.getPositions(b);for(let A=0;A<y.length;A++){const p=y[A][0],E=y[A][1];for(let w=-1;w<=7;w++)if(!(p+w<=-1||v<=p+w))for(let P=-1;P<=7;P++)E+P<=-1||v<=E+P||(w>=0&&w<=6&&(P===0||P===6)||P>=0&&P<=6&&(w===0||w===6)||w>=2&&w<=4&&P>=2&&P<=4?m.set(p+w,E+P,!0,!0):m.set(p+w,E+P,!1,!0))}}function R(m){const b=m.size;for(let v=8;v<b-8;v++){const y=v%2===0;m.set(v,6,y,!0),m.set(6,v,y,!0)}}function M(m,b){const v=e.getPositions(b);for(let y=0;y<v.length;y++){const A=v[y][0],p=v[y][1];for(let E=-2;E<=2;E++)for(let w=-2;w<=2;w++)E===-2||E===2||w===-2||w===2||E===0&&w===0?m.set(A+E,p+w,!0,!0):m.set(A+E,p+w,!1,!0)}}function F(m,b){const v=m.size,y=f.getEncodedBits(b);let A,p,E;for(let w=0;w<18;w++)A=Math.floor(w/3),p=w%3+v-8-3,E=(y>>w&1)===1,m.set(A,p,E,!0),m.set(p,A,E,!0)}function _(m,b,v){const y=m.size,A=c.getEncodedBits(b,v);let p,E;for(p=0;p<15;p++)E=(A>>p&1)===1,p<6?m.set(p,8,E,!0):p<8?m.set(p+1,8,E,!0):m.set(y-15+p,8,E,!0),p<8?m.set(8,y-p-1,E,!0):p<9?m.set(8,15-p-1+1,E,!0):m.set(8,15-p-1,E,!0);m.set(y-8,8,1,!0)}function T(m,b){const v=m.size;let y=-1,A=v-1,p=7,E=0;for(let w=v-1;w>0;w-=2)for(w===6&&w--;;){for(let P=0;P<2;P++)if(!m.isReserved(A,w-P)){let K=!1;E<b.length&&(K=(b[E]>>>p&1)===1),m.set(A,w-P,K),p--,p===-1&&(E++,p=7)}if(A+=y,A<0||v<=A){A-=y,y=-y;break}}}function B(m,b,v){const y=new s;v.forEach(function(P){y.put(P.mode.bit,4),y.put(P.getLength(),C.getCharCountIndicator(P.mode,m)),P.write(y)});const A=n.getSymbolTotalCodewords(m),p=d.getTotalCodewordsCount(m,b),E=(A-p)*8;for(y.getLengthInBits()+4<=E&&y.put(0,4);y.getLengthInBits()%8!==0;)y.putBit(0);const w=(E-y.getLengthInBits())/8;for(let P=0;P<w;P++)y.put(P%2?17:236,8);return k(y,m,b)}function k(m,b,v){const y=n.getSymbolTotalCodewords(b),A=d.getTotalCodewordsCount(b,v),p=y-A,E=d.getBlocksCount(b,v),w=y%E,P=E-w,K=Math.floor(y/E),j=Math.floor(p/E),z=j+1,Z=K-j,oe=new l(Z);let X=0;const J=new Array(E),ee=new Array(E);let h=0;const u=new Uint8Array(m.buffer);for(let G=0;G<E;G++){const se=G<P?j:z;J[G]=u.slice(X,X+se),ee[G]=oe.encode(J[G]),X+=se,h=Math.max(h,se)}const I=new Uint8Array(y);let te=0,V,O;for(V=0;V<h;V++)for(O=0;O<E;O++)V<J[O].length&&(I[te++]=J[O][V]);for(V=0;V<Z;V++)for(O=0;O<E;O++)I[te++]=ee[O][V];return I}function L(m,b,v,y){let A;if(Array.isArray(m))A=x.fromArray(m);else if(typeof m=="string"){let K=b;if(!K){const j=x.rawSplit(m);K=f.getBestVersionForData(j,v)}A=x.fromString(m,K||40)}else throw new Error("Invalid data");const p=f.getBestVersionForData(A,v);if(!p)throw new Error("The amount of data is too big to be stored in a QR Code");if(!b)b=p;else if(b<p)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+p+`.
`);const E=B(b,v,A),w=n.getSymbolSize(b),P=new r(w);return g(P,b),R(P),M(P,b),_(P,v,0),b>=7&&F(P,b),T(P,E),isNaN(y)&&(y=i.getBestMask(P,_.bind(null,P,v))),i.applyMask(y,P),_(P,v,y),{modules:P,version:b,errorCorrectionLevel:v,maskPattern:y,segments:A}}return ae.create=function(b,v){if(typeof b>"u"||b==="")throw new Error("No input text");let y=o.M,A,p;return typeof v<"u"&&(y=o.from(v.errorCorrectionLevel,o.M),A=f.from(v.version),p=i.from(v.maskPattern),v.toSJISFunc&&n.setToSJISFunction(v.toSJISFunc)),L(b,A,y,p)},ae}var Be={},Ae={},rt;function ut(){return rt||(rt=1,function(n){function o(s){if(typeof s=="number"&&(s=s.toString()),typeof s!="string")throw new Error("Color should be defined as hex string");let r=s.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+s);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(t){return[t,t]}))),r.length===6&&r.push("F","F");const e=parseInt(r.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+r.slice(0,6).join("")}}n.getOptions=function(r){r||(r={}),r.color||(r.color={});const e=typeof r.margin>"u"||r.margin===null||r.margin<0?4:r.margin,t=r.width&&r.width>=21?r.width:void 0,i=r.scale||4;return{width:t,scale:t?4:i,margin:e,color:{dark:o(r.color.dark||"#000000ff"),light:o(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},n.getScale=function(r,e){return e.width&&e.width>=r+e.margin*2?e.width/(r+e.margin*2):e.scale},n.getImageWidth=function(r,e){const t=n.getScale(r,e);return Math.floor((r+e.margin*2)*t)},n.qrToImageData=function(r,e,t){const i=e.modules.size,d=e.modules.data,l=n.getScale(i,t),f=Math.floor((i+t.margin*2)*l),c=t.margin*l,C=[t.color.light,t.color.dark];for(let x=0;x<f;x++)for(let g=0;g<f;g++){let R=(x*f+g)*4,M=t.color.light;if(x>=c&&g>=c&&x<f-c&&g<f-c){const F=Math.floor((x-c)/l),_=Math.floor((g-c)/l);M=C[d[F*i+_]?1:0]}r[R++]=M.r,r[R++]=M.g,r[R++]=M.b,r[R]=M.a}}}(Ae)),Ae}var nt;function Jt(){return nt||(nt=1,function(n){const o=ut();function s(e,t,i){e.clearRect(0,0,t.width,t.height),t.style||(t.style={}),t.height=i,t.width=i,t.style.height=i+"px",t.style.width=i+"px"}function r(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}n.render=function(t,i,d){let l=d,f=i;typeof l>"u"&&(!i||!i.getContext)&&(l=i,i=void 0),i||(f=r()),l=o.getOptions(l);const c=o.getImageWidth(t.modules.size,l),C=f.getContext("2d"),x=C.createImageData(c,c);return o.qrToImageData(x.data,t,l),s(C,f,c),C.putImageData(x,0,0),f},n.renderToDataURL=function(t,i,d){let l=d;typeof l>"u"&&(!i||!i.getContext)&&(l=i,i=void 0),l||(l={});const f=n.render(t,i,l),c=l.type||"image/png",C=l.rendererOpts||{};return f.toDataURL(c,C.quality)}}(Be)),Be}var Re={},ot;function Yt(){if(ot)return Re;ot=1;const n=ut();function o(e,t){const i=e.a/255,d=t+'="'+e.hex+'"';return i<1?d+" "+t+'-opacity="'+i.toFixed(2).slice(1)+'"':d}function s(e,t,i){let d=e+t;return typeof i<"u"&&(d+=" "+i),d}function r(e,t,i){let d="",l=0,f=!1,c=0;for(let C=0;C<e.length;C++){const x=Math.floor(C%t),g=Math.floor(C/t);!x&&!f&&(f=!0),e[C]?(c++,C>0&&x>0&&e[C-1]||(d+=f?s("M",x+i,.5+g+i):s("m",l,0),l=0,f=!1),x+1<t&&e[C+1]||(d+=s("h",c),c=0)):l++}return d}return Re.render=function(t,i,d){const l=n.getOptions(i),f=t.modules.size,c=t.modules.data,C=f+l.margin*2,x=l.color.light.a?"<path "+o(l.color.light,"fill")+' d="M0 0h'+C+"v"+C+'H0z"/>':"",g="<path "+o(l.color.dark,"stroke")+' d="'+r(c,f,l.margin)+'"/>',R='viewBox="0 0 '+C+" "+C+'"',F='<svg xmlns="http://www.w3.org/2000/svg" '+(l.width?'width="'+l.width+'" height="'+l.width+'" ':"")+R+' shape-rendering="crispEdges">'+x+g+`</svg>
`;return typeof d=="function"&&d(null,F),F},Re}var st;function Gt(){if(st)return W;st=1;const n=Rt(),o=Ht(),s=Jt(),r=Yt();function e(t,i,d,l,f){const c=[].slice.call(arguments,1),C=c.length,x=typeof c[C-1]=="function";if(!x&&!n())throw new Error("Callback required as last argument");if(x){if(C<2)throw new Error("Too few arguments provided");C===2?(f=d,d=i,i=l=void 0):C===3&&(i.getContext&&typeof f>"u"?(f=l,l=void 0):(f=l,l=d,d=i,i=void 0))}else{if(C<1)throw new Error("Too few arguments provided");return C===1?(d=i,i=l=void 0):C===2&&!i.getContext&&(l=d,d=i,i=void 0),new Promise(function(g,R){try{const M=o.create(d,l);g(t(M,i,l))}catch(M){R(M)}})}try{const g=o.create(d,l);f(null,t(g,i,l))}catch(g){f(g)}}return W.create=o.create,W.toCanvas=e.bind(null,s.render),W.toDataURL=e.bind(null,s.renderToDataURL),W.toString=e.bind(null,function(t,i,d){return r.render(t,d)}),W}var Qt=Gt();const $t=At(Qt),Xt={class:"h-screen w-full flex flex-col overflow-x-hidden bg-gray-50 dark:bg-gray-900"},Wt={class:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4"},Zt={class:"max-w-4xl mx-auto flex justify-between items-center"},er={class:"flex items-center space-x-4"},tr={key:0,class:"flex items-center space-x-2"},rr={key:1,class:"flex items-center space-x-4"},nr={class:"text-sm"},or={class:"ml-1 font-medium text-gray-900 dark:text-white"},sr={class:"text-sm"},ir={class:"ml-1 font-medium text-gray-900 dark:text-white"},ar={key:2,class:"text-sm text-gray-500 dark:text-gray-400"},lr={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},dr={class:"max-w-4xl mx-auto"},ur={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},cr={class:"ml-11"},fr={class:"bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900 dark:to-red-900 border border-orange-200 dark:border-orange-700 rounded-lg p-4 mb-4"},gr={class:"flex items-start space-x-3"},hr={class:"flex-1"},mr={class:"flex items-center space-x-2"},yr=["disabled"],vr={key:0,class:"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg"},pr={key:1,class:"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg"},br={class:"flex items-center space-x-2"},wr={class:"text-sm text-red-700 dark:text-red-300"},xr={key:2,class:"text-center py-8"},kr={key:3,class:"space-y-4"},Cr=["onClick"],Er={class:"flex items-center space-x-3"},_r={class:"flex-shrink-0"},Br={key:0,class:"w-2 h-2 rounded-full bg-white"},Ar={class:"flex-1 flex justify-between items-center"},Rr={class:"font-medium text-gray-900 dark:text-white"},Mr={class:"text-sm text-gray-500 dark:text-gray-400"},Pr={class:"text-right"},Tr={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ir={class:"text-xs text-gray-500 dark:text-gray-400"},Sr={key:4,class:"text-center py-8"},Nr={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},Lr={class:"ml-11"},Dr={class:"text-center mb-4"},Fr=["disabled"],Ur={key:0,class:"text-center"},qr={class:"text-sm text-gray-500 dark:text-gray-400"},jr={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},zr={class:"ml-11"},Vr={key:0,class:"mb-4 p-4 border border-blue-500 bg-blue-50 dark:bg-blue-900 rounded-lg"},Or={class:"flex items-center space-x-3"},Kr={class:"flex-1 flex justify-between items-center"},Hr={class:"font-medium text-gray-900 dark:text-white"},Jr={class:"text-sm text-gray-500 dark:text-gray-400"},Yr={class:"text-right"},Gr={class:"text-lg font-semibold text-gray-900 dark:text-white"},Qr={class:"text-xs text-gray-500 dark:text-gray-400"},$r={key:1,class:"text-center py-8"},Xr={key:2,class:"space-y-4"},Wr={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600"},Zr={key:0,class:"text-center"},en={key:0,class:"text-sm text-green-600 dark:text-green-400 font-medium"},tn={key:1,class:"text-sm text-red-600 dark:text-red-400 font-medium"},rn={key:1,class:"text-center"},nn={key:3,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},on={key:4,class:"text-center py-8"},sn={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},an={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},ln={class:"flex justify-between items-center mb-4"},dn={class:"space-y-4"},un={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},cn=["disabled"],fn={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},gn=["disabled"],hn={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},mn={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},yn={class:"flex justify-between items-center mb-4"},vn={class:"text-center space-y-4"},pn={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},bn={class:"font-medium text-gray-900 dark:text-white mb-2"},wn={class:"text-2xl font-bold text-green-600 dark:text-green-400"},xn={class:"text-sm text-gray-500 dark:text-gray-400"},kn={class:"flex justify-center"},Cn={key:0,class:"flex items-center space-x-2"},En={key:1,class:"bg-white p-4 rounded-lg"},_n=["src"],Bn={class:"text-sm text-gray-600 dark:text-gray-300 space-y-2"},An={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg"},it=300,Rn=ft({__name:"Activated",setup(n){const o=gt(),s=Bt(),r=D(""),e=D(""),t=D(!1),i=D(""),d=D(null),l=D(!1),f=D([]),c=D(null),C=D(!1),x=D(!1),g=D(null),R=D(""),M=D(!1),F=D(!1),_=D(!1),T=D(!1),B=D(!1),k=D(null),L=D(0),m=D(!1),b=D(""),v=D(!1),y=D(null);ht(()=>{o.initTheme();const h=new URLSearchParams(window.location.search),u=h.get("deviceCode"),I=h.get("utype");u?r.value=u:i.value="未找到设备码参数",I!==null&&(y.value=parseInt(I)),p()}),mt(()=>{j()});const A=async()=>{if(!c.value){i.value="请先选择一个许可证";return}try{t.value=!0,i.value="";const h=await s.generateActivateCode(r.value,c.value.orderId);h&&(e.value=h,v.value=!0)}catch(h){i.value=h instanceof Error?h.message:"生成激活码失败",console.error("Failed to generate activate code:",h)}finally{t.value=!1}},p=async()=>{try{l.value=!0;const[h,u]=await Promise.all([yt(),vt()]);h.success&&h.data?d.value=h.data:console.error("Failed to load user info:",h.error),u.success?f.value=u.data||[]:console.error("Failed to load licenses:",u.error)}catch(h){console.error("Failed to load user info:",h)}finally{l.value=!1}},E=h=>{c.value=h},w=async h=>{h===1?_.value=!0:h===2&&(T.value=!0);try{const I=await wt({licenseType:h,payMethod:"wechat",payPrice:h===0?0:h===1?6900:19900});I.success?(j(),g.value=I.data,C.value=!1,M.value=!0,await P(I.data.wxCodeUrl),K(I.data.orderId)):console.error("Failed to create order:",I.error)}catch(u){console.error("Failed to create order:",u)}finally{h===1?_.value=!1:h===2&&(T.value=!1)}},P=async h=>{try{F.value=!0;const u=await $t.toDataURL(h,{width:256,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});R.value=u}catch(u){console.error("Failed to generate QR code:",u)}finally{F.value=!1}},K=h=>{if(B.value)return;B.value=!0,L.value=0;const u=async()=>{try{L.value++,console.log(`Polling order status, attempt ${L.value}`),(await kt({orderId:h})).success?(console.log("Order payment successful!"),j(),M.value=!1,await p(),alert("支付成功! 您现在可以使用许可证生成激活码了！")):L.value>=it?(console.log("Max polling count reached, stopping polling"),j(),alert("支付超时，请重新创建订单。")):k.value=setTimeout(u,1e3)}catch(I){console.error("Error polling order status:",I),L.value>=it?(j(),alert("检查订单状态失败，请手动刷新页面。")):k.value=setTimeout(u,1e3)}};u()},j=()=>{k.value&&(clearTimeout(k.value),k.value=null),B.value=!1,L.value=0},z=D(""),Z=async()=>{try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(e.value),z.value="复制成功!",console.log("Activate code copied to clipboard");else{const h=document.createElement("textarea");h.value=e.value,h.style.position="fixed",h.style.left="-999999px",h.style.top="-999999px",document.body.appendChild(h),h.focus(),h.select();const u=document.execCommand("copy");document.body.removeChild(h),u?(z.value="复制成功!",console.log("Activate code copied to clipboard")):(z.value="复制失败，请手动复制",console.error("Failed to copy activate code"))}setTimeout(()=>{z.value=""},3e3)}catch(h){z.value="复制失败，请手动复制",console.error("Failed to copy activate code:",h)}},oe=async()=>{try{m.value=!0,b.value="";const h=await xt();h.success?(await p(),alert("试用许可证申请成功！您现在可以使用许可证生成激活码了！")):b.value=h.error||"申请试用许可证失败"}catch(h){b.value=h instanceof Error?h.message:"申请试用许可证失败",console.error("Failed to apply trial license:",h)}finally{m.value=!1}},X=h=>h===0?"试用许可证":h===1?"一年有效期":h===2?"永久有效期":"未知类型",J=h=>h===0?"免费":(h/100).toFixed(0)+"元",ee=h=>new Date(h).toLocaleDateString("zh-CN");return(h,u)=>(N(),S("div",Xt,[a("div",Wt,[a("div",Zt,[a("div",er,[u[8]||(u[8]=a("div",{class:"flex items-center space-x-2"},[a("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})]),a("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"用户信息")],-1)),l.value?(N(),S("div",tr,u[5]||(u[5]=[a("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1),a("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"加载中...",-1)]))):d.value?(N(),S("div",rr,[a("div",nr,[u[6]||(u[6]=a("span",{class:"text-gray-500 dark:text-gray-400"},"ID:",-1)),a("span",or,U(d.value.uniqueId),1)]),a("div",sr,[u[7]||(u[7]=a("span",{class:"text-gray-500 dark:text-gray-400"},"昵称:",-1)),a("span",ir,U(d.value.username),1)])])):(N(),S("div",ar," 未获取到用户信息 "))]),a("div",{class:"flex items-center space-x-2"},[a("button",{onClick:p,class:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200"}," 刷新 ")])])]),a("div",lr,[a("div",dr,[u[25]||(u[25]=a("div",{class:"text-center mb-8"},[a("h1",{class:"text-3xl font-bold mb-4 text-gray-900 dark:text-white"}," XCodeMap 激活码生成 ")],-1)),a("div",ur,[u[17]||(u[17]=a("div",{class:"flex items-center mb-4"},[a("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 1 "),a("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第一步 选择许可证 ")],-1)),a("div",cr,[a("div",fr,[a("div",gr,[u[11]||(u[11]=a("div",{class:"flex-shrink-0 mt-1"},[a("svg",{class:"w-5 h-5 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),a("div",hr,[u[9]||(u[9]=a("h4",{class:"font-medium text-orange-800 dark:text-orange-200 mb-2"}," 🚨 项目需要您的支持！ ",-1)),u[10]||(u[10]=a("p",{class:"text-sm text-orange-700 dark:text-orange-300 mb-3 leading-relaxed"}," 再不收费，项目就要黄了(╥﹏╥)，请购买许可证，以支持 XCodeMap 的持续发展，为开发者创造颠覆性的源码调试体验。 ",-1)),a("div",mr,[a("button",{onClick:u[0]||(u[0]=I=>C.value=!0),class:"px-6 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md transition-colors duration-200 shadow-sm"}," 💝 69 元购买许可证，一个月后将恢复原价 "),y.value===0?(N(),S("button",{key:0,onClick:oe,disabled:m.value,class:"px-4 py-2 bg-gray-800 hover:bg-gray-900 disabled:bg-gray-400 text-white text-xs font-medium rounded-md transition-colors duration-200 shadow-sm"},U(m.value?"申请中...":"申请试用许可证"),9,yr)):q("",!0)])])])]),u[16]||(u[16]=a("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," 请选择一个可用的许可证来生成激活码 ",-1)),v.value?(N(),S("div",vr,u[12]||(u[12]=[a("div",{class:"flex items-center space-x-2"},[a("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})]),a("span",{class:"text-sm text-yellow-700 dark:text-yellow-300 font-medium"}," 已生成激活码，每个许可证仅可激活一次 ")],-1)]))):q("",!0),b.value?(N(),S("div",pr,[a("div",br,[u[13]||(u[13]=a("svg",{class:"w-4 h-4 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),a("span",wr,U(b.value),1)])])):q("",!0),x.value?(N(),S("div",xr,u[14]||(u[14]=[a("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),a("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在加载许可证...",-1)]))):f.value.length>0?(N(),S("div",kr,[(N(!0),S(pt,null,bt(f.value,I=>{var te,V,O;return N(),S("div",{key:I.id,onClick:G=>v.value?null:E(I),class:Pe(["p-4 border rounded-lg transition-colors duration-200",v.value?"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-60":((te=c.value)==null?void 0:te.id)===I.id?"border-blue-500 bg-blue-50 dark:bg-blue-900 cursor-pointer":"border-gray-300 dark:border-gray-600 hover:border-blue-300 cursor-pointer"])},[a("div",Er,[a("div",_r,[a("div",{class:Pe(["w-5 h-5 rounded-full border-2 flex items-center justify-center",((V=c.value)==null?void 0:V.id)===I.id?"border-blue-600 bg-blue-600":"border-gray-400 dark:border-gray-500"])},[((O=c.value)==null?void 0:O.id)===I.id?(N(),S("div",Br)):q("",!0)],2)]),a("div",Ar,[a("div",null,[a("h3",Rr,U(X(I.licenseType)),1),a("p",Mr," 过期时间: "+U(ee(I.activationExpiredTime)),1)]),a("div",Pr,[a("p",Tr,U(J(I.payPrice)),1),a("p",Ir," 订单号: "+U(I.orderId),1)])])])],10,Cr)}),128))])):(N(),S("div",Sr,u[15]||(u[15]=[a("p",{class:"text-gray-500 dark:text-gray-400"},"暂无可用许可证",-1)])))])]),a("div",Nr,[u[18]||(u[18]=a("div",{class:"flex items-center mb-4"},[a("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 2 "),a("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第二步 生成激活码 ")],-1)),a("div",Lr,[a("div",Dr,[a("button",{onClick:A,disabled:t.value||!c.value||v.value,class:"px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-md transition-colors duration-200"},U(t.value?"正在生成激活码...":v.value?"已生成激活码":"生成激活码"),9,Fr)]),c.value?q("",!0):(N(),S("div",Ur,[a("p",qr,U(v.value?"已生成激活码，无法再次生成":"请先选择许可证"),1)]))])]),a("div",jr,[u[24]||(u[24]=a("div",{class:"flex items-center mb-4"},[a("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 3 "),a("div",null,[a("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第三步 获取激活码 "),a("p",{class:"text-sm text-gray-500 dark:text-gray-400 mt-1"}," 请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ")])],-1)),a("div",zr,[c.value&&e.value?(N(),S("div",Vr,[a("div",Or,[u[19]||(u[19]=a("div",{class:"flex-shrink-0"},[a("div",{class:"w-5 h-5 rounded-full border-2 border-blue-600 bg-blue-600 flex items-center justify-center"},[a("div",{class:"w-2 h-2 rounded-full bg-white"})])],-1)),a("div",Kr,[a("div",null,[a("h3",Hr,U(X(c.value.licenseType)),1),a("p",Jr," 过期时间: "+U(ee(c.value.activationExpiredTime)),1)]),a("div",Yr,[a("p",Gr,U(J(c.value.payPrice)),1),a("p",Qr," 订单号: "+U(c.value.orderId),1)])])])])):q("",!0),t.value?(N(),S("div",$r,u[20]||(u[20]=[a("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),a("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在生成激活码...",-1)]))):e.value&&!t.value?(N(),S("div",Xr,[a("div",Wr,[a("code",{class:"activate-code-text text-sm font-mono break-all text-gray-900 dark:text-gray-100 font-medium select-all cursor-pointer",onClick:Z,title:"点击复制激活码"},U(e.value),1)]),a("div",{class:"flex justify-center"},[a("button",{onClick:Z,class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 复制激活码 ")]),z.value?(N(),S("div",Zr,[z.value==="复制成功!"?(N(),S("span",en," ✓ "+U(z.value),1)):(N(),S("span",tn," ✗ "+U(z.value),1))])):q("",!0),z.value==="复制失败，请手动复制"?(N(),S("div",rn,u[21]||(u[21]=[a("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 您可以点击上方的激活码文本进行手动复制 ",-1)]))):q("",!0),u[22]||(u[22]=a("div",{class:"text-center"},[a("p",{class:"text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-700"}," ✨ 激活码已生成，请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ✨ ")],-1))])):i.value?(N(),S("div",nn,U(i.value),1)):(N(),S("div",on,u[23]||(u[23]=[a("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请先完成前两步操作 ",-1)])))])])])]),C.value?(N(),S("div",sn,[a("div",an,[a("div",ln,[u[27]||(u[27]=a("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 购买许可证 ",-1)),a("button",{onClick:u[1]||(u[1]=I=>C.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},u[26]||(u[26]=[a("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),a("div",dn,[a("div",un,[u[28]||(u[28]=Te('<div class="flex justify-between items-center" data-v-dcfca3ee><div data-v-dcfca3ee><h4 class="font-medium text-gray-900 dark:text-white" data-v-dcfca3ee>一年有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-dcfca3ee>适合短期使用</p></div><div class="text-right" data-v-dcfca3ee><div class="flex items-center space-x-2" data-v-dcfca3ee><span class="text-xl font-semibold text-red-600 dark:text-red-400" data-v-dcfca3ee>69元</span><span class="text-sm text-gray-500 dark:text-gray-400 line-through" data-v-dcfca3ee>原价99元</span></div></div></div>',1)),a("button",{onClick:u[2]||(u[2]=I=>w(1)),disabled:_.value,class:"w-full mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},U(_.value?"创建订单中...":"立即购买"),9,cn)]),a("div",fn,[u[29]||(u[29]=Te('<div class="flex justify-between items-center" data-v-dcfca3ee><div data-v-dcfca3ee><h4 class="font-medium text-gray-900 dark:text-white" data-v-dcfca3ee>永久有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-dcfca3ee>一次购买，永久使用</p></div><div class="text-right" data-v-dcfca3ee><div class="flex items-center space-x-2" data-v-dcfca3ee><span class="text-xl font-semibold text-red-600 dark:text-red-400" data-v-dcfca3ee>199元</span><span class="text-sm text-gray-500 dark:text-gray-400 line-through" data-v-dcfca3ee>原价299元</span></div></div></div>',1)),a("button",{onClick:u[3]||(u[3]=I=>w(2)),disabled:T.value,class:"w-full mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},U(T.value?"创建订单中...":"立即购买"),9,gn)])])])])):q("",!0),M.value?(N(),S("div",hn,[a("div",mn,[a("div",yn,[u[31]||(u[31]=a("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 微信支付 ",-1)),a("button",{onClick:u[4]||(u[4]=()=>{M.value=!1,j()}),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},u[30]||(u[30]=[a("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),a("div",vn,[g.value?(N(),S("div",pn,[a("h4",bn,U(g.value.licenseType===0?"试用许可证":g.value.licenseType===1?"一年有效期许可证":"永久许可证"),1),a("p",wn,U(J(g.value.payPrice)),1),a("p",xn," 订单号: "+U(g.value.orderId),1)])):q("",!0),a("div",kn,[F.value?(N(),S("div",Cn,u[32]||(u[32]=[a("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),a("span",{class:"text-sm text-gray-600 dark:text-gray-300"},"生成二维码中...",-1)]))):R.value?(N(),S("div",En,[a("img",{src:R.value,alt:"微信支付二维码",class:"w-64 h-64"},null,8,_n)])):q("",!0)]),a("div",Bn,[u[34]||(u[34]=a("p",null,"请使用微信扫描二维码完成支付",-1)),u[35]||(u[35]=a("p",null,"支付成功后，您可以使用许可证生成激活码",-1)),B.value?(N(),S("div",An,u[33]||(u[33]=[a("div",{class:"flex items-center space-x-2"},[a("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),a("span",{class:"text-sm text-blue-600 dark:text-blue-400"}," 正在等待支付完成...请尽快完成支付 ")],-1)]))):q("",!0)])])])])):q("",!0)]))}}),Mn=Ct(Rn,[["__scopeId","data-v-dcfca3ee"]]),ct=Et(Mn);ct.use(_t());ct.mount("#app");
