
/**
 * Synchronous search function
 * @param {Object} params - Search parameters
 * @param {string} params.processId - Process ID
 * @param {string} params.threadId - Thread ID
 * @param {string} params.rootFunc - Root function
 * @param {string} params.currCallId - Current call ID
 * @param {Object} params.namedSelector - Named selector data
 * @returns {Object} Search response data
 */
async function searchCallIds(params) {
    const postData = {
        processId: params.processId,
        threadId: params.threadId,
        rootFunc: params.rootFunc,
        currCallId: params.currCallId,
        namedSelector: params.namedSelector
    };

    try {
        const response = await axios.post("./search", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        throw error;
    }
}

/**
 * Get function call detail
 * @param {Object} params - Function call parameters
 * @param {string} params.processId - Process ID
 * @param {string} params.threadId - Thread ID
 * @param {string} params.callId - Call ID
 * @returns {Object} Function call detail response data
 */
async function getFunctionCallDetail(params) {
    const postData = {
        processId: params.processId,
        threadId: params.threadId,
        callId: params.callId
    };

    try {
        const response = await axios.post("./functionCallDetail", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        throw error;
    }
}

/**
 * Get object detail
 * @param {Object} params - Object detail parameters
 * @param {string} params.processId - Process ID
 * @param {string} params.threadId - Thread ID
 * @param {string} params.callId - Call ID
 * @param {string} params.labelKey - Label key
 * @param {string} params.maxVersion - Max version
 * @param {string} params.uniqId - Unique ID
 * @param {number} params.depth - Depth level (default: 1)
 * @returns {Object} Object detail response data
 */
async function getObjectDetail(params) {
    const postData = {
        processId: params.processId,
        threadId: params.threadId,
        callId: params.callId,
        labelKey: params.labelKey,
        maxVersion: params.maxVersion,
        uniqId: params.uniqId,
        depth: params.depth === undefined ? 1 : params.depth
    };

    try {
        const response = await axios.post("./objectDetail", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        throw error;
    }
}

