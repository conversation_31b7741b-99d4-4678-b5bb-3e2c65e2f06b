import{i as ft,u as gt,r as F,v as ht,z as mt,X as yt,Y as vt,j as I,o as S,l as a,m as V,t as U,F as bt,k as pt,n as Pe,B as Te,Z as wt,$ as xt,a0 as kt,_ as Ct,S as Et,T as _t}from"./style-B61I5Qk-.js";import{u as Bt}from"./activation-CThAJnRv.js";function At(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Z={},ie,Ie;function Rt(){return Ie||(Ie=1,ie=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),ie}var ae={},H={},Se;function G(){if(Se)return H;Se=1;let n;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return H.getSymbolSize=function(r){if(!r)throw new Error('"version" cannot be null or undefined');if(r<1||r>40)throw new Error('"version" should be in range from 1 to 40');return r*4+17},H.getSymbolTotalCodewords=function(r){return o[r]},H.getBCHDigit=function(s){let r=0;for(;s!==0;)r++,s>>>=1;return r},H.setToSJISFunction=function(r){if(typeof r!="function")throw new Error('"toSJISFunc" is not a valid function.');n=r},H.isKanjiModeEnabled=function(){return typeof n<"u"},H.toSJIS=function(r){return n(r)},H}var le={},Ne;function Me(){return Ne||(Ne=1,function(n){n.L={bit:1},n.M={bit:0},n.Q={bit:3},n.H={bit:2};function o(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"l":case"low":return n.L;case"m":case"medium":return n.M;case"q":case"quartile":return n.Q;case"h":case"high":return n.H;default:throw new Error("Unknown EC Level: "+s)}}n.isValid=function(r){return r&&typeof r.bit<"u"&&r.bit>=0&&r.bit<4},n.from=function(r,e){if(n.isValid(r))return r;try{return o(r)}catch{return e}}}(le)),le}var ue,Le;function Mt(){if(Le)return ue;Le=1;function n(){this.buffer=[],this.length=0}return n.prototype={get:function(o){const s=Math.floor(o/8);return(this.buffer[s]>>>7-o%8&1)===1},put:function(o,s){for(let r=0;r<s;r++)this.putBit((o>>>s-r-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const s=Math.floor(this.length/8);this.buffer.length<=s&&this.buffer.push(0),o&&(this.buffer[s]|=128>>>this.length%8),this.length++}},ue=n,ue}var de,De;function Pt(){if(De)return de;De=1;function n(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return n.prototype.set=function(o,s,r,e){const t=o*this.size+s;this.data[t]=r,e&&(this.reservedBit[t]=!0)},n.prototype.get=function(o,s){return this.data[o*this.size+s]},n.prototype.xor=function(o,s,r){this.data[o*this.size+s]^=r},n.prototype.isReserved=function(o,s){return this.reservedBit[o*this.size+s]},de=n,de}var ce={},Fe;function Tt(){return Fe||(Fe=1,function(n){const o=G().getSymbolSize;n.getRowColCoords=function(r){if(r===1)return[];const e=Math.floor(r/7)+2,t=o(r),i=t===145?26:Math.ceil((t-13)/(2*e-2))*2,u=[t-7];for(let l=1;l<e-1;l++)u[l]=u[l-1]-i;return u.push(6),u.reverse()},n.getPositions=function(r){const e=[],t=n.getRowColCoords(r),i=t.length;for(let u=0;u<i;u++)for(let l=0;l<i;l++)u===0&&l===0||u===0&&l===i-1||u===i-1&&l===0||e.push([t[u],t[l]]);return e}}(ce)),ce}var fe={},Ue;function It(){if(Ue)return fe;Ue=1;const n=G().getSymbolSize,o=7;return fe.getPositions=function(r){const e=n(r);return[[0,0],[e-o,0],[0,e-o]]},fe}var ge={},qe;function St(){return qe||(qe=1,function(n){n.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};n.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},n.from=function(e){return n.isValid(e)?parseInt(e,10):void 0},n.getPenaltyN1=function(e){const t=e.size;let i=0,u=0,l=0,f=null,c=null;for(let E=0;E<t;E++){u=l=0,f=c=null;for(let p=0;p<t;p++){let g=e.get(E,p);g===f?u++:(u>=5&&(i+=o.N1+(u-5)),f=g,u=1),g=e.get(p,E),g===c?l++:(l>=5&&(i+=o.N1+(l-5)),c=g,l=1)}u>=5&&(i+=o.N1+(u-5)),l>=5&&(i+=o.N1+(l-5))}return i},n.getPenaltyN2=function(e){const t=e.size;let i=0;for(let u=0;u<t-1;u++)for(let l=0;l<t-1;l++){const f=e.get(u,l)+e.get(u,l+1)+e.get(u+1,l)+e.get(u+1,l+1);(f===4||f===0)&&i++}return i*o.N2},n.getPenaltyN3=function(e){const t=e.size;let i=0,u=0,l=0;for(let f=0;f<t;f++){u=l=0;for(let c=0;c<t;c++)u=u<<1&2047|e.get(f,c),c>=10&&(u===1488||u===93)&&i++,l=l<<1&2047|e.get(c,f),c>=10&&(l===1488||l===93)&&i++}return i*o.N3},n.getPenaltyN4=function(e){let t=0;const i=e.data.length;for(let l=0;l<i;l++)t+=e.data[l];return Math.abs(Math.ceil(t*100/i/5)-10)*o.N4};function s(r,e,t){switch(r){case n.Patterns.PATTERN000:return(e+t)%2===0;case n.Patterns.PATTERN001:return e%2===0;case n.Patterns.PATTERN010:return t%3===0;case n.Patterns.PATTERN011:return(e+t)%3===0;case n.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(t/3))%2===0;case n.Patterns.PATTERN101:return e*t%2+e*t%3===0;case n.Patterns.PATTERN110:return(e*t%2+e*t%3)%2===0;case n.Patterns.PATTERN111:return(e*t%3+(e+t)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}n.applyMask=function(e,t){const i=t.size;for(let u=0;u<i;u++)for(let l=0;l<i;l++)t.isReserved(l,u)||t.xor(l,u,s(e,l,u))},n.getBestMask=function(e,t){const i=Object.keys(n.Patterns).length;let u=0,l=1/0;for(let f=0;f<i;f++){t(f),n.applyMask(f,e);const c=n.getPenaltyN1(e)+n.getPenaltyN2(e)+n.getPenaltyN3(e)+n.getPenaltyN4(e);n.applyMask(f,e),c<l&&(l=c,u=f)}return u}}(ge)),ge}var oe={},je;function at(){if(je)return oe;je=1;const n=Me(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return oe.getBlocksCount=function(e,t){switch(t){case n.L:return o[(e-1)*4+0];case n.M:return o[(e-1)*4+1];case n.Q:return o[(e-1)*4+2];case n.H:return o[(e-1)*4+3];default:return}},oe.getTotalCodewordsCount=function(e,t){switch(t){case n.L:return s[(e-1)*4+0];case n.M:return s[(e-1)*4+1];case n.Q:return s[(e-1)*4+2];case n.H:return s[(e-1)*4+3];default:return}},oe}var he={},ee={},ze;function Nt(){if(ze)return ee;ze=1;const n=new Uint8Array(512),o=new Uint8Array(256);return function(){let r=1;for(let e=0;e<255;e++)n[e]=r,o[r]=e,r<<=1,r&256&&(r^=285);for(let e=255;e<512;e++)n[e]=n[e-255]}(),ee.log=function(r){if(r<1)throw new Error("log("+r+")");return o[r]},ee.exp=function(r){return n[r]},ee.mul=function(r,e){return r===0||e===0?0:n[o[r]+o[e]]},ee}var Ve;function Lt(){return Ve||(Ve=1,function(n){const o=Nt();n.mul=function(r,e){const t=new Uint8Array(r.length+e.length-1);for(let i=0;i<r.length;i++)for(let u=0;u<e.length;u++)t[i+u]^=o.mul(r[i],e[u]);return t},n.mod=function(r,e){let t=new Uint8Array(r);for(;t.length-e.length>=0;){const i=t[0];for(let l=0;l<e.length;l++)t[l]^=o.mul(e[l],i);let u=0;for(;u<t.length&&t[u]===0;)u++;t=t.slice(u)}return t},n.generateECPolynomial=function(r){let e=new Uint8Array([1]);for(let t=0;t<r;t++)e=n.mul(e,new Uint8Array([1,o.exp(t)]));return e}}(he)),he}var me,Oe;function Dt(){if(Oe)return me;Oe=1;const n=Lt();function o(s){this.genPoly=void 0,this.degree=s,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(r){this.degree=r,this.genPoly=n.generateECPolynomial(this.degree)},o.prototype.encode=function(r){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(r.length+this.degree);e.set(r);const t=n.mod(e,this.genPoly),i=this.degree-t.length;if(i>0){const u=new Uint8Array(this.degree);return u.set(t,i),u}return t},me=o,me}var ye={},ve={},be={},Ke;function lt(){return Ke||(Ke=1,be.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),be}var K={},He;function ut(){if(He)return K;He=1;const n="[0-9]+",o="[A-Z $%*+\\-./:]+";let s="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";s=s.replace(/u/g,"\\u");const r="(?:(?![A-Z0-9 $%*+\\-./:]|"+s+`)(?:.|[\r
]))+`;K.KANJI=new RegExp(s,"g"),K.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),K.BYTE=new RegExp(r,"g"),K.NUMERIC=new RegExp(n,"g"),K.ALPHANUMERIC=new RegExp(o,"g");const e=new RegExp("^"+s+"$"),t=new RegExp("^"+n+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return K.testKanji=function(l){return e.test(l)},K.testNumeric=function(l){return t.test(l)},K.testAlphanumeric=function(l){return i.test(l)},K}var Je;function Q(){return Je||(Je=1,function(n){const o=lt(),s=ut();n.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},n.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},n.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},n.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},n.MIXED={bit:-1},n.getCharCountIndicator=function(t,i){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!o.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?t.ccBits[0]:i<27?t.ccBits[1]:t.ccBits[2]},n.getBestModeForData=function(t){return s.testNumeric(t)?n.NUMERIC:s.testAlphanumeric(t)?n.ALPHANUMERIC:s.testKanji(t)?n.KANJI:n.BYTE},n.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},n.isValid=function(t){return t&&t.bit&&t.ccBits};function r(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return n.NUMERIC;case"alphanumeric":return n.ALPHANUMERIC;case"kanji":return n.KANJI;case"byte":return n.BYTE;default:throw new Error("Unknown mode: "+e)}}n.from=function(t,i){if(n.isValid(t))return t;try{return r(t)}catch{return i}}}(ve)),ve}var Ye;function Ft(){return Ye||(Ye=1,function(n){const o=G(),s=at(),r=Me(),e=Q(),t=lt(),i=7973,u=o.getBCHDigit(i);function l(p,g,R){for(let M=1;M<=40;M++)if(g<=n.getCapacity(M,R,p))return M}function f(p,g){return e.getCharCountIndicator(p,g)+4}function c(p,g){let R=0;return p.forEach(function(M){const D=f(M.mode,g);R+=D+M.getBitsLength()}),R}function E(p,g){for(let R=1;R<=40;R++)if(c(p,R)<=n.getCapacity(R,g,e.MIXED))return R}n.from=function(g,R){return t.isValid(g)?parseInt(g,10):R},n.getCapacity=function(g,R,M){if(!t.isValid(g))throw new Error("Invalid QR Code version");typeof M>"u"&&(M=e.BYTE);const D=o.getSymbolTotalCodewords(g),B=s.getTotalCodewordsCount(g,R),T=(D-B)*8;if(M===e.MIXED)return T;const A=T-f(M,g);switch(M){case e.NUMERIC:return Math.floor(A/10*3);case e.ALPHANUMERIC:return Math.floor(A/11*2);case e.KANJI:return Math.floor(A/13);case e.BYTE:default:return Math.floor(A/8)}},n.getBestVersionForData=function(g,R){let M;const D=r.from(R,r.M);if(Array.isArray(g)){if(g.length>1)return E(g,D);if(g.length===0)return 1;M=g[0]}else M=g;return l(M.mode,M.getLength(),D)},n.getEncodedBits=function(g){if(!t.isValid(g)||g<7)throw new Error("Invalid QR Code version");let R=g<<12;for(;o.getBCHDigit(R)-u>=0;)R^=i<<o.getBCHDigit(R)-u;return g<<12|R}}(ye)),ye}var pe={},Ge;function Ut(){if(Ge)return pe;Ge=1;const n=G(),o=1335,s=21522,r=n.getBCHDigit(o);return pe.getEncodedBits=function(t,i){const u=t.bit<<3|i;let l=u<<10;for(;n.getBCHDigit(l)-r>=0;)l^=o<<n.getBCHDigit(l)-r;return(u<<10|l)^s},pe}var we={},xe,Qe;function qt(){if(Qe)return xe;Qe=1;const n=Q();function o(s){this.mode=n.NUMERIC,this.data=s.toString()}return o.getBitsLength=function(r){return 10*Math.floor(r/3)+(r%3?r%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(r){let e,t,i;for(e=0;e+3<=this.data.length;e+=3)t=this.data.substr(e,3),i=parseInt(t,10),r.put(i,10);const u=this.data.length-e;u>0&&(t=this.data.substr(e),i=parseInt(t,10),r.put(i,u*3+1))},xe=o,xe}var ke,$e;function jt(){if($e)return ke;$e=1;const n=Q(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(r){this.mode=n.ALPHANUMERIC,this.data=r}return s.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let i=o.indexOf(this.data[t])*45;i+=o.indexOf(this.data[t+1]),e.put(i,11)}this.data.length%2&&e.put(o.indexOf(this.data[t]),6)},ke=s,ke}var Ce,Xe;function zt(){if(Xe)return Ce;Xe=1;const n=Q();function o(s){this.mode=n.BYTE,typeof s=="string"?this.data=new TextEncoder().encode(s):this.data=new Uint8Array(s)}return o.getBitsLength=function(r){return r*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(s){for(let r=0,e=this.data.length;r<e;r++)s.put(this.data[r],8)},Ce=o,Ce}var Ee,Ze;function Vt(){if(Ze)return Ee;Ze=1;const n=Q(),o=G();function s(r){this.mode=n.KANJI,this.data=r}return s.getBitsLength=function(e){return e*13},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(r){let e;for(e=0;e<this.data.length;e++){let t=o.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else if(t>=57408&&t<=60351)t-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);t=(t>>>8&255)*192+(t&255),r.put(t,13)}},Ee=s,Ee}var _e={exports:{}},We;function Ot(){return We||(We=1,function(n){var o={single_source_shortest_paths:function(s,r,e){var t={},i={};i[r]=0;var u=o.PriorityQueue.make();u.push(r,0);for(var l,f,c,E,p,g,R,M,D;!u.empty();){l=u.pop(),f=l.value,E=l.cost,p=s[f]||{};for(c in p)p.hasOwnProperty(c)&&(g=p[c],R=E+g,M=i[c],D=typeof i[c]>"u",(D||M>R)&&(i[c]=R,u.push(c,R),t[c]=f))}if(typeof e<"u"&&typeof i[e]>"u"){var B=["Could not find a path from ",r," to ",e,"."].join("");throw new Error(B)}return t},extract_shortest_path_from_predecessor_list:function(s,r){for(var e=[],t=r;t;)e.push(t),s[t],t=s[t];return e.reverse(),e},find_path:function(s,r,e){var t=o.single_source_shortest_paths(s,r,e);return o.extract_shortest_path_from_predecessor_list(t,e)},PriorityQueue:{make:function(s){var r=o.PriorityQueue,e={},t;s=s||{};for(t in r)r.hasOwnProperty(t)&&(e[t]=r[t]);return e.queue=[],e.sorter=s.sorter||r.default_sorter,e},default_sorter:function(s,r){return s.cost-r.cost},push:function(s,r){var e={value:s,cost:r};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};n.exports=o}(_e)),_e.exports}var et;function Kt(){return et||(et=1,function(n){const o=Q(),s=qt(),r=jt(),e=zt(),t=Vt(),i=ut(),u=G(),l=Ot();function f(B){return unescape(encodeURIComponent(B)).length}function c(B,T,A){const k=[];let N;for(;(N=B.exec(A))!==null;)k.push({data:N[0],index:N.index,mode:T,length:N[0].length});return k}function E(B){const T=c(i.NUMERIC,o.NUMERIC,B),A=c(i.ALPHANUMERIC,o.ALPHANUMERIC,B);let k,N;return u.isKanjiModeEnabled()?(k=c(i.BYTE,o.BYTE,B),N=c(i.KANJI,o.KANJI,B)):(k=c(i.BYTE_KANJI,o.BYTE,B),N=[]),T.concat(A,k,N).sort(function(b,v){return b.index-v.index}).map(function(b){return{data:b.data,mode:b.mode,length:b.length}})}function p(B,T){switch(T){case o.NUMERIC:return s.getBitsLength(B);case o.ALPHANUMERIC:return r.getBitsLength(B);case o.KANJI:return t.getBitsLength(B);case o.BYTE:return e.getBitsLength(B)}}function g(B){return B.reduce(function(T,A){const k=T.length-1>=0?T[T.length-1]:null;return k&&k.mode===A.mode?(T[T.length-1].data+=A.data,T):(T.push(A),T)},[])}function R(B){const T=[];for(let A=0;A<B.length;A++){const k=B[A];switch(k.mode){case o.NUMERIC:T.push([k,{data:k.data,mode:o.ALPHANUMERIC,length:k.length},{data:k.data,mode:o.BYTE,length:k.length}]);break;case o.ALPHANUMERIC:T.push([k,{data:k.data,mode:o.BYTE,length:k.length}]);break;case o.KANJI:T.push([k,{data:k.data,mode:o.BYTE,length:f(k.data)}]);break;case o.BYTE:T.push([{data:k.data,mode:o.BYTE,length:f(k.data)}])}}return T}function M(B,T){const A={},k={start:{}};let N=["start"];for(let h=0;h<B.length;h++){const b=B[h],v=[];for(let y=0;y<b.length;y++){const _=b[y],w=""+h+y;v.push(w),A[w]={node:_,lastCount:0},k[w]={};for(let C=0;C<N.length;C++){const x=N[C];A[x]&&A[x].node.mode===_.mode?(k[x][w]=p(A[x].lastCount+_.length,_.mode)-p(A[x].lastCount,_.mode),A[x].lastCount+=_.length):(A[x]&&(A[x].lastCount=_.length),k[x][w]=p(_.length,_.mode)+4+o.getCharCountIndicator(_.mode,T))}}N=v}for(let h=0;h<N.length;h++)k[N[h]].end=0;return{map:k,table:A}}function D(B,T){let A;const k=o.getBestModeForData(B);if(A=o.from(T,k),A!==o.BYTE&&A.bit<k.bit)throw new Error('"'+B+'" cannot be encoded with mode '+o.toString(A)+`.
 Suggested mode is: `+o.toString(k));switch(A===o.KANJI&&!u.isKanjiModeEnabled()&&(A=o.BYTE),A){case o.NUMERIC:return new s(B);case o.ALPHANUMERIC:return new r(B);case o.KANJI:return new t(B);case o.BYTE:return new e(B)}}n.fromArray=function(T){return T.reduce(function(A,k){return typeof k=="string"?A.push(D(k,null)):k.data&&A.push(D(k.data,k.mode)),A},[])},n.fromString=function(T,A){const k=E(T,u.isKanjiModeEnabled()),N=R(k),h=M(N,A),b=l.find_path(h.map,"start","end"),v=[];for(let y=1;y<b.length-1;y++)v.push(h.table[b[y]].node);return n.fromArray(g(v))},n.rawSplit=function(T){return n.fromArray(E(T,u.isKanjiModeEnabled()))}}(we)),we}var tt;function Ht(){if(tt)return ae;tt=1;const n=G(),o=Me(),s=Mt(),r=Pt(),e=Tt(),t=It(),i=St(),u=at(),l=Dt(),f=Ft(),c=Ut(),E=Q(),p=Kt();function g(h,b){const v=h.size,y=t.getPositions(b);for(let _=0;_<y.length;_++){const w=y[_][0],C=y[_][1];for(let x=-1;x<=7;x++)if(!(w+x<=-1||v<=w+x))for(let P=-1;P<=7;P++)C+P<=-1||v<=C+P||(x>=0&&x<=6&&(P===0||P===6)||P>=0&&P<=6&&(x===0||x===6)||x>=2&&x<=4&&P>=2&&P<=4?h.set(w+x,C+P,!0,!0):h.set(w+x,C+P,!1,!0))}}function R(h){const b=h.size;for(let v=8;v<b-8;v++){const y=v%2===0;h.set(v,6,y,!0),h.set(6,v,y,!0)}}function M(h,b){const v=e.getPositions(b);for(let y=0;y<v.length;y++){const _=v[y][0],w=v[y][1];for(let C=-2;C<=2;C++)for(let x=-2;x<=2;x++)C===-2||C===2||x===-2||x===2||C===0&&x===0?h.set(_+C,w+x,!0,!0):h.set(_+C,w+x,!1,!0)}}function D(h,b){const v=h.size,y=f.getEncodedBits(b);let _,w,C;for(let x=0;x<18;x++)_=Math.floor(x/3),w=x%3+v-8-3,C=(y>>x&1)===1,h.set(_,w,C,!0),h.set(w,_,C,!0)}function B(h,b,v){const y=h.size,_=c.getEncodedBits(b,v);let w,C;for(w=0;w<15;w++)C=(_>>w&1)===1,w<6?h.set(w,8,C,!0):w<8?h.set(w+1,8,C,!0):h.set(y-15+w,8,C,!0),w<8?h.set(8,y-w-1,C,!0):w<9?h.set(8,15-w-1+1,C,!0):h.set(8,15-w-1,C,!0);h.set(y-8,8,1,!0)}function T(h,b){const v=h.size;let y=-1,_=v-1,w=7,C=0;for(let x=v-1;x>0;x-=2)for(x===6&&x--;;){for(let P=0;P<2;P++)if(!h.isReserved(_,x-P)){let j=!1;C<b.length&&(j=(b[C]>>>w&1)===1),h.set(_,x-P,j),w--,w===-1&&(C++,w=7)}if(_+=y,_<0||v<=_){_-=y,y=-y;break}}}function A(h,b,v){const y=new s;v.forEach(function(P){y.put(P.mode.bit,4),y.put(P.getLength(),E.getCharCountIndicator(P.mode,h)),P.write(y)});const _=n.getSymbolTotalCodewords(h),w=u.getTotalCodewordsCount(h,b),C=(_-w)*8;for(y.getLengthInBits()+4<=C&&y.put(0,4);y.getLengthInBits()%8!==0;)y.putBit(0);const x=(C-y.getLengthInBits())/8;for(let P=0;P<x;P++)y.put(P%2?17:236,8);return k(y,h,b)}function k(h,b,v){const y=n.getSymbolTotalCodewords(b),_=u.getTotalCodewordsCount(b,v),w=y-_,C=u.getBlocksCount(b,v),x=y%C,P=C-x,j=Math.floor(y/C),q=Math.floor(w/C),te=q+1,re=j-q,ne=new l(re);let J=0;const Y=new Array(C),m=new Array(C);let d=0;const L=new Uint8Array(h.buffer);for(let X=0;X<C;X++){const se=X<P?q:te;Y[X]=L.slice(J,J+se),m[X]=ne.encode(Y[X]),J+=se,d=Math.max(d,se)}const $=new Uint8Array(y);let W=0,z,O;for(z=0;z<d;z++)for(O=0;O<C;O++)z<Y[O].length&&($[W++]=Y[O][z]);for(z=0;z<re;z++)for(O=0;O<C;O++)$[W++]=m[O][z];return $}function N(h,b,v,y){let _;if(Array.isArray(h))_=p.fromArray(h);else if(typeof h=="string"){let j=b;if(!j){const q=p.rawSplit(h);j=f.getBestVersionForData(q,v)}_=p.fromString(h,j||40)}else throw new Error("Invalid data");const w=f.getBestVersionForData(_,v);if(!w)throw new Error("The amount of data is too big to be stored in a QR Code");if(!b)b=w;else if(b<w)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+w+`.
`);const C=A(b,v,_),x=n.getSymbolSize(b),P=new r(x);return g(P,b),R(P),M(P,b),B(P,v,0),b>=7&&D(P,b),T(P,C),isNaN(y)&&(y=i.getBestMask(P,B.bind(null,P,v))),i.applyMask(y,P),B(P,v,y),{modules:P,version:b,errorCorrectionLevel:v,maskPattern:y,segments:_}}return ae.create=function(b,v){if(typeof b>"u"||b==="")throw new Error("No input text");let y=o.M,_,w;return typeof v<"u"&&(y=o.from(v.errorCorrectionLevel,o.M),_=f.from(v.version),w=i.from(v.maskPattern),v.toSJISFunc&&n.setToSJISFunction(v.toSJISFunc)),N(b,_,y,w)},ae}var Be={},Ae={},rt;function dt(){return rt||(rt=1,function(n){function o(s){if(typeof s=="number"&&(s=s.toString()),typeof s!="string")throw new Error("Color should be defined as hex string");let r=s.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+s);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(t){return[t,t]}))),r.length===6&&r.push("F","F");const e=parseInt(r.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+r.slice(0,6).join("")}}n.getOptions=function(r){r||(r={}),r.color||(r.color={});const e=typeof r.margin>"u"||r.margin===null||r.margin<0?4:r.margin,t=r.width&&r.width>=21?r.width:void 0,i=r.scale||4;return{width:t,scale:t?4:i,margin:e,color:{dark:o(r.color.dark||"#000000ff"),light:o(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},n.getScale=function(r,e){return e.width&&e.width>=r+e.margin*2?e.width/(r+e.margin*2):e.scale},n.getImageWidth=function(r,e){const t=n.getScale(r,e);return Math.floor((r+e.margin*2)*t)},n.qrToImageData=function(r,e,t){const i=e.modules.size,u=e.modules.data,l=n.getScale(i,t),f=Math.floor((i+t.margin*2)*l),c=t.margin*l,E=[t.color.light,t.color.dark];for(let p=0;p<f;p++)for(let g=0;g<f;g++){let R=(p*f+g)*4,M=t.color.light;if(p>=c&&g>=c&&p<f-c&&g<f-c){const D=Math.floor((p-c)/l),B=Math.floor((g-c)/l);M=E[u[D*i+B]?1:0]}r[R++]=M.r,r[R++]=M.g,r[R++]=M.b,r[R]=M.a}}}(Ae)),Ae}var nt;function Jt(){return nt||(nt=1,function(n){const o=dt();function s(e,t,i){e.clearRect(0,0,t.width,t.height),t.style||(t.style={}),t.height=i,t.width=i,t.style.height=i+"px",t.style.width=i+"px"}function r(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}n.render=function(t,i,u){let l=u,f=i;typeof l>"u"&&(!i||!i.getContext)&&(l=i,i=void 0),i||(f=r()),l=o.getOptions(l);const c=o.getImageWidth(t.modules.size,l),E=f.getContext("2d"),p=E.createImageData(c,c);return o.qrToImageData(p.data,t,l),s(E,f,c),E.putImageData(p,0,0),f},n.renderToDataURL=function(t,i,u){let l=u;typeof l>"u"&&(!i||!i.getContext)&&(l=i,i=void 0),l||(l={});const f=n.render(t,i,l),c=l.type||"image/png",E=l.rendererOpts||{};return f.toDataURL(c,E.quality)}}(Be)),Be}var Re={},ot;function Yt(){if(ot)return Re;ot=1;const n=dt();function o(e,t){const i=e.a/255,u=t+'="'+e.hex+'"';return i<1?u+" "+t+'-opacity="'+i.toFixed(2).slice(1)+'"':u}function s(e,t,i){let u=e+t;return typeof i<"u"&&(u+=" "+i),u}function r(e,t,i){let u="",l=0,f=!1,c=0;for(let E=0;E<e.length;E++){const p=Math.floor(E%t),g=Math.floor(E/t);!p&&!f&&(f=!0),e[E]?(c++,E>0&&p>0&&e[E-1]||(u+=f?s("M",p+i,.5+g+i):s("m",l,0),l=0,f=!1),p+1<t&&e[E+1]||(u+=s("h",c),c=0)):l++}return u}return Re.render=function(t,i,u){const l=n.getOptions(i),f=t.modules.size,c=t.modules.data,E=f+l.margin*2,p=l.color.light.a?"<path "+o(l.color.light,"fill")+' d="M0 0h'+E+"v"+E+'H0z"/>':"",g="<path "+o(l.color.dark,"stroke")+' d="'+r(c,f,l.margin)+'"/>',R='viewBox="0 0 '+E+" "+E+'"',D='<svg xmlns="http://www.w3.org/2000/svg" '+(l.width?'width="'+l.width+'" height="'+l.width+'" ':"")+R+' shape-rendering="crispEdges">'+p+g+`</svg>
`;return typeof u=="function"&&u(null,D),D},Re}var st;function Gt(){if(st)return Z;st=1;const n=Rt(),o=Ht(),s=Jt(),r=Yt();function e(t,i,u,l,f){const c=[].slice.call(arguments,1),E=c.length,p=typeof c[E-1]=="function";if(!p&&!n())throw new Error("Callback required as last argument");if(p){if(E<2)throw new Error("Too few arguments provided");E===2?(f=u,u=i,i=l=void 0):E===3&&(i.getContext&&typeof f>"u"?(f=l,l=void 0):(f=l,l=u,u=i,i=void 0))}else{if(E<1)throw new Error("Too few arguments provided");return E===1?(u=i,i=l=void 0):E===2&&!i.getContext&&(l=u,u=i,i=void 0),new Promise(function(g,R){try{const M=o.create(u,l);g(t(M,i,l))}catch(M){R(M)}})}try{const g=o.create(u,l);f(null,t(g,i,l))}catch(g){f(g)}}return Z.create=o.create,Z.toCanvas=e.bind(null,s.render),Z.toDataURL=e.bind(null,s.renderToDataURL),Z.toString=e.bind(null,function(t,i,u){return r.render(t,u)}),Z}var Qt=Gt();const $t=At(Qt),Xt={class:"h-screen w-full flex flex-col overflow-x-hidden bg-gray-50 dark:bg-gray-900"},Zt={class:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4"},Wt={class:"max-w-4xl mx-auto flex justify-between items-center"},er={class:"flex items-center space-x-4"},tr={key:0,class:"flex items-center space-x-2"},rr={key:1,class:"flex items-center space-x-4"},nr={class:"text-sm"},or={class:"ml-1 font-medium text-gray-900 dark:text-white"},sr={class:"text-sm"},ir={class:"ml-1 font-medium text-gray-900 dark:text-white"},ar={key:2,class:"text-sm text-gray-500 dark:text-gray-400"},lr={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},ur={class:"max-w-4xl mx-auto"},dr={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},cr={class:"ml-11"},fr={class:"bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900 dark:to-red-900 border border-orange-200 dark:border-orange-700 rounded-lg p-4 mb-4"},gr={class:"flex items-start space-x-3"},hr={class:"flex-1"},mr={class:"flex items-center space-x-2"},yr=["disabled"],vr={key:0,class:"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg"},br={key:1,class:"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg"},pr={class:"flex items-center space-x-2"},wr={class:"text-sm text-red-700 dark:text-red-300"},xr={key:2,class:"text-center py-8"},kr={key:3,class:"space-y-4"},Cr=["onClick"],Er={class:"flex items-center space-x-3"},_r={class:"flex-shrink-0"},Br={key:0,class:"w-2 h-2 rounded-full bg-white"},Ar={class:"flex-1 flex justify-between items-center"},Rr={class:"font-medium text-gray-900 dark:text-white"},Mr={class:"text-sm text-gray-500 dark:text-gray-400"},Pr={class:"text-right"},Tr={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ir={class:"text-xs text-gray-500 dark:text-gray-400"},Sr={key:4,class:"text-center py-8"},Nr={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},Lr={class:"ml-11"},Dr={class:"text-center mb-4"},Fr=["disabled"],Ur={key:0,class:"text-center"},qr={class:"text-sm text-gray-500 dark:text-gray-400"},jr={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},zr={class:"ml-11"},Vr={key:0,class:"mb-4 p-4 border border-blue-500 bg-blue-50 dark:bg-blue-900 rounded-lg"},Or={class:"flex items-center space-x-3"},Kr={class:"flex-1 flex justify-between items-center"},Hr={class:"font-medium text-gray-900 dark:text-white"},Jr={class:"text-sm text-gray-500 dark:text-gray-400"},Yr={class:"text-right"},Gr={class:"text-lg font-semibold text-gray-900 dark:text-white"},Qr={class:"text-xs text-gray-500 dark:text-gray-400"},$r={key:1,class:"text-center py-8"},Xr={key:2,class:"space-y-4"},Zr={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600"},Wr={key:0,class:"text-center"},en={key:0,class:"text-sm text-green-600 dark:text-green-400 font-medium"},tn={key:1,class:"text-sm text-red-600 dark:text-red-400 font-medium"},rn={key:1,class:"text-center"},nn={key:3,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},on={key:4,class:"text-center py-8"},sn={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},an={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},ln={class:"flex justify-between items-center mb-4"},un={class:"space-y-4"},dn={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},cn=["disabled"],fn={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},gn=["disabled"],hn={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},mn={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},yn={class:"flex justify-between items-center mb-4"},vn={class:"text-center space-y-4"},bn={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},pn={class:"font-medium text-gray-900 dark:text-white mb-2"},wn={class:"text-2xl font-bold text-green-600 dark:text-green-400"},xn={class:"text-sm text-gray-500 dark:text-gray-400"},kn={class:"flex justify-center"},Cn={key:0,class:"flex items-center space-x-2"},En={key:1,class:"bg-white p-4 rounded-lg"},_n=["src"],Bn={class:"text-sm text-gray-600 dark:text-gray-300 space-y-2"},An={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg"},it=300,Rn=ft({__name:"Activated",setup(n){const o=gt(),s=Bt(),r=F(""),e=F(""),t=F(!1),i=F(""),u=F(null),l=F(!1),f=F([]),c=F(null),E=F(!1),p=F(!1),g=F(null),R=F(""),M=F(!1),D=F(!1),B=F(!1),T=F(!1),A=F(!1),k=F(null),N=F(0),h=F(!1),b=F(""),v=F(!1);ht(()=>{o.initTheme();const d=new URLSearchParams(window.location.search).get("deviceCode");d?r.value=d:i.value="未找到设备码参数",_()}),mt(()=>{j()});const y=async()=>{if(!c.value){i.value="请先选择一个许可证";return}try{t.value=!0,i.value="";const m=await s.generateActivateCode(r.value,c.value.orderId);m&&(e.value=m,v.value=!0)}catch(m){i.value=m instanceof Error?m.message:"生成激活码失败",console.error("Failed to generate activate code:",m)}finally{t.value=!1}},_=async()=>{try{l.value=!0;const[m,d]=await Promise.all([yt(),vt()]);m.success&&m.data?u.value=m.data:console.error("Failed to load user info:",m.error),d.success?f.value=d.data||[]:console.error("Failed to load licenses:",d.error)}catch(m){console.error("Failed to load user info:",m)}finally{l.value=!1}},w=m=>{c.value=m},C=async m=>{m===1?B.value=!0:m===2&&(T.value=!0);try{const L=await wt({licenseType:m,payMethod:"wechat",payPrice:m===0?0:m===1?9900:19900});L.success?(g.value=L.data,E.value=!1,M.value=!0,await x(L.data.wxCodeUrl),P(L.data.orderId)):console.error("Failed to create order:",L.error)}catch(d){console.error("Failed to create order:",d)}finally{m===1?B.value=!1:m===2&&(T.value=!1)}},x=async m=>{try{D.value=!0;const d=await $t.toDataURL(m,{width:256,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});R.value=d}catch(d){console.error("Failed to generate QR code:",d)}finally{D.value=!1}},P=m=>{if(A.value)return;A.value=!0,N.value=0;const d=async()=>{try{N.value++,console.log(`Polling order status, attempt ${N.value}`),(await kt({orderId:m})).success?(console.log("Order payment successful!"),j(),M.value=!1,await _(),alert("支付成功! 您现在可以使用许可证生成激活码了！")):N.value>=it?(console.log("Max polling count reached, stopping polling"),j(),alert("支付超时，请重新创建订单。")):k.value=setTimeout(d,1e3)}catch(L){console.error("Error polling order status:",L),N.value>=it?(j(),alert("检查订单状态失败，请手动刷新页面。")):k.value=setTimeout(d,1e3)}};d()},j=()=>{k.value&&(clearTimeout(k.value),k.value=null),A.value=!1,N.value=0},q=F(""),te=async()=>{try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(e.value),q.value="复制成功!",console.log("Activate code copied to clipboard");else{const m=document.createElement("textarea");m.value=e.value,m.style.position="fixed",m.style.left="-999999px",m.style.top="-999999px",document.body.appendChild(m),m.focus(),m.select();const d=document.execCommand("copy");document.body.removeChild(m),d?(q.value="复制成功!",console.log("Activate code copied to clipboard")):(q.value="复制失败，请手动复制",console.error("Failed to copy activate code"))}setTimeout(()=>{q.value=""},3e3)}catch(m){q.value="复制失败，请手动复制",console.error("Failed to copy activate code:",m)}},re=async()=>{try{h.value=!0,b.value="";const m=await xt();m.success?(await _(),alert("试用许可证申请成功！您现在可以使用许可证生成激活码了！")):b.value=m.error||"申请试用许可证失败"}catch(m){b.value=m instanceof Error?m.message:"申请试用许可证失败",console.error("Failed to apply trial license:",m)}finally{h.value=!1}},ne=m=>m===0?"试用许可证":m===1?"一年有效期":m===2?"永久有效期":"未知类型",J=m=>m===0?"免费":(m/100).toFixed(0)+"元",Y=m=>new Date(m).toLocaleDateString("zh-CN");return(m,d)=>(S(),I("div",Xt,[a("div",Zt,[a("div",Wt,[a("div",er,[d[8]||(d[8]=a("div",{class:"flex items-center space-x-2"},[a("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})]),a("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"用户信息")],-1)),l.value?(S(),I("div",tr,d[5]||(d[5]=[a("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1),a("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"加载中...",-1)]))):u.value?(S(),I("div",rr,[a("div",nr,[d[6]||(d[6]=a("span",{class:"text-gray-500 dark:text-gray-400"},"ID:",-1)),a("span",or,U(u.value.uniqueId),1)]),a("div",sr,[d[7]||(d[7]=a("span",{class:"text-gray-500 dark:text-gray-400"},"昵称:",-1)),a("span",ir,U(u.value.username),1)])])):(S(),I("div",ar," 未获取到用户信息 "))]),a("div",{class:"flex items-center space-x-2"},[a("button",{onClick:_,class:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200"}," 刷新 ")])])]),a("div",lr,[a("div",ur,[d[25]||(d[25]=a("div",{class:"text-center mb-8"},[a("h1",{class:"text-3xl font-bold mb-4 text-gray-900 dark:text-white"}," XCodeMap 激活码生成 ")],-1)),a("div",dr,[d[17]||(d[17]=a("div",{class:"flex items-center mb-4"},[a("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 1 "),a("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第一步 选择许可证 ")],-1)),a("div",cr,[a("div",fr,[a("div",gr,[d[11]||(d[11]=a("div",{class:"flex-shrink-0 mt-1"},[a("svg",{class:"w-5 h-5 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),a("div",hr,[d[9]||(d[9]=a("h4",{class:"font-medium text-orange-800 dark:text-orange-200 mb-2"}," 🚨 项目需要您的支持！ ",-1)),d[10]||(d[10]=a("p",{class:"text-sm text-orange-700 dark:text-orange-300 mb-3 leading-relaxed"}," 再不收费，项目就要黄了(╥﹏╥)，请购买许可证，以支持 XCodeMap 的持续发展，为开发者创造颠覆性的源码调试体验。 ",-1)),a("div",mr,[a("button",{onClick:d[0]||(d[0]=L=>E.value=!0),class:"px-6 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md transition-colors duration-200 shadow-sm"}," 💝 立即购买许可证，限时优惠不可错过 "),a("button",{onClick:re,disabled:h.value,class:"px-4 py-2 bg-gray-800 hover:bg-gray-900 disabled:bg-gray-400 text-white text-xs font-medium rounded-md transition-colors duration-200 shadow-sm"},U(h.value?"申请中...":"申请试用许可证"),9,yr)])])])]),d[16]||(d[16]=a("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," 请选择一个可用的许可证来生成激活码 ",-1)),v.value?(S(),I("div",vr,d[12]||(d[12]=[a("div",{class:"flex items-center space-x-2"},[a("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})]),a("span",{class:"text-sm text-yellow-700 dark:text-yellow-300 font-medium"}," 已生成激活码，每个许可证仅可激活一次 ")],-1)]))):V("",!0),b.value?(S(),I("div",br,[a("div",pr,[d[13]||(d[13]=a("svg",{class:"w-4 h-4 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),a("span",wr,U(b.value),1)])])):V("",!0),p.value?(S(),I("div",xr,d[14]||(d[14]=[a("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),a("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在加载许可证...",-1)]))):f.value.length>0?(S(),I("div",kr,[(S(!0),I(bt,null,pt(f.value,L=>{var $,W,z;return S(),I("div",{key:L.id,onClick:O=>v.value?null:w(L),class:Pe(["p-4 border rounded-lg transition-colors duration-200",v.value?"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-60":(($=c.value)==null?void 0:$.id)===L.id?"border-blue-500 bg-blue-50 dark:bg-blue-900 cursor-pointer":"border-gray-300 dark:border-gray-600 hover:border-blue-300 cursor-pointer"])},[a("div",Er,[a("div",_r,[a("div",{class:Pe(["w-5 h-5 rounded-full border-2 flex items-center justify-center",((W=c.value)==null?void 0:W.id)===L.id?"border-blue-600 bg-blue-600":"border-gray-400 dark:border-gray-500"])},[((z=c.value)==null?void 0:z.id)===L.id?(S(),I("div",Br)):V("",!0)],2)]),a("div",Ar,[a("div",null,[a("h3",Rr,U(ne(L.licenseType)),1),a("p",Mr," 过期时间: "+U(Y(L.activationExpiredTime)),1)]),a("div",Pr,[a("p",Tr,U(J(L.payPrice)),1),a("p",Ir," 订单号: "+U(L.orderId),1)])])])],10,Cr)}),128))])):(S(),I("div",Sr,d[15]||(d[15]=[a("p",{class:"text-gray-500 dark:text-gray-400"},"暂无可用许可证",-1)])))])]),a("div",Nr,[d[18]||(d[18]=a("div",{class:"flex items-center mb-4"},[a("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 2 "),a("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第二步 生成激活码 ")],-1)),a("div",Lr,[a("div",Dr,[a("button",{onClick:y,disabled:t.value||!c.value||v.value,class:"px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-md transition-colors duration-200"},U(t.value?"正在生成激活码...":v.value?"已生成激活码":"生成激活码"),9,Fr)]),c.value?V("",!0):(S(),I("div",Ur,[a("p",qr,U(v.value?"已生成激活码，无法再次生成":"请先选择许可证"),1)]))])]),a("div",jr,[d[24]||(d[24]=a("div",{class:"flex items-center mb-4"},[a("div",{class:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold mr-3"}," 3 "),a("div",null,[a("h3",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 第三步 获取激活码 "),a("p",{class:"text-sm text-gray-500 dark:text-gray-400 mt-1"}," 请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ")])],-1)),a("div",zr,[c.value&&e.value?(S(),I("div",Vr,[a("div",Or,[d[19]||(d[19]=a("div",{class:"flex-shrink-0"},[a("div",{class:"w-5 h-5 rounded-full border-2 border-blue-600 bg-blue-600 flex items-center justify-center"},[a("div",{class:"w-2 h-2 rounded-full bg-white"})])],-1)),a("div",Kr,[a("div",null,[a("h3",Hr,U(ne(c.value.licenseType)),1),a("p",Jr," 过期时间: "+U(Y(c.value.activationExpiredTime)),1)]),a("div",Yr,[a("p",Gr,U(J(c.value.payPrice)),1),a("p",Qr," 订单号: "+U(c.value.orderId),1)])])])])):V("",!0),t.value?(S(),I("div",$r,d[20]||(d[20]=[a("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),a("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在生成激活码...",-1)]))):e.value&&!t.value?(S(),I("div",Xr,[a("div",Zr,[a("code",{class:"activate-code-text text-sm font-mono break-all text-gray-900 dark:text-gray-100 font-medium select-all cursor-pointer",onClick:te,title:"点击复制激活码"},U(e.value),1)]),a("div",{class:"flex justify-center"},[a("button",{onClick:te,class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"}," 复制激活码 ")]),q.value?(S(),I("div",Wr,[q.value==="复制成功!"?(S(),I("span",en," ✓ "+U(q.value),1)):(S(),I("span",tn," ✗ "+U(q.value),1))])):V("",!0),q.value==="复制失败，请手动复制"?(S(),I("div",rn,d[21]||(d[21]=[a("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 您可以点击上方的激活码文本进行手动复制 ",-1)]))):V("",!0),d[22]||(d[22]=a("div",{class:"text-center"},[a("p",{class:"text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-700"}," ✨ 激活码已生成，请复制此激活码输入到 XCodeMap 插件激活界面完成激活 ✨ ")],-1))])):i.value?(S(),I("div",nn,U(i.value),1)):(S(),I("div",on,d[23]||(d[23]=[a("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请先完成前两步操作 ",-1)])))])])])]),E.value?(S(),I("div",sn,[a("div",an,[a("div",ln,[d[27]||(d[27]=a("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 购买许可证 ",-1)),a("button",{onClick:d[1]||(d[1]=L=>E.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},d[26]||(d[26]=[a("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),a("div",un,[a("div",dn,[d[28]||(d[28]=Te('<div class="flex justify-between items-center" data-v-cc681389><div data-v-cc681389><h4 class="font-medium text-gray-900 dark:text-white" data-v-cc681389>一年有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-cc681389>适合短期使用</p></div><div class="text-right" data-v-cc681389><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-cc681389>99元</p></div></div>',1)),a("button",{onClick:d[2]||(d[2]=L=>C(1)),disabled:B.value,class:"w-full mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},U(B.value?"创建订单中...":"立即购买"),9,cn)]),a("div",fn,[d[29]||(d[29]=Te('<div class="flex justify-between items-center" data-v-cc681389><div data-v-cc681389><h4 class="font-medium text-gray-900 dark:text-white" data-v-cc681389>永久有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-cc681389>一次购买，永久使用</p></div><div class="text-right" data-v-cc681389><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-cc681389>199元</p></div></div>',1)),a("button",{onClick:d[3]||(d[3]=L=>C(2)),disabled:T.value,class:"w-full mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},U(T.value?"创建订单中...":"立即购买"),9,gn)])])])])):V("",!0),M.value?(S(),I("div",hn,[a("div",mn,[a("div",yn,[d[31]||(d[31]=a("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 微信支付 ",-1)),a("button",{onClick:d[4]||(d[4]=L=>M.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},d[30]||(d[30]=[a("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),a("div",vn,[g.value?(S(),I("div",bn,[a("h4",pn,U(g.value.licenseType===0?"试用许可证":g.value.licenseType===1?"一年有效期许可证":"永久许可证"),1),a("p",wn,U(J(g.value.payPrice)),1),a("p",xn," 订单号: "+U(g.value.orderId),1)])):V("",!0),a("div",kn,[D.value?(S(),I("div",Cn,d[32]||(d[32]=[a("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),a("span",{class:"text-sm text-gray-600 dark:text-gray-300"},"生成二维码中...",-1)]))):R.value?(S(),I("div",En,[a("img",{src:R.value,alt:"微信支付二维码",class:"w-64 h-64"},null,8,_n)])):V("",!0)]),a("div",Bn,[d[34]||(d[34]=a("p",null,"请使用微信扫描二维码完成支付",-1)),d[35]||(d[35]=a("p",null,"支付成功后，您可以使用许可证生成激活码",-1)),A.value?(S(),I("div",An,d[33]||(d[33]=[a("div",{class:"flex items-center space-x-2"},[a("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),a("span",{class:"text-sm text-blue-600 dark:text-blue-400"}," 正在等待支付完成...请尽快完成支付 ")],-1)]))):V("",!0)])])])])):V("",!0)]))}}),Mn=Ct(Rn,[["__scopeId","data-v-cc681389"]]),ct=Et(Mn);ct.use(_t());ct.mount("#app");
