<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>XCodeMap Detail</title>
    <link href="../assets/element-ui-2.15.14/index.min.css" rel="stylesheet">
    <link href="../assets/xcodemap/theme.css" rel="stylesheet">
    <script src="../assets/xcodemap/common.js"></script>
</head>
<body>
<div id="app">
    <el-container>
        <el-main>
            <div class="main-container">
                <div class="search-tab graph-popup-content">
                    <el-scrollbar>
                        <el-tabs type="border-card" class="search-tab-header" v-model="searchTab.activeTabName" @tab-click="searchTab.inputChange">
                            <el-tab-pane label="函数" name="funcName"></el-tab-pane>
                            <el-tab-pane label="类" name="class"></el-tab-pane>
                            <el-tab-pane label="对象" name="objectUsage"></el-tab-pane>
                            <el-tab-pane label="值" name="value"></el-tab-pane>
                            <el-tab-pane label="历史" name="history"></el-tab-pane>
                        </el-tabs>
                        <div class="search-tab-content">
                            <div v-if="searchTab.activeTabName === 'funcName'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchGlobal(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{searchTab.names[index].name}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'class'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchGlobal(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{searchTab.names[index].name}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'objectUsage'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchGlobal(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{searchTab.names[index].name}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'value'">
                                <el-input v-model="searchTab.searchText" @input="searchTab.inputChange"></el-input>
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.names">
                                        <div class="search-result-item" @click="searchTab.searchGlobal(searchTab.names[index], true)">
                                            <el-tag>{{searchTab.names[index].type}}</el-tag>
                                            {{formatText(searchTab.names[index].name)}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="searchTab.activeTabName === 'history'">
                                <div class="search-tab-content-result">
                                    <div :key="index"  v-for="(item, index) in searchTab.history">
                                        <div class="search-result-item" @click="searchTab.searchGlobal(searchTab.history[index], true)">
                                            <el-tag>{{searchTab.history[index].type}}</el-tag>
                                            {{formatText(searchTab.history[index].name)}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="search-global-result-data" v-show="searchTab.globalResult.show">
                            <strong>找到相关请求 {{searchTab.globalResult.count}} 个</strong>
                            <el-tree
                                    :accordion="true"
                                    :data="searchTab.globalResult.reqTree.treeData"
                                    :highlight-current="true"
                                    :indent="1"
                                    :default-expanded-keys="searchTab.globalResult.reqTree.defaultExpandedKeys"
                                    :props="searchTab.globalResult.reqTree.defaultProps"
                                    @node-click="searchTab.globalResult.reqTree.clickTreeNode"
                                    class="filter-tree"
                                    icon-class="none"
                                    node-key="nodeKey"
                                    ref="reqTree">
                                <span slot-scope="{ node, data }">
                                    <i :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"
                                       v-if="data.leaf===false"></i>
                                    <i class="el-icon-document" v-else></i>
                                    <span :id="data.nodeKey">{{ node.label}}</span>
                                </span>
                            </el-tree>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </el-main>
    </el-container>
</div>
</body>

<script src="../assets/axios-1.6.3/axios.min.js"></script>
<!-- 导入 Mermaid 库 -->
<script src="../assets/vue-2.5.22/vue.min.js"></script>
<script src="../assets/element-ui-2.15.14/index.min.js"></script>
<script src="../assets/xcodemap/detail.graph.js"></script>



<script>
    var vue = new Vue({
        el: '#app',
        data: function () {
            return {
                project: NewProject(),
                searchTab: NewSearchTab(),
                leftAside: NewAside("left", ""),
                rightAside: NewAside("right", "")
            }
        },
        created() {
            console.log("this", this)
            this.project.doInitProjectInfo();
            document.addEventListener("mouseup", this.leftAside.mouseUp);
            document.addEventListener("mouseup", this.rightAside.mouseUp);
        },
        mounted() {
        },
        destroyed() {
            document.removeEventListener("mouseup", this.leftAside.mouseUp);
            document.removeEventListener("mouseup", this.rightAside.mouseUp);
        },
        methods: {
            handleChangeScopeCommand(command) {
                this.searchTab.changeScope(command)
            },
        }
    })
    if (vue.project.searchType !== null &&
        (vue.project.searchId !== null || vue.project.searchName != null)) {
        vue.searchTab.activeTabName = vue.project.searchType
        vue.searchTab.searchGlobal({
            "type": vue.project.searchType,
            "name": vue.project.searchName,
            "id": vue.project.searchId,
            "scope": vue.project.searchScope
        })
    }
</script>


<style>
    body {
        margin: 0px;
    }

    .el-scrollbar {
        height: 100vh;
    }

    .el-scrollbar__bar.is-vertical {
        width: 10px;
    }

    .el-scrollbar__bar.is-horizontal {
        height: 10px;
    }

    .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
        white-space: nowrap;
    }
</style>

<style>
    .el-tree-node {
        position: relative;
    }
    .el-tree-node::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        left: -3px;
        top: -26px;
        border-width: 1px;
        border-left: 1px dashed #52627c;
    }

    .el-tree-node:last-child::before {
        height: 38px;
    }
    .el-tree-node::after {
        content: '';
        width: 14px;
        height: 10px;
        position: absolute;
        left: -3px;
        top: 12px;
        border-width: 1px;
        border-top: 1px dashed #52627c;
    }
    .el-tree-node__label {
        white-space: normal;
        word-break: break-all;
    }
    .el-tree-node__children {
        padding-left: 10px;
    }

    .el-tree-node__content {
        height: inherit;
        padding-bottom: 5px;
    }
    .el-tree-node__content>.el-tree-node__expand-icon {
         padding: 1px;
    }
</style>

<style>
    /* all tabs related*/
    .graph-popup-content  .el-scrollbar {
        position: absolute;
        top: 1%;
        left: 15%;
        /* transform: translate(-50%, -50%); */
        width: 70%;
        height: calc(99% - 10px);
        background-color: #45494a;
        z-index: 1;
    }
    .search-tab-header >.el-tabs__header {
        background-color: #45494a;
        border-bottom: 1px solid #272626;
        margin: 0;
    }
    .search-tab-header>.el-tabs__header .el-tabs__item.is-active {
        color: #409EFF;
        background-color: #5d5b5b;
        border-right-color: #DCDFE6;
        border-left-color: #DCDFE6;
    }

    .search-result-item {
        color: gray;
    }
    .search-result-item:hover {
        color: #409EFF;
        cursor: pointer;
    }

    .el-tabs--border-card {
         border: 0px solid #DCDFE6;
    }

    .el-tabs--border-card>.el-tabs__content {
         padding: 0px;
    }

    .el-tabs__content {
        padding: 0px;
    }
    .el-tabs {
        padding: 0 0px;
    }

    .el-tabs__nav-wrap {
        margin-left: 0px;
    }

    .el-tabs__header {
        margin: 0 0 24px;
    }
    .el-tabs__item.is-left, .el-tabs__item.is-right {
        width: 25px;
        height: 100px;
        padding: 0;
        margin: 0;
        writing-mode: vertical-lr; /* 将文字垂直显示，从左到右
        line-height: 25px;
    }

    .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
        text-align: center; /* 文字居中 */
    }

    .el-tabs--right.el-tabs--card .el-tabs__item.is-right {
        text-align: center; /* 文字居中 */
    }
    .el-tabs__item.is-active {
        opacity: 1;
    }
    .el-tabs__header {
        margin: 0;
    }
</style>

<style>
    #functionTraceGraphDetail {
        /*margin-top: 12px;*/
        margin-bottom: 32px;
    }

    .el-aside, .el-main, .el-header {
        height: 100%;
    }

    .el-container {
        width: 100%;
    }

    .el-header {
        padding: 0 32px;
        display: flex;
        /*
        justify-content: center; !* Center content horizontally *!
        */
        align-items: center; /* Center content vertically */
        height: 30px;
        margin-bottom: 5px;
    }

    .el-main {
        padding: 0px;
    }

    .el-breadcrumb {
        /*
        margin-top: 15px;
        */
        margin-left: 0px;
        font-size: 16px;
    }



    .el-table {
        margin-top: 24px;
    }



    .el-header, .el-tab-pane {
        white-space: pre-line;
    }

    .el-menu-item > a, .el-menu-item > * > a, .el-menu-item > * > * > a, .el-menu-item > * > * > * > a {
        text-decoration: none; /* 去掉下划线 */
    }

    .el-button > a, .el-button > * > a, .el-button > * > * > a, .el-button > * > * > * > a {
        text-decoration: none;
    }

    .codeCanvasDiv {
        height: 100%;
        width: 100%;
        overflow: auto;
    }

    .codeCanvasPre {
        width: 100%;
        overflow: auto;
    }

    .flowCanvasDiv {
        height: 100%;
        width: 550px;
        overflow: auto;
    }

    .left-aside {
        display: block;
        margin-right: 5px;
        justify-content: flex-end;
    }

    .left-tab-content {
        #width: calc(100% - 25px);
        margin-top: 5px;
        overflow-wrap: break-word;
    }

    .right-aside {
        display: block;
        margin-left: 5px;
        justify-content: flex-end;
    }

    .right-tab-content {
        #width: calc(100% - 25px);
        margin-top: 24px;
        overflow-wrap: break-word;
    }

    .usageListSelectDiv {
        width: 270px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .focus-on-token {
        text-decoration: underline;
    }



    .x-handle {
        width: 3px;
        cursor: ew-resize;
        z-index: 10;
        background: #ccc;
    }



    .custom-tab-left {
        position: fixed;
        width: 25px;
        height: 100px;
    }

    .custom-tab-right {
        position: fixed;
        width: 25px;
        height: 100px;
    }


    .main-container {
        position: relative;
        height: 100vh;
    }

    .left-container {
        position: relative;
        height: 100vh;
        /*
        margin-left: 25px;
        */
    }

    .right-container {
        position: relative;
        /*width: calc(100% - 25px);
        margin-right: 25px;*/
        height: 100vh;
    }

    .hide-icon-left {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: right;
        z-index: 2;
    }
    .hide-icon-right {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: left;
        z-index: 2;
    }

    .zdiv {
        margin: 0;
        border: 1px solid rgb(187, 186, 186);
        background: rgb(255, 255, 255);
        z-index: 1000;
        position: absolute;
        list-style-type: none;
        padding: 5px;
        border-radius: 7px;
        font-size: 12px;
    }

    .zdiv li {
        margin: 0;
        padding: 5px;
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .zdiv li:hover {
        background: #e1e1e1;
        cursor: pointer;
    }

    text.actor:hover, text.messageText:hover {
        cursor: pointer;
    }
</style>

<script>
    console.log("xcodemap-idea-plugin-ready");
</script>
</html>