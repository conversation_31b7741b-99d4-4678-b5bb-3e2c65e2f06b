import{i as ce,u as fe,r as F,v as ge,z as he,X as me,Y as ye,j as L,o as D,l as u,m as O,t as U,F as ve,k as we,n as pe,B as Tt,Z as be,$ as xe,_ as Ce,S as Ee,T as ke}from"./style-lyg0-ODQ.js";import{u as Be}from"./activation-CVDw6ARG.js";function _e(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Q={},ot,It;function Ae(){return It||(It=1,ot=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),ot}var st={},H={},Mt;function Y(){if(Mt)return H;Mt=1;let r;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return H.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17},H.getSymbolTotalCodewords=function(n){return o[n]},H.getBCHDigit=function(s){let n=0;for(;s!==0;)n++,s>>>=1;return n},H.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');r=n},H.isKanjiModeEnabled=function(){return typeof r<"u"},H.toSJIS=function(n){return r(n)},H}var it={},St;function Rt(){return St||(St=1,function(r){r.L={bit:1},r.M={bit:0},r.Q={bit:3},r.H={bit:2};function o(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"l":case"low":return r.L;case"m":case"medium":return r.M;case"q":case"quartile":return r.Q;case"h":case"high":return r.H;default:throw new Error("Unknown EC Level: "+s)}}r.isValid=function(n){return n&&typeof n.bit<"u"&&n.bit>=0&&n.bit<4},r.from=function(n,t){if(r.isValid(n))return n;try{return o(n)}catch{return t}}}(it)),it}var at,Nt;function Re(){if(Nt)return at;Nt=1;function r(){this.buffer=[],this.length=0}return r.prototype={get:function(o){const s=Math.floor(o/8);return(this.buffer[s]>>>7-o%8&1)===1},put:function(o,s){for(let n=0;n<s;n++)this.putBit((o>>>s-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const s=Math.floor(this.length/8);this.buffer.length<=s&&this.buffer.push(0),o&&(this.buffer[s]|=128>>>this.length%8),this.length++}},at=r,at}var lt,Lt;function Pe(){if(Lt)return lt;Lt=1;function r(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return r.prototype.set=function(o,s,n,t){const e=o*this.size+s;this.data[e]=n,t&&(this.reservedBit[e]=!0)},r.prototype.get=function(o,s){return this.data[o*this.size+s]},r.prototype.xor=function(o,s,n){this.data[o*this.size+s]^=n},r.prototype.isReserved=function(o,s){return this.reservedBit[o*this.size+s]},lt=r,lt}var ut={},Dt;function Te(){return Dt||(Dt=1,function(r){const o=Y().getSymbolSize;r.getRowColCoords=function(n){if(n===1)return[];const t=Math.floor(n/7)+2,e=o(n),i=e===145?26:Math.ceil((e-13)/(2*t-2))*2,l=[e-7];for(let a=1;a<t-1;a++)l[a]=l[a-1]-i;return l.push(6),l.reverse()},r.getPositions=function(n){const t=[],e=r.getRowColCoords(n),i=e.length;for(let l=0;l<i;l++)for(let a=0;a<i;a++)l===0&&a===0||l===0&&a===i-1||l===i-1&&a===0||t.push([e[l],e[a]]);return t}}(ut)),ut}var dt={},Ft;function Ie(){if(Ft)return dt;Ft=1;const r=Y().getSymbolSize,o=7;return dt.getPositions=function(n){const t=r(n);return[[0,0],[t-o,0],[0,t-o]]},dt}var ct={},Ut;function Me(){return Ut||(Ut=1,function(r){r.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};r.isValid=function(t){return t!=null&&t!==""&&!isNaN(t)&&t>=0&&t<=7},r.from=function(t){return r.isValid(t)?parseInt(t,10):void 0},r.getPenaltyN1=function(t){const e=t.size;let i=0,l=0,a=0,c=null,g=null;for(let E=0;E<e;E++){l=a=0,c=g=null;for(let y=0;y<e;y++){let f=t.get(E,y);f===c?l++:(l>=5&&(i+=o.N1+(l-5)),c=f,l=1),f=t.get(y,E),f===g?a++:(a>=5&&(i+=o.N1+(a-5)),g=f,a=1)}l>=5&&(i+=o.N1+(l-5)),a>=5&&(i+=o.N1+(a-5))}return i},r.getPenaltyN2=function(t){const e=t.size;let i=0;for(let l=0;l<e-1;l++)for(let a=0;a<e-1;a++){const c=t.get(l,a)+t.get(l,a+1)+t.get(l+1,a)+t.get(l+1,a+1);(c===4||c===0)&&i++}return i*o.N2},r.getPenaltyN3=function(t){const e=t.size;let i=0,l=0,a=0;for(let c=0;c<e;c++){l=a=0;for(let g=0;g<e;g++)l=l<<1&2047|t.get(c,g),g>=10&&(l===1488||l===93)&&i++,a=a<<1&2047|t.get(g,c),g>=10&&(a===1488||a===93)&&i++}return i*o.N3},r.getPenaltyN4=function(t){let e=0;const i=t.data.length;for(let a=0;a<i;a++)e+=t.data[a];return Math.abs(Math.ceil(e*100/i/5)-10)*o.N4};function s(n,t,e){switch(n){case r.Patterns.PATTERN000:return(t+e)%2===0;case r.Patterns.PATTERN001:return t%2===0;case r.Patterns.PATTERN010:return e%3===0;case r.Patterns.PATTERN011:return(t+e)%3===0;case r.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(e/3))%2===0;case r.Patterns.PATTERN101:return t*e%2+t*e%3===0;case r.Patterns.PATTERN110:return(t*e%2+t*e%3)%2===0;case r.Patterns.PATTERN111:return(t*e%3+(t+e)%2)%2===0;default:throw new Error("bad maskPattern:"+n)}}r.applyMask=function(t,e){const i=e.size;for(let l=0;l<i;l++)for(let a=0;a<i;a++)e.isReserved(a,l)||e.xor(a,l,s(t,a,l))},r.getBestMask=function(t,e){const i=Object.keys(r.Patterns).length;let l=0,a=1/0;for(let c=0;c<i;c++){e(c),r.applyMask(c,t);const g=r.getPenaltyN1(t)+r.getPenaltyN2(t)+r.getPenaltyN3(t)+r.getPenaltyN4(t);r.applyMask(c,t),g<a&&(a=g,l=c)}return l}}(ct)),ct}var Z={},qt;function se(){if(qt)return Z;qt=1;const r=Rt(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return Z.getBlocksCount=function(t,e){switch(e){case r.L:return o[(t-1)*4+0];case r.M:return o[(t-1)*4+1];case r.Q:return o[(t-1)*4+2];case r.H:return o[(t-1)*4+3];default:return}},Z.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return s[(t-1)*4+0];case r.M:return s[(t-1)*4+1];case r.Q:return s[(t-1)*4+2];case r.H:return s[(t-1)*4+3];default:return}},Z}var ft={},X={},zt;function Se(){if(zt)return X;zt=1;const r=new Uint8Array(512),o=new Uint8Array(256);return function(){let n=1;for(let t=0;t<255;t++)r[t]=n,o[n]=t,n<<=1,n&256&&(n^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),X.log=function(n){if(n<1)throw new Error("log("+n+")");return o[n]},X.exp=function(n){return r[n]},X.mul=function(n,t){return n===0||t===0?0:r[o[n]+o[t]]},X}var Vt;function Ne(){return Vt||(Vt=1,function(r){const o=Se();r.mul=function(n,t){const e=new Uint8Array(n.length+t.length-1);for(let i=0;i<n.length;i++)for(let l=0;l<t.length;l++)e[i+l]^=o.mul(n[i],t[l]);return e},r.mod=function(n,t){let e=new Uint8Array(n);for(;e.length-t.length>=0;){const i=e[0];for(let a=0;a<t.length;a++)e[a]^=o.mul(t[a],i);let l=0;for(;l<e.length&&e[l]===0;)l++;e=e.slice(l)}return e},r.generateECPolynomial=function(n){let t=new Uint8Array([1]);for(let e=0;e<n;e++)t=r.mul(t,new Uint8Array([1,o.exp(e)]));return t}}(ft)),ft}var gt,jt;function Le(){if(jt)return gt;jt=1;const r=Ne();function o(s){this.genPoly=void 0,this.degree=s,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(n){this.degree=n,this.genPoly=r.generateECPolynomial(this.degree)},o.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(n.length+this.degree);t.set(n);const e=r.mod(t,this.genPoly),i=this.degree-e.length;if(i>0){const l=new Uint8Array(this.degree);return l.set(e,i),l}return e},gt=o,gt}var ht={},mt={},yt={},Ot;function ie(){return Ot||(Ot=1,yt.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),yt}var z={},Kt;function ae(){if(Kt)return z;Kt=1;const r="[0-9]+",o="[A-Z $%*+\\-./:]+";let s="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";s=s.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+s+`)(?:.|[\r
]))+`;z.KANJI=new RegExp(s,"g"),z.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),z.BYTE=new RegExp(n,"g"),z.NUMERIC=new RegExp(r,"g"),z.ALPHANUMERIC=new RegExp(o,"g");const t=new RegExp("^"+s+"$"),e=new RegExp("^"+r+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return z.testKanji=function(a){return t.test(a)},z.testNumeric=function(a){return e.test(a)},z.testAlphanumeric=function(a){return i.test(a)},z}var Ht;function $(){return Ht||(Ht=1,function(r){const o=ie(),s=ae();r.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},r.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},r.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},r.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},r.MIXED={bit:-1},r.getCharCountIndicator=function(e,i){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!o.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?e.ccBits[0]:i<27?e.ccBits[1]:e.ccBits[2]},r.getBestModeForData=function(e){return s.testNumeric(e)?r.NUMERIC:s.testAlphanumeric(e)?r.ALPHANUMERIC:s.testKanji(e)?r.KANJI:r.BYTE},r.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},r.isValid=function(e){return e&&e.bit&&e.ccBits};function n(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return r.NUMERIC;case"alphanumeric":return r.ALPHANUMERIC;case"kanji":return r.KANJI;case"byte":return r.BYTE;default:throw new Error("Unknown mode: "+t)}}r.from=function(e,i){if(r.isValid(e))return e;try{return n(e)}catch{return i}}}(mt)),mt}var Jt;function De(){return Jt||(Jt=1,function(r){const o=Y(),s=se(),n=Rt(),t=$(),e=ie(),i=7973,l=o.getBCHDigit(i);function a(y,f,A){for(let T=1;T<=40;T++)if(f<=r.getCapacity(T,A,y))return T}function c(y,f){return t.getCharCountIndicator(y,f)+4}function g(y,f){let A=0;return y.forEach(function(T){const M=c(T.mode,f);A+=M+T.getBitsLength()}),A}function E(y,f){for(let A=1;A<=40;A++)if(g(y,A)<=r.getCapacity(A,f,t.MIXED))return A}r.from=function(f,A){return e.isValid(f)?parseInt(f,10):A},r.getCapacity=function(f,A,T){if(!e.isValid(f))throw new Error("Invalid QR Code version");typeof T>"u"&&(T=t.BYTE);const M=o.getSymbolTotalCodewords(f),B=s.getTotalCodewordsCount(f,A),R=(M-B)*8;if(T===t.MIXED)return R;const k=R-c(T,f);switch(T){case t.NUMERIC:return Math.floor(k/10*3);case t.ALPHANUMERIC:return Math.floor(k/11*2);case t.KANJI:return Math.floor(k/13);case t.BYTE:default:return Math.floor(k/8)}},r.getBestVersionForData=function(f,A){let T;const M=n.from(A,n.M);if(Array.isArray(f)){if(f.length>1)return E(f,M);if(f.length===0)return 1;T=f[0]}else T=f;return a(T.mode,T.getLength(),M)},r.getEncodedBits=function(f){if(!e.isValid(f)||f<7)throw new Error("Invalid QR Code version");let A=f<<12;for(;o.getBCHDigit(A)-l>=0;)A^=i<<o.getBCHDigit(A)-l;return f<<12|A}}(ht)),ht}var vt={},Yt;function Fe(){if(Yt)return vt;Yt=1;const r=Y(),o=1335,s=21522,n=r.getBCHDigit(o);return vt.getEncodedBits=function(e,i){const l=e.bit<<3|i;let a=l<<10;for(;r.getBCHDigit(a)-n>=0;)a^=o<<r.getBCHDigit(a)-n;return(l<<10|a)^s},vt}var wt={},pt,$t;function Ue(){if($t)return pt;$t=1;const r=$();function o(s){this.mode=r.NUMERIC,this.data=s.toString()}return o.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(n){let t,e,i;for(t=0;t+3<=this.data.length;t+=3)e=this.data.substr(t,3),i=parseInt(e,10),n.put(i,10);const l=this.data.length-t;l>0&&(e=this.data.substr(t),i=parseInt(e,10),n.put(i,l*3+1))},pt=o,pt}var bt,Gt;function qe(){if(Gt)return bt;Gt=1;const r=$(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(n){this.mode=r.ALPHANUMERIC,this.data=n}return s.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let i=o.indexOf(this.data[e])*45;i+=o.indexOf(this.data[e+1]),t.put(i,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},bt=s,bt}var xt,Qt;function ze(){if(Qt)return xt;Qt=1;const r=$();function o(s){this.mode=r.BYTE,typeof s=="string"?this.data=new TextEncoder().encode(s):this.data=new Uint8Array(s)}return o.getBitsLength=function(n){return n*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(s){for(let n=0,t=this.data.length;n<t;n++)s.put(this.data[n],8)},xt=o,xt}var Ct,Xt;function Ve(){if(Xt)return Ct;Xt=1;const r=$(),o=Y();function s(n){this.mode=r.KANJI,this.data=n}return s.getBitsLength=function(t){return t*13},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(n){let t;for(t=0;t<this.data.length;t++){let e=o.toSJIS(this.data[t]);if(e>=33088&&e<=40956)e-=33088;else if(e>=57408&&e<=60351)e-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);e=(e>>>8&255)*192+(e&255),n.put(e,13)}},Ct=s,Ct}var Et={exports:{}},Zt;function je(){return Zt||(Zt=1,function(r){var o={single_source_shortest_paths:function(s,n,t){var e={},i={};i[n]=0;var l=o.PriorityQueue.make();l.push(n,0);for(var a,c,g,E,y,f,A,T,M;!l.empty();){a=l.pop(),c=a.value,E=a.cost,y=s[c]||{};for(g in y)y.hasOwnProperty(g)&&(f=y[g],A=E+f,T=i[g],M=typeof i[g]>"u",(M||T>A)&&(i[g]=A,l.push(g,A),e[g]=c))}if(typeof t<"u"&&typeof i[t]>"u"){var B=["Could not find a path from ",n," to ",t,"."].join("");throw new Error(B)}return e},extract_shortest_path_from_predecessor_list:function(s,n){for(var t=[],e=n;e;)t.push(e),s[e],e=s[e];return t.reverse(),t},find_path:function(s,n,t){var e=o.single_source_shortest_paths(s,n,t);return o.extract_shortest_path_from_predecessor_list(e,t)},PriorityQueue:{make:function(s){var n=o.PriorityQueue,t={},e;s=s||{};for(e in n)n.hasOwnProperty(e)&&(t[e]=n[e]);return t.queue=[],t.sorter=s.sorter||n.default_sorter,t},default_sorter:function(s,n){return s.cost-n.cost},push:function(s,n){var t={value:s,cost:n};this.queue.push(t),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};r.exports=o}(Et)),Et.exports}var Wt;function Oe(){return Wt||(Wt=1,function(r){const o=$(),s=Ue(),n=qe(),t=ze(),e=Ve(),i=ae(),l=Y(),a=je();function c(B){return unescape(encodeURIComponent(B)).length}function g(B,R,k){const w=[];let N;for(;(N=B.exec(k))!==null;)w.push({data:N[0],index:N.index,mode:R,length:N[0].length});return w}function E(B){const R=g(i.NUMERIC,o.NUMERIC,B),k=g(i.ALPHANUMERIC,o.ALPHANUMERIC,B);let w,N;return l.isKanjiModeEnabled()?(w=g(i.BYTE,o.BYTE,B),N=g(i.KANJI,o.KANJI,B)):(w=g(i.BYTE_KANJI,o.BYTE,B),N=[]),R.concat(k,w,N).sort(function(x,C){return x.index-C.index}).map(function(x){return{data:x.data,mode:x.mode,length:x.length}})}function y(B,R){switch(R){case o.NUMERIC:return s.getBitsLength(B);case o.ALPHANUMERIC:return n.getBitsLength(B);case o.KANJI:return e.getBitsLength(B);case o.BYTE:return t.getBitsLength(B)}}function f(B){return B.reduce(function(R,k){const w=R.length-1>=0?R[R.length-1]:null;return w&&w.mode===k.mode?(R[R.length-1].data+=k.data,R):(R.push(k),R)},[])}function A(B){const R=[];for(let k=0;k<B.length;k++){const w=B[k];switch(w.mode){case o.NUMERIC:R.push([w,{data:w.data,mode:o.ALPHANUMERIC,length:w.length},{data:w.data,mode:o.BYTE,length:w.length}]);break;case o.ALPHANUMERIC:R.push([w,{data:w.data,mode:o.BYTE,length:w.length}]);break;case o.KANJI:R.push([w,{data:w.data,mode:o.BYTE,length:c(w.data)}]);break;case o.BYTE:R.push([{data:w.data,mode:o.BYTE,length:c(w.data)}])}}return R}function T(B,R){const k={},w={start:{}};let N=["start"];for(let m=0;m<B.length;m++){const x=B[m],C=[];for(let h=0;h<x.length;h++){const _=x[h],p=""+m+h;C.push(p),k[p]={node:_,lastCount:0},w[p]={};for(let v=0;v<N.length;v++){const b=N[v];k[b]&&k[b].node.mode===_.mode?(w[b][p]=y(k[b].lastCount+_.length,_.mode)-y(k[b].lastCount,_.mode),k[b].lastCount+=_.length):(k[b]&&(k[b].lastCount=_.length),w[b][p]=y(_.length,_.mode)+4+o.getCharCountIndicator(_.mode,R))}}N=C}for(let m=0;m<N.length;m++)w[N[m]].end=0;return{map:w,table:k}}function M(B,R){let k;const w=o.getBestModeForData(B);if(k=o.from(R,w),k!==o.BYTE&&k.bit<w.bit)throw new Error('"'+B+'" cannot be encoded with mode '+o.toString(k)+`.
 Suggested mode is: `+o.toString(w));switch(k===o.KANJI&&!l.isKanjiModeEnabled()&&(k=o.BYTE),k){case o.NUMERIC:return new s(B);case o.ALPHANUMERIC:return new n(B);case o.KANJI:return new e(B);case o.BYTE:return new t(B)}}r.fromArray=function(R){return R.reduce(function(k,w){return typeof w=="string"?k.push(M(w,null)):w.data&&k.push(M(w.data,w.mode)),k},[])},r.fromString=function(R,k){const w=E(R,l.isKanjiModeEnabled()),N=A(w),m=T(N,k),x=a.find_path(m.map,"start","end"),C=[];for(let h=1;h<x.length-1;h++)C.push(m.table[x[h]].node);return r.fromArray(f(C))},r.rawSplit=function(R){return r.fromArray(E(R,l.isKanjiModeEnabled()))}}(wt)),wt}var te;function Ke(){if(te)return st;te=1;const r=Y(),o=Rt(),s=Re(),n=Pe(),t=Te(),e=Ie(),i=Me(),l=se(),a=Le(),c=De(),g=Fe(),E=$(),y=Oe();function f(m,x){const C=m.size,h=e.getPositions(x);for(let _=0;_<h.length;_++){const p=h[_][0],v=h[_][1];for(let b=-1;b<=7;b++)if(!(p+b<=-1||C<=p+b))for(let P=-1;P<=7;P++)v+P<=-1||C<=v+P||(b>=0&&b<=6&&(P===0||P===6)||P>=0&&P<=6&&(b===0||b===6)||b>=2&&b<=4&&P>=2&&P<=4?m.set(p+b,v+P,!0,!0):m.set(p+b,v+P,!1,!0))}}function A(m){const x=m.size;for(let C=8;C<x-8;C++){const h=C%2===0;m.set(C,6,h,!0),m.set(6,C,h,!0)}}function T(m,x){const C=t.getPositions(x);for(let h=0;h<C.length;h++){const _=C[h][0],p=C[h][1];for(let v=-2;v<=2;v++)for(let b=-2;b<=2;b++)v===-2||v===2||b===-2||b===2||v===0&&b===0?m.set(_+v,p+b,!0,!0):m.set(_+v,p+b,!1,!0)}}function M(m,x){const C=m.size,h=c.getEncodedBits(x);let _,p,v;for(let b=0;b<18;b++)_=Math.floor(b/3),p=b%3+C-8-3,v=(h>>b&1)===1,m.set(_,p,v,!0),m.set(p,_,v,!0)}function B(m,x,C){const h=m.size,_=g.getEncodedBits(x,C);let p,v;for(p=0;p<15;p++)v=(_>>p&1)===1,p<6?m.set(p,8,v,!0):p<8?m.set(p+1,8,v,!0):m.set(h-15+p,8,v,!0),p<8?m.set(8,h-p-1,v,!0):p<9?m.set(8,15-p-1+1,v,!0):m.set(8,15-p-1,v,!0);m.set(h-8,8,1,!0)}function R(m,x){const C=m.size;let h=-1,_=C-1,p=7,v=0;for(let b=C-1;b>0;b-=2)for(b===6&&b--;;){for(let P=0;P<2;P++)if(!m.isReserved(_,b-P)){let q=!1;v<x.length&&(q=(x[v]>>>p&1)===1),m.set(_,b-P,q),p--,p===-1&&(v++,p=7)}if(_+=h,_<0||C<=_){_-=h,h=-h;break}}}function k(m,x,C){const h=new s;C.forEach(function(P){h.put(P.mode.bit,4),h.put(P.getLength(),E.getCharCountIndicator(P.mode,m)),P.write(h)});const _=r.getSymbolTotalCodewords(m),p=l.getTotalCodewordsCount(m,x),v=(_-p)*8;for(h.getLengthInBits()+4<=v&&h.put(0,4);h.getLengthInBits()%8!==0;)h.putBit(0);const b=(v-h.getLengthInBits())/8;for(let P=0;P<b;P++)h.put(P%2?17:236,8);return w(h,m,x)}function w(m,x,C){const h=r.getSymbolTotalCodewords(x),_=l.getTotalCodewordsCount(x,C),p=h-_,v=l.getBlocksCount(x,C),b=h%v,P=v-b,q=Math.floor(h/v),K=Math.floor(p/v),W=K+1,I=q-K,d=new a(I);let S=0;const J=new Array(v),tt=new Array(v);let et=0;const de=new Uint8Array(m.buffer);for(let G=0;G<v;G++){const rt=G<P?K:W;J[G]=de.slice(S,S+rt),tt[G]=d.encode(J[G]),S+=rt,et=Math.max(et,rt)}const nt=new Uint8Array(h);let Pt=0,V,j;for(V=0;V<et;V++)for(j=0;j<v;j++)V<J[j].length&&(nt[Pt++]=J[j][V]);for(V=0;V<I;V++)for(j=0;j<v;j++)nt[Pt++]=tt[j][V];return nt}function N(m,x,C,h){let _;if(Array.isArray(m))_=y.fromArray(m);else if(typeof m=="string"){let q=x;if(!q){const K=y.rawSplit(m);q=c.getBestVersionForData(K,C)}_=y.fromString(m,q||40)}else throw new Error("Invalid data");const p=c.getBestVersionForData(_,C);if(!p)throw new Error("The amount of data is too big to be stored in a QR Code");if(!x)x=p;else if(x<p)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+p+`.
`);const v=k(x,C,_),b=r.getSymbolSize(x),P=new n(b);return f(P,x),A(P),T(P,x),B(P,C,0),x>=7&&M(P,x),R(P,v),isNaN(h)&&(h=i.getBestMask(P,B.bind(null,P,C))),i.applyMask(h,P),B(P,C,h),{modules:P,version:x,errorCorrectionLevel:C,maskPattern:h,segments:_}}return st.create=function(x,C){if(typeof x>"u"||x==="")throw new Error("No input text");let h=o.M,_,p;return typeof C<"u"&&(h=o.from(C.errorCorrectionLevel,o.M),_=c.from(C.version),p=i.from(C.maskPattern),C.toSJISFunc&&r.setToSJISFunction(C.toSJISFunc)),N(x,_,h,p)},st}var kt={},Bt={},ee;function le(){return ee||(ee=1,function(r){function o(s){if(typeof s=="number"&&(s=s.toString()),typeof s!="string")throw new Error("Color should be defined as hex string");let n=s.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+s);(n.length===3||n.length===4)&&(n=Array.prototype.concat.apply([],n.map(function(e){return[e,e]}))),n.length===6&&n.push("F","F");const t=parseInt(n.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:t&255,hex:"#"+n.slice(0,6).join("")}}r.getOptions=function(n){n||(n={}),n.color||(n.color={});const t=typeof n.margin>"u"||n.margin===null||n.margin<0?4:n.margin,e=n.width&&n.width>=21?n.width:void 0,i=n.scale||4;return{width:e,scale:e?4:i,margin:t,color:{dark:o(n.color.dark||"#000000ff"),light:o(n.color.light||"#ffffffff")},type:n.type,rendererOpts:n.rendererOpts||{}}},r.getScale=function(n,t){return t.width&&t.width>=n+t.margin*2?t.width/(n+t.margin*2):t.scale},r.getImageWidth=function(n,t){const e=r.getScale(n,t);return Math.floor((n+t.margin*2)*e)},r.qrToImageData=function(n,t,e){const i=t.modules.size,l=t.modules.data,a=r.getScale(i,e),c=Math.floor((i+e.margin*2)*a),g=e.margin*a,E=[e.color.light,e.color.dark];for(let y=0;y<c;y++)for(let f=0;f<c;f++){let A=(y*c+f)*4,T=e.color.light;if(y>=g&&f>=g&&y<c-g&&f<c-g){const M=Math.floor((y-g)/a),B=Math.floor((f-g)/a);T=E[l[M*i+B]?1:0]}n[A++]=T.r,n[A++]=T.g,n[A++]=T.b,n[A]=T.a}}}(Bt)),Bt}var ne;function He(){return ne||(ne=1,function(r){const o=le();function s(t,e,i){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=i,e.width=i,e.style.height=i+"px",e.style.width=i+"px"}function n(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}r.render=function(e,i,l){let a=l,c=i;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),i||(c=n()),a=o.getOptions(a);const g=o.getImageWidth(e.modules.size,a),E=c.getContext("2d"),y=E.createImageData(g,g);return o.qrToImageData(y.data,e,a),s(E,c,g),E.putImageData(y,0,0),c},r.renderToDataURL=function(e,i,l){let a=l;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),a||(a={});const c=r.render(e,i,a),g=a.type||"image/png",E=a.rendererOpts||{};return c.toDataURL(g,E.quality)}}(kt)),kt}var _t={},re;function Je(){if(re)return _t;re=1;const r=le();function o(t,e){const i=t.a/255,l=e+'="'+t.hex+'"';return i<1?l+" "+e+'-opacity="'+i.toFixed(2).slice(1)+'"':l}function s(t,e,i){let l=t+e;return typeof i<"u"&&(l+=" "+i),l}function n(t,e,i){let l="",a=0,c=!1,g=0;for(let E=0;E<t.length;E++){const y=Math.floor(E%e),f=Math.floor(E/e);!y&&!c&&(c=!0),t[E]?(g++,E>0&&y>0&&t[E-1]||(l+=c?s("M",y+i,.5+f+i):s("m",a,0),a=0,c=!1),y+1<e&&t[E+1]||(l+=s("h",g),g=0)):a++}return l}return _t.render=function(e,i,l){const a=r.getOptions(i),c=e.modules.size,g=e.modules.data,E=c+a.margin*2,y=a.color.light.a?"<path "+o(a.color.light,"fill")+' d="M0 0h'+E+"v"+E+'H0z"/>':"",f="<path "+o(a.color.dark,"stroke")+' d="'+n(g,c,a.margin)+'"/>',A='viewBox="0 0 '+E+" "+E+'"',M='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+A+' shape-rendering="crispEdges">'+y+f+`</svg>
`;return typeof l=="function"&&l(null,M),M},_t}var oe;function Ye(){if(oe)return Q;oe=1;const r=Ae(),o=Ke(),s=He(),n=Je();function t(e,i,l,a,c){const g=[].slice.call(arguments,1),E=g.length,y=typeof g[E-1]=="function";if(!y&&!r())throw new Error("Callback required as last argument");if(y){if(E<2)throw new Error("Too few arguments provided");E===2?(c=l,l=i,i=a=void 0):E===3&&(i.getContext&&typeof c>"u"?(c=a,a=void 0):(c=a,a=l,l=i,i=void 0))}else{if(E<1)throw new Error("Too few arguments provided");return E===1?(l=i,i=a=void 0):E===2&&!i.getContext&&(a=l,l=i,i=void 0),new Promise(function(f,A){try{const T=o.create(l,a);f(e(T,i,a))}catch(T){A(T)}})}try{const f=o.create(l,a);c(null,e(f,i,a))}catch(f){c(f)}}return Q.create=o.create,Q.toCanvas=t.bind(null,s.render),Q.toDataURL=t.bind(null,s.renderToDataURL),Q.toString=t.bind(null,function(e,i,l){return n.render(e,l)}),Q}var $e=Ye();const Ge=_e($e),Qe={class:"h-screen w-full flex flex-col overflow-x-hidden bg-gray-50 dark:bg-gray-900"},Xe={class:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4"},Ze={class:"max-w-4xl mx-auto flex justify-between items-center"},We={class:"flex items-center space-x-4"},tn={key:0,class:"flex items-center space-x-2"},en={key:1,class:"flex items-center space-x-4"},nn={class:"text-sm"},rn={class:"ml-1 font-medium text-gray-900 dark:text-white"},on={class:"text-sm"},sn={class:"ml-1 font-medium text-gray-900 dark:text-white"},an={key:2,class:"text-sm text-gray-500 dark:text-gray-400"},ln={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},un={class:"max-w-4xl mx-auto"},dn={class:"text-center mb-8"},cn={class:"text-lg text-gray-700 dark:text-gray-200"},fn={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},gn={class:"flex justify-between items-center mb-4"},hn={key:0,class:"text-center py-8"},mn={key:1,class:"space-y-4"},yn=["onClick"],vn={class:"flex justify-between items-center"},wn={class:"font-medium text-gray-900 dark:text-white"},pn={class:"text-sm text-gray-500 dark:text-gray-400"},bn={class:"text-right"},xn={class:"text-lg font-semibold text-gray-900 dark:text-white"},Cn={class:"text-xs text-gray-500 dark:text-gray-400"},En={key:2,class:"text-center py-8"},kn={class:"text-center mb-6"},Bn=["disabled"],_n={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},An={class:"space-y-6"},Rn={key:0,class:"text-center py-8"},Pn={key:1,class:"space-y-4"},Tn={class:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600"},In={class:"text-sm font-mono break-all text-gray-900 dark:text-gray-100 font-medium"},Mn={key:2,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},Sn={key:0,class:"text-center"},Nn=["disabled"],Ln={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Dn={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},Fn={class:"flex justify-between items-center mb-4"},Un={class:"space-y-4"},qn={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},zn=["disabled"],Vn={class:"border border-gray-300 dark:border-gray-600 rounded-lg p-4"},jn=["disabled"],On={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Kn={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},Hn={class:"flex justify-between items-center mb-4"},Jn={class:"text-center space-y-4"},Yn={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},$n={class:"font-medium text-gray-900 dark:text-white mb-2"},Gn={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Qn={class:"text-sm text-gray-500 dark:text-gray-400"},Xn={class:"flex justify-center"},Zn={key:0,class:"flex items-center space-x-2"},Wn={key:1,class:"bg-white p-4 rounded-lg"},tr=["src"],er={class:"text-sm text-gray-600 dark:text-gray-300 space-y-2"},nr={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg"},rr={class:"flex items-center space-x-2"},or={class:"text-sm text-blue-600 dark:text-blue-400"},sr={class:"flex space-x-2"},ir=["disabled"],At=300,ar=ce({__name:"Activated",setup(r){const o=fe(),s=Be(),n=F(""),t=F(""),e=F(!1),i=F(""),l=F(null),a=F(!1),c=F([]),g=F(null),E=F(!1),y=F(!1),f=F(!1),A=F(null),T=F(""),M=F(!1),B=F(!1),R=F(!1),k=F(null),w=F(0);ge(()=>{o.initTheme();const d=new URLSearchParams(window.location.search).get("deviceCode");d?n.value=d:i.value="未找到设备码参数",x(),m()}),he(()=>{v()});const N=async()=>{if(!g.value){i.value="请先选择一个许可证";return}try{e.value=!0,i.value="";const I=await s.generateActivateCode(n.value,g.value.orderId);I&&(t.value=I)}catch(I){i.value=I instanceof Error?I.message:"生成激活码失败",console.error("Failed to generate activate code:",I)}finally{e.value=!1}},m=async()=>{try{y.value=!0;const I=await me();I.success?c.value=I.data||[]:console.error("Failed to load licenses:",I.error)}catch(I){console.error("Failed to load licenses:",I)}finally{y.value=!1}},x=async()=>{try{a.value=!0;const I=await ye();I.success&&I.data?l.value=I.data:console.error("Failed to load user info:",I.error)}catch(I){console.error("Failed to load user info:",I)}finally{a.value=!1}},C=I=>{g.value=I},h=async I=>{try{f.value=!0;const S=await be({licenseType:I,payMethod:"wechat",payPrice:I===1?9900:19900});S.success?(A.value=S.data,E.value=!1,M.value=!0,await _(S.data.wxCodeUrl),p(S.data.orderId)):console.error("Failed to create order:",S.error)}catch(d){console.error("Failed to create order:",d)}finally{f.value=!1}},_=async I=>{try{B.value=!0;const d=await Ge.toDataURL(I,{width:256,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});T.value=d}catch(d){console.error("Failed to generate QR code:",d)}finally{B.value=!1}},p=I=>{if(R.value)return;R.value=!0,w.value=0;const d=async()=>{try{w.value++,console.log(`Polling order status, attempt ${w.value}`),(await xe({orderId:I})).success?(console.log("Order payment successful!"),v(),M.value=!1,await m(),alert("支付成功！许可证已激活。")):w.value>=At?(console.log("Max polling count reached, stopping polling"),v(),alert("支付超时，请重新创建订单。")):k.value=setTimeout(d,1e3)}catch(S){console.error("Error polling order status:",S),w.value>=At?(v(),alert("检查订单状态失败，请手动刷新页面。")):k.value=setTimeout(d,1e3)}};d()},v=()=>{k.value&&(clearTimeout(k.value),k.value=null),R.value=!1,w.value=0},b=async()=>{try{await navigator.clipboard.writeText(t.value),console.log("Activate code copied to clipboard")}catch(I){console.error("Failed to copy activate code:",I)}},P=()=>i.value?"生成激活码失败":e.value?"正在生成激活码...":t.value?"激活码生成成功":"准备生成激活码...",q=I=>I===1?"一年有效期":"永久有效期",K=I=>(I/100).toFixed(0)+"元",W=I=>new Date(I).toLocaleDateString("zh-CN");return(I,d)=>(D(),L("div",Qe,[u("div",Xe,[u("div",Ze,[u("div",We,[d[10]||(d[10]=u("div",{class:"flex items-center space-x-2"},[u("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})]),u("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"用户信息")],-1)),a.value?(D(),L("div",tn,d[7]||(d[7]=[u("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1),u("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"加载中...",-1)]))):l.value?(D(),L("div",en,[u("div",nn,[d[8]||(d[8]=u("span",{class:"text-gray-500 dark:text-gray-400"},"ID:",-1)),u("span",rn,U(l.value.uniqueId),1)]),u("div",on,[d[9]||(d[9]=u("span",{class:"text-gray-500 dark:text-gray-400"},"昵称:",-1)),u("span",sn,U(l.value.username),1)])])):(D(),L("div",an," 未获取到用户信息 "))]),u("div",{class:"flex items-center space-x-2"},[u("button",{onClick:x,class:"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200"}," 刷新 ")])])]),u("div",ln,[u("div",un,[u("div",dn,[d[11]||(d[11]=u("h1",{class:"text-3xl font-bold mb-4 text-gray-900 dark:text-white"}," XCodeMap 激活码生成 ",-1)),u("p",cn,U(P()),1)]),u("div",fn,[u("div",gn,[d[12]||(d[12]=u("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white"}," 可用许可证 ",-1)),u("button",{onClick:d[0]||(d[0]=S=>E.value=!0),class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200"}," 购买许可证 ")]),y.value?(D(),L("div",hn,d[13]||(d[13]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在加载许可证...",-1)]))):c.value.length>0?(D(),L("div",mn,[(D(!0),L(ve,null,we(c.value,S=>{var J;return D(),L("div",{key:S.id,onClick:tt=>C(S),class:pe(["p-4 border rounded-lg cursor-pointer transition-colors duration-200",((J=g.value)==null?void 0:J.id)===S.id?"border-blue-500 bg-blue-50 dark:bg-blue-900":"border-gray-300 dark:border-gray-600 hover:border-blue-300"])},[u("div",vn,[u("div",null,[u("h3",wn,U(q(S.licenseType)),1),u("p",pn," 过期时间: "+U(W(S.activationExpiredTime)),1)]),u("div",bn,[u("p",xn,U(K(S.payPrice)),1),u("p",Cn," 订单号: "+U(S.orderId),1)])])],10,yn)}),128))])):(D(),L("div",En,d[14]||(d[14]=[u("p",{class:"text-gray-500 dark:text-gray-400"},"暂无可用许可证",-1)])))]),u("div",kn,[u("button",{onClick:N,disabled:e.value||!g.value,class:"px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-md transition-colors duration-200"},U(e.value?"正在生成激活码...":"激活许可证"),9,Bn)]),u("div",_n,[u("div",An,[e.value?(D(),L("div",Rn,d[15]||(d[15]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-300"},"正在生成激活码...",-1)]))):O("",!0),t.value&&!e.value?(D(),L("div",Pn,[d[16]||(d[16]=u("div",{class:"text-center"},[u("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 激活码 "),u("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," 请复制此激活码并发送给用户 ")],-1)),u("div",Tn,[u("code",In,U(t.value),1)]),u("div",{class:"text-center"},[u("button",{onClick:b,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200"}," 复制激活码 ")]),d[17]||(d[17]=u("div",{class:"text-center"},[u("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 激活码已生成，请将此激活码提供给用户完成激活 ")],-1))])):O("",!0),i.value?(D(),L("div",Mn,U(i.value),1)):O("",!0)])]),i.value?(D(),L("div",Sn,[u("button",{onClick:N,disabled:!g.value,class:"bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"}," 重新生成 ",8,Nn)])):O("",!0)])]),E.value?(D(),L("div",Ln,[u("div",Dn,[u("div",Fn,[d[19]||(d[19]=u("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 购买许可证 ",-1)),u("button",{onClick:d[1]||(d[1]=S=>E.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},d[18]||(d[18]=[u("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",Un,[u("div",qn,[d[20]||(d[20]=Tt('<div class="flex justify-between items-center" data-v-ef9289d0><div data-v-ef9289d0><h4 class="font-medium text-gray-900 dark:text-white" data-v-ef9289d0>一年有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-ef9289d0>适合短期使用</p></div><div class="text-right" data-v-ef9289d0><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-ef9289d0>99元</p></div></div>',1)),u("button",{onClick:d[2]||(d[2]=S=>h(1)),disabled:f.value,class:"w-full mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},U(f.value?"创建订单中...":"立即购买"),9,zn)]),u("div",Vn,[d[21]||(d[21]=Tt('<div class="flex justify-between items-center" data-v-ef9289d0><div data-v-ef9289d0><h4 class="font-medium text-gray-900 dark:text-white" data-v-ef9289d0>永久有效期</h4><p class="text-sm text-gray-500 dark:text-gray-400" data-v-ef9289d0>一次购买，永久使用</p></div><div class="text-right" data-v-ef9289d0><p class="text-xl font-semibold text-gray-900 dark:text-white" data-v-ef9289d0>199元</p></div></div>',1)),u("button",{onClick:d[3]||(d[3]=S=>h(2)),disabled:f.value,class:"w-full mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},U(f.value?"创建订单中...":"立即购买"),9,jn)])])])])):O("",!0),M.value?(D(),L("div",On,[u("div",Kn,[u("div",Hn,[d[23]||(d[23]=u("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," 微信支付 ",-1)),u("button",{onClick:d[4]||(d[4]=S=>M.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},d[22]||(d[22]=[u("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),u("div",Jn,[A.value?(D(),L("div",Yn,[u("h4",$n,U(A.value.licenseType===1?"一年有效期许可证":"永久许可证"),1),u("p",Gn,U(K(A.value.payPrice)),1),u("p",Qn," 订单号: "+U(A.value.orderId),1)])):O("",!0),u("div",Xn,[B.value?(D(),L("div",Zn,d[24]||(d[24]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),u("span",{class:"text-sm text-gray-600 dark:text-gray-300"},"生成二维码中...",-1)]))):T.value?(D(),L("div",Wn,[u("img",{src:T.value,alt:"微信支付二维码",class:"w-64 h-64"},null,8,tr)])):O("",!0)]),u("div",er,[d[27]||(d[27]=u("p",null,"请使用微信扫描二维码完成支付",-1)),d[28]||(d[28]=u("p",null,"支付成功后，许可证将自动激活",-1)),R.value?(D(),L("div",nr,[u("div",rr,[d[25]||(d[25]=u("div",{class:"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1)),u("span",or," 正在检查支付状态... ("+U(w.value)+"/"+U(At)+") ",1)]),d[26]||(d[26]=u("p",{class:"text-xs text-blue-500 dark:text-blue-300 mt-1"}," 系统正在自动检查支付状态，请稍候 ",-1))])):O("",!0)]),u("div",sr,[u("button",{onClick:d[5]||(d[5]=S=>M.value=!1),disabled:R.value,class:"flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200"},U(R.value?"检查中...":"关闭"),9,ir),R.value?(D(),L("button",{key:0,onClick:v,class:"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors duration-200"}," 停止检查 ")):(D(),L("button",{key:1,onClick:d[6]||(d[6]=()=>{M.value=!1,m()}),class:"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"}," 刷新许可证 "))])])])])):O("",!0)]))}}),lr=Ce(ar,[["__scopeId","data-v-ef9289d0"]]),ue=Ee(lr);ue.use(ke());ue.mount("#app");
