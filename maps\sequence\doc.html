<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>XCodeMap List</title>
    <link href="../assets/element-ui-2.15.14/index.min.css" rel="stylesheet">
    <link href="../assets/xcodemap/theme.css" rel="stylesheet">
    <link href="../assets/xcodemap/doc.css" rel="stylesheet">
</head>
<body style="background-color: #3d3f41">
<div id="app">
    <el-container>
        <el-main>
            <div class="left-container">
                <el-scrollbar>
                    <div class="doc-list">
                        <strong>五步掌握 XCodeMap</strong>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第一步</el-tag> <el-link href="./help_step1.html?functionName=Learn-Step-1" >Debug with XCodeMap，启动程序并开始录制</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第二步</el-tag> <el-link href="./help_step2.html?functionName=Learn-Step-2" >点击 + 展开序列图，并用“浏览记录”和“忽略规则”来修剪序列图</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第三步</el-tag> <el-link href="./help_step3.html?functionName=Learn-Step-3" >点击高亮执行源码，与序列图联动</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第四步</el-tag> <el-link href="./help_step4.html?functionName=Learn-Step-4" >点击展开上下文数据，包括参数、返回值等</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="warning" size="mini">第五步</el-tag> <el-link href="./help_step5.html?functionName=Learn-Step-5" >通过搜索和回溯，实现时光穿梭</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="info" size="mini">加餐</el-tag> <el-link href="./why.html?functionName=why-xcodemap" >为什么说 “重现” 是搞定 “屎山代码” 的良药？</el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="info" size="mini">加餐</el-tag> <el-link href="./imp.html?functionName=重要提示" >如何避免 IDE 卡顿？ </el-link>
                        </div>
                        <div class="doc-list-item">
                            <el-tag type="info" size="mini">加餐</el-tag> <el-link href="./contact.html?functionName=关注我们" >加微信，进一步交流 </el-link>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </el-main>
    </el-container>

</div>
</body>

<script src="../assets/axios-1.6.3/axios.min.js"></script>
<!-- 导入 Mermaid 库 -->
<script src="../assets/mermaid-10.9.0/dist/mermaid.min.js"></script>
<script src="../assets/vue-2.5.22/vue.min.js"></script>
<script src="../assets/element-ui-2.15.14/index.min.js"></script>
<script src="../assets/xcodemap/common.js"></script>

<script>
    var vue = new Vue({
        el: '#app',
        data: function () {
            return {
                project: NewProject(),
            }
        },
        created() {
            console.log("this", this)
            this.project.doInitProjectInfo();
        },
        destroyed() {
        },
        methods: {
        }
    })
    fixScrollClass();
</script>

<script>
</script>

<script>
    // 按键状态标记
    let isKeyPressed = false;
    // 注册按键按下事件监听器
    window.addEventListener('keydown', (event) => {
        //console.log("key down", event)
        // 检查按下的按键是否为你想要的按键（这里假设是 Ctrl 键）
        if (event.ctrlKey) {
            isKeyPressed = true;
        }
    });
    // 注册按键释放事件监听器
    window.addEventListener('keyup', (event) => {
        //console.log("key up", event)
        isKeyPressed = false;
    });
    // 添加键盘事件监听器
    //document.addEventListener('keydown', forwardOrBackward);
</script>


<style>
    body {
        margin: 0px;
    }
    .el-scrollbar {
        height: 100vh;
        width: 100%;
    }
    .el-scrollbar__bar.is-vertical {
        width: 10px;
    }
    .el-scrollbar__bar.is-horizontal {
        height: 10px;
    }
    /*.el-scrollbar__wrap {
        overflow-x: auto;
        height: calc(100% + 20px);
    }*/
    .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
        white-space: nowrap;
    }
</style>

<style>
    .controlBarMenu:hover {
        cursor: pointer;
    }
    .controlBarMenu {
        display: inline-block;
    }
    .controlBarMenuMinus {
        height: 32px;
        line-height: 32px;
        padding: 4px;
    }
    .controlBarMenuPlus {
        height: 24px;
        line-height: 22px;
        padding: 4px;
        border-width: 1px;
    }
</style>

<style>
    .filter-button-wrap {
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 95%;
        margin-left: 5px;
    }

    .filter-button {
        text-align: center;
        width: 49%;
    }

    .filter-button > .el-button {
        width: 100%;
    }

    .el-input.is-disabled .el-input__inner {
        cursor: auto;
    }

    .included-filter-body .el-tag {
        font-size: 14px;
        white-space: normal;
        word-break: break-all;
        height: 100%;
    }



    .var-exp-op {
        width: 220px;
        margin: 0px 2px;
    }

    .var-exp-name {

    }

    .var-op-value {

    }

    .filter-detail-list {
        width: 95%;
        margin-left: 5px;
    }

    .included-filter-label {
        height: 32px;
        padding: 0 10px;
        line-height: 30px;
        font-size: 12px;
        /* box-sizing: border-box; */
        /* white-space: nowrap; */
        margin-top: 10px;
    }

    .included-filter-body .el-input__inner {
        text-align: left;
        padding: 0 0px;
    }

    .included-filter-body {
        margin-top: 4px;
        display: flex;
    }

    .included-filter-notes {
        font-style: italic;
        font-size: small;
    }

    /* start filterBarMenu */
    .filterBarMenu:hover {
        cursor: pointer;
    }

    .filterBarMenu {
        display: inline-block;
    }

    .filterBarMenuMinus {
        height: 32px;
        line-height: 32px;
        padding: 4px;
    }
    .filterBarMenuPlus {
        height: 24px;
        line-height: 22px;
        padding: 4px;
        border-width: 1px;
    }
    .filterBarMenuChat {
        position: absolute;
        right: 16px;
    }
</style>


<style>
    .el-tabs {
        padding: 0 0px;
        margin-top: 24px;
    }

    .el-tabs__nav-wrap {
        margin-left: 0px;
    }

    .el-tabs__header {
        margin: 0 0 24px;
    }

    .el-tabs__content {
        padding: 0px;
    }

    .el-tabs__item.is-left, .el-tabs__item.is-right {
        width: 25px;
        height: 100px;
        padding: 0;
        margin: 0;
        writing-mode: vertical-lr; /* 将文字垂直显示，从左到右 */
        line-height: 25px;
    }

    .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
        text-align: center; /* 文字居中 */
    }

    .el-tabs--right.el-tabs--card .el-tabs__item.is-right {
        text-align: center; /* 文字居中 */
    }
    .el-tabs__item.is-active {
        opacity: 1;
    }
    .el-tabs__header {
        margin: 0;
    }
    .el-tabs--right .el-tabs__header.is-right {
        margin-left: 0px;
    }
</style>

<style>
    .el-aside, .el-main, .el-header {
        height: 100%;
    }

    .el-header {
        padding: 0 32px;
        display: flex;
        /*
        justify-content: center; !* Center content horizontally *!
        */
        align-items: center; /* Center content vertically */
        height: 30px;
        margin-bottom: 5px;
    }

    .el-main {
        padding: 0px;
    }


    .el-breadcrumb {
        /*
        margin-top: 15px;
        */
        margin-left: 0px;
        font-size: 16px;
    }



    .el-table {
        margin-top: 24px;
    }



    .el-header, .el-tab-pane {
        white-space: pre-line;
    }

    .el-menu-item > a, .el-menu-item > * > a, .el-menu-item > * > * > a, .el-menu-item > * > * > * > a {
        text-decoration: none; /* 去掉下划线 */
    }

    .el-button > a, .el-button > * > a, .el-button > * > * > a, .el-button > * > * > * > a {
        text-decoration: none;
    }


    .el-main {
        display: flex;
        flex-direction: row;
        padding-left: 0;
        padding-right: 0;
    }

    .codeCanvasDiv {
        height: 100%;
        width: 100%;
        overflow: auto;
    }

    .codeCanvasPre {
        width: 100%;
        overflow: auto;
    }

    .flowCanvasDiv {
        height: 100%;
        width: 550px;
        overflow: auto;
    }

    .left-aside {
        display: flex;
        margin-right: 5px;
    }

    .left-tab-content {
        #width: calc(100% - 25px);
        #margin-top: 20px;
    }

    .right-aside {
        display: flex;
        margin-left: 5px;
        justify-content: flex-end;
    }

    .right-tab-content {
        #width: calc(100% - 25px);
        margin-top: 20px;
        overflow-wrap: break-word;
        margin-left: 6px;
    }

    .usageListSelectDiv {
        width: 270px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .focus-on-token {
        text-decoration: underline;
    }


    .x-handle {
        width: 3px;
        cursor: ew-resize;
        z-index: 10;
    }



    .custom-tab-left {
        position: fixed;
        width: 25px;
        height: 100px;
    }

    .custom-tab-right {
        position: fixed;
        width: 25px;
        height: 100px;
    }



    .left-container {
        position: relative;
        margin-left: 25px;
        width: 100%;
    }

    .right-container {
        position: relative;
        width: calc(100% - 25px);
        margin-right: 25px;
        height: 100vh;
    }

    .hide-icon-left {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: right;
        z-index: 2;
    }


    .hide-icon-right {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
        text-align: left;
        z-index: 2;
    }




    .zdiv {
        margin: 0;
        border: 1px solid rgb(187, 186, 186);
        background: rgb(255, 255, 255);
        z-index: 1000;
        position: absolute;
        list-style-type: none;
        padding: 5px;
        border-radius: 7px;
        font-size: 12px;
    }

    .zdiv li {
        margin: 0;
        padding: 5px;
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .zdiv li:hover {
        background: #e1e1e1;
        cursor: pointer;
    }

    .usageTraceDetail {
        margin-bottom: 2px;
    }

    .usageTraceDetail:hover {
        cursor: pointer;
    }

    .code-content-widget-detail-parent {
        max-width: 600px;
    }

    .code-content-widget-detail-header {
        font-size: larger;
    }

    .code-content-widget-detail-header::after {
        clear: both;
        content: '';
        display: table;
    }

    .code-content-widget-detail-header div:first-child {
        float: left;
    }

    .code-content-widget-detail-header div:nth-child(2) {
        float: right;
    }

    .code-content-widget-detail-header div i {
        margin-left: 5px;
        font-size: larger;
    }

    .code-content-widget-detail-header div i:hover {
        cursor: pointer;
    }

    .code-content-widget-detail-desc {
        /*padding-top: 10px;*/
        padding-bottom: 32px
    }

    .code-content-widget-button-parent {
        height: 18px;
    }

    .code-content-widget-button {
        height: 18px;
        opacity: 0.7;
        margin-top: 18px;
    }

    .code-content-widget-button:hover {
        cursor: pointer;
    }

    .editor-extra-class {
        margin-right: 100px;
    }

</style>

<style>
    .el-tree {
        margin-bottom: 20px;
    }
    .el-tree-node {
        position: relative;
        padding-left: 1px;
    }

    .el-tree-node__content {
        margin-top: 1px;
    }

    .el-tree-node__children {
        padding-left: 20px;
    }

    .el-tree-node::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        left: -3px;
        top: -26px;
        border-width: 1px;
        border-left: 1px dashed #52627c;
    }

    .el-tree-node:last-child::before {
        height: 38px;
    }

    .el-tree-node::after {
        content: '';
        width: 14px;
        height: 10px;
        position: absolute;
        left: -3px;
        top: 12px;
        border-width: 1px;
        border-top: 1px dashed #52627c;
    }

    /*.el-tree-node > .el-tree-node::after {
        border-top: none;
    }

    .el-tree-node > .el-tree-node::before {
        border-left: none;
    }*/


    .el-tree-node__expand-icon {
        font-size: 18px;
    }

    .el-tree-node__expand-icon > .is-leaf {
        #display: none;
    }
</style>

<script>
    console.log("xcodemap-idea-plugin-ready");
</script>
</html>