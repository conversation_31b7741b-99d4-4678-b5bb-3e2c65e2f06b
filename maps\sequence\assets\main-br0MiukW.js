var gn=Object.defineProperty;var mn=(r,e,t)=>e in r?gn(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var N=(r,e,t)=>mn(r,typeof e!="symbol"?e+"":e,t);import{d as ot,r as M,u as Tt,c as Ue,g as bn,a as xn,b as vn,e as Pt,f as kn,p as wn,s as yn,h as Yt,i as $r,w as kt,j as y,o as w,F as be,k as Te,l as d,m as U,n as S,q as f,t as j,_ as Ur,v as Br,x as _n,y as Tn,z as Sn,A as _e,B as Ee,C as En,D as An,E as Cn,G as Rn,H as Dn,I as Mn,J as yr,K as Ln,L as _r,M as In,N as Nn,O as On,P as Pn,Q as zn,R as $n,S as Un}from"./style-SR2_fW0E.js";const Bn=""+new URL("logo-lk9YP_Ej.svg",import.meta.url).href,Zt=ot("chatStatus",()=>{const r=M("idle"),e=M("");return{status:r,statusMessage:e,setStatus:s=>{r.value=s},setStatusMessage:s=>{e.value=s}}}),Fr=ot("modelStatus",()=>{const r=M(null),e=M(null),t=Tt(),n=Ue(()=>!r.value||!e.value?null:e.value.modelDescList.find(i=>i.uuid===r.value)),s=Ue(()=>{var i;return((i=e.value)==null?void 0:i.modelDescList)||[]});return{currentModelUuid:r,modelConfig:e,currentModel:n,availableModels:s,getModelConfigData:async()=>{var i;try{const h=await bn();if(h.success&&h.data)e.value=h.data,h.data.defaultModelId?h.data.modelDescList.find(p=>p.uuid===h.data.defaultModelId)?r.value=h.data.defaultModelId:r.value=((i=h.data.modelDescList[0])==null?void 0:i.uuid)||null:!r.value&&h.data.modelDescList.length>0&&(r.value=h.data.modelDescList[0].uuid);else throw new Error(h.error||"Failed to get model config")}catch(h){console.error("Failed to get model config:",h),t.setError(h instanceof Error?h.message:"Failed to get model config","GET_MODEL_CONFIG_ERROR")}},setCurrentModel:i=>{var l;((l=e.value)==null?void 0:l.modelDescList.find(p=>p.uuid===i))&&(r.value=i)}}}),ue="EMPTY_PLACE_HOLDER",Hr=ot("database",()=>{const r=M([{id:ue,name:"请选择数据源",recordState:"idle",dataId:null,active:!1,serverSelected:!1}]),e=M(ue),t=Ue(()=>r.value.find(b=>b.id===e.value)||null),n=b=>{r.value.push(b)},s=b=>{e.value=b},a=async(b,g)=>{const E=r.value.find(T=>T.id===b);if(E)try{await xn({executionId:b,cmd:"change",state:g}),E.recordState="preparing"}catch(T){console.error("Failed to change record state:",T)}};return{databases:r,currentDatabase:t,currentDatabaseId:e,addDatabase:n,setCurrentDatabase:s,changeState:a,queryState:b=>{var g;return(g=r.value.find(E=>E.id===b))==null?void 0:g.recordState},startRecord:b=>{const g=r.value.find(E=>E.id===b);g&&g.recordState==="idle"&&a(b,"start")},endRecord:b=>{const g=r.value.find(E=>E.id===b);g&&g.recordState==="recording"&&a(b,"stop")},restartRecord:b=>{const g=r.value.find(E=>E.id===b);g&&g.recordState==="paused"&&(a(b,"start"),Wr().createNewChat())},getDatabase:async()=>{try{const b=await vn();r.value=[r.value[0]],b.forEach(g=>{n(g)})}catch(b){console.error("Failed to fetch process data:",b)}}}}),Gr=ot("inputBox",()=>{const r=M(""),e=Tt(),t=Zt(),n=Hr(),s=()=>!n.currentDatabase||!n.currentDatabase.dataId||t.status==="waiting"||n.currentDatabaseId===ue;return{message:r,appendText:l=>{if(s())return e.setError("当前状态不可添加文本到输入框"),!1;const p=r.value&&!r.value.endsWith(" ");return r.value+=(p?" ":"")+l,!0},setText:l=>{r.value=l},clearText:()=>{r.value=""},getText:()=>r.value,isDisabled:s}}),Fn="SystemStatus",Hn="AddToChat",Wr=ot("chat",()=>{const r=M([]),e=M(null),t=M(!1);let n=null;const s=Tt(),a=Zt(),c=Fr(),i=Gr(),h=Ue(()=>r.value.find(A=>A.id===e.value)),l=async()=>{var A;try{e.value&&await Pt(e.value);const C={id:crypto.randomUUID(),title:"New Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now()};if(await kn(C.id,((A=c.currentModel)==null?void 0:A.uuid)??""))return r.value=[C],e.value=C.id,t.value||p(),C;throw new Error("Failed to create chat channel")}catch(C){throw console.error("Failed to create chat channel:",C),s.setError(C instanceof Error?C.message:"Failed to create chat channel","CREATE_CHAT_ERROR"),e.value=null,r.value=[],C}},p=()=>{n&&clearInterval(n),t.value=!0,n=window.setInterval(async()=>{if(t.value)try{const A=await wn();if(A&&A.length>0)for(const C of A){if(C.messageId===Hn){i.appendText(C.content);continue}if(C.messageId===Fn){a.setStatus("waiting"),a.setStatusMessage(C.content);continue}const B=r.value.find(D=>D.id===C.chatId);if(B){a.setStatus("waiting");const D=B.messages[B.messages.length-1];D&&D.role===C.role?D.content+=C.content:B.messages.push(C),B.updatedAt=Date.now()}}else a.setStatus("sending"),a.setStatusMessage("正在思考中...")}catch(A){console.error("Failed to poll messages:",A),s.setError(A instanceof Error?A.message:"Failed to poll messages","POLL_ERROR")}},1e3)},b=()=>{n&&(clearInterval(n),n=null),t.value=!1};return{chats:r,currentChatId:e,currentChat:h,createNewChat:l,sendMessage:async A=>{e.value||await l();const C=h.value;if(!C)return;if(!c.currentModel){s.setError("请先选择模型再发送消息","SEND_MESSAGE_ERROR");return}a.setStatus("waiting");const B={messageId:crypto.randomUUID(),content:A,role:"user",timestamp:Date.now(),chatId:e.value,modelId:c.currentModel.uuid};try{if(!e.value)return;if(await yn(B))C.messages.push(B),C.updatedAt=Date.now();else throw new Error("会话可能已失效，请重新创建会话")}catch(D){throw console.error("Failed to send message:",D),s.setError(D instanceof Error?D.message:"会话可能已失效，请重新创建会话","SEND_MESSAGE_ERROR"),D}},removeChat:async A=>{try{if(await Pt(A))r.value=r.value.filter(B=>B.id!==A),e.value===A&&(e.value=null);else throw new Error("Failed to remove chat channel")}catch(C){throw console.error("Failed to remove chat:",C),s.setError(C instanceof Error?C.message:"Failed to remove chat","REMOVE_CHAT_ERROR"),C}},startPolling:p,stopPolling:b,cleanup:()=>{b(),e.value&&Pt(e.value).catch(console.error)}}}),Gn={class:"space-y-0.5"},Wn=["onClick"],jn={key:0,class:"p-0.5 rounded flex-shrink-0"},qn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Vn=["onClick"],Yn={key:0,class:"p-0.5 rounded flex-shrink-0"},Zn={key:0,class:"pl-3 mt-0.5 space-y-0.5"},Xn=["onClick"],Kn=Yt({__name:"XTree",props:{nodes:{},defaultExpanded:{type:Boolean,default:!1}},emits:["nodeClick"],setup(r,{emit:e}){const t=r,n=$r(),s=M(t.nodes.map(l=>({...l,isExpanded:t.defaultExpanded,children:l.children?l.children.map(p=>({...p,isExpanded:t.defaultExpanded,children:p.children?p.children.map(b=>({...b,isExpanded:t.defaultExpanded})):[]})):[]})));kt(()=>t.nodes,l=>{s.value=l.map(p=>({...p,isExpanded:t.defaultExpanded,children:p.children?p.children.map(b=>({...b,isExpanded:t.defaultExpanded,children:b.children?b.children.map(g=>({...g,isExpanded:t.defaultExpanded})):[]})):[]}))},{deep:!0});const a=e,c=l=>{a("nodeClick",l)},i=l=>{l.isExpanded=!l.isExpanded},h=(l,p)=>{l.children&&l.children.length>0?i(l):c(l)};return(l,p)=>(w(),y("div",Gn,[(w(!0),y(be,null,Te(s.value,b=>(w(),y("div",{key:b.nodeKey,class:"tree-node"},[d("div",{class:S(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:g=>h(b)},[b.children&&b.children.length>0?(w(),y("div",jn,[(w(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":b.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},p[0]||(p[0]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):U("",!0),d("span",{class:S({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},j(b.label),3)],10,Wn),b.children&&b.children.length>0&&b.isExpanded?(w(),y("div",qn,[(w(!0),y(be,null,Te(b.children,g=>(w(),y("div",{key:g.nodeKey,class:"tree-node"},[d("div",{class:S(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:E=>h(g)},[g.children&&g.children.length>0?(w(),y("div",Yn,[(w(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-4 w-4 transition-transform duration-200",{"transform rotate-90":g.isExpanded,"text-gray-600":f(n).theme==="light","text-gray-400":f(n).theme==="dark"}]),viewBox:"0 0 20 20",fill:"currentColor"},p[1]||(p[1]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):U("",!0),d("span",{class:S({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},j(g.label),3)],10,Vn),g.children&&g.children.length>0&&g.isExpanded?(w(),y("div",Zn,[(w(!0),y(be,null,Te(g.children,E=>(w(),y("div",{key:E.nodeKey,class:"pl-3"},[d("div",{class:S(["flex items-center gap-0.5 rounded px-1 py-0.5 transition-all duration-200 cursor-pointer",f(n).theme==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"]),onClick:T=>h(E)},[d("span",{class:S({"text-gray-700":f(n).theme==="light","text-gray-300":f(n).theme==="dark","flex-grow":!0})},j(E.label),3)],10,Xn)]))),128))])):U("",!0)]))),128))])):U("",!0)]))),128))]))}}),Qn=Ur(Kn,[["__scopeId","data-v-139689ce"]]),Jn=Yt({__name:"TypeWriter",props:{text:{},speed:{}},emits:["complete"],setup(r,{emit:e}){const t=r,n=e,s=M(""),a=M(0),c=M(!1),i=()=>{s.value="",a.value=0,c.value=!0;const h=setInterval(()=>{a.value<t.text.length?(s.value+=t.text[a.value],a.value++):(clearInterval(h),c.value=!1,n("complete"))},t.speed||50)};return kt(()=>t.text,()=>{i()}),Br(()=>{i()}),(h,l)=>(w(),y("span",null,j(s.value),1))}});function Xt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var Ce=Xt();function jr(r){Ce=r}var at={exec:()=>null};function I(r,e=""){let t=typeof r=="string"?r:r.source;const n={replace:(s,a)=>{let c=typeof a=="string"?a:a.source;return c=c.replace(Q.caret,"$1"),t=t.replace(s,c),n},getRegex:()=>new RegExp(t,e)};return n}var Q={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:r=>new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}#`),htmlBeginRegex:r=>new RegExp(`^ {0,${Math.min(3,r-1)}}<(?:[a-z].*>|!--)`,"i")},es=/^(?:[ \t]*(?:\n|$))+/,ts=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,rs=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,lt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,ns=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Kt=/(?:[*+-]|\d{1,9}[.)])/,qr=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Vr=I(qr).replace(/bull/g,Kt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),ss=I(qr).replace(/bull/g,Kt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Qt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,as=/^[^\n]+/,Jt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,os=I(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Jt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ls=I(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Kt).getRegex(),St="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",er=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,is=I("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",er).replace("tag",St).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Yr=I(Qt).replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex(),cs=I(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Yr).getRegex(),tr={blockquote:cs,code:ts,def:os,fences:rs,heading:ns,hr:lt,html:is,lheading:Vr,list:ls,newline:es,paragraph:Yr,table:at,text:as},Tr=I("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex(),us={...tr,lheading:ss,table:Tr,paragraph:I(Qt).replace("hr",lt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Tr).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",St).getRegex()},ds={...tr,html:I(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",er).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:at,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:I(Qt).replace("hr",lt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Vr).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ps=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,hs=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Zr=/^( {2,}|\\)\n(?!\s*$)/,fs=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Et=/[\p{P}\p{S}]/u,rr=/[\s\p{P}\p{S}]/u,Xr=/[^\s\p{P}\p{S}]/u,gs=I(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,rr).getRegex(),Kr=/(?!~)[\p{P}\p{S}]/u,ms=/(?!~)[\s\p{P}\p{S}]/u,bs=/(?:[^\s\p{P}\p{S}]|~)/u,xs=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Qr=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,vs=I(Qr,"u").replace(/punct/g,Et).getRegex(),ks=I(Qr,"u").replace(/punct/g,Kr).getRegex(),Jr="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",ws=I(Jr,"gu").replace(/notPunctSpace/g,Xr).replace(/punctSpace/g,rr).replace(/punct/g,Et).getRegex(),ys=I(Jr,"gu").replace(/notPunctSpace/g,bs).replace(/punctSpace/g,ms).replace(/punct/g,Kr).getRegex(),_s=I("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Xr).replace(/punctSpace/g,rr).replace(/punct/g,Et).getRegex(),Ts=I(/\\(punct)/,"gu").replace(/punct/g,Et).getRegex(),Ss=I(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Es=I(er).replace("(?:-->|$)","-->").getRegex(),As=I("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Es).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),wt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Cs=I(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",wt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),en=I(/^!?\[(label)\]\[(ref)\]/).replace("label",wt).replace("ref",Jt).getRegex(),tn=I(/^!?\[(ref)\](?:\[\])?/).replace("ref",Jt).getRegex(),Rs=I("reflink|nolink(?!\\()","g").replace("reflink",en).replace("nolink",tn).getRegex(),nr={_backpedal:at,anyPunctuation:Ts,autolink:Ss,blockSkip:xs,br:Zr,code:hs,del:at,emStrongLDelim:vs,emStrongRDelimAst:ws,emStrongRDelimUnd:_s,escape:ps,link:Cs,nolink:tn,punctuation:gs,reflink:en,reflinkSearch:Rs,tag:As,text:fs,url:at},Ds={...nr,link:I(/^!?\[(label)\]\((.*?)\)/).replace("label",wt).getRegex(),reflink:I(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",wt).getRegex()},Gt={...nr,emStrongRDelimAst:ys,emStrongLDelim:ks,url:I(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ms={...Gt,br:I(Zr).replace("{2,}","*").getRegex(),text:I(Gt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},gt={normal:tr,gfm:us,pedantic:ds},Qe={normal:nr,gfm:Gt,breaks:Ms,pedantic:Ds},Ls={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Sr=r=>Ls[r];function ce(r,e){if(e){if(Q.escapeTest.test(r))return r.replace(Q.escapeReplace,Sr)}else if(Q.escapeTestNoEncode.test(r))return r.replace(Q.escapeReplaceNoEncode,Sr);return r}function Er(r){try{r=encodeURI(r).replace(Q.percentDecode,"%")}catch{return null}return r}function Ar(r,e){var a;const t=r.replace(Q.findPipe,(c,i,h)=>{let l=!1,p=i;for(;--p>=0&&h[p]==="\\";)l=!l;return l?"|":" |"}),n=t.split(Q.splitPipe);let s=0;if(n[0].trim()||n.shift(),n.length>0&&!((a=n.at(-1))!=null&&a.trim())&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(Q.slashPipe,"|");return n}function Je(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n&&r.charAt(n-s-1)===e;)s++;return r.slice(0,n-s)}function Is(r,e){if(r.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<r.length;n++)if(r[n]==="\\")n++;else if(r[n]===e[0])t++;else if(r[n]===e[1]&&(t--,t<0))return n;return t>0?-2:-1}function Cr(r,e,t,n,s){const a=e.href,c=e.title||null,i=r[1].replace(s.other.outputLinkReplace,"$1");n.state.inLink=!0;const h={type:r[0].charAt(0)==="!"?"image":"link",raw:t,href:a,title:c,text:i,tokens:n.inlineTokens(i)};return n.state.inLink=!1,h}function Ns(r,e,t){const n=r.match(t.other.indentCodeCompensation);if(n===null)return e;const s=n[1];return e.split(`
`).map(a=>{const c=a.match(t.other.beginningSpace);if(c===null)return a;const[i]=c;return i.length>=s.length?a.slice(s.length):a}).join(`
`)}var yt=class{constructor(r){N(this,"options");N(this,"rules");N(this,"lexer");this.options=r||Ce}space(r){const e=this.rules.block.newline.exec(r);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(r){const e=this.rules.block.code.exec(r);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:Je(t,`
`)}}}fences(r){const e=this.rules.block.fences.exec(r);if(e){const t=e[0],n=Ns(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(r){const e=this.rules.block.heading.exec(r);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const n=Je(t,"#");(this.options.pedantic||!n||this.rules.other.endingSpaceChar.test(n))&&(t=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(r){const e=this.rules.block.hr.exec(r);if(e)return{type:"hr",raw:Je(e[0],`
`)}}blockquote(r){const e=this.rules.block.blockquote.exec(r);if(e){let t=Je(e[0],`
`).split(`
`),n="",s="";const a=[];for(;t.length>0;){let c=!1;const i=[];let h;for(h=0;h<t.length;h++)if(this.rules.other.blockquoteStart.test(t[h]))i.push(t[h]),c=!0;else if(!c)i.push(t[h]);else break;t=t.slice(h);const l=i.join(`
`),p=l.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${l}`:l,s=s?`${s}
${p}`:p;const b=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(p,a,!0),this.lexer.state.top=b,t.length===0)break;const g=a.at(-1);if((g==null?void 0:g.type)==="code")break;if((g==null?void 0:g.type)==="blockquote"){const E=g,T=E.raw+`
`+t.join(`
`),A=this.blockquote(T);a[a.length-1]=A,n=n.substring(0,n.length-E.raw.length)+A.raw,s=s.substring(0,s.length-E.text.length)+A.text;break}else if((g==null?void 0:g.type)==="list"){const E=g,T=E.raw+`
`+t.join(`
`),A=this.list(T);a[a.length-1]=A,n=n.substring(0,n.length-g.raw.length)+A.raw,s=s.substring(0,s.length-E.raw.length)+A.raw,t=T.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:n,tokens:a,text:s}}}list(r){let e=this.rules.block.list.exec(r);if(e){let t=e[1].trim();const n=t.length>1,s={type:"list",raw:"",ordered:n,start:n?+t.slice(0,-1):"",loose:!1,items:[]};t=n?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=n?t:"[*+-]");const a=this.rules.other.listItemRegex(t);let c=!1;for(;r;){let h=!1,l="",p="";if(!(e=a.exec(r))||this.rules.block.hr.test(r))break;l=e[0],r=r.substring(l.length);let b=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,B=>" ".repeat(3*B.length)),g=r.split(`
`,1)[0],E=!b.trim(),T=0;if(this.options.pedantic?(T=2,p=b.trimStart()):E?T=e[1].length+1:(T=e[2].search(this.rules.other.nonSpaceChar),T=T>4?1:T,p=b.slice(T),T+=e[1].length),E&&this.rules.other.blankLine.test(g)&&(l+=g+`
`,r=r.substring(g.length+1),h=!0),!h){const B=this.rules.other.nextBulletRegex(T),D=this.rules.other.hrRegex(T),q=this.rules.other.fencesBeginRegex(T),O=this.rules.other.headingBeginRegex(T),se=this.rules.other.htmlBeginRegex(T);for(;r;){const re=r.split(`
`,1)[0];let le;if(g=re,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),le=g):le=g.replace(this.rules.other.tabCharGlobal,"    "),q.test(g)||O.test(g)||se.test(g)||B.test(g)||D.test(g))break;if(le.search(this.rules.other.nonSpaceChar)>=T||!g.trim())p+=`
`+le.slice(T);else{if(E||b.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||q.test(b)||O.test(b)||D.test(b))break;p+=`
`+g}!E&&!g.trim()&&(E=!0),l+=re+`
`,r=r.substring(re.length+1),b=le.slice(T)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(l)&&(c=!0));let A=null,C;this.options.gfm&&(A=this.rules.other.listIsTask.exec(p),A&&(C=A[0]!=="[ ] ",p=p.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:l,task:!!A,checked:C,loose:!1,text:p,tokens:[]}),s.raw+=l}const i=s.items.at(-1);if(i)i.raw=i.raw.trimEnd(),i.text=i.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let h=0;h<s.items.length;h++)if(this.lexer.state.top=!1,s.items[h].tokens=this.lexer.blockTokens(s.items[h].text,[]),!s.loose){const l=s.items[h].tokens.filter(b=>b.type==="space"),p=l.length>0&&l.some(b=>this.rules.other.anyLine.test(b.raw));s.loose=p}if(s.loose)for(let h=0;h<s.items.length;h++)s.items[h].loose=!0;return s}}html(r){const e=this.rules.block.html.exec(r);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(r){const e=this.rules.block.def.exec(r);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:n,title:s}}}table(r){var c;const e=this.rules.block.table.exec(r);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=Ar(e[1]),n=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===n.length){for(const i of n)this.rules.other.tableAlignRight.test(i)?a.align.push("right"):this.rules.other.tableAlignCenter.test(i)?a.align.push("center"):this.rules.other.tableAlignLeft.test(i)?a.align.push("left"):a.align.push(null);for(let i=0;i<t.length;i++)a.header.push({text:t[i],tokens:this.lexer.inline(t[i]),header:!0,align:a.align[i]});for(const i of s)a.rows.push(Ar(i,a.header.length).map((h,l)=>({text:h,tokens:this.lexer.inline(h),header:!1,align:a.align[l]})));return a}}lheading(r){const e=this.rules.block.lheading.exec(r);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(r){const e=this.rules.block.paragraph.exec(r);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(r){const e=this.rules.block.text.exec(r);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(r){const e=this.rules.inline.escape.exec(r);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(r){const e=this.rules.inline.tag.exec(r);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(r){const e=this.rules.inline.link.exec(r);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const a=Je(t.slice(0,-1),"\\");if((t.length-a.length)%2===0)return}else{const a=Is(e[2],"()");if(a===-2)return;if(a>-1){const i=(e[0].indexOf("!")===0?5:4)+e[1].length+a;e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,i).trim(),e[3]=""}}let n=e[2],s="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(n);a&&(n=a[1],s=a[3])}else s=e[3]?e[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?n=n.slice(1):n=n.slice(1,-1)),Cr(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(r,e){let t;if((t=this.rules.inline.reflink.exec(r))||(t=this.rules.inline.nolink.exec(r))){const n=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[n.toLowerCase()];if(!s){const a=t[0].charAt(0);return{type:"text",raw:a,text:a}}return Cr(t,s,t[0],this.lexer,this.rules)}}emStrong(r,e,t=""){let n=this.rules.inline.emStrongLDelim.exec(r);if(!n||n[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(n[1]||n[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const a=[...n[0]].length-1;let c,i,h=a,l=0;const p=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,e=e.slice(-1*r.length+a);(n=p.exec(e))!=null;){if(c=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!c)continue;if(i=[...c].length,n[3]||n[4]){h+=i;continue}else if((n[5]||n[6])&&a%3&&!((a+i)%3)){l+=i;continue}if(h-=i,h>0)continue;i=Math.min(i,i+h+l);const b=[...n[0]][0].length,g=r.slice(0,a+n.index+b+i);if(Math.min(a,i)%2){const T=g.slice(1,-1);return{type:"em",raw:g,text:T,tokens:this.lexer.inlineTokens(T)}}const E=g.slice(2,-2);return{type:"strong",raw:g,text:E,tokens:this.lexer.inlineTokens(E)}}}}codespan(r){const e=this.rules.inline.code.exec(r);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return n&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(r){const e=this.rules.inline.br.exec(r);if(e)return{type:"br",raw:e[0]}}del(r){const e=this.rules.inline.del.exec(r);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(r){const e=this.rules.inline.autolink.exec(r);if(e){let t,n;return e[2]==="@"?(t=e[1],n="mailto:"+t):(t=e[1],n=t),{type:"link",raw:e[0],text:t,href:n,tokens:[{type:"text",raw:t,text:t}]}}}url(r){var t;let e;if(e=this.rules.inline.url.exec(r)){let n,s;if(e[2]==="@")n=e[0],s="mailto:"+n;else{let a;do a=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(a!==e[0]);n=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(r){const e=this.rules.inline.text.exec(r);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},xe=class Wt{constructor(e){N(this,"tokens");N(this,"options");N(this,"state");N(this,"tokenizer");N(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Ce,this.options.tokenizer=this.options.tokenizer||new yt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:Q,block:gt.normal,inline:Qe.normal};this.options.pedantic?(t.block=gt.pedantic,t.inline=Qe.pedantic):this.options.gfm&&(t.block=gt.gfm,this.options.breaks?t.inline=Qe.breaks:t.inline=Qe.gfm),this.tokenizer.rules=t}static get rules(){return{block:gt,inline:Qe}}static lex(e,t){return new Wt(t).lex(e)}static lexInline(e,t){return new Wt(t).inlineTokens(e)}lex(e){e=e.replace(Q.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){var s,a,c;for(this.options.pedantic&&(e=e.replace(Q.tabCharGlobal,"    ").replace(Q.spaceLine,""));e;){let i;if((a=(s=this.options.extensions)==null?void 0:s.block)!=null&&a.some(l=>(i=l.call({lexer:this},e,t))?(e=e.substring(i.raw.length),t.push(i),!0):!1))continue;if(i=this.tokenizer.space(e)){e=e.substring(i.raw.length);const l=t.at(-1);i.raw.length===1&&l!==void 0?l.raw+=`
`:t.push(i);continue}if(i=this.tokenizer.code(e)){e=e.substring(i.raw.length);const l=t.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+i.raw,l.text+=`
`+i.text,this.inlineQueue.at(-1).src=l.text):t.push(i);continue}if(i=this.tokenizer.fences(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.heading(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.hr(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.blockquote(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.list(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.html(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.def(e)){e=e.substring(i.raw.length);const l=t.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+i.raw,l.text+=`
`+i.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[i.tag]||(this.tokens.links[i.tag]={href:i.href,title:i.title});continue}if(i=this.tokenizer.table(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.lheading(e)){e=e.substring(i.raw.length),t.push(i);continue}let h=e;if((c=this.options.extensions)!=null&&c.startBlock){let l=1/0;const p=e.slice(1);let b;this.options.extensions.startBlock.forEach(g=>{b=g.call({lexer:this},p),typeof b=="number"&&b>=0&&(l=Math.min(l,b))}),l<1/0&&l>=0&&(h=e.substring(0,l+1))}if(this.state.top&&(i=this.tokenizer.paragraph(h))){const l=t.at(-1);n&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=`
`+i.raw,l.text+=`
`+i.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):t.push(i),n=h.length!==e.length,e=e.substring(i.raw.length);continue}if(i=this.tokenizer.text(e)){e=e.substring(i.raw.length);const l=t.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=`
`+i.raw,l.text+=`
`+i.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):t.push(i);continue}if(e){const l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var i,h,l;let n=e,s=null;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)p.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,c="";for(;e;){a||(c=""),a=!1;let p;if((h=(i=this.options.extensions)==null?void 0:i.inline)!=null&&h.some(g=>(p=g.call({lexer:this},e,t))?(e=e.substring(p.raw.length),t.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);const g=t.at(-1);p.type==="text"&&(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(p=this.tokenizer.emStrong(e,n,c)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),t.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),t.push(p);continue}let b=e;if((l=this.options.extensions)!=null&&l.startInline){let g=1/0;const E=e.slice(1);let T;this.options.extensions.startInline.forEach(A=>{T=A.call({lexer:this},E),typeof T=="number"&&T>=0&&(g=Math.min(g,T))}),g<1/0&&g>=0&&(b=e.substring(0,g+1))}if(p=this.tokenizer.inlineText(b)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(c=p.raw.slice(-1)),a=!0;const g=t.at(-1);(g==null?void 0:g.type)==="text"?(g.raw+=p.raw,g.text+=p.text):t.push(p);continue}if(e){const g="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(g);break}else throw new Error(g)}}return t}},_t=class{constructor(r){N(this,"options");N(this,"parser");this.options=r||Ce}space(r){return""}code({text:r,lang:e,escaped:t}){var a;const n=(a=(e||"").match(Q.notSpaceStart))==null?void 0:a[0],s=r.replace(Q.endingNewline,"")+`
`;return n?'<pre><code class="language-'+ce(n)+'">'+(t?s:ce(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:ce(s,!0))+`</code></pre>
`}blockquote({tokens:r}){return`<blockquote>
${this.parser.parse(r)}</blockquote>
`}html({text:r}){return r}heading({tokens:r,depth:e}){return`<h${e}>${this.parser.parseInline(r)}</h${e}>
`}hr(r){return`<hr>
`}list(r){const e=r.ordered,t=r.start;let n="";for(let c=0;c<r.items.length;c++){const i=r.items[c];n+=this.listitem(i)}const s=e?"ol":"ul",a=e&&t!==1?' start="'+t+'"':"";return"<"+s+a+`>
`+n+"</"+s+`>
`}listitem(r){var t;let e="";if(r.task){const n=this.checkbox({checked:!!r.checked});r.loose?((t=r.tokens[0])==null?void 0:t.type)==="paragraph"?(r.tokens[0].text=n+" "+r.tokens[0].text,r.tokens[0].tokens&&r.tokens[0].tokens.length>0&&r.tokens[0].tokens[0].type==="text"&&(r.tokens[0].tokens[0].text=n+" "+ce(r.tokens[0].tokens[0].text),r.tokens[0].tokens[0].escaped=!0)):r.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):e+=n+" "}return e+=this.parser.parse(r.tokens,!!r.loose),`<li>${e}</li>
`}checkbox({checked:r}){return"<input "+(r?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:r}){return`<p>${this.parser.parseInline(r)}</p>
`}table(r){let e="",t="";for(let s=0;s<r.header.length;s++)t+=this.tablecell(r.header[s]);e+=this.tablerow({text:t});let n="";for(let s=0;s<r.rows.length;s++){const a=r.rows[s];t="";for(let c=0;c<a.length;c++)t+=this.tablecell(a[c]);n+=this.tablerow({text:t})}return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+n+`</table>
`}tablerow({text:r}){return`<tr>
${r}</tr>
`}tablecell(r){const e=this.parser.parseInline(r.tokens),t=r.header?"th":"td";return(r.align?`<${t} align="${r.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:r}){return`<strong>${this.parser.parseInline(r)}</strong>`}em({tokens:r}){return`<em>${this.parser.parseInline(r)}</em>`}codespan({text:r}){return`<code>${ce(r,!0)}</code>`}br(r){return"<br>"}del({tokens:r}){return`<del>${this.parser.parseInline(r)}</del>`}link({href:r,title:e,tokens:t}){const n=this.parser.parseInline(t),s=Er(r);if(s===null)return n;r=s;let a='<a href="'+r+'"';return e&&(a+=' title="'+ce(e)+'"'),a+=">"+n+"</a>",a}image({href:r,title:e,text:t,tokens:n}){n&&(t=this.parser.parseInline(n,this.parser.textRenderer));const s=Er(r);if(s===null)return ce(t);r=s;let a=`<img src="${r}" alt="${t}"`;return e&&(a+=` title="${ce(e)}"`),a+=">",a}text(r){return"tokens"in r&&r.tokens?this.parser.parseInline(r.tokens):"escaped"in r&&r.escaped?r.text:ce(r.text)}},sr=class{strong({text:r}){return r}em({text:r}){return r}codespan({text:r}){return r}del({text:r}){return r}html({text:r}){return r}text({text:r}){return r}link({text:r}){return""+r}image({text:r}){return""+r}br(){return""}},ve=class jt{constructor(e){N(this,"options");N(this,"renderer");N(this,"textRenderer");this.options=e||Ce,this.options.renderer=this.options.renderer||new _t,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new sr}static parse(e,t){return new jt(t).parse(e)}static parseInline(e,t){return new jt(t).parseInline(e)}parse(e,t=!0){var s,a;let n="";for(let c=0;c<e.length;c++){const i=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[i.type]){const l=i,p=this.options.extensions.renderers[l.type].call({parser:this},l);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){n+=p||"";continue}}const h=i;switch(h.type){case"space":{n+=this.renderer.space(h);continue}case"hr":{n+=this.renderer.hr(h);continue}case"heading":{n+=this.renderer.heading(h);continue}case"code":{n+=this.renderer.code(h);continue}case"table":{n+=this.renderer.table(h);continue}case"blockquote":{n+=this.renderer.blockquote(h);continue}case"list":{n+=this.renderer.list(h);continue}case"html":{n+=this.renderer.html(h);continue}case"paragraph":{n+=this.renderer.paragraph(h);continue}case"text":{let l=h,p=this.renderer.text(l);for(;c+1<e.length&&e[c+1].type==="text";)l=e[++c],p+=`
`+this.renderer.text(l);t?n+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):n+=p;continue}default:{const l='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return n}parseInline(e,t=this.renderer){var s,a;let n="";for(let c=0;c<e.length;c++){const i=e[c];if((a=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&a[i.type]){const l=this.options.extensions.renderers[i.type].call({parser:this},i);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=l||"";continue}}const h=i;switch(h.type){case"escape":{n+=t.text(h);break}case"html":{n+=t.html(h);break}case"link":{n+=t.link(h);break}case"image":{n+=t.image(h);break}case"strong":{n+=t.strong(h);break}case"em":{n+=t.em(h);break}case"codespan":{n+=t.codespan(h);break}case"br":{n+=t.br(h);break}case"del":{n+=t.del(h);break}case"text":{n+=t.text(h);break}default:{const l='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return n}},Ht,xt=(Ht=class{constructor(r){N(this,"options");N(this,"block");this.options=r||Ce}preprocess(r){return r}postprocess(r){return r}processAllTokens(r){return r}provideLexer(){return this.block?xe.lex:xe.lexInline}provideParser(){return this.block?ve.parse:ve.parseInline}},N(Ht,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Ht),Os=class{constructor(...r){N(this,"defaults",Xt());N(this,"options",this.setOptions);N(this,"parse",this.parseMarkdown(!0));N(this,"parseInline",this.parseMarkdown(!1));N(this,"Parser",ve);N(this,"Renderer",_t);N(this,"TextRenderer",sr);N(this,"Lexer",xe);N(this,"Tokenizer",yt);N(this,"Hooks",xt);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const a of r)switch(t=t.concat(e.call(this,a)),a.type){case"table":{const c=a;for(const i of c.header)t=t.concat(this.walkTokens(i.tokens,e));for(const i of c.rows)for(const h of i)t=t.concat(this.walkTokens(h.tokens,e));break}case"list":{const c=a;t=t.concat(this.walkTokens(c.items,e));break}default:{const c=a;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(i=>{const h=c[i].flat(1/0);t=t.concat(this.walkTokens(h,e))}):c.tokens&&(t=t.concat(this.walkTokens(c.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=e.renderers[s.name];a?e.renderers[s.name]=function(...c){let i=s.renderer.apply(this,c);return i===!1&&(i=a.apply(this,c)),i}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=e[s.level];a?a.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new _t(this.defaults);for(const a in t.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const c=a,i=t.renderer[c],h=s[c];s[c]=(...l)=>{let p=i.apply(s,l);return p===!1&&(p=h.apply(s,l)),p||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new yt(this.defaults);for(const a in t.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const c=a,i=t.tokenizer[c],h=s[c];s[c]=(...l)=>{let p=i.apply(s,l);return p===!1&&(p=h.apply(s,l)),p}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new xt;for(const a in t.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const c=a,i=t.hooks[c],h=s[c];xt.passThroughHooks.has(a)?s[c]=l=>{if(this.defaults.async)return Promise.resolve(i.call(s,l)).then(b=>h.call(s,b));const p=i.call(s,l);return h.call(s,p)}:s[c]=(...l)=>{let p=i.apply(s,l);return p===!1&&(p=h.apply(s,l)),p}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,a=t.walkTokens;n.walkTokens=function(c){let i=[];return i.push(a.call(this,c)),s&&(i=i.concat(s.call(this,c))),i}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return xe.lex(r,e??this.defaults)}parser(r,e){return ve.parse(r,e??this.defaults)}parseMarkdown(r){return(t,n)=>{const s={...n},a={...this.defaults,...s},c=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&s.async===!1)return c(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return c(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return c(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=r);const i=a.hooks?a.hooks.provideLexer():r?xe.lex:xe.lexInline,h=a.hooks?a.hooks.provideParser():r?ve.parse:ve.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(l=>i(l,a)).then(l=>a.hooks?a.hooks.processAllTokens(l):l).then(l=>a.walkTokens?Promise.all(this.walkTokens(l,a.walkTokens)).then(()=>l):l).then(l=>h(l,a)).then(l=>a.hooks?a.hooks.postprocess(l):l).catch(c);try{a.hooks&&(t=a.hooks.preprocess(t));let l=i(t,a);a.hooks&&(l=a.hooks.processAllTokens(l)),a.walkTokens&&this.walkTokens(l,a.walkTokens);let p=h(l,a);return a.hooks&&(p=a.hooks.postprocess(p)),p}catch(l){return c(l)}}}onError(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+ce(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}}},Ae=new Os;function L(r,e){return Ae.parse(r,e)}L.options=L.setOptions=function(r){return Ae.setOptions(r),L.defaults=Ae.defaults,jr(L.defaults),L};L.getDefaults=Xt;L.defaults=Ce;L.use=function(...r){return Ae.use(...r),L.defaults=Ae.defaults,jr(L.defaults),L};L.walkTokens=function(r,e){return Ae.walkTokens(r,e)};L.parseInline=Ae.parseInline;L.Parser=ve;L.parser=ve.parse;L.Renderer=_t;L.TextRenderer=sr;L.Lexer=xe;L.lexer=xe.lex;L.Tokenizer=yt;L.Hooks=xt;L.parse=L;L.options;L.setOptions;L.use;L.walkTokens;L.parseInline;ve.parse;xe.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:rn,setPrototypeOf:Rr,isFrozen:Ps,getPrototypeOf:zs,getOwnPropertyDescriptor:$s}=Object;let{freeze:J,seal:ae,create:nn}=Object,{apply:qt,construct:Vt}=typeof Reflect<"u"&&Reflect;J||(J=function(e){return e});ae||(ae=function(e){return e});qt||(qt=function(e,t,n){return e.apply(t,n)});Vt||(Vt=function(e,t){return new e(...t)});const mt=ee(Array.prototype.forEach),Us=ee(Array.prototype.lastIndexOf),Dr=ee(Array.prototype.pop),et=ee(Array.prototype.push),Bs=ee(Array.prototype.splice),vt=ee(String.prototype.toLowerCase),zt=ee(String.prototype.toString),Mr=ee(String.prototype.match),tt=ee(String.prototype.replace),Fs=ee(String.prototype.indexOf),Hs=ee(String.prototype.trim),oe=ee(Object.prototype.hasOwnProperty),K=ee(RegExp.prototype.test),rt=Gs(TypeError);function ee(r){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return qt(r,e,n)}}function Gs(r){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Vt(r,t)}}function R(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:vt;Rr&&Rr(r,null);let n=e.length;for(;n--;){let s=e[n];if(typeof s=="string"){const a=t(s);a!==s&&(Ps(e)||(e[n]=a),s=a)}r[s]=!0}return r}function Ws(r){for(let e=0;e<r.length;e++)oe(r,e)||(r[e]=null);return r}function me(r){const e=nn(null);for(const[t,n]of rn(r))oe(r,t)&&(Array.isArray(n)?e[t]=Ws(n):n&&typeof n=="object"&&n.constructor===Object?e[t]=me(n):e[t]=n);return e}function nt(r,e){for(;r!==null;){const n=$s(r,e);if(n){if(n.get)return ee(n.get);if(typeof n.value=="function")return ee(n.value)}r=zs(r)}function t(){return null}return t}const Lr=J(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),$t=J(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ut=J(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),js=J(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Bt=J(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),qs=J(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Ir=J(["#text"]),Nr=J(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Ft=J(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Or=J(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),bt=J(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Vs=ae(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Ys=ae(/<%[\w\W]*|[\w\W]*%>/gm),Zs=ae(/\$\{[\w\W]*/gm),Xs=ae(/^data-[\-\w.\u00B7-\uFFFF]+$/),Ks=ae(/^aria-[\-\w]+$/),sn=ae(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Qs=ae(/^(?:\w+script|data):/i),Js=ae(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),an=ae(/^html$/i),ea=ae(/^[a-z][.\w]*(-[.\w]+)+$/i);var Pr=Object.freeze({__proto__:null,ARIA_ATTR:Ks,ATTR_WHITESPACE:Js,CUSTOM_ELEMENT:ea,DATA_ATTR:Xs,DOCTYPE_NAME:an,ERB_EXPR:Ys,IS_ALLOWED_URI:sn,IS_SCRIPT_OR_DATA:Qs,MUSTACHE_EXPR:Vs,TMPLIT_EXPR:Zs});const st={element:1,text:3,progressingInstruction:7,comment:8,document:9},ta=function(){return typeof window>"u"?null:window},ra=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(n=t.getAttribute(s));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML(c){return c},createScriptURL(c){return c}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},zr=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function on(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ta();const e=_=>on(_);if(e.version="3.2.6",e.removed=[],!r||!r.document||r.document.nodeType!==st.document||!r.Element)return e.isSupported=!1,e;let{document:t}=r;const n=t,s=n.currentScript,{DocumentFragment:a,HTMLTemplateElement:c,Node:i,Element:h,NodeFilter:l,NamedNodeMap:p=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:b,DOMParser:g,trustedTypes:E}=r,T=h.prototype,A=nt(T,"cloneNode"),C=nt(T,"remove"),B=nt(T,"nextSibling"),D=nt(T,"childNodes"),q=nt(T,"parentNode");if(typeof c=="function"){const _=t.createElement("template");_.content&&_.content.ownerDocument&&(t=_.content.ownerDocument)}let O,se="";const{implementation:re,createNodeIterator:le,createDocumentFragment:Se,getElementsByTagName:ke}=t,{importNode:Re}=n;let V=zr();e.isSupported=typeof rn=="function"&&typeof q=="function"&&re&&re.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Be,ERB_EXPR:Fe,TMPLIT_EXPR:He,DATA_ATTR:At,ARIA_ATTR:Ct,IS_SCRIPT_OR_DATA:Ge,ATTR_WHITESPACE:We,CUSTOM_ELEMENT:Rt}=Pr;let{IS_ALLOWED_URI:it}=Pr,H=null;const je=R({},[...Lr,...$t,...Ut,...Bt,...Ir]);let F=null;const qe=R({},[...Nr,...Ft,...Or,...bt]);let $=Object.seal(nn(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),we=null,Ve=null,De=!0,Ye=!0,ct=!1,ut=!0,ye=!1,Me=!0,de=!1,Ze=!1,x=!1,u=!1,Y=!1,P=!1,v=!0,pe=!1;const Le="user-content-";let Ie=!0,ne=!1,Ne={},Oe=null;const or=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let lr=null;const ir=R({},["audio","video","img","source","image","track"]);let Dt=null;const cr=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),dt="http://www.w3.org/1998/Math/MathML",pt="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml";let Pe=he,Mt=!1,Lt=null;const ln=R({},[dt,pt,he],zt);let ht=R({},["mi","mo","mn","ms","mtext"]),ft=R({},["annotation-xml"]);const cn=R({},["title","style","font","a","script"]);let Xe=null;const un=["application/xhtml+xml","text/html"],dn="text/html";let W=null,ze=null;const pn=t.createElement("form"),ur=function(o){return o instanceof RegExp||o instanceof Function},It=function(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(ze&&ze===o)){if((!o||typeof o!="object")&&(o={}),o=me(o),Xe=un.indexOf(o.PARSER_MEDIA_TYPE)===-1?dn:o.PARSER_MEDIA_TYPE,W=Xe==="application/xhtml+xml"?zt:vt,H=oe(o,"ALLOWED_TAGS")?R({},o.ALLOWED_TAGS,W):je,F=oe(o,"ALLOWED_ATTR")?R({},o.ALLOWED_ATTR,W):qe,Lt=oe(o,"ALLOWED_NAMESPACES")?R({},o.ALLOWED_NAMESPACES,zt):ln,Dt=oe(o,"ADD_URI_SAFE_ATTR")?R(me(cr),o.ADD_URI_SAFE_ATTR,W):cr,lr=oe(o,"ADD_DATA_URI_TAGS")?R(me(ir),o.ADD_DATA_URI_TAGS,W):ir,Oe=oe(o,"FORBID_CONTENTS")?R({},o.FORBID_CONTENTS,W):or,we=oe(o,"FORBID_TAGS")?R({},o.FORBID_TAGS,W):me({}),Ve=oe(o,"FORBID_ATTR")?R({},o.FORBID_ATTR,W):me({}),Ne=oe(o,"USE_PROFILES")?o.USE_PROFILES:!1,De=o.ALLOW_ARIA_ATTR!==!1,Ye=o.ALLOW_DATA_ATTR!==!1,ct=o.ALLOW_UNKNOWN_PROTOCOLS||!1,ut=o.ALLOW_SELF_CLOSE_IN_ATTR!==!1,ye=o.SAFE_FOR_TEMPLATES||!1,Me=o.SAFE_FOR_XML!==!1,de=o.WHOLE_DOCUMENT||!1,u=o.RETURN_DOM||!1,Y=o.RETURN_DOM_FRAGMENT||!1,P=o.RETURN_TRUSTED_TYPE||!1,x=o.FORCE_BODY||!1,v=o.SANITIZE_DOM!==!1,pe=o.SANITIZE_NAMED_PROPS||!1,Ie=o.KEEP_CONTENT!==!1,ne=o.IN_PLACE||!1,it=o.ALLOWED_URI_REGEXP||sn,Pe=o.NAMESPACE||he,ht=o.MATHML_TEXT_INTEGRATION_POINTS||ht,ft=o.HTML_INTEGRATION_POINTS||ft,$=o.CUSTOM_ELEMENT_HANDLING||{},o.CUSTOM_ELEMENT_HANDLING&&ur(o.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&($.tagNameCheck=o.CUSTOM_ELEMENT_HANDLING.tagNameCheck),o.CUSTOM_ELEMENT_HANDLING&&ur(o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&($.attributeNameCheck=o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),o.CUSTOM_ELEMENT_HANDLING&&typeof o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&($.allowCustomizedBuiltInElements=o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ye&&(Ye=!1),Y&&(u=!0),Ne&&(H=R({},Ir),F=[],Ne.html===!0&&(R(H,Lr),R(F,Nr)),Ne.svg===!0&&(R(H,$t),R(F,Ft),R(F,bt)),Ne.svgFilters===!0&&(R(H,Ut),R(F,Ft),R(F,bt)),Ne.mathMl===!0&&(R(H,Bt),R(F,Or),R(F,bt))),o.ADD_TAGS&&(H===je&&(H=me(H)),R(H,o.ADD_TAGS,W)),o.ADD_ATTR&&(F===qe&&(F=me(F)),R(F,o.ADD_ATTR,W)),o.ADD_URI_SAFE_ATTR&&R(Dt,o.ADD_URI_SAFE_ATTR,W),o.FORBID_CONTENTS&&(Oe===or&&(Oe=me(Oe)),R(Oe,o.FORBID_CONTENTS,W)),Ie&&(H["#text"]=!0),de&&R(H,["html","head","body"]),H.table&&(R(H,["tbody"]),delete we.tbody),o.TRUSTED_TYPES_POLICY){if(typeof o.TRUSTED_TYPES_POLICY.createHTML!="function")throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof o.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');O=o.TRUSTED_TYPES_POLICY,se=O.createHTML("")}else O===void 0&&(O=ra(E,s)),O!==null&&typeof se=="string"&&(se=O.createHTML(""));J&&J(o),ze=o}},dr=R({},[...$t,...Ut,...js]),pr=R({},[...Bt,...qs]),hn=function(o){let m=q(o);(!m||!m.tagName)&&(m={namespaceURI:Pe,tagName:"template"});const k=vt(o.tagName),z=vt(m.tagName);return Lt[o.namespaceURI]?o.namespaceURI===pt?m.namespaceURI===he?k==="svg":m.namespaceURI===dt?k==="svg"&&(z==="annotation-xml"||ht[z]):!!dr[k]:o.namespaceURI===dt?m.namespaceURI===he?k==="math":m.namespaceURI===pt?k==="math"&&ft[z]:!!pr[k]:o.namespaceURI===he?m.namespaceURI===pt&&!ft[z]||m.namespaceURI===dt&&!ht[z]?!1:!pr[k]&&(cn[k]||!dr[k]):!!(Xe==="application/xhtml+xml"&&Lt[o.namespaceURI]):!1},ie=function(o){et(e.removed,{element:o});try{q(o).removeChild(o)}catch{C(o)}},$e=function(o,m){try{et(e.removed,{attribute:m.getAttributeNode(o),from:m})}catch{et(e.removed,{attribute:null,from:m})}if(m.removeAttribute(o),o==="is")if(u||Y)try{ie(m)}catch{}else try{m.setAttribute(o,"")}catch{}},hr=function(o){let m=null,k=null;if(x)o="<remove></remove>"+o;else{const G=Mr(o,/^[\r\n\t ]+/);k=G&&G[0]}Xe==="application/xhtml+xml"&&Pe===he&&(o='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+o+"</body></html>");const z=O?O.createHTML(o):o;if(Pe===he)try{m=new g().parseFromString(z,Xe)}catch{}if(!m||!m.documentElement){m=re.createDocument(Pe,"template",null);try{m.documentElement.innerHTML=Mt?se:z}catch{}}const Z=m.body||m.documentElement;return o&&k&&Z.insertBefore(t.createTextNode(k),Z.childNodes[0]||null),Pe===he?ke.call(m,de?"html":"body")[0]:de?m.documentElement:Z},fr=function(o){return le.call(o.ownerDocument||o,o,l.SHOW_ELEMENT|l.SHOW_COMMENT|l.SHOW_TEXT|l.SHOW_PROCESSING_INSTRUCTION|l.SHOW_CDATA_SECTION,null)},Nt=function(o){return o instanceof b&&(typeof o.nodeName!="string"||typeof o.textContent!="string"||typeof o.removeChild!="function"||!(o.attributes instanceof p)||typeof o.removeAttribute!="function"||typeof o.setAttribute!="function"||typeof o.namespaceURI!="string"||typeof o.insertBefore!="function"||typeof o.hasChildNodes!="function")},gr=function(o){return typeof i=="function"&&o instanceof i};function fe(_,o,m){mt(_,k=>{k.call(e,o,m,ze)})}const mr=function(o){let m=null;if(fe(V.beforeSanitizeElements,o,null),Nt(o))return ie(o),!0;const k=W(o.nodeName);if(fe(V.uponSanitizeElement,o,{tagName:k,allowedTags:H}),Me&&o.hasChildNodes()&&!gr(o.firstElementChild)&&K(/<[/\w!]/g,o.innerHTML)&&K(/<[/\w!]/g,o.textContent)||o.nodeType===st.progressingInstruction||Me&&o.nodeType===st.comment&&K(/<[/\w]/g,o.data))return ie(o),!0;if(!H[k]||we[k]){if(!we[k]&&xr(k)&&($.tagNameCheck instanceof RegExp&&K($.tagNameCheck,k)||$.tagNameCheck instanceof Function&&$.tagNameCheck(k)))return!1;if(Ie&&!Oe[k]){const z=q(o)||o.parentNode,Z=D(o)||o.childNodes;if(Z&&z){const G=Z.length;for(let te=G-1;te>=0;--te){const ge=A(Z[te],!0);ge.__removalCount=(o.__removalCount||0)+1,z.insertBefore(ge,B(o))}}}return ie(o),!0}return o instanceof h&&!hn(o)||(k==="noscript"||k==="noembed"||k==="noframes")&&K(/<\/no(script|embed|frames)/i,o.innerHTML)?(ie(o),!0):(ye&&o.nodeType===st.text&&(m=o.textContent,mt([Be,Fe,He],z=>{m=tt(m,z," ")}),o.textContent!==m&&(et(e.removed,{element:o.cloneNode()}),o.textContent=m)),fe(V.afterSanitizeElements,o,null),!1)},br=function(o,m,k){if(v&&(m==="id"||m==="name")&&(k in t||k in pn))return!1;if(!(Ye&&!Ve[m]&&K(At,m))){if(!(De&&K(Ct,m))){if(!F[m]||Ve[m]){if(!(xr(o)&&($.tagNameCheck instanceof RegExp&&K($.tagNameCheck,o)||$.tagNameCheck instanceof Function&&$.tagNameCheck(o))&&($.attributeNameCheck instanceof RegExp&&K($.attributeNameCheck,m)||$.attributeNameCheck instanceof Function&&$.attributeNameCheck(m))||m==="is"&&$.allowCustomizedBuiltInElements&&($.tagNameCheck instanceof RegExp&&K($.tagNameCheck,k)||$.tagNameCheck instanceof Function&&$.tagNameCheck(k))))return!1}else if(!Dt[m]){if(!K(it,tt(k,We,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&o!=="script"&&Fs(k,"data:")===0&&lr[o])){if(!(ct&&!K(Ge,tt(k,We,"")))){if(k)return!1}}}}}}return!0},xr=function(o){return o!=="annotation-xml"&&Mr(o,Rt)},vr=function(o){fe(V.beforeSanitizeAttributes,o,null);const{attributes:m}=o;if(!m||Nt(o))return;const k={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let z=m.length;for(;z--;){const Z=m[z],{name:G,namespaceURI:te,value:ge}=Z,Ke=W(G),Ot=ge;let X=G==="value"?Ot:Hs(Ot);if(k.attrName=Ke,k.attrValue=X,k.keepAttr=!0,k.forceKeepAttr=void 0,fe(V.uponSanitizeAttribute,o,k),X=k.attrValue,pe&&(Ke==="id"||Ke==="name")&&($e(G,o),X=Le+X),Me&&K(/((--!?|])>)|<\/(style|title)/i,X)){$e(G,o);continue}if(k.forceKeepAttr)continue;if(!k.keepAttr){$e(G,o);continue}if(!ut&&K(/\/>/i,X)){$e(G,o);continue}ye&&mt([Be,Fe,He],wr=>{X=tt(X,wr," ")});const kr=W(o.nodeName);if(!br(kr,Ke,X)){$e(G,o);continue}if(O&&typeof E=="object"&&typeof E.getAttributeType=="function"&&!te)switch(E.getAttributeType(kr,Ke)){case"TrustedHTML":{X=O.createHTML(X);break}case"TrustedScriptURL":{X=O.createScriptURL(X);break}}if(X!==Ot)try{te?o.setAttributeNS(te,G,X):o.setAttribute(G,X),Nt(o)?ie(o):Dr(e.removed)}catch{$e(G,o)}}fe(V.afterSanitizeAttributes,o,null)},fn=function _(o){let m=null;const k=fr(o);for(fe(V.beforeSanitizeShadowDOM,o,null);m=k.nextNode();)fe(V.uponSanitizeShadowNode,m,null),mr(m),vr(m),m.content instanceof a&&_(m.content);fe(V.afterSanitizeShadowDOM,o,null)};return e.sanitize=function(_){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,k=null,z=null,Z=null;if(Mt=!_,Mt&&(_="<!-->"),typeof _!="string"&&!gr(_))if(typeof _.toString=="function"){if(_=_.toString(),typeof _!="string")throw rt("dirty is not a string, aborting")}else throw rt("toString is not a function");if(!e.isSupported)return _;if(Ze||It(o),e.removed=[],typeof _=="string"&&(ne=!1),ne){if(_.nodeName){const ge=W(_.nodeName);if(!H[ge]||we[ge])throw rt("root node is forbidden and cannot be sanitized in-place")}}else if(_ instanceof i)m=hr("<!---->"),k=m.ownerDocument.importNode(_,!0),k.nodeType===st.element&&k.nodeName==="BODY"||k.nodeName==="HTML"?m=k:m.appendChild(k);else{if(!u&&!ye&&!de&&_.indexOf("<")===-1)return O&&P?O.createHTML(_):_;if(m=hr(_),!m)return u?null:P?se:""}m&&x&&ie(m.firstChild);const G=fr(ne?_:m);for(;z=G.nextNode();)mr(z),vr(z),z.content instanceof a&&fn(z.content);if(ne)return _;if(u){if(Y)for(Z=Se.call(m.ownerDocument);m.firstChild;)Z.appendChild(m.firstChild);else Z=m;return(F.shadowroot||F.shadowrootmode)&&(Z=Re.call(n,Z,!0)),Z}let te=de?m.outerHTML:m.innerHTML;return de&&H["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&K(an,m.ownerDocument.doctype.name)&&(te="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+te),ye&&mt([Be,Fe,He],ge=>{te=tt(te,ge," ")}),O&&P?O.createHTML(te):te},e.setConfig=function(){let _=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};It(_),Ze=!0},e.clearConfig=function(){ze=null,Ze=!1},e.isValidAttribute=function(_,o,m){ze||It({});const k=W(_),z=W(o);return br(k,z,m)},e.addHook=function(_,o){typeof o=="function"&&et(V[_],o)},e.removeHook=function(_,o){if(o!==void 0){const m=Us(V[_],o);return m===-1?void 0:Bs(V[_],m,1)[0]}return Dr(V[_])},e.removeHooks=function(_){V[_]=[]},e.removeAllHooks=function(){V=zr()},e}var na=on();const sa={class:"flex-1"},aa={class:"flex items-center"},oa={class:"relative"},la={key:1,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5 sticky top-[40px] z-10"},ia={key:0,class:"flex items-center gap-1 text-gray-500 text-xs"},ca={key:1,class:"flex flex-col gap-1"},ua=["onClick"],da={key:2,class:"bg-[var(--bg-color)] border-b border-[var(--border-color)] py-0.5 px-0.5"},pa={class:"flex items-center gap-2"},ha={class:"flex-1 overflow-y-auto overflow-x-hidden"},fa={class:"px-0.5"},ga={class:"mb-1"},ma={key:0},ba={key:1,class:"text-gray-500"},xa={class:"flex items-center justify-between mt-0.5"},va={class:"flex items-center gap-1"},ka={class:"flex flex-wrap gap-1"},wa=["onClick"],ya={key:0,class:"flex items-start gap-1.5 max-w-full"},_a={class:"flex-1 bg-[var(--bg-color)] rounded-lg p-2 shadow-sm break-words"},Ta={class:"text-[var(--text-color)] leading-tight text-[15px]"},Sa=["innerHTML"],Ea={key:1,class:"h-full flex items-center justify-center text-gray-500 text-[15px]"},Aa={class:"border-t border-[var(--border-color)] bg-[var(--bg-color)] w-full input-bar-container"},Ca={class:"flex items-center justify-between"},Ra={class:"text-xs opacity-75"},Da={class:"p-0.5 max-w-full"},Ma=["placeholder","disabled"],La={class:"flex items-center justify-between"},Ia={class:"flex items-center gap-1"},Na={class:"relative model-selector"},Oa={class:"max-h-[60vh] overflow-y-auto"},Pa=["onClick"],za={class:"flex items-center gap-2"},$a=["disabled"],Ua={class:"flex-1 overflow-y-auto p-4"},Ba={class:"flex-1 min-w-0"},Fa={class:"flex items-center gap-2"},Ha={class:"font-medium text-gray-900 truncate"},Ga={class:"text-xs text-gray-400"},Wa={key:0,class:"text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full"},ja={class:"text-xs text-gray-500 break-all"},qa={class:"flex items-center gap-1"},Va=["onClick"],Ya=["onClick"],Za={key:0,class:"text-center text-gray-500 py-8"},Xa={key:4,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ka={class:"add-model-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 flex flex-col"},Qa={class:"p-4 space-y-4"},Ja={key:0,class:"mt-4"},eo={class:"flex items-center gap-2"},to={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},ro={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},no={class:"p-4 border-t border-[var(--border-color)] flex gap-2"},so=["disabled"],ao={key:0,class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},oo=Yt({__name:"Main",setup(r){const e=Wr(),t=Hr(),n=Tt(),s=Zt(),a=Fr(),c=$r(),i=Gr(),h=M(!1),l=M(!1),p=M(null),b=M(""),g=M({entryDisplayConfig:{skipJsCss:!0,excludedPathPatterns:[]}}),E=M(!1),T=M(""),A=M(!1),C=M(!1),B=M(!1),D=M({showName:"",baseUrl:"",modelName:"",apiKey:""}),q=M(null),O=M(!1);let se=null;const re=M(!0),le=M(!0),Se=ue;let ke=0;const Re=M(null),V=Ue(()=>!t.currentDatabase||t.currentDatabaseId===ue?"请选择数据源再对话":t.currentDatabase.dataId?"Type your message...":"请先录制程序数据然后再对话"),Be=Ue(()=>!t.currentDatabase||!t.currentDatabase.dataId||s.status==="waiting"),Fe=()=>{var x;return t.databases.length===1&&t.currentDatabaseId===ue?"当前无可用的数据源，请使用 Debug with XCodeMap 录制程序数据。":t.currentDatabaseId===ue?"请先选择数据源再对话":(x=t.currentDatabase)!=null&&x.dataId?"你好！我是你的代码排查助手 XCodeMap。请告诉我具体是哪一个函数调用（CallID_Number）让你感到困惑，或者提供相关的类名、函数名等信息，我将尽力为你分析和解释。":"请先录制程序数据然后再对话"},He=async()=>{var u;const x=i.getText();if(x.trim()){if(!((u=t.currentDatabase)!=null&&u.dataId)){n.setError("当前没有数据，不可聊天。请先选择数据源。或者使用 Debug with XCodeMap 创建新的数据源。");return}if(!a.currentModel){n.setError("请先选择模型再发送消息"),A.value=!0;return}i.clearText(),await e.sendMessage(x)}},At=()=>{h.value=!h.value},Ct=async x=>{var P;const u=t.currentDatabase,Y=!u||u.id!==x;if(ke=0,t.setCurrentDatabase(x),h.value=!1,Y){if(!await yr(x,((P=t.currentDatabase)==null?void 0:P.dataId)||"")){n.setError("Failed to switch process data");return}e.createNewChat(),p.value=null,re.value=!0,l.value=!0}},Ge=async()=>{var x;if((x=t.currentDatabase)!=null&&x.dataId)try{const u=await Ln({processId:t.currentDatabase.dataId,first:re.value,filterText:b.value}),Y=JSON.stringify(u),P=JSON.stringify(p.value);Y!==P&&(console.log("Data has changed, updating treeData",Y),p.value=u),re.value=!1}catch(u){console.error("Failed to fetch tree data:",u)}},We=async()=>{const x=[...t.databases],u=t.currentDatabase;await t.getDatabase();const Y=t.databases,P=t.currentDatabase,v=new Set(x.map(ne=>ne.id)),pe=Y.filter(ne=>!v.has(ne.id)),Le=x.length===0||x.length===1&&x[0].id===Se;if(pe.filter(ne=>!ne.id.toLowerCase().includes("local")&&!ne.id.toLowerCase().includes("remote")).length>0&&!Le&&n.setError("发现了新的数据源"),u&&u.id!==Se&&!P){console.log("Current database is no longer available, resetting to default"),t.setCurrentDatabase(Se),h.value=!0,p.value=null,re.value=!0,l.value=!1,e.createNewChat(),ke=0;return}if(u&&P&&u.id===P.id&&u.dataId!==P.dataId&&!await yr(P.id,P.dataId||"")){n.setError("Failed to switch process data");return}P&&P.id!==Se&&(P.serverSelected?ke=0:(ke++,ke>=3&&(console.log("Database consistency check failed 3 times, resetting to default"),t.setCurrentDatabase(Se),h.value=!0,p.value=null,re.value=!0,l.value=!1,e.createNewChat(),ke=0)))};_n(()=>{Ge()});const Rt=x=>{x.labelKey==="url"&&x.labelValue&&window.open(x.labelValue,"_blank"),console.log("Clicked tree node:",x)},it=x=>{const u=g.value.entryDisplayConfig.excludedPathPatterns.indexOf(x);u>-1&&(g.value.entryDisplayConfig.excludedPathPatterns.splice(u,1),F())},H=()=>{E.value=!0,_r(()=>{const x=document.querySelector(".input-new-tag input");x&&x.focus()})},je=()=>{T.value&&(g.value.entryDisplayConfig.excludedPathPatterns.push(T.value),F()),E.value=!1,T.value=""},F=async()=>{try{await In(g.value)||n.setError("Failed to update filter configuration")}catch(x){console.error("Failed to update filter configuration:",x)}},qe=x=>{const u=x.target;u.closest(".model-selector")||(A.value=!1),u.closest(".model-manager-modal")||(C.value=!1)},$=async()=>{A.value=!1;try{await a.getModelConfigData(),C.value=!0}catch(x){console.error("Failed to open model manager:",x),n.setError("Failed to open model manager")}},we=()=>{C.value=!1},Ve=()=>{B.value=!0,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""}},De=()=>{B.value=!1,D.value={showName:"",baseUrl:"",modelName:"",apiKey:""},q.value=null},Ye=async()=>{const x={showName:D.value.showName.trim(),baseUrl:D.value.baseUrl.trim(),modelName:D.value.modelName.trim(),apiKey:D.value.apiKey.trim()};if(!x.showName||!x.baseUrl||!x.modelName||!x.apiKey){n.setError("请填写所有必填字段");return}O.value=!0,q.value=null;const u={uuid:crypto.randomUUID(),showName:x.showName,baseUrl:x.baseUrl,modelName:x.modelName,apiKey:x.apiKey},Y=await Nn(u);if(!Y.success){q.value={success:!1,message:Y.error||"模型连通性测试失败"},O.value=!1;return}const P=await On(u);P.success?(De(),await a.getModelConfigData()):q.value={success:!1,message:P.error||"添加模型失败"},O.value=!1},ct=async x=>{const u=await Pn(x);u.success?await a.getModelConfigData():n.setError(u.error||"删除模型失败")},ut=async x=>{const u=await zn(x);u.success?await a.getModelConfigData():n.setError(u.error||"设置默认模型失败")};Br(()=>{c.initTheme(),We(),Ge(),Tn().then(x=>{x.success&&x.data&&(g.value.entryDisplayConfig=x.data.entryDisplayConfig)}),a.getModelConfigData(),document.addEventListener("click",qe),se=window.setInterval(()=>{We(),Ge()},1e3)}),Sn(()=>{se!==null&&(clearInterval(se),se=null),document.removeEventListener("click",qe)});const ye=(x,u)=>u!=="tool"?x.replace(/`?CallID_(\d+)`?/g,"[CallID_$1](./findFunc?callId=$1)"):x,Me=x=>{L.setOptions({breaks:!0,gfm:!0,pedantic:!0});const u=L.parse(x);return na.sanitize(u,{ALLOWED_TAGS:["p","br","strong","em","code","pre","blockquote","ul","ol","li","h1","h2","h3","h4","h5","h6","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title","class"]})},de=x=>{const u=x.target;u.style.height="auto",u.style.height=Math.min(u.scrollHeight,200)+"px"},Ze=()=>{_r(()=>{Re.value&&(Re.value.scrollTop=Re.value.scrollHeight)})};return kt(()=>{var x;return(x=e.currentChat)==null?void 0:x.messages},()=>{var x;Ze(),(x=e.currentChat)!=null&&x.messages&&e.currentChat.messages.length>0&&(l.value=!1)},{deep:!0}),kt(()=>i.message,()=>{i.message.trim()&&(l.value=!1)}),(x,u)=>{var Y,P;return w(),y("div",{class:S(["h-screen w-full flex flex-col overflow-x-hidden",(f(c).theme==="dark","bg-[var(--bg-color)]")])},[f(n).error?(w(),y("div",{key:0,class:S(["fixed top-0 left-0 right-0 px-4 py-3 z-[9999] flex items-center justify-between",f(c).theme==="dark"?"bg-red-900 text-red-100":"bg-red-100 text-red-700"]),role:"alert"},[d("span",sa,j(f(n).error.message),1),d("button",{onClick:u[0]||(u[0]=v=>f(n).clearError()),class:S(["ml-4 p-1 rounded-full hover:bg-opacity-20",f(c).theme==="dark"?"hover:bg-red-100":"hover:bg-red-200"])},u[18]||(u[18]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),2)],2)):U("",!0),d("div",{class:S(["border-b py-0.5 px-0.5 flex items-center justify-between sticky top-0 z-20 bg-[var(--bg-color)]",(f(c).theme==="dark","border-[var(--border-color)]")])},[d("div",aa,[d("div",oa,[d("button",{onClick:At,class:S(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,j(f(t).currentDatabase?f(t).currentDatabase.name:"请选择数据源"),1),(w(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":h.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[19]||(u[19]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2)])]),d("button",{onClick:u[1]||(u[1]=()=>{f(e).createNewChat(),le.value=!0}),class:S(["p-1 rounded-full",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"]),title:"新建聊天"},u[20]||(u[20]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)],2),h.value||f(t).currentDatabaseId===f(ue)?(w(),y("div",la,[f(t).databases.length===1?(w(),y("div",ia,u[21]||(u[21]=[d("span",null,"当前无可用的数据源，请使用",-1),d("img",{src:Bn,alt:"XCodeMap Logo",class:"w-4 h-4"},null,-1),d("span",null,"Debug with XCodeMap 录制程序数据。",-1)]))):(w(),y("div",ca,[(w(!0),y(be,null,Te(f(t).databases,v=>(w(),y("button",{key:v.id,onClick:pe=>Ct(v.id),class:S(["w-full px-2 py-0.5 rounded-lg text-xs font-medium text-left border-2 transition-all duration-150 focus:outline-none",[v.id===f(t).currentDatabaseId?f(c).theme==="dark"?"button-selected-dark":"button-selected-light":f(c).theme==="dark"?"button-hover-dark":"button-hover-light"]])},j(v.name),11,ua))),128))]))])):U("",!0),f(t).currentDatabase&&f(t).currentDatabase.active?(w(),y("div",da,[d("div",pa,[d("span",{class:S(["px-1.5 py-0.5 text-xs rounded-full opacity-75",{"bg-green-50 text-green-600":f(t).currentDatabase.recordState==="recording","bg-gray-50 text-gray-500":f(t).currentDatabase.recordState==="idle","bg-yellow-50 text-yellow-600":f(t).currentDatabase.recordState==="paused"}])},j(f(t).currentDatabase.recordState),3),f(t).currentDatabase.recordState==="idle"?(w(),y("button",{key:0,onClick:u[2]||(u[2]=v=>f(t).startRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-green-50 text-green-600 hover:bg-green-200 hover:text-green-800 border border-green-200 hover:border-green-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"开始录制"}," 开始录制 ")):U("",!0),f(t).currentDatabase.recordState==="recording"?(w(),y("button",{key:1,onClick:u[3]||(u[3]=v=>f(t).endRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-red-50 text-red-600 hover:bg-red-200 hover:text-red-800 border border-red-200 hover:border-red-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"结束录制"}," 结束录制 ")):U("",!0),f(t).currentDatabase.recordState==="paused"?(w(),y("button",{key:2,onClick:u[4]||(u[4]=v=>f(t).restartRecord(f(t).currentDatabase.id)),class:"text-xs px-2 py-0.5 rounded-md bg-yellow-50 text-yellow-600 hover:bg-yellow-200 hover:text-yellow-800 border border-yellow-200 hover:border-yellow-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105",title:"重新录制"}," 重新录制 ")):U("",!0)])])):U("",!0),d("div",ha,[f(t).currentDatabaseId!==f(ue)?(w(),y("div",{key:0,class:S([(f(c).theme==="dark","bg-[var(--bg-color)] border-[var(--border-color)]"),"border-b border-t"])},[d("div",fa,[d("div",{onClick:u[5]||(u[5]=v=>l.value=!l.value),class:S(["flex items-center cursor-pointer rounded-lg px-1 py-0.5 border-b border-[var(--border-color)] bg-[var(--undercaret-bg-color)] transition-colors duration-150",(f(c).theme==="dark","hover:bg-[var(--header-hover-bg-color)]")])},[(w(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-3 w-3 transition-transform duration-200",{"transform rotate-90":l.value,"text-[var(--text-color)]":!0}]),viewBox:"0 0 20 20",fill:"currentColor"},u[22]||(u[22]=[d("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2)),u[23]||(u[23]=d("span",{class:"text-xs font-medium px-1 text-[var(--text-color)]"},"请求与线程入口",-1))],2)]),l.value?(w(),y("div",{key:0,class:S(["p-1 bg-[var(--undercaret-bg-color)] overflow-y-auto",(f(c).theme==="dark","border-t border-[var(--border-color)]")])},[d("div",ga,[_e(d("input",{"onUpdate:modelValue":u[6]||(u[6]=v=>b.value=v),type:"text",placeholder:"搜索网络请求...",class:S(["w-full px-2 py-0.5 rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400":"bg-white border-gray-300 text-gray-700"])},null,2),[[Ee,b.value]])]),p.value?(w(),y("div",ma,[En(Qn,{nodes:p.value.rootNodes,onNodeClick:Rt},null,8,["nodes"])])):(w(),y("div",ba,"Loading tree data...")),d("div",xa,[d("div",va,[d("span",{class:S(["text-xs opacity-50",f(c).theme==="dark"?"text-gray-400":"text-gray-600"])},"排除请求：",2),d("label",{class:S(["flex items-center gap-1 text-xs cursor-pointer opacity-50 hover:opacity-100 transition-opacity",f(c).theme==="dark"?"text-gray-500":"text-gray-400"])},[_e(d("input",{type:"checkbox","onUpdate:modelValue":u[7]||(u[7]=v=>g.value.entryDisplayConfig.skipJsCss=v),onChange:F,class:S(["rounded focus:ring-0 focus:ring-offset-0 h-2.5 w-2.5 cursor-pointer",f(c).theme==="dark"?"bg-gray-800 border-gray-600 text-gray-500":"border-gray-300 text-gray-400"])},null,34),[[An,g.value.entryDisplayConfig.skipJsCss]]),u[24]||(u[24]=d("span",{class:"text-[11px]"},"忽略css/js",-1))],2),d("div",ka,[(w(!0),y(be,null,Te(g.value.entryDisplayConfig.excludedPathPatterns,v=>(w(),y("div",{key:v,class:S(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"])},[d("span",null,j(v),1),d("button",{onClick:pe=>it(v),class:S(f(c).theme==="dark"?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700")},u[25]||(u[25]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,wa)],2))),128)),E.value?_e((w(),y("input",{key:0,"onUpdate:modelValue":u[8]||(u[8]=v=>T.value=v),class:S(["input-new-tag px-1 py-0 border rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",f(c).theme==="dark"?"bg-gray-700 border-gray-600 text-gray-200":"border-gray-300 text-gray-700"]),placeholder:"输入排除表达式，* 匹配一级路径，** 匹配多级路径",onKeyup:Cn(je,["enter"]),onBlur:je},null,34)),[[Ee,T.value]]):(w(),y("button",{key:1,onClick:H,class:S(["inline-flex items-center gap-0.5 px-1 py-0 rounded-full text-xs",f(c).theme==="dark"?"bg-gray-700 hover:bg-gray-600 text-blue-400":"bg-gray-100 hover:bg-gray-200 text-blue-700"])},u[26]||(u[26]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-2.5 w-2.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),d("span",null,"New Filter",-1)]),2))])]),d("button",{onClick:u[9]||(u[9]=v=>l.value=!1),class:S(["rounded-full flex items-center",f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"]),title:"收起网络请求"},u[27]||(u[27]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{"fill-rule":"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),2)])],2)):U("",!0)],2)):U("",!0),d("div",{ref_key:"messageContainerRef",ref:Re,class:"p-0.5 space-y-0.5 w-full border-b border-[var(--border-color)]"},[f(e).currentChat?(w(),y(be,{key:0},[f(e).currentChat.messages.length===0?(w(),y("div",ya,[d("div",_a,[d("p",Ta,[(w(),Rn(Jn,{key:(Y=f(e).currentChat)==null?void 0:Y.id,text:Fe(),speed:20,onComplete:u[10]||(u[10]=v=>le.value=!1)},null,8,["text"]))])])])):U("",!0),(w(!0),y(be,null,Te(f(e).currentChat.messages,v=>(w(),y("div",{key:v.messageId,class:S(["flex items-start gap-1.5 max-w-full",{"justify-end":v.role==="user"}])},[d("div",{class:S(["flex-1 rounded-lg p-2 shadow-sm break-words min-w-0",v.role==="user"?(f(c).theme==="dark","bg-[var(--undercaret-bg-color)]"):(f(c).theme==="dark","bg-[var(--bg-color)]")])},[d("div",{class:S([v.role==="user"?(f(c).theme==="dark","text-[var(--text-color)]"):(f(c).theme==="dark","text-[var(--text-color)]"),"leading-tight text-[15px] markdown-content break-words whitespace-pre-wrap"]),innerHTML:Me(ye(v.content,v.role))},null,10,Sa)],2)],2))),128))],64)):(w(),y("div",Ea," 请选择数据源后再对话 "))],512)]),d("div",Aa,[f(s).status==="waiting"&&f(t).currentDatabase&&f(t).currentDatabaseId!==f(ue)?(w(),y("div",{key:0,class:S(["border-t border-b py-1.5 px-3",(f(c).theme==="dark","bg-[var(--undercaret-bg-color)] border-[var(--border-color)] text-[var(--text-color)]")])},[d("div",Ca,[d("span",Ra,j(f(s).statusMessage||"未知状态..."),1)])],2)):U("",!0),d("div",Da,[_e(d("textarea",{"onUpdate:modelValue":u[11]||(u[11]=v=>f(i).message=v),class:"input w-full resize-none mb-0.5",rows:"1",placeholder:V.value,disabled:Be.value,onInput:de,style:{"min-height":"28px","max-height":"200px","overflow-y":"auto"}},null,40,Ma),[[Ee,f(i).message]]),d("div",La,[d("div",Ia,[d("span",{class:S(["text-xs",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])},"模型",2),d("div",Na,[d("button",{onClick:u[12]||(u[12]=v=>A.value=!A.value),class:S(["flex items-center gap-0.5 px-1 py-0.5 text-xs rounded-lg transition-colors",f(c).theme==="dark"?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-100"])},[d("span",null,j(((P=f(a).currentModel)==null?void 0:P.showName)||"请选择模型"),1),(w(),y("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-3 w-3 transition-transform duration-200",{"transform rotate-180":A.value}]),viewBox:"0 0 20 20",fill:"currentColor"},u[28]||(u[28]=[d("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]),2))],2),A.value?(w(),y("div",{key:0,class:S(["absolute bottom-full left-0 mb-2 w-80 border rounded-lg shadow-lg z-50",f(c).theme==="dark"?"bg-gray-800 border-gray-700":"bg-white border-gray-200"])},[d("div",{class:S(["p-2 border-b",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("span",{class:S(["text-sm font-medium",f(c).theme==="dark"?"text-gray-200":"text-gray-700"])},"选择模型",2)],2),d("div",Oa,[(w(!0),y(be,null,Te(f(a).availableModels,v=>(w(),y("button",{key:v.uuid,onClick:()=>{f(a).setCurrentModel(v.uuid),A.value=!1},class:S(["w-full px-3 py-2 text-left text-sm transition-all duration-150 border-2 focus:outline-none",{"button-selected-dark":f(a).currentModelUuid===v.uuid&&f(c).theme==="dark","button-selected-light":f(a).currentModelUuid===v.uuid&&f(c).theme==="light","button-hover-dark":f(c).theme==="dark"&&f(a).currentModelUuid!==v.uuid,"button-hover-light":f(c).theme==="light"&&f(a).currentModelUuid!==v.uuid}])},[d("div",za,[d("span",null,j(v.showName),1),d("span",{class:S(["text-xs opacity-75",[f(a).currentModelUuid===v.uuid?f(c).theme==="dark"?"text-gray-300":"text-blue-500":f(c).theme==="dark"?"text-gray-500":"text-gray-400"]])},"("+j(v.modelName)+")",3)])],10,Pa))),128)),f(a).availableModels.length===0?(w(),y("div",{key:0,class:S(["px-3 py-2 text-sm",f(c).theme==="dark"?"text-gray-400":"text-gray-500"])}," 暂无可用模型 ",2)):U("",!0)]),d("div",{class:S(["border-t",f(c).theme==="dark"?"border-gray-700":"border-gray-200"])},[d("button",{onClick:$,class:S(["w-full px-3 py-2 text-left text-sm transition-colors",f(c).theme==="dark"?"text-blue-400 hover:bg-gray-700":"text-blue-600 hover:bg-blue-50"])}," 管理模型 ",2)],2)],2)):U("",!0)])]),d("button",{onClick:He,class:S(["p-0.5 rounded-full transition-colors",[f(c).theme==="dark"?"hover:bg-gray-700 text-gray-400 hover:text-gray-200":"hover:bg-gray-100 text-gray-600",{"opacity-50 cursor-not-allowed":!f(i).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting"}]]),disabled:!f(i).message.trim()||!f(t).currentDatabase||!f(t).currentDatabase.dataId||f(s).status==="waiting",title:"发送消息"},u[29]||(u[29]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3.5 w-3.5",viewBox:"0 0 20 20",fill:"currentColor"},[d("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})],-1)]),10,$a)])])]),C.value?(w(),y("div",{key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:we},[d("div",{class:"model-manager-modal bg-[var(--bg-color)] rounded-lg shadow-lg w-96 max-h-[80vh] flex flex-col",onClick:u[13]||(u[13]=Mn(()=>{},["stop"]))},[d("div",{class:"p-2 border-b border-[var(--border-color)] flex items-center justify-between"},[u[31]||(u[31]=d("h3",{class:"text-base font-medium text-[var(--text-color)]"},"模型管理",-1)),d("button",{onClick:we,class:"text-gray-400 hover:text-gray-600"},u[30]||(u[30]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",Ua,[(w(!0),y(be,null,Te(f(a).availableModels,v=>{var pe,Le;return w(),y("div",{key:v.uuid,class:"flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-1"},[d("div",Ba,[d("div",Fa,[d("span",Ha,j(v.showName),1),d("span",Ga,"("+j(v.modelName)+")",1),((pe=f(a).modelConfig)==null?void 0:pe.defaultModelId)===v.uuid?(w(),y("span",Wa," 默认 ")):U("",!0)]),d("div",ja,j(v.baseUrl),1)]),d("div",qa,[((Le=f(a).modelConfig)==null?void 0:Le.defaultModelId)!==v.uuid?(w(),y("button",{key:0,onClick:Ie=>ut(v),class:"flex-shrink-0 text-blue-500 hover:text-blue-700",title:"设为默认"},u[32]||(u[32]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]),8,Va)):U("",!0),d("button",{onClick:Ie=>ct(v),class:"flex-shrink-0 text-red-500 hover:text-red-700",title:"删除模型"},u[33]||(u[33]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ya)])])}),128)),f(a).availableModels.length===0?(w(),y("div",Za," 暂无已添加的模型 ")):U("",!0)]),d("div",{class:"p-4 border-t border-[var(--border-color)]"},[d("button",{onClick:Ve,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"},u[34]||(u[34]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Dn(" 添加新模型 ")]))])])])):U("",!0),B.value?(w(),y("div",Xa,[d("div",Ka,[d("div",{class:"p-4 border-b border-[var(--border-color)] flex items-center justify-between"},[u[36]||(u[36]=d("h3",{class:"text-lg font-medium text-[var(--text-color)]"},"添加新模型",-1)),d("button",{onClick:De,class:"text-gray-400 hover:text-gray-600"},u[35]||(u[35]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),d("div",Qa,[d("div",null,[u[37]||(u[37]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型显示名称",-1)),_e(d("input",{"onUpdate:modelValue":u[14]||(u[14]=v=>D.value.showName=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入模型显示名称"},null,512),[[Ee,D.value.showName]]),u[38]||(u[38]=d("p",{class:"mt-1 text-xs text-gray-500"},"模型需要兼容OpenAI的API，需要支持 FunctionCall。",-1))]),d("div",null,[u[39]||(u[39]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Base URL",-1)),_e(d("input",{"onUpdate:modelValue":u[15]||(u[15]=v=>D.value.baseUrl=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: https://api.openai.com/v1"},null,512),[[Ee,D.value.baseUrl]]),u[40]||(u[40]=d("p",{class:"mt-1 text-xs text-gray-500"},"例如: https://dashscope.aliyuncs.com/compatible-mode/v1",-1))]),d("div",null,[u[41]||(u[41]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"模型名称",-1)),_e(d("input",{"onUpdate:modelValue":u[16]||(u[16]=v=>D.value.modelName=v),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如: gpt-3.5-turbo"},null,512),[[Ee,D.value.modelName]]),u[42]||(u[42]=d("p",{class:"mt-1 text-xs text-gray-500"},"国内推荐阿里云qwen-max-latest 或者 deepseek-chat，国外推荐 Claude Sonnet 系列",-1))]),d("div",null,[u[43]||(u[43]=d("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"API Key",-1)),_e(d("input",{"onUpdate:modelValue":u[17]||(u[17]=v=>D.value.apiKey=v),type:"password",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入API Key"},null,512),[[Ee,D.value.apiKey]])]),q.value?(w(),y("div",Ja,[d("div",{class:S(["p-3 rounded-lg",{"bg-green-50 text-green-700":q.value.success,"bg-red-50 text-red-700":!q.value.success}])},[d("div",eo,[q.value.success?(w(),y("svg",to,u[44]||(u[44]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(w(),y("svg",ro,u[45]||(u[45]=[d("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 00-1.414-1.414L10 10l-1.293-1.293z","clip-rule":"evenodd"},null,-1)]))),d("span",null,j(q.value.message),1)])],2)])):U("",!0)]),d("div",no,[d("button",{onClick:De,class:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"}," 取消 "),d("button",{onClick:Ye,class:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",disabled:O.value},[O.value?(w(),y("svg",ao,u[46]||(u[46]=[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):U("",!0),d("span",null,j(O.value?"测试中...":"保存"),1)],8,so)])])])):U("",!0)],2)}}}),lo=Ur(oo,[["__scopeId","data-v-0dc872c5"]]),ar=$n(lo);ar.use(Un());ar.mount("#app");window.$vm=ar._instance;
