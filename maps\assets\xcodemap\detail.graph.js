function NewSearchTab() {
    const obj = {
        "show": false,
        "activeTabName" : 'funcName',
        "searchText": "",
        "names": [],
        "history": [],
        "showNodeFrames": false,
        "newlySearchedNumbers":[],
        "currSearchNumber": null,
        "scopes": [{"name": "all", "desc": "all"}],
        "globalResult": {
            "show": false,
            "count": 0,
            "reqTree": NewLocatorTree(),
        }
    }
    obj.nodeFrameScrollUp = function () {
        if (obj.currSearchNumber === null ||
            obj.newlySearchedNumbers.length === 0) {
            return
        }
        const currSearchNumber = obj.currSearchNumber;
        const newlySearchedNumbers = obj.newlySearchedNumbers;
        for (let i = 0; i < newlySearchedNumbers.length; i++) {
            const number = newlySearchedNumbers[i]
            if (number === currSearchNumber) {
                if (i > 0) {
                    const nextNumber = newlySearchedNumbers[i - 1]
                    obj.focusOnNodeFrame(nextNumber)
                    vue.graph.focusOnNodeByNumber(nextNumber)
                }
                break
            }
        }
    }
    obj.nodeFrameScrollDown = function () {
        if (obj.currSearchNumber === null ||
            obj.newlySearchedNumbers.length === 0) {
            return
        }
        const currSearchNumber = obj.currSearchNumber;
        const newlySearchedNumbers = obj.newlySearchedNumbers;
        for (let i = 0; i < newlySearchedNumbers.length; i++) {
            const number = newlySearchedNumbers[i]
            if (number === currSearchNumber) {
                if (i < newlySearchedNumbers.length - 1) {
                    const nextNumber = newlySearchedNumbers[i + 1]
                    obj.focusOnNodeFrame(nextNumber)
                    vue.graph.focusOnNodeByNumber(nextNumber)
                }
                break
            }
        }
    }
    obj.handleClickOnNodeFrame = function (e) {
        console.log("Handle click on node frame", e.target)
        const element = e.target;
        const numberInElement = parseInt(element.textContent.replace(/['"]/g, '').trim());
        obj.focusOnNodeFrame(numberInElement)
        vue.graph.focusOnNodeByNumber(numberInElement)
    }
    obj.focusOnNodeFrame = function (numer) {
        console.log("Focus on node frame by number", numer)
        document.querySelectorAll(".node-frame").forEach(element => {
            const numberInElement = parseInt(element.textContent.replace(/['"]/g, '').trim());
            if (numberInElement === numer) {
                obj.currSearchNumber = numer
                obj.scrollNodeFrameToCenter(element)
                if (!element.classList.contains("node-frame-active")) {
                    element.classList.add("node-frame-active")
                }
            } else {
                element.classList.remove("node-frame-active")
            }
        })
    }
    obj.scrollNodeFrameToCenter = function (element) {
        console.log("Scroll node frame to center", element)
        const parentElement = document.querySelector(".node-frames-container")
        {
            const parentHeight = parentElement.getBoundingClientRect().height;
            const scrollTop = parentElement.scrollTop;
            const height = element.getBoundingClientRect().height;
            const originY = element.getBoundingClientRect().y + scrollTop;
            parentElement.scrollTop = originY - (parentHeight / 2) + (height / 2);
        }
    }
    obj.initDropdownItem = function () {
        if (obj.history === undefined || obj.history === null || obj.history.length === 0) {
            return;
        }
        const lastNameSelector = obj.history[0];
        const type = lastNameSelector['type'];
        if (type === 'objectCaller') {
            obj.scopes = [
                {"name": "all", "desc": "all"},
                {"name": "setter", "desc": "setter"},
                {"name": "getter", "desc": "getter"},
                {"name": "caller", "desc": "caller"}
            ]
            obj.enableAllDropdownItem()
        } /*else if ("objectUsage" === type || "string" === type || "long" === type || "double" === type || "boolean" === type || "hashCode" === type) {
            obj.scopes = [
                {"name": "all", "desc": "all"},
                {"name": "args", "desc": "args"},
                {"name": "ret", "desc": "return"}
            ]
            obj.enableAllDropdownItem()
        } */else {
            obj.scopes = [
                {"name": "all", "desc": "all"},
            ]
            obj.disableAllDropdownItem()
        }
    }
    obj.enableAllDropdownItem = function () {
        document.querySelectorAll(".el-dropdown-menu__item").forEach( element => {
            element.classList.remove("is-disabled")
            element.removeAttribute("aria-disabled")
        })
    }
    obj.disableAllDropdownItem = function () {
        document.querySelectorAll(".el-dropdown-menu__item").forEach( element => {
            if (!element.classList.contains("is-disabled")) {
                element.classList.add("is-disabled")
            }
            element.setAttribute("aria-disabled", "true")
        })
    }
    obj.disableDropdownItem = function (scope) {
        document.querySelectorAll(".el-dropdown-menu__item").forEach( element => {
            const data = element.textContent.replace(/['"]/g, '');
            console.log("disable dropdown item", data, element, scope, data === scope)
            if ( data === scope) {
                if (!element.classList.contains("is-disabled")) {
                    element.classList.add("is-disabled")
                }
                element.setAttribute("aria-disabled", "true")
            }
        })
    }
    obj.changeScope = function (scope) {
        console.log("Change scope", scope)
        if (scope === undefined || scope === null) {
            return
        }
        if (obj.history === undefined || obj.history === null || obj.history.length === 0) {
            return;
        }
        const lastNameSelector = obj.history[0];
        lastNameSelector["scope"] = scope;
        vue.graph.clearStatusForSearch()
        vue.graph.searchFunction(lastNameSelector, false, 1)
        obj.initDropdownItem();
        obj.disableDropdownItem(scope)
    }
    obj.inputChange = function () {
        console.log("inputChange", obj.searchText)
        obj.globalResult.show = false
        if (obj.searchText !== null &&
            obj.searchText !== undefined &&
            obj.searchText.length > 0) {
            const data = {
                "type": obj.activeTabName,
                "name": obj.searchText,
                "id": obj.searchText
            }
            obj.searchName(data)
        } else {
            obj.names = []
        }
    }
    obj.searchName = function (data) {
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "rootFunc": vue.project.rootFunc,
            "namedSelector": data
        };
        axios.post("./searchName", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./searchName ", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                obj.names = res.data.data.names;
            }
        }).catch(err => {
            console.log(err)
        })
    }
    obj.searchFuncByName = function (data, addToHistory) {
        openLink("metrics://" + "SEQUENCE_SEARCH_DO")
        vue.graph.clearStatusForSearch()
        vue.graph.searchFunction(data, addToHistory, 1)
        obj.show = false
    }
    obj.searchGlobal = function (data) {
        const postData = {
            "processId": vue.project.processId,
            "currCallId": vue.project.currCallId,
            "namedSelector": data
        };
        axios.post("./searchGlobal", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./searchGlobal ", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                obj.names = [];
                obj.searchText = data.name;
                obj.globalResult.reqTree.treeData = res.data.data.rootNodes;
                obj.globalResult.show = true
                obj.globalResult.count = res.data.data.rootNodes.length;
            }
        }).catch(err => {
            console.log(err)
        })
    }
    obj.showTab = function () {
        openLink("metrics://" + "SEQUENCE_SEARCH_TAB")
        if (obj.show) {
            obj.show = false;
        } else {
            obj.show = true;
        }
    }
    return obj;
}

function NewSettingTab() {
    const obj = {
        "show": false,
    }
    obj.showTab = function () {
        if (obj.show) {
            obj.show = false;
        } else {
            obj.show = true;
        }
    }
    return obj;
}

function NewGraphFilterConfig() {
    return {
        "rootFunc": null,
        "packageDeep": 5,
        "removedObjects":[],
        "clickedStatus": {},
        "defaultSelectors": [],
        "pinSelectors": [],
        "actorLevelMap": {},
        "allSearchedCallIds": [],
        "newlySearchedCallIds": [],
        "nextCallId": null,
        sourceDisplayConfig : null,
        funcDisplayConfig: null,
        refreshSearch: false,
    };
}

/*function NewFunctionCallTree() {
    return  {
        treeData: {
            rootNodes: [],
                defaultExpandedKeys:[],
        },
        defaultProps: {
            children: 'children',
                label: 'label',
                nodeKey: 'nodeKey',
                isLeaf: 'isLeaf'
        }
    }
}*/

function NewGraph(graphName) {
    const obj = {
        lastClickTimeMs: 0,
        isMetaKeyPressed: false,
        clickedFuncCalls: new Map(),
        notesLabel:{"0": false, "1": false, "2": false, "3": false},
        contextMenus:[],
        rightRefreshed: false,
        leftFilterConfig: NewGraphFilterConfig(),
        graphFilterConfig: NewGraphFilterConfig(),
        resetZoomLevel: 0.5,
        zoomLevel: 0.5,
        graphName: graphName,
        mermaidData: {},
        // 所有流程列表，每个item包含root functionKey
        graphList: [],
        // 当前流程ContextFuncKey中的functionKey
        rootFunctionKey: '',
        currFunctionKey: '',
        // key为functionKey，value为对应的contextFuncKey对象的jsonString
        function2nodeMap: {},
        node2functionMap: {},
        functionGraph: {},
        mermaidGraphText: 'PureAPI-0-0-0(PureAPI-0-0-0)\nclick PureAPI-0-0-0 call vue.graph.clickNode()\n',
        currFuncCallDetail: {},
        functionCallStackData: {
            "stacks": [],
            "refreshStacks": true
        },
        functionCallDetailTree: NewLocatorTree(),
        functionCallLocalVarTree: NewLocatorTree(),
        functionCallInnerOutputRequestsTree: NewLocatorTree(),
        // SQL requests lazy-load state
        sqlRequestsCount: 0,
        innerOutputRequestCallIds: [],
        sqlRequestsLoadedForCallId: null,
        sqlRequestsCollapsed: true,
        currCallId: null,
        currNavState: {},
        jbColors: [{
            cls: "jbcolor-bg-blue",
            value: 'blue',
            label: '蓝'
        }, {
            cls: "jbcolor-bg-red",
            value: 'red',
            label: '红'
        }, {
            cls: "jbcolor-bg-pink",
            value: 'pink',
            label: '粉'
        }, {
            cls: "jbcolor-bg-orange",
            value: 'orange',
            label: '橘'
        }, {
            cls: "jbcolor-bg-yellow",
            value: 'yellow',
            label: '黄'
        }, {
            cls: "jbcolor-bg-green",
            value: 'green',
            label: '绿'
        }, {
            cls: "jbcolor-bg-magenta",
            value: 'magenta',
            label: '品红'
        }, {
            cls: "jbcolor-bg-cyan",
            value: 'cyan',
            label: '青'
        }],
        currSourceColorCls: "jbcolor-bg-blue",
        detailTabName: "args",
        pinedNodeColor: "red",
        clickedNodeColor: "#409EFF",
    }
    obj.setCurrentJbSourceColorClass = function (value) {
        obj.currSourceColorCls = "jbcolor-bg-" + obj.graphFilterConfig.sourceDisplayConfig.color
    }
    obj.setMetaKeyPresses = function (state) {
        obj.isMetaKeyPressed = state;
    }
    obj.setNotesLabel= function (index) {
        console.log("start", index, obj.notesLabel)
        if (obj.notesLabel[index]) {
            obj.notesLabel[index] = false
        } else {
            obj.notesLabel[index] = true
        }
        console.log("end", index, obj.notesLabel)
    }
    obj.setRoot = function (data) {
        if (data !== null && data.id !== null) {
            if (obj.leftFilterConfig.rootFunc === null ||
                obj.leftFilterConfig.rootFunc === undefined ||
                obj.leftFilterConfig.rootFunc.id !== data.id) {
                obj.leftFilterConfig.rootFunc = { type:"functionTree", name: data.name, id: data.id}
                return true;
            }
        }
        return false;
    }
    obj.openFunction = function (data) {
        let url = window.location.origin + window.location.pathname;
        //const urlParams = new URLSearchParams(window.location.search);
        let graphNodeKey = vue.project.processId + "/" + vue.project.threadId + "/" + data.id;
        url = url + "?graphNodeKey=" + graphNodeKey + "&functionName=" + encodeURIComponent(data.name);
        window.openLink(url)
    }
    obj.openSearchGlobal = function (data) {
        let url = "./search.html";
        let graphNodeKey = vue.project.processId + "/" + vue.project.threadId;
        if (vue.project.rootFunctionId !== null) {
            graphNodeKey = graphNodeKey + "/" + vue.project.rootFunctionId;
        }
        url = url + "?graphNodeKey=" + graphNodeKey + "&functionName=" + encodeURIComponent(vue.project.funcName);
        url = url + "&searchType=" + data["type"];
        url = url + "&searchName=" + encodeURIComponent(data["name"]);
        url = url + "&searchId=" + encodeURIComponent(data["id"]);
        if (data["scope"] !== null && data["scope"] !== undefined) {
            url = url + "&searchScope=" + data["scope"];
        }
        if (obj.currCallId !== null) {
            url = url + "&currCallId=" + obj.currCallId;
        }
        window.openLink(url)
    }
    obj.searchFunction = function (data, addToHistory, gotoDefOrUsage, callBack) {
        console.log("searchFunction", data, addToHistory, gotoDefOrUsage, callBack)
        let rootFunc = obj.leftFilterConfig.rootFunc
        if (data["rootFunc"] !== undefined && data["rootFunc"] !== null) {
            rootFunc = data["rootFunc"]
        }
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "rootFunc": rootFunc,
            "currCallId": obj.currCallId,
            "namedSelector": data
        };
        axios.post("./search", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./search ", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                if (addToHistory) {
                    vue.searchTab.history.unshift(data)
                }
                obj.leftFilterConfig.newlySearchedCallIds = []
                let callNum = res.data.data.funcCallIds.length;
                for (let i = 0; i < callNum; i++) {
                    obj.leftFilterConfig.allSearchedCallIds.push(res.data.data.funcCallIds[i])
                }
                let relatedNUum = res.data.data.relatedFuncCallIds.length;
                for (let i = 0; i < relatedNUum; i++) {
                    obj.leftFilterConfig.allSearchedCallIds.push(res.data.data.relatedFuncCallIds[i])
                }
                for (let i = 0; i < callNum; i++) {
                    obj.leftFilterConfig.newlySearchedCallIds.push(res.data.data.funcCallIds[i])
                }
                obj.leftFilterConfig.nextCallId = res.data.data.nextCallId;
                if (callNum > 0) {
                    console.log("newlySearchedCallIds", res.data.data.funcCallIds, obj.leftFilterConfig.newlySearchedCallIds)
                    const scrollSearchToCenter = function () {
                        obj.refreshSearchNumbers();
                        if (callBack !== undefined) {
                            callBack(obj.leftFilterConfig.newlySearchedCallIds);
                        }
                        setTimeout(function () {
                            obj.scrollSearchElementToCenter(data, gotoDefOrUsage);
                        }, 300)
                    }
                    obj.onNodeKeyChange(scrollSearchToCenter)
                } else {
                    if (callBack !== undefined) {
                        callBack(obj.leftFilterConfig.newlySearchedCallIds);
                    }
                }
            }
        }).catch(err => {
            console.log(err)
        })
    }
    obj.refreshSearchNumbers = function () {
        const newlySearchedNumbers = []
        obj.leftFilterConfig.newlySearchedCallIds.forEach((searchCallId, index) => {
            for (let number in obj.mermaidData.number2Call) {
                if (searchCallId === obj.mermaidData.number2Call[number]) {
                    newlySearchedNumbers.push(parseInt(number))
                }
            }
        })
        vue.searchTab.showNodeFrames = newlySearchedNumbers.length > 0
        vue.searchTab.newlySearchedNumbers = newlySearchedNumbers;
        vue.searchTab.initDropdownItem();
    }
    obj.focusOnNodeByNumber = function (number) {
        console.log("Focus on node by number", number)
        document.querySelectorAll(".messageText").forEach(parentElement => {
            const numberInElement = parseInt(parentElement.querySelector(".numberLabel").textContent.trim());
            if (numberInElement === number) {
                console.log("Focus on node by number, find element", number, parentElement)
                obj.labelCurrentNode(parentElement)
                obj.gotoDefOrUsageByNumber(number)
                obj.scrollElementToCenter(parentElement)
            }
        })
    }
    obj.getRedFlagTspan = function () {
        return obj.asTspan("⭐️", "redFlag");
    }
    obj.clearRedFlag = function () {
        document.querySelectorAll(".messageText").forEach( parentElement => {
            const redFlagElement = parentElement.querySelector('.redFlag');
            if (redFlagElement !== undefined && redFlagElement !== null) {
                parentElement.removeChild(redFlagElement)
            }
        })
    }
    obj.labelCurrentNode = function (parentElement) {
        obj.clearRedFlag()
        parentElement.innerHTML = obj.getRedFlagTspan() + parentElement.innerHTML;
        const functionNode = parentElement.querySelector(".functionName");
        if (functionNode != null) {
            functionNode.style.fill = obj.clickedNodeColor;
            functionNode.style.stroke = obj.clickedNodeColor;
        }
    }
    obj.gotoDefOrUsageByNumber = function (number) {
        const currTimeMs = new Date().getTime();
        const callId = obj.mermaidData.number2Call[number]
        if (callId !== null) {
            obj.doGetFunctionCallDetail(callId)
            let data = {"type": "usage", "id": callId}
            if (currTimeMs - obj.lastClickTimeMs <= 300) {
                data = {"type": "definition", "id": callId}
            }
            obj.gotoDefOrUsage(data)
        }
    }
    obj.gotoCurrentDefAndFocus = function (isReturn) {
        obj.gotoCurrentDef(isReturn)
        obj.onMessageElementByCallId(obj.currCallId,  (element, number) => {
            obj.scrollElementToCenter(element)
        })
    }
    obj.gotoCurrentDef = function (isReturn) {
        const data = {"type": "definition", "id": obj.currCallId};
        if (isReturn !== undefined && isReturn) {
            data.return = "true";
        }
        obj.gotoDefOrUsage(data)
    }
    obj.gotoCurrentUsage = function () {
        obj.gotoDefOrUsage({"type": "usage", "id": obj.currCallId, "force":"true"})
    }
    obj.getCurrentLocalVars = function () {
        const data = obj.currNavState;
        const url = obj.getPsiUrl(data);
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "psiUrl": url,
            "callId": data.id
        };
        axios.post("./getLocalVars", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("getLocalVars", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                obj.functionCallLocalVarTree.treeData = res.data.data.treeData;
                obj.getAllObjectDetails(obj.functionCallLocalVarTree.treeData.rootNodes)
            }
        }).catch(err => {
            console.log(err)
        })
    }
    obj.searchCurrentUsage = function () {
        const data = {
            "type": "function",
            "id": obj.currCallId,
            "scope": "codeLine"
        }
        obj.searchFunction(data, false, 3)
    }
    obj.searchCurrentStack = function () {
        openLink("metrics://" + "DATA_STACK")
        const data = {
            "type": "stack",
            "id": obj.currCallId,
        }
        obj.searchFunction(data, false, 1)
        //obj.gotoDefOrUsage({"type": "usage", "id": obj.currCallId})
    }
    obj.getPsiUrl = function (data) {
        if (data.type === 'stack') {
            const url = "psi:///functionStack/" + vue.project.processId + "/" + data.id + "?className=" + data.className + "&functionName=" + data.functionName  + "&codeLine=" + data.codeLine;
            return url;
        } else if (data.type === 'usage') {
            let url = "psi:///functionUsage/" + vue.project.processId + "/" + data.id;
            if (data.force === "true") {
                url += "?force=true";
            }
            return url;
        } else if (data.type === 'definition') {
            let url = "psi:///functionDef/" + vue.project.processId + "/" + data.id;
            if (data.return === "true") {
                url += "?return=true";
            }
            return url;
        }
    }
    obj.gotoDefOrUsage = function (data) {
        const url = obj.getPsiUrl(data);
        if (data.type === 'stack') {
            openLink(url);
        } else if (data.type === "usage") {
            obj.functionCallLocalVarTree.treeData = {
                "rootNodes": [],
                "expandedKeys": []
            };
            obj.currNavState = data;
            openLink(url);
        } else if (data.type === "definition") {
            obj.functionCallLocalVarTree.treeData = {
                "rootNodes": [],
                "expandedKeys": []
            };
            obj.currNavState = data;
            openLink(url);
        }
    }
    obj.clickContextMenuItem = function (index) {
        const data = obj.contextMenus[index];
        //console.log("clickContextMenuItem before", index, data, obj.leftFilterConfig)
        let changed = false;
        if (data.op === "remove") {
            openLink("metrics://" + "SEQUENCE_CONTEXT_DO")
            changed = obj.addRemovedObject(data)
            console.log("Add removed object", changed)
            if (changed) {
                obj.leftFilterConfig.refreshSearch = true
                obj.onNodeKeyChange(obj.restoreLastCallId);
            }
        }
        if (data.op === "setRoot") {
            openLink("metrics://" + "SEQUENCE_CONTEXT_DO")
            changed = obj.setRoot(data)
            if (changed) {
                obj.leftFilterConfig.refreshSearch = true
                obj.onNodeKeyChange(obj.restoreLastCallId);
            }
        }
        if (data.op === 'open') {
            openLink("metrics://" + "SEQUENCE_CONTEXT_DO")
            obj.openFunction(data)
        }
        if (data.op === 'goto') {
            openLink("metrics://" + "SEQUENCE_CONTEXT_DO")
            obj.gotoDefOrUsage(data)
        }
        if (data.op === 'search') {
            openLink("metrics://" + "DATA_CONTEXT_DO")
            obj.clearStatusForSearch();
            obj.searchFunction(data, true, 1)
            //handle object change
            if (data.type === "objectChange") {
                obj.findObjectChangeHistory(data)
            }
        }
        if (data.op === 'searchGlobal') {
            openLink("metrics://" + "DATA_CONTEXT_DO")
            obj.openSearchGlobal(data)
        }
        if (data.op === 'clear') {
            openLink("metrics://" + "SEQUENCE_CONTEXT_DO")
            obj.clearStatus();
            obj.onNodeKeyChange(obj.restoreLastCallId)
        }
        if (data.op === "writeClipboard") {
            openLink("metrics://" + "DATA_CONTEXT_DO")
            writeToClipboard(data.name + "")
        }
        if (data.op === "viewJson") {
            openLink("metrics://" + "DATA_CONTEXT_DO")
            obj.openJsonViewer(data)
        }
        if (data.op === "removeClickStatus") {
            obj.removeClickStatus(data);
        }
    }
    obj.removeClickStatus = function (data) {
        const callId = parseInt(data.id)
        obj.clickedFuncCalls.delete(callId);
        obj.onMessageElementByCallId(callId, function (element, number) {
            const functionNode = element.querySelector(".functionName");
            if (functionNode != null) {
                functionNode.style.fill = null;
                functionNode.style.stroke = null;
            }
        })
        vue.$message({
            message: '已取消点击状态',
            type: 'success'
        });
    }
    obj.clearStatusForSearch = function () {
        obj.leftFilterConfig.clickedStatus = {}
        obj.leftFilterConfig.newlySearchedCallIds = []
        obj.leftFilterConfig.allSearchedCallIds = []
    }
    obj.clearStatus = function () {
        obj.currCallId = null;
        obj.leftFilterConfig.nextCallId = null;
        obj.leftFilterConfig.clickedStatus = {}
        obj.leftFilterConfig.newlySearchedCallIds = []
        obj.leftFilterConfig.allSearchedCallIds = []
        obj.leftFilterConfig.removedObjects = []
        obj.leftFilterConfig.pinSelectors = []
        obj.leftFilterConfig.defaultSelectors = []
        obj.clickedFuncCalls = new Map()
        obj.startShow();
    }
    obj.openCurrentFunctionAsPopupPage = function () {
        let url = "./detail_func.html?";
        url += "processId=" + encodeURIComponent(vue.project.processId);
        url += "&currCallId=" + encodeURIComponent(obj.currCallId);
        url += "&navStateType=" + encodeURIComponent(vue.project.navStateType);
        if (vue.project.functionName) {
            url += "&functionName=" + encodeURIComponent(vue.project.functionName);
        }
        openLink(url);
    }
    obj.openJsonViewer = function (data) {
        // 构建json-viewer页面的URL
        let url = "./json-viewer.html?";
        url += "processId=" + encodeURIComponent(vue.project.processId);
        
        // 从数据中获取参数
        if (data.labelKey) {
            url += "&labelKey=" + encodeURIComponent(data.labelKey);
        }
        if (data.maxVersion) {
            url += "&maxVersion=" + encodeURIComponent(data.maxVersion);
        }
        if (data.uniqId) {
            url += "&uniqId=" + encodeURIComponent(data.uniqId);
        }
        
        // 添加深度参数，默认为1
        url += "&depth=4";
        
        // 添加主题参数，保持与当前页面一致
        const currentTheme = document.body.getAttribute('data-theme') || 'dark';
        url += "&theme=" + encodeURIComponent(currentTheme);
        
        console.log("Opening JSON viewer with URL:", url);
        window.open(url, '_blank');
    }
    obj.clickFunctionCodeByLink = function (link) {
        console.log("clickFunctionCodeByLink", link)
        const url = new URL(link.replace("psi://", ""), 'http://example.com');
        const pathParts = url.pathname.split('/');
        const functionName = url.searchParams.get('functionName');
        const codeLine = url.searchParams.get('codeLine');
        const doubleClick = url.searchParams.get("doubleClick");
        const action = pathParts[1];  // "functionClick"
        const processId = pathParts[2];      // "1889"
        if (processId !== vue.project.processId) {
            console.log("process id dose not match")
        } else {
            const callId = pathParts[3];   // "6"
            const navStateType = url.searchParams.get('navStateType');
            if (action === "stepIntoFromPopup" || action === "stepOutFromPopup" || action === "stepBackFromPopup" || action === "stepOverFromPopup") {
                switch(action) {
                    case "stepIntoFromPopup":
                        obj.stepIntoFromPopup(callId, navStateType);
                        break;
                    case "stepOutFromPopup":
                        obj.stepOutFromPopup(callId, navStateType);
                        break;
                    case "stepBackFromPopup":
                        obj.stepBackFromPopup(callId, navStateType);
                        break;
                    case "stepOverFromPopup":
                        obj.stepOverFromPopup(callId, navStateType);
                        break;
                }
            } else {
                const data = {
                    rootFunc: {
                        "name":"",
                        "id": callId,
                        "type":"functionTree"
                    },
                    "type": "funcClickCode",
                    "id": codeLine,
                    "name": functionName
                }
                if (navStateType === "definition") {
                    obj.searchFunction(data, false, 2)
                } else {
                    if (doubleClick === "true") {
                        obj.searchFunction(data, false, 2)
                    } else {
                        obj.searchFunction(data, false, 1)
                    }
                }
            }
        }
    }
    obj.clickStackItem = function (stackItem) {
        if (stackItem.callId === null ||
            stackItem.callId === undefined ||
            stackItem.callId <= 0) {
            const data = {
                "id": obj.currCallId,
                "type":"stack",
                "className": stackItem.className,
                "functionName" : stackItem.methodName,
                "codeLine": stackItem.codeLine
            }
            obj.gotoDefOrUsage(data)
        } else {
            obj.functionCallStackData.refreshStacks = false;
            obj.searchFunction({
                "type": "functionCall",
                "id": stackItem.callId,
            }, false, 3)
        }
        console.log("click stack item", stackItem)
    }
    obj.clickFuncTreeElement =  function (data, node, other) {
        if (data.labelKey === "url") {
            const url = data.labelValue;
            window.open(url, '_blank');
            return;
        }
        console.log("clickFuncTreeElement", data, node, other)
        const nodeKey = data["nodeKey"];
        if (nodeKey !== undefined && nodeKey !== null) {
            const items = nodeKey.split("/");
            if (items.length >= 3) {
                if ("stack" === items[0]) {
                    const stackSize = parseInt(items[1]);
                    const currStackIndex = parseInt(items[2])
                    const stackItem = obj.currFuncCallDetail.stackTraceItems[currStackIndex]
                    if (stackItem !== null && stackItem !== undefined) {
                        const data = {
                            "id": obj.currFuncCallDetail.callId,
                            "type":"stack",
                            "className": stackItem.className,
                            "functionName" : stackItem.methodName,
                            "codeLine": stackItem.codeLine
                        }
                        obj.gotoDefOrUsage(data)
                    }
                } else if ("object" === items[0]) {
                    if (data["dataLoaded"] === false) {
                        if (data["dataId"] !== null) {
                            obj.doGetObjectDetail(data)
                        }
                    }
                }
            }
        }
    }
    obj.initContextMenuForFuncTreeElement = function (event, data, node, other) {
        openLink("metrics://" + "DATA_CONTEXT_MENU")
        obj.contextMenus = [];
        const nodeKey = data["nodeKey"];
        if (nodeKey !== undefined && nodeKey !== null) {
            const items = nodeKey.split("/");
            const label = data["label"]
            const labelValue = data["labelValue"];
            const dataId = data["dataId"]
            const labelKey = data["labelKey"]
            const parentDataId = data["parentDataId"]
            const maxVersion = data["maxVersion"]
            const uniqId = data["dataId"]
            if (items.length >= 3) {
                const type = items[0]
                if (labelKey !== null && parentDataId !== null) {
                    const data = {"name": labelKey, "id": parentDataId, "type": "objectChange", "op":"search", "scope":"fieldName", "desc": "追踪属性 '" + labelKey + "' 的变更历史"}
                    obj.contextMenus.push(data)
                }
                if (labelValue != null) {
                    const data = {"name": labelValue, "id": dataId, "type": "value", "op":"writeClipboard", "desc": "复制 value"}
                    obj.contextMenus.push(data)
                }
                if ("object" === type) {
                    // 添加查看JSON菜单项
                    {
                        const contextData = {"name": "", "id": dataId, "type": "object", "op":"viewJson", "desc": "view as json", "labelKey": label, "maxVersion": maxVersion, "uniqId": uniqId}
                        obj.contextMenus.push(contextData)
                    }
                    /*{
                        const nameSelector = {"name": labelValue, "id": dataId, "type": "whereFrom", "op":"search", "desc": "追踪 object '" + dataId + "' 从哪里来"}
                        if (data["inReturn"]) {
                            nameSelector["currCallIdTime"] = obj.currFuncCallDetail.endCallId;
                        }
                        obj.contextMenus.push(nameSelector)
                    }
                    {
                        const nameSelector = {"name": labelValue, "id": dataId, "type": "whereGo", "op":"search", "desc": "追踪 object '" + dataId + "' 到哪里去"}
                        if (data["inReturn"]) {
                            nameSelector["currCallIdTime"] = obj.currFuncCallDetail.endCallId;
                        }
                        obj.contextMenus.push(nameSelector)
                    }*/
                    {
                        const data = {"name": labelValue, "id": dataId, "type": "objectChange", "op":"search", "desc": "追踪 object '" + dataId + "' 的变更历史"}
                        obj.contextMenus.push(data)
                    }
                    {
                        const data = {"name": labelValue, "id": dataId, "type": "objectUsage", "op":"search", "scope": "ret", "desc": "追踪 object '" + dataId + "' 作为返回值的轨迹"}
                        obj.contextMenus.push(data)
                    }
                    {
                        const data = {"name": labelValue, "id": dataId, "type": "objectUsage", "op":"search", "scope": "args", "desc": "追踪 object '" + dataId + "' 作为参数的轨迹"}
                        obj.contextMenus.push(data)
                    }
                    {
                        const data = {"name": labelValue, "id": dataId, "type": "objectCaller", "op":"search", "scope": "caller", "desc": "追踪 object '" + dataId + "' 作为调用者的轨迹"}
                        obj.contextMenus.push(data)
                    }
                    {
                        const data = {"name": labelValue, "id": dataId, "type": "objectUsage", "scope": "all", "op":"searchGlobal", "desc": "全局搜索 object '" + dataId + "' "}
                        obj.contextMenus.push(data)
                    }
                    
                } else if ("string" === type || "long" === type || "double" === type || "boolean" === type || "hashCode" === type) {
                    if (dataId !== undefined && dataId !== null) {
                        //if dataId too long, only show the first 10 characters
                        if (dataId.length > 10) {
                            dataId = dataId.substring(0, 10) + "...";
                        }
                        {
                            const data = {"name": labelValue, "id": dataId, "type": type, "op":"search", "scope": "ret", "desc": "追踪 " + type + " '" + dataId + "' 作为返回值的轨迹"}
                            obj.contextMenus.push(data)
                        }
                        {
                            const data = {"name": labelValue, "id": dataId, "type": type, "op":"search", "scope": "args", "desc": "追踪 " + type + " '" + dataId + "' 作为参数的轨迹"}
                            obj.contextMenus.push(data)
                        }
                        {
                            const data = {"name": labelValue, "id": dataId, "type": type, "op":"searchGlobal", "desc": "全局搜索 " +  type + "' " + dataId + "' "}
                            obj.contextMenus.push(data)
                        }
                    }
                }
            }
            
        }
        return obj.contextMenus.length > 0
    }
    obj.initContextMenusForGraphTextElement = function (element) {
        //console.log("initContextMenusForGraphTextElement", element)
        let effective = false;
        if (element.classList.contains("text")) {
            //this the group
            const groupName = element.querySelector(".groupName").textContent.trim()
            const groupMeta = obj.mermaidData.groupMetas[groupName];
            if (groupMeta === undefined || groupMeta === null) {
                effective = false;
            } else {
                const defaultGroup = groupMeta.f1;
                const groupType = obj.mermaidData.groupTypes[defaultGroup]
                obj.contextMenus = []
                const data = {"name": defaultGroup, "id": defaultGroup, "type": groupType, "op":"remove", "desc": "remove " + groupType + " " + defaultGroup}
                obj.contextMenus.push(data)
                effective = true;
            }
        } else if (element.classList.contains("actor")) {
            const actorName = element.querySelector(".actorName").textContent.trim()
            const showActorMeta = obj.mermaidData.showActorMetas[actorName];
            if (showActorMeta === undefined || showActorMeta === null) {
                effective = false;
            } else {
                const defaultGroup = showActorMeta.f1;
                const defaultClass = showActorMeta.f2;
                const foldLabelNode = element.querySelector(".foldLabel");
                let data = {}
                let actorLevel
                if (foldLabelNode != null) {
                    const foldLabel = foldLabelNode.textContent.trim();
                    if (foldLabel === "[+]") {
                        actorLevel = "ACTOR"
                    } else {
                        actorLevel = "CLASS"
                    }
                } else {
                    actorLevel = "CLASS"
                }
                obj.contextMenus = []
                if (actorLevel === "CLASS") {
                    const dataType = "class";
                    {
                        data = {"name": defaultClass, "id": defaultClass, "type": dataType, "op":"search", "desc": "search usage of  '" + defaultClass + "'"}
                        obj.contextMenus.push(data)
                    }
                    {
                        data = {"name": defaultClass, "id": defaultClass, "type": dataType, "op":"remove", "desc": "remove " + dataType + " '" + defaultClass + "'"}
                        obj.contextMenus.push(data)
                    }
                } else {
                    const groupType = obj.mermaidData.groupTypes[defaultGroup]
                    data = {"name": defaultGroup, "id": defaultGroup, "type": groupType, "op":"remove", "desc": "remove " + groupType + " '" + defaultGroup + "'"}
                    obj.contextMenus.push(data)
                }
                effective = true;
            }
        } else if (element.classList.contains("messageText")) {
            const number = parseInt(element.querySelector(".numberLabel").textContent.trim());
            const functionName = element.querySelector(".functionName").textContent.trim()
            const callId = obj.mermaidData.number2Call[number]
            const functionId = obj.mermaidData.number2Method[number];
            if (callId !== null && functionId != null) {
                obj.contextMenus = []
                {
                    const data = {"op":"open", "type":"functionTree", "name": functionName, "id": callId, "desc": "在新标签打开 '" + functionName + "'"}
                    obj.contextMenus.push(data)
                }
                {
                    const data = {"op":"search", "scope":"caller", "type":"function", "name": functionName, "id": callId + "", "desc":"查看当前函数内的用法  '" + functionName + "'"}
                    obj.contextMenus.push(data)
                }
                {
                    const data = {"op":"search", "scope":"all", "type":"function", "name": functionName, "id": callId + "", "desc":"查看所有用法  '" + functionName + "'"}
                    obj.contextMenus.push(data)
                }
                if (obj.clickedFuncCalls.get(callId)) {
                    const data = {"op":"removeClickStatus", "scope":"all", "type":"function", "name": functionName, "id": callId + "", "desc":"取消点击状态"}
                    obj.contextMenus.push(data)
                }
                effective = true;
            }
        }
        return effective;
    }
    obj.initContextMenusForGraphElement = function (element) {
        openLink("metrics://" + "SEQUENCE_CONTEXT_MENU")
        //console.log("initContextMenusForElement", element)
        let effective = false;
        if (element.tagName === 'rect') {
            if (element.classList.contains("actor")) {
                let textElement = null
                element.parentElement.querySelectorAll(".actor").forEach((childElement) => {
                    if (childElement.tagName === 'text') {
                        textElement = childElement;
                    }
                })
                if (textElement !== null) {
                    effective = obj.initContextMenusForGraphTextElement(textElement)
                }
            }
        } else if (element.tagName === "text") {
            effective = obj.initContextMenusForGraphTextElement(element)
        } else if (element.tagName === "tspan") {
            if (element.parentElement.tagName === "text") {
                effective = obj.initContextMenusForGraphTextElement(element.parentElement)
            }
        }
        if (!effective) {
            {
                obj.contextMenus = []
                const data = {"op":"clear", "type":"status", "name": "", "id": "", "desc":"clear status"}
                obj.contextMenus.push(data)
            }
            effective = true;
        }
        return effective;
    }
    obj.updateClickedStatus = function (data) {
        obj.leftFilterConfig.clickedStatus[data.callId] = data.childLevel;
    }
    obj.addFilterInRightTab = function (name, data) {
        if (obj.graphFilterConfig[name] === null) {
            obj.graphFilterConfig[name] = []
        }
        let exists = false;
        for (let i = 0; i < obj.graphFilterConfig[name].length; i++) {
            let tempData = obj.graphFilterConfig[name][i];
            if (data.name === tempData.name && data.op === tempData.op) {
                exists = true;
                obj.graphFilterConfig[name].splice(i, 1, data)
            }
        }
        if (!exists) {
            obj.graphFilterConfig[name].push(data)
        }
    }
    obj.addPinSelector = function (data) {
        if (data === undefined || data === null) {
            data = {
                "name":"costMs", "op":"top", "value": 10, "ops":["top"]
            };
        }
        console.log("Add pin selector", data)
        //TODO check the final result
        //obj.resetClickedStatus()
        obj.addFilterInRightTab("pinSelectors", data)
    }
    obj.addDefaultSelector = function (data) {
        if (data === undefined || data === null) {
            data = {
                "name":"costMs", "op":">", "value": 0, "ops":[">", ">=", "<", "<=", "=="]
            };
        }
        console.log("Add default selector", data)
        //TODO check the final result
        //obj.resetClickedStatus()
        obj.addFilterInRightTab("defaultSelectors", data)
    }
    obj.deleteDefaultSelector = function (index) {
        //obj.resetClickedStatus()
        obj.graphFilterConfig.defaultSelectors.splice(index, 1)
    }
    obj.deletePinSelector = function (index) {
        //obj.resetClickedStatus()
        obj.graphFilterConfig.pinSelectors.splice(index, 1)
    }
    obj.addRemovedObject = function (data) {
        if (obj.leftFilterConfig.removedObjects === null) {
            obj.leftFilterConfig.removedObjects = []
        }
        let exists = false;
        for (let i = 0; i < obj.leftFilterConfig.removedObjects.length; i++) {
            let tempData = obj.leftFilterConfig.removedObjects[i];
            if (data.id === tempData.id && data.type === tempData.type) {
                exists = true;
                obj.leftFilterConfig.removedObjects.splice(i, 1, data)
            }
        }
        if (!exists) {
            obj.leftFilterConfig.removedObjects.push(data)
        }
        return !exists;
    }
    obj.deleteRemovedObject = function (index) {
        obj.graphFilterConfig.removedObjects.splice(index, 1)
    }
    obj.deleteRootFunc = function () {
        obj.graphFilterConfig.rootFunc = null
    }
    obj.resetClickedStatus = function (leftSide) {
        if (leftSide) {
            obj.leftFilterConfig.clickedStatus = {}
        } else {
            obj.graphFilterConfig.clickedStatus = {}
        }
    }
    obj.resetClickedCalls = function () {
        obj.clickedFuncCalls = new Map();
    }
    /*obj.plusPackageDeep = function (fresh) {
        if (fresh) {
            obj.leftFilterConfig.packageDeep = obj.leftFilterConfig.packageDeep + 1
            obj.resetClickedStatus(true)
            obj.onNodeKeyChange()
        } else {
            obj.graphFilterConfig.packageDeep = obj.graphFilterConfig.packageDeep + 1
        }
    }
    obj.minusPackageDeep = function (fresh) {
        if (fresh) {
            if (obj.leftFilterConfig.packageDeep >= 2) {
                obj.leftFilterConfig.packageDeep = obj.leftFilterConfig.packageDeep - 1
            }
            obj.resetClickedStatus(true)
            obj.onNodeKeyChange()
        } else {
            if (obj.graphFilterConfig.packageDeep >= 2) {
                obj.graphFilterConfig.packageDeep = obj.graphFilterConfig.packageDeep - 1;
            }
        }
    }*/
    /*obj.hasNextFunction = function (nextFuncKey) {
        //TODO need another way to check if the next function should display
        const nodeId = vue.graph.function2nodeMap.get(nextFuncKey);
        if (nodeId !== undefined && nodeId !== null) {
            return true;
        } else {
            return false;
        }
    }
    obj.focusOnNextFunction = function (nextFuncKey) {
        vue.fileTree.clickFunction(nextFuncKey)
    }
    obj.hasPrevFunction = function (currFuncKey) {
        return obj.functionGraph.allNodes.hasOwnProperty(currFuncKey);
    }
    obj.focusOnPrevFunction = function (currFuncKey) {
        if (this.hasPrevFunction(currFuncKey)) {
            const node = this.functionGraph.allNodes[currFuncKey];
            this.doClickFunction(node.parentFuncKey)
        } else {
            console.log("There is no prev function for", currFuncKey)
        }
    }*/
/*
    obj.focusOnClickedFunction = function (clickedFunctionKey) {
        console.log("Try to focus on the clicked function", clickedFunctionKey)
        const succCallBack = function () {
            const nodeId = vue.graph.function2nodeMap.get(clickedFunctionKey);
            if (nodeId !== undefined && nodeId !== null) {
                vue.graph.currFunctionKey = clickedFunctionKey;
                const focusCurNode = "style " + nodeId + " stroke:#409EFF,stroke-width:2px\n";
                vue.graph.doRefreshGraph(vue.graph.mermaidGraphText + focusCurNode, nodeId);
                /!*if (vue.leftAside.functionTrace.isFunctionTraceTab()) {
                    vue.usageTraceList.findUsageTraceListByFuncKey();
                }*!/
            }
        }
        if (this.functionGraph.allNodes.hasOwnProperty(clickedFunctionKey)) {
            const node = this.functionGraph.allNodes[clickedFunctionKey];
            if (node !== undefined && node !== null) {
                console.log("In the same function graph, just focus the function node", clickedFunctionKey)
                succCallBack();
            }
        } else {
            if (vue.leftAside.functionTrace.isFunctionTraceTab()) {
                let funcKeys = [];
                funcKeys.push(clickedFunctionKey)
                funcKeys.push(this.functionGraph.rootFuncKey);
                funcKeys = funcKeys.reverse();
                console.log("Not in the same function graph, get graph detail by function keys", funcKeys)
                vue.graph.doGetGraphDetail(funcKeys, succCallBack, undefined)
            } else {
                console.log("Don't adjust the graph in usageTrace mode")
            }
        }
    }
*/
/*
    obj.doListGraph = function (vue) {
        // 获取所有流程
        axios.post("/webapi/project/detail/graphList", {
            groupId: vue.project.groupId,
            artifactId: vue.project.artifactId,
            version: vue.project.version,
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("/webapi/project/detail/graphList", res.data)
            const parsedData = JSON.parse(res.data.data)
            vue.graph.graphList = []
            parsedData.forEach(obj => {
                vue.graph.graphList.push(obj);
            });
        }).catch(err => {
            console.log(err)
        })
    }
*/
    /*obj.reset = function () {
        obj.leftFilterConfig = NewGraphFilterConfig();
        obj.graphFilterConfig = NewGraphFilterConfig();
        obj.refreshWithoutAllClickedStates()
    }
    obj.refreshWithoutAllClickedStates = function () {
        obj.resetClickedStatus(false)
        obj.resetClickedCalls()
        //inherit
        obj.graphFilterConfig.packageDeep = obj.leftFilterConfig.packageDeep
        obj.graphFilterConfig.actorLevelMap = structuredClone(obj.leftFilterConfig.actorLevelMap)
        if (obj.graphFilterConfig.packageDeep < 5) {
            obj.graphFilterConfig.packageDeep = 5;
        }
        obj.rightRefreshed = true;
        obj.onNodeKeyChange(obj.restoreLastCallId);
        obj.rightRefreshed = false;
    }*/
    obj.updateFunctionDisplayConfig = function (data, callback) {
        axios.post("./updateFunctionDisplayConfig", data, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            const errorCode = res.data.errorCode;
            console.log("./updateFunctionDisplayConfig", res.data)
            if (errorCode !== '200') {
                vue.$message.error("Update Failed")
                console.log("updateFunctionDisplayConfig failed", res.data)
            } else {
                vue.$message({
                    message: 'Update Succeed',
                    type: 'success'
                });
            }
        }).catch(err => {
            vue.$message.error("Update Failed")
            console.log(err)
        }).finally(()=> {
            if (callback !== undefined) {
                callback();
            }
        })
    }
    obj.cancelGraphFilterConfig = function () {
        vue.settingTab.show = false;
    }
    obj.saveGraphFilterConfig = function () {
        obj.leftFilterConfig.removedObjects = obj.graphFilterConfig.removedObjects;
        obj.leftFilterConfig.defaultSelectors = obj.graphFilterConfig.defaultSelectors;
        obj.leftFilterConfig.pinSelectors = obj.graphFilterConfig.pinSelectors;
        obj.leftFilterConfig.funcDisplayConfig = obj.graphFilterConfig.funcDisplayConfig;
        obj.leftFilterConfig.sourceDisplayConfig = obj.graphFilterConfig.sourceDisplayConfig;
        obj.leftFilterConfig.refreshSearch = true
        const data = {
            "sourceDisplayConfig": obj.leftFilterConfig.sourceDisplayConfig,
            "funcDisplayConfig": obj.leftFilterConfig.funcDisplayConfig,
        }
        //TODO save the funcDisplayConfig
        obj.updateFunctionDisplayConfig(data, () => {
            obj.onNodeKeyChange(() => {
                obj.restoreLastCallId();
                vue.settingTab.show = false
            });
        })
    }
    obj.onNodeKeyChange = function (succCallback, errorCallBack) {
        obj.doGetGraphDetail(succCallback, errorCallBack)
    }
    obj.initGraphFilterConfig = function (callback) {
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "rootFunctionId": vue.project.rootFunctionId,
            "graphFilterConfig": obj.leftFilterConfig
        };
        axios.post("./defaultConfig", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./defaultConfig ", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                obj.graphFilterConfig = res.data.data.graphFilterConfig
                obj.leftFilterConfig = structuredClone(obj.graphFilterConfig)
            }
        }).catch(err => {
            console.log(err)
        }).finally(() => {
            obj.onNodeKeyChange(callback)
        })
    }
    obj.stepBack = function () {
        if (vue.project.inPopup === "true") {
            obj.stepBackInPopup();
            return;
        }
        openLink("metrics://" + "DATA_STEPS")
        if (obj.currNavState.type === "definition") {
            obj.gotoCurrentUsage();
        } else {
            obj.searchFunction({
                "type": "stepBack",
                "id": obj.currCallId,
                "name": obj.currCallId
            }, false, 3, function (callIds) {
                if (callIds.length <= 0) {
                    obj.stepOut(2)
                    //obj.stepOut();
                    //openLink("notify://" + "函数已到开始处，无法再 StepBack。请尝试 StepOver 或其它操作。")
                }
            })
        }
    }
    obj.stepOver = function () {
        if (vue.project.inPopup === "true") {
            obj.stepOverInPopup();
            return;
        }
        openLink("metrics://" + "DATA_STEPS")
        if (obj.currNavState.type === "definition") {
            if (obj.currNavState.return === 'true') {
                obj.gotoCurrentUsage();
            } else {
                obj.searchFunction({
                    "type": "stepInto",
                    "id": obj.currCallId,
                    "name": obj.currCallId
                }, false, 3, function (callIds) {
                    if (callIds.length <= 0) {
                        obj.gotoCurrentDef(true);
                    }
                })
            }
        } else {
            obj.searchFunction({
                "type": "stepOver",
                "id": obj.currCallId,
                "name": obj.currCallId
            }, false, 3, function (callIds) {
                if (callIds.length <= 0) {
                    obj.stepOut(4);
                    //openLink("notify://" + "函数已到结束处，无法再 StepOver。请尝试 StepBack 或其它操作。")
                }
            })
        }

    }
    obj.stepInto = function () {
        if (vue.project.inPopup === "true") {
            obj.stepIntoInPopup();
            return;
        }
        openLink("metrics://" + "DATA_STEPS")
        if (obj.currNavState.type === "usage") {
            obj.gotoCurrentDef();
        } else {
            obj.searchFunction({
                "type": "stepInto",
                "id": obj.currCallId,
                "name": obj.currCallId
            }, false, 3, function (callIds) {
                if (callIds.length <= 0) {
                    openLink("notify://" + "函数已到底，或者不在采集范围内，请尝试 StepOut 或其它操作。")
                }
            })
        }
    }
    obj.nextLoop = function () {
        openLink("metrics://" + "DATA_STEPS")
        obj.searchFunction({
            "type": "nextLoop",
            "id": obj.currCallId,
            "name": obj.currCallId,
            "scope": obj.currNavState.type
        }, false, 0, function (callIds) {
            if (callIds.length <= 0) {
                openLink("notify://" + "没有下一个循环")
            }
        })
    }
    obj.prevLoop = function () {
        openLink("metrics://" + "DATA_STEPS")
        obj.searchFunction({
            "type": "prevLoop",
            "id": obj.currCallId,
            "name": obj.currCallId,
            "scope": obj.currNavState.type
        }, false, 0, function (callIds) {
            if (callIds.length <= 0) {
                openLink("notify://" + "没有上一个循环")
            }
        })
    }
    obj.stepOut = function (defOrUsage) {
        if (vue.project.inPopup === "true") {
            obj.stepOutInPopup();
            return;
        }
        openLink("metrics://" + "DATA_STEPS")
        if (defOrUsage === undefined) {
            defOrUsage = 3;
        }
        if (obj.currNavState.type === "definition") {
            obj.gotoCurrentUsage();
        } else {
            obj.searchFunction({
                "type": "stepOut",
                "id": obj.currCallId,
                "name": obj.currCallId
            }, false, defOrUsage, function (callIds) {
                if (callIds.length <= 0) {
                    openLink("notify://" + "函数已到顶，请尝试 StepInto 或其它操作。")
                }
            })
        }
    }
    obj.stepIntoInPopup = function () {
        openLink("metrics://" + "DATA_STEPS_IN_POPUP")
        const psiUrl = `parentpsi://stepIntoFromPopup/${vue.project.processId}/${obj.currCallId}?navStateType=${vue.project.navStateType}`;
        openLink(psiUrl);
    }
    obj.stepOutInPopup = function () {
        openLink("metrics://" + "DATA_STEPS_IN_POPUP")
        const psiUrl = `parentpsi://stepOutFromPopup/${vue.project.processId}/${obj.currCallId}?navStateType=${vue.project.navStateType}`;
        openLink(psiUrl);
    }
    obj.stepOverInPopup = function () {
        openLink("metrics://" + "DATA_STEPS_IN_POPUP")
        const psiUrl = `parentpsi://stepOverFromPopup/${vue.project.processId}/${obj.currCallId}?navStateType=${vue.project.navStateType}`;
        openLink(psiUrl);
    }
    obj.stepBackInPopup = function () {
        openLink("metrics://" + "DATA_STEPS_IN_POPUP")
        const psiUrl = `parentpsi://stepBackFromPopup/${vue.project.processId}/${obj.currCallId}?navStateType=${vue.project.navStateType}`;
        openLink(psiUrl);
    }
    obj.stepIntoFromPopup = function (callId, navStateType) {
        obj.leftFilterConfig.allSearchedCallIds.push(callId);
        obj.leftFilterConfig.newlySearchedCallIds.push(callId);
        obj.leftFilterConfig.nextCallId = callId;
        obj.currNavState.type = navStateType;
        obj.doGetGraphDetail(function() {
            obj.doGetFunctionCallDetail(callId, function() {
                obj.stepInto();
            });
        });
    }
    obj.stepOutFromPopup = function (callId, navStateType) {
        obj.leftFilterConfig.allSearchedCallIds.push(callId);
        obj.leftFilterConfig.newlySearchedCallIds.push(callId);
        obj.leftFilterConfig.nextCallId = callId;
        obj.currNavState.type = navStateType;
        obj.doGetGraphDetail(function() {
            obj.doGetFunctionCallDetail(callId, function() {
                obj.stepOut();
            });
        });
    }
    obj.stepOverFromPopup = function (callId, navStateType) {
        obj.leftFilterConfig.allSearchedCallIds.push(callId);
        obj.leftFilterConfig.newlySearchedCallIds.push(callId);
        obj.leftFilterConfig.nextCallId = callId;
        obj.currNavState.type = navStateType;
        obj.doGetGraphDetail(function() {
            obj.doGetFunctionCallDetail(callId, function() {
                obj.stepOver();
            });
        });
    }
    obj.stepBackFromPopup = function (callId, navStateType) {
        obj.leftFilterConfig.allSearchedCallIds.push(callId);
        obj.leftFilterConfig.newlySearchedCallIds.push(callId);
        obj.leftFilterConfig.nextCallId = callId;
        obj.currNavState.type = navStateType;
        obj.doGetGraphDetail(function() {
            obj.doGetFunctionCallDetail(callId, function() {
                obj.stepBack();
            });
        });
    }
    obj.startObjectDetail = function () {
        obj.functionCallDetailTree.treeData = {
            "rootNodes":[],
            "expandedKeys": []
        }

        if (!vue.project.currObjectId) {
            // If currObjectId is undefined/null/empty, create a default treeNode
            const defaultTreeNode = {
                "nodeKey": "root",
                "label": vue.project.searchName + " = " + vue.project.searchId,
                "children": []
            }
            obj.functionCallDetailTree.treeData.rootNodes.push(defaultTreeNode);
            obj.functionCallDetailTree.treeData.expandedKeys.push(defaultTreeNode.nodeKey);
        } else {
            const originalNode = {
                "labelKey": vue.project.searchName,
                "maxVersion": vue.project.searchId, 
                "dataId": vue.project.currObjectId
            }
            obj.doGetObjectDetail(originalNode, function (treeNode) {
                obj.functionCallDetailTree.treeData.rootNodes.push(treeNode);
                obj.functionCallDetailTree.treeData.expandedKeys.push(treeNode["nodeKey"])
                obj.getAllObjectDetails(obj.functionCallDetailTree.treeData.rootNodes)
            })
        }
    }
    obj.startFuncDetail =  function () {
        obj.doGetFunctionCallDetail(parseInt(vue.project.currCallId));
    }
    obj.getDefOrUsageNumber = function (searchType) {
        let defOrUsage = 2;
        if (vue.project.searchType === "classCodeLine") {
            defOrUsage = 3;
        }
        return defOrUsage;
    }
    obj.startShow = function () {
        obj.initGraphFilterConfig(function () {
            if (vue.project.searchType !== null &&
                (vue.project.searchId !== null || vue.project.searchName != null)) {
                obj.searchFunction({
                    "type": vue.project.searchType,
                    "name": vue.project.searchName,
                    "id": vue.project.searchId,
                    "scope": vue.project.searchScope
                }, true, obj.getDefOrUsageNumber(vue.project.searchType))
            } else {
                //trigger to def
                obj.lastClickTimeMs = new Date().getTime();
                obj.focusOnNextCallId();
            }
        });
    }
    obj.focusOnCallId = function (callId) {
        obj.onMessageElementByCallId(callId,  (element, number) => {
            obj.labelCurrentNode(element)
            obj.gotoDefOrUsageByNumber(number)
            obj.scrollElementToCenter(element)
        })
    }
    obj.focusOnNextCallId = function () {
        if (obj.graphFilterConfig.nextCallId !== null) {
            obj.focusOnCallId(obj.graphFilterConfig.nextCallId)
        } else {
            obj.focusOnFirstNumber()
        }
    }
    obj.focusOnFirstNumber = function () {
        if (obj.graphFilterConfig.allSearchedCallIds !== null &&
            obj.graphFilterConfig.allSearchedCallIds.length > 0) {
            const firstCallId = obj.graphFilterConfig.allSearchedCallIds[0];
            obj.focusOnCallId(firstCallId)
        } else {
            const firstElement = document.querySelector(".messageText");
            if (firstElement !== null && firstElement !== undefined) {
                const numberInElement = parseInt(firstElement.querySelector(".numberLabel").textContent.trim());
                obj.labelCurrentNode(firstElement)
                obj.gotoDefOrUsageByNumber(numberInElement)
                obj.scrollElementToCenter(firstElement)
            }
        }
    }
    obj.showGraphDetail = function (functionGraph, callback) {
        vue.graph.mermaidGraphText = "flowchart TD\n";
        vue.graph.function2nodeMap = new Map();
        vue.graph.node2functionMap = new Map();
        vue.graph.functionGraph = functionGraph
        const allFuncNodes = functionGraph.allNodes;
        for (const key in allFuncNodes) {
            if (allFuncNodes.hasOwnProperty(key)) {
                const node = allFuncNodes[key];
                //console.log("Graph node ", `${key}: ${node}`);
                const curNodeId = node.md5Key
                // 设置context与contextFuncKey的映射关系，展示代码时使用
                vue.graph.node2functionMap.set(curNodeId, node.currentFuncKey);
                vue.graph.function2nodeMap.set(node.currentFuncKey, curNodeId);
                // 展示每个流程图节点
                vue.graph.mermaidGraphText += curNodeId + "(\"" + node.funcKeyDisplay + "\")\n"
                // 节点的click事件
                vue.graph.mermaidGraphText += "click " + curNodeId + " call vue.graph.clickNode()\n"
                // 将节点连接所有子节点
                node.childFuncDescs.forEach(childItem => {
                    if (allFuncNodes.hasOwnProperty(childItem.functionKey)) {
                        const childNode = allFuncNodes[childItem.functionKey]
                        const childNodeId = childNode.md5Key;
                        vue.graph.mermaidGraphText += curNodeId + " --> " + childNodeId + "\n";
                    }
                });
            }
        }
        vue.graph.doRefreshGraph(vue.graph.mermaidGraphText).then(() => {
            if (callback !== undefined) {
                callback();
            }
        });
    }
    obj.doGetGraphDetail = function (succCallback, errorCallBack) {
        const rightRefreshed = obj.rightRefreshed;
        let number2Call = null;
        if (obj.mermaidData !== null && obj.mermaidData !== undefined && obj.mermaidData.number2Call !== undefined) {
            number2Call = obj.mermaidData.number2Call;
        }
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "rootFunctionId": vue.project.rootFunctionId,
            "graphFilterConfig": rightRefreshed ? obj.graphFilterConfig : obj.leftFilterConfig,
            "number2Call": number2Call
        };
        axios.post("./detail", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./detail ", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                if (res.data.data !== null && res.data.data !== undefined) {
                    obj.mermaidData = res.data.data;
                    obj.mermaidGraphText = obj.mermaidData.mermaidText;
                    if (rightRefreshed) {
                        obj.graphFilterConfig = obj.mermaidData.graphFilterConfig
                        obj.leftFilterConfig = structuredClone(obj.graphFilterConfig)
                    } else {
                        obj.leftFilterConfig = obj.mermaidData.graphFilterConfig
                        const rightFilterConfig = structuredClone(obj.leftFilterConfig);
                        //add the changed but not submitted data
                        rightFilterConfig.defaultSelectors = obj.graphFilterConfig.defaultSelectors;
                        rightFilterConfig.pinSelectors = obj.graphFilterConfig.pinSelectors;
                        obj.graphFilterConfig = rightFilterConfig;
                    }
                    obj.doRefreshGraph(obj.mermaidGraphText).then(() => {
                        if (succCallback !== undefined) {
                            succCallback();
                        }
                    })
                } else {
                    console.log("Get null detail")
                }
            } else {
                if (errorCallBack !== undefined) {
                    errorCallBack();
                }
            }
        }).catch(err => {
            console.log(err)
        })
    }
    obj.doGetFunctionCallStacks = function (callId) {
        // 如果currCallId已经改变，说明有新的请求，不更新数据
        if (obj.currCallId !== callId) {
            return;
        }
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "callId": callId,
            "stacks": obj.functionCallStackData.stacks,
            "refreshStacks": obj.functionCallStackData.refreshStacks
        };
        axios.post("./functionCallStacks", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./functionCallStacks ", res, postData)
            const errorCode = res.data.errorCode;
            obj.functionCallStackData.refreshStacks = true;
            if (errorCode === '200') {
                // 再次检查currCallId是否改变
                if (obj.currCallId !== callId) {
                    return;
                }
                const scrollTop = vue.$refs.scrollMenuLeftRef.wrap.scrollTop
                obj.functionCallStackData.stacks = res.data.data.stacks;
                if (res.data.data.reused) {
                    //scroll to left
                    vue.$refs.scrollMenuLeftRef.wrap.scrollTop = scrollTop;
                }
            } else {
                obj.functionCallStackData.stacks = [];
            }
        }).catch(err => {
            obj.functionCallStackData.stacks = [];
            obj.functionCallStackData.refreshStacks = true;
            console.log(err)
        })
    }
    obj.doGetFunctionCallDetail = function (callId, callback) {
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "callId": callId
        };
        axios.post("./functionCallDetail", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./functionCallDetail ", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                obj.currCallId = callId
                obj.clickedFuncCalls.set(callId, true)
                obj.currFuncCallDetail = res.data.data;
                if (obj.currFuncCallDetail.treeData !== undefined ) {
                    obj.functionCallDetailTree.treeData = obj.currFuncCallDetail.treeData;
                    //obj.getAllObjectDetails(obj.functionCallDetailTree.treeData.rootNodes)
                    if (obj.functionCallDetailTree.unFoldAll) {
                        obj.functionCallDetailTree.unfoldAllNodes();
                    } else {
                        obj.functionCallDetailTree.foldAllNodes();
                    }
                }
                obj.currFuncCallDetail.callId = callId;
                obj.doGetFunctionCallStacks(callId);
                // Only fetch SQL request IDs first; details are loaded on expand
                obj.doGetInnerOutputRequestsIds(callId);
                if (callback) {
                    callback();
                }
            } else {
                obj.currCallId = null;
                obj.currFuncCallDetail = {};
                obj.functionCallDetailTree.treeData = {
                    "rootNodes": [],
                    "expandedKeys": []
                }
            }
        }).catch(err => {
            obj.currCallId = null;
            obj.currFuncCallDetail = {};
            obj.functionCallDetailTree.treeData = {
                "rootNodes": [],
                "expandedKeys": []
            }
            console.log(err)
        })
    }
    obj.getAllObjectDetails = function (rootNodes) {
        console.log("getAllObjectDetails", rootNodes)
        rootNodes.forEach(node => {
            if (node["dataId"] !== null && node["nodeKey"] && node["nodeKey"].startsWith("object/")) {
                obj.doGetObjectDetail(node, null, 2)
            }
        })
    }
    obj.doGetObjectDetail = function (originTreeNode, callback, depth) {
        const postData = {
            "processId": vue.project.processId,
            "threadId": vue.project.threadId,
            "callId": vue.graph.currCallId,
            "labelKey": originTreeNode["labelKey"],
            "maxVersion": originTreeNode["maxVersion"],
            "uniqId": originTreeNode["dataId"],
            "depth": depth === undefined ? 1 : depth
        };
        axios.post("./objectDetail", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./objectDetail ", res, postData)
            const errorCode = res.data.errorCode;
            if (errorCode === '200') {
                const rootNodes = res.data.data.rootNodes;
                if (rootNodes.length >= 1) {
                    const treeNode = rootNodes[0];
                    if (originTreeNode != null) {
                        originTreeNode["labelKey"] = treeNode["labelKey"]
                        originTreeNode["labelValue"] = treeNode["labelValue"]
                        originTreeNode["label"] = treeNode["label"]
                        originTreeNode["maxVersion"] = treeNode["maxVersion"]
                        originTreeNode["children"] = treeNode["children"]
                        originTreeNode["leaf"] = treeNode["leaf"]
                    }
                    //console.log("callback", callback)
                    if (callback != null) {
                        callback(treeNode);
                    }
                }
            }
        }).catch(err => {
            console.log(err)
        })
    }
    obj.zoomTo = function (zoomLevel) {
        const svgIdName = obj.graphName + 'Detail';
        const functionTraceGraphElement =  document.getElementById(obj.graphName);
        const svgElement = document.getElementById(svgIdName);
        const maxWidth = parseInt(svgElement.style.maxWidth.replace("px", ""));
        functionTraceGraphElement.style.width = maxWidth * zoomLevel + "px";
    }
    obj.zoomBack = function () {
        obj.zoomTo(obj.zoomLevel)
    }
    obj.resetZoom = function () {
        obj.zoomLevel = obj.resetZoomLevel;
        obj.zoomTo(obj.zoomLevel)
    }
    obj.zoomIn = function () {
        if (obj.zoomLevel >= 1) {
            return
        }
        obj.zoomLevel = obj.zoomLevel + 0.1;
        obj.zoomTo(obj.zoomLevel)
    }
    obj.zoomOut = function () {
        if (obj.zoomLevel <= 0.3) {
            return
        }
        obj.zoomLevel = obj.zoomLevel - 0.1
        obj.zoomTo(obj.zoomLevel)
    }
    obj.asTspan = function (text, className) {
        return "<tspan class='" + className + "'>" + text + "</tspan>"
    }
    obj.adjustYAll = function (event) {
        document.querySelectorAll(".actor").forEach((element) => {
            let shouldAdjust = false;
            let actorElement = null;
            if (element.tagName === 'text') {
                const previousElement = element.previousElementSibling;
                if (previousElement.tagName === 'rect' && previousElement.classList.contains("actor-top")) {
                    actorElement = previousElement
                    shouldAdjust = true;
                }
            } else if (element.tagName === 'rect') {
                if (element.classList.contains("actor-top")) {
                    actorElement = element;
                    shouldAdjust = true;
                }
            }
            if (shouldAdjust) {
                obj.adjustY(element, actorElement)
            }
        })
    }
    obj.getRatio = function (actorElement) {
        const width = parseFloat(actorElement.getAttribute("width"));
        const realWidth = actorElement.getBoundingClientRect().width;
        return  realWidth / width;
    }
    obj.scrollElementToCenter = function (element) {
        const graphElement = document.querySelector(".main-container")
        {
            const parentY = graphElement.getBoundingClientRect().y;
            const parentHeight = graphElement.getBoundingClientRect().height;
            const scrollTop = vue.$refs.scrollMenuRef.wrap.scrollTop;
            const height = element.getBoundingClientRect().height;
            const originY = element.getBoundingClientRect().y + scrollTop;
            vue.$refs.scrollMenuRef.wrap.scrollTop = originY - (parentY + (parentHeight / 2) + (height / 2));
        }
        {
            const parentX = graphElement.getBoundingClientRect().x;
            const parentWidth = graphElement.getBoundingClientRect().width;
            const scrollLeft = vue.$refs.scrollMenuRef.wrap.scrollLeft;
            const width = element.getBoundingClientRect().width;
            const originX = element.getBoundingClientRect().x + scrollLeft;
            vue.$refs.scrollMenuRef.wrap.scrollLeft = originX - (parentX + (parentWidth / 2) + (width / 2));
        }
    }
    obj.scrollToLeftTop = function (element) {
        const graphElement = document.querySelector(".main-container")
        {
            const parentY = graphElement.getBoundingClientRect().y;
            const scrollTop = vue.$refs.scrollMenuRef.wrap.scrollTop;
            const originY = element.getBoundingClientRect().y + scrollTop;
            vue.$refs.scrollMenuRef.wrap.scrollTop = originY - (parentY + 60);
        }
        {
            const parentX = graphElement.getBoundingClientRect().x;
            const scrollLeft = vue.$refs.scrollMenuRef.wrap.scrollLeft;
            const originX = element.getBoundingClientRect().x + scrollLeft;
            vue.$refs.scrollMenuRef.wrap.scrollLeft = originX - (parentX + 60);
        }
    }
    obj.scrollToRightBottom = function (element) {
        const graphElement = document.querySelector(".main-container")
        {
            const parentBottom = graphElement.getBoundingClientRect().y + graphElement.getBoundingClientRect().height;
            const scrollTop = vue.$refs.scrollMenuRef.wrap.scrollTop;
            const height = element.getBoundingClientRect().height;
            const originY = element.getBoundingClientRect().y + scrollTop;
            vue.$refs.scrollMenuRef.wrap.scrollTop = originY - (parentBottom - height - 120);
        }
        {
            const parentXRight = graphElement.getBoundingClientRect().x + graphElement.getBoundingClientRect().width;
            const scrollLeft = vue.$refs.scrollMenuRef.wrap.scrollLeft;
            const width = element.getBoundingClientRect().width;
            const originX = element.getBoundingClientRect().x + scrollLeft;
            vue.$refs.scrollMenuRef.wrap.scrollLeft = originX - (parentXRight - width - 120);
        }
    }
    obj.adjustY = function (element, actorElement) {
        const scrollTop = vue.$refs.scrollMenuRef.wrap.scrollTop;
        const ratio = obj.getRatio(actorElement)
        const actorY = parseFloat(actorElement.getAttribute("originY"))
        const elementY = parseFloat(element.getAttribute("originY"));
        const scrolledY = (scrollTop / ratio);
        if (scrolledY < actorY) {
            element.setAttribute("y", elementY)
        } else {
            element.setAttribute("y", scrolledY + (elementY - actorY))
        }
    }
    obj.rememberY = function (functionTraceGraphElement) {
        functionTraceGraphElement.querySelectorAll(".actor").forEach((element) => {
            let shouldAdjust = false;
            if (element.tagName === 'text') {
                shouldAdjust = true;
            } else if (element.tagName === 'rect') {
                if (element.classList.contains("actor-top")) {
                   shouldAdjust = true;
                }
            }
            if (shouldAdjust) {
                const originY = element.getAttribute("y")
                element.setAttribute("originY", originY)
            }
        })
    }
    obj.reformatSvgDiagram = function (functionTraceGraphElement) {
        functionTraceGraphElement.querySelectorAll(".text").forEach((element) => {
            if (element.tagName === 'text') {
                const textContent = element.textContent;
                let index = textContent.indexOf("[");
                if (index > 0) {
                    let part1 = _.escape(textContent.substring(0, index));
                    let part2 = _.escape(textContent.substring(index, textContent.length))
                    element.innerHTML = obj.asTspan(part1, "groupName") + obj.asTspan(part2, "foldLabel")
                } else {
                    element.innerHTML = obj.asTspan(_.escape(textContent), "groupName")
                }
            }
        })

        functionTraceGraphElement.querySelectorAll(".actor").forEach((element) => {
            if (element.tagName === 'text') {
                const textContent = element.textContent;
                let index = textContent.indexOf("[");
                if (index > 0) {
                    let part1 = _.escape(textContent.substring(0, index));
                    let part2 = _.escape(textContent.substring(index, textContent.length))
                    element.innerHTML = obj.asTspan(part1, "actorName") + obj.asTspan(part2, "foldLabel")
                } else {
                    element.innerHTML = obj.asTspan(_.escape(textContent), "actorName")
                }
            } else if (element.tagName === 'rect') {
                //element.style.fill = '#ecf5ff';
            }
        })
        functionTraceGraphElement.querySelectorAll(".messageText").forEach((element) => {
            if (element.tagName === 'text') {
                const isMethodEnd = obj.isMethodCallEndText(element);
                if (isMethodEnd) {
                    const textContent = element.textContent;
                    let numberIndex = textContent.indexOf(".")
                    const numberLabel = _.escape(textContent.substring(0, numberIndex));
                    element.setAttribute("numberLabel", numberLabel)
                    element.innerHTML = element.innerHTML = obj.asTspan("", "numberLabel") + obj.asTspan("...", "functionName")
                        + obj.asTspan("", "costLabel")
                } else {
                    const textContent = element.textContent;
                    let numberIndex = textContent.indexOf(".")
                    let costMsIndex = textContent.indexOf("(")
                    let foldIndex = textContent.indexOf("[")
                    if (numberIndex >= 0) {
                        //TODO
                    }
                    const part1 = _.escape(textContent.substring(0, numberIndex + 1));
                    const part2 = _.escape(textContent.substring(numberIndex + 1, costMsIndex));
                    let part3 = null
                    let part4 = null
                    if (foldIndex > 0) {
                        part3 = _.escape(textContent.substring(costMsIndex, foldIndex));
                        part4 = _.escape(textContent.substring(foldIndex, textContent.length));
                        element.innerHTML = obj.asTspan(part1, "numberLabel") + obj.asTspan(part2, "functionName")
                            + obj.asTspan(part3, "costLabel") + obj.asTspan(part4, "foldLabel")
                    } else {
                        part3 = _.escape(textContent.substring(costMsIndex, textContent.length));
                        element.innerHTML = obj.asTspan(part1, "numberLabel") + obj.asTspan(part2, "functionName")
                            + obj.asTspan(part3, "costLabel")
                    }
                }
            }
        })
    }
    obj.scrollElementToPos = function (element, lastX, lastY) {
        console.log("scrollElementToPos", element, lastX, lastY)
        {
            const scrollTop = vue.$refs.scrollMenuRef.wrap.scrollTop;
            const originY = element.getBoundingClientRect().y + scrollTop;
            vue.$refs.scrollMenuRef.wrap.scrollTop = originY - lastY;
        }
        {
            const scrollLeft = vue.$refs.scrollMenuRef.wrap.scrollLeft;
            const originX = element.getBoundingClientRect().x + scrollLeft;
            vue.$refs.scrollMenuRef.wrap.scrollLeft = originX - lastX;
        }
    }
    obj.scrollElementToPosByCallId = function (callId, lastX, lastY) {
        console.log("scrollElementToPosByCallId", callId, lastX, lastY)
        const callback = function (element) {
            obj.scrollElementToPos(element, lastX, lastY)
        }
        obj.onMessageElementByCallId(callId, callback)
    }
    obj.scrollSearchElementToCenter = function (searchData, gotoDefOrUsage) {
        const newAddedCallIds = obj.leftFilterConfig.newlySearchedCallIds;
        if (newAddedCallIds === undefined ||
            newAddedCallIds === null ||
            newAddedCallIds.length <= 0) {
            return
        }
        const searchType = searchData["type"];
        const isStack = searchType === 'stack';
        let callId = obj.leftFilterConfig.nextCallId;
        if (callId === null || callId === undefined) {
            if (searchType === 'stack' || searchType === 'function') {
                callId = searchData["id"]
            } else {
                callId = newAddedCallIds[0];
            }
        }
        const searchCallId = callId;
        const callback = function (element, number) {
            if (!isStack) {
                console.log("Try to scroll to leftTop by callId", searchCallId, newAddedCallIds, searchData, element)
                obj.scrollToLeftTop(element)
            } else {
                console.log("Try to scroll to rightBottom by callId", searchCallId, newAddedCallIds, searchData, element)
                obj.scrollToRightBottom(element)
            }
            vue.searchTab.focusOnNodeFrame(number)
            obj.labelCurrentNode(element)
            obj.doGetFunctionCallDetail(searchCallId)
            if (gotoDefOrUsage === 0) {
                obj.gotoDefOrUsage({"type": obj.currNavState.type, "id": searchCallId})
            } else if (gotoDefOrUsage === 1) {
                //all go to usage
                obj.gotoDefOrUsage({"type": "usage", "id": searchCallId, "force": "true"})
            }  else if (gotoDefOrUsage === 2) {
                obj.gotoDefOrUsage({"type": "definition", "id": searchCallId})
            } else if (gotoDefOrUsage === 3) {
                obj.gotoDefOrUsage({"type": "usage", "id": searchCallId, "force": "true"})
            } else if (gotoDefOrUsage === 4) {
                obj.gotoDefOrUsage({"type": "definition", "id": searchCallId, "return": "true"})
            }
        }
        obj.onMessageElementByCallId(searchCallId, callback)
    }
    obj.onMessageElementByCallId = function (searchCallId, callBack) {
        const graphElement = document.getElementById(obj.graphName)
        graphElement.querySelectorAll(".messageText").forEach((element) => {
            if (element.tagName === 'text') {
                const numberStr = element.querySelector(".numberLabel").textContent.trim();
                const number = parseInt(numberStr);
                const callId = obj.mermaidData.number2Call[number]
                if (callId === searchCallId) {
                    callBack(element, number)
                }
            }
        })
    }
    obj.labelNewlySearchedNodes = function (functionTraceGraphElement) {
        const newAddedCallIds = obj.mermaidData.graphFilterConfig.newlySearchedCallIds;
        if (newAddedCallIds === undefined || newAddedCallIds === null) {
            return
        }
        //console.log("newAddedCallIds", newAddedCallIds)
        functionTraceGraphElement.querySelectorAll(".messageText").forEach((element) => {
            if (element.tagName === 'text') {
                const numberStr = element.querySelector(".numberLabel").textContent.trim();
                const number = parseInt(numberStr);
                const callId = obj.mermaidData.number2Call[number]
                if (callId !== null) {
                    const isInSearchedCalls = newAddedCallIds.includes(callId);
                    //console.log("Call id ", callId, isInSearchedCalls, number, numberStr)
                    if (isInSearchedCalls) {
                        const node = element.querySelector(".numberLabel");
                        if (node !== null) {
                            node.style.fill = obj.pinedNodeColor;
                        }
                        const nextSiblingElement = element.nextElementSibling;
                        if (nextSiblingElement.classList.contains("messageLine0")) {
                            nextSiblingElement.style.stroke = obj.pinedNodeColor;
                        }
                    }
                }
            }
        })
    }
    obj.labelThePinNodes = function (functionTraceGraphElement) {
        if (obj.mermaidData.pinedCallIds === undefined || obj.mermaidData.pinedCallIds === null) {
            return
        }
        functionTraceGraphElement.querySelectorAll(".messageText").forEach((element) => {
            if (element.tagName === 'text') {
                const number = parseInt(element.querySelector(".numberLabel").textContent.trim());
                const callId = obj.mermaidData.number2Call[number]
                if (callId !== null) {
                    const labelName = obj.mermaidData.pinedCallIds[callId];
                    if (labelName !== null) {
                        const node = element.querySelector("." + labelName);
                        if (node !== null) {
                            node.style.fill = obj.pinedNodeColor;
                        }
                    }
                }
            }
        })
    }
    obj.labelTheClickedNodes = function (functionTraceGraphElement) {
        if (obj.clickedFuncCalls === undefined || obj.clickedFuncCalls === null) {
            return
        }
        functionTraceGraphElement.querySelectorAll(".messageText").forEach((element) => {
            if (element.tagName === 'text') {
                const number = parseInt(element.querySelector(".numberLabel").textContent.trim());
                const callId = obj.mermaidData.number2Call[number]
                if (callId !== null
                    && obj.clickedFuncCalls.has(callId)) {
                    const functionNode = element.querySelector(".functionName");
                    if (functionNode != null) {
                        functionNode.style.fill = obj.clickedNodeColor;
                        functionNode.style.stroke = obj.clickedNodeColor;
                    }
                }
            }
        })
    }
    obj.restoreLastCallId = function () {
        obj.refreshSearchNumbers();
        const currCallId = obj.currCallId;
        if (currCallId !== null) {
            for (let number in obj.mermaidData.number2Call) {
                if (currCallId === obj.mermaidData.number2Call[number]) {
                    const finalNumber = parseInt(number);
                    setTimeout(function () {
                        vue.searchTab.focusOnNodeFrame(finalNumber)
                    }, 300)
                }
            }
            obj.onMessageElementByCallId(currCallId, function (element, number) {
                obj.labelCurrentNode(element)
            })
        }
    }
    obj.recoverToClickedNodes = function (stretchStack) {
        openLink("metrics://" + "SEQUENCE_HISTORY")
        obj.leftFilterConfig.clickedStatus = {}
        obj.leftFilterConfig.newlySearchedCallIds = []
        obj.leftFilterConfig.allSearchedCallIds = []
        obj.leftFilterConfig.stretchStack = stretchStack;
        for (let [key, value] of obj.clickedFuncCalls) {
            if (value === true) {
                obj.leftFilterConfig.allSearchedCallIds.push(key);
            }
        }
        const succCallBack = function () {
            obj.restoreLastCallId();
            vue.$message({
                message: '梳理浏览记录，展示点击过的节点，以及它们共同的父栈节点!',
                type: 'success'
            });
        }

        obj.onNodeKeyChange(succCallBack)
    }
    obj.isMethodCallEndText = function (parentElement) {
        const parentNextElement = parentElement.nextElementSibling;
        if (parentNextElement !== undefined &&
            parentNextElement !== null &&
            parentNextElement.classList.contains("messageLine1")) {
            return true;
        }
        return false;
    }
    obj.handleClickNode = function (element) {
        console.log("Click node", element)
        if (element.tagName === "tspan") {
            const parentElement = element.parentElement;
            if (parentElement.classList.contains("actor")) {
                const actorName = parentElement.querySelector(".actorName").textContent.trim();
                const labelNode = parentElement.querySelector(".foldLabel")
                if (labelNode != null) {
                    const foldLabel = labelNode.textContent.trim();
                    let actorLevel = null
                    if (foldLabel === "[+]") {
                        actorLevel = "CLASS"
                    } else {
                        actorLevel = "ACTOR"
                    }
                    const actorMeta = obj.mermaidData.showActorMetas[actorName];
                    if (actorMeta != null) {
                        const defaultGroup = actorMeta.f1;
                        obj.leftFilterConfig.actorLevelMap[defaultGroup] = actorLevel;
                        obj.onNodeKeyChange(obj.restoreLastCallId)
                    }
                }
            } else if (parentElement.classList.contains("messageText")) {
                const isMethodEnd = obj.isMethodCallEndText(parentElement);
                if (isMethodEnd) {
                    obj.labelCurrentNode(parentElement)
                    const number = parseInt(parentElement.getAttribute("numberLabel"));
                    const callId = obj.mermaidData.number2Call[number]
                    console.log("Click messageText end", number)
                    if (callId !== null) {
                        obj.doGetFunctionCallDetail(callId)
                        obj.gotoDefOrUsage({"type": "definition", "id": callId, "return": "true"})
                    }
                    vue.searchTab.focusOnNodeFrame(number)
                } else {
                    const number = parseInt(parentElement.querySelector(".numberLabel").textContent.trim());
                    console.log("Click messageText end", number)
                    const currTimeMs = new Date().getTime();
                    if (element.classList.contains("foldLabel")) {
                        openLink("metrics://" + "SEQUENCE_CLICK_EXPAND")
                        if (currTimeMs - obj.lastClickTimeMs > 300) {
                            const foldLabel = element.textContent;
                            let childLevel = 0;
                            if (foldLabel === "[+]") {
                                childLevel = 1
                            } else {
                                childLevel = 0
                            }
                            const callId = obj.mermaidData.number2Call[number]
                            if (callId !== null) {
                                obj.updateClickedStatus({
                                    "callId": callId + "",
                                    "childLevel": childLevel
                                })
                                const lastX = parentElement.getBoundingClientRect().x;
                                const lastY = parentElement.getBoundingClientRect().y;
                                const callBack = function () {
                                    obj.scrollElementToPosByCallId(callId, lastX, lastY);
                                    obj.restoreLastCallId();
                                }
                                obj.onNodeKeyChange(callBack);
                            }
                        }
                    } else {
                        openLink("metrics://" + "SEQUENCE_CLICK_NODE")
                        obj.labelCurrentNode(parentElement)
                        obj.gotoDefOrUsageByNumber(number)
                        vue.searchTab.focusOnNodeFrame(number)
                    }
                    obj.lastClickTimeMs = currTimeMs;
                }
            }
        }
    }
    obj.doRefreshGraph = async function (mermaidGraphText, curNodeId) {
        //console.log("current graph", obj, mermaidGraphText)
        if (mermaidGraphText == null) {
            console.log("mermaidGraphText is null")
            return
        }
        obj.setCurrentJbSourceColorClass()
        const scrollLeft = vue.$refs.scrollMenuRef.wrap.scrollLeft
        const scrollTop = vue.$refs.scrollMenuRef.wrap.scrollTop
        let functionTraceGraphElement = document.getElementById(obj.graphName)
        try {
            const svgIdName = obj.graphName + 'Detail';
            const {svg, bindFunctions} = await mermaid.render(svgIdName, mermaidGraphText);
            functionTraceGraphElement.innerHTML = svg
            const svgElement = document.getElementById('functionTraceGraphDetail');
            //svgElement.style.backgroundColor = "#3d3f41"
            //console.log("refresh graph", svg)
            if (bindFunctions) {
                bindFunctions(functionTraceGraphElement);
            }
        } catch (error) {
            console.log("Render mermaid text failed", error)
            functionTraceGraphElement.innerHTML = mermaidGraphText;
            return
        }
        obj.zoomBack();
        vue.$refs.scrollMenuRef.wrap.scrollLeft = scrollLeft;
        vue.$refs.scrollMenuRef.wrap.scrollTop = scrollTop;
        obj.reformatSvgDiagram(functionTraceGraphElement);
        obj.labelThePinNodes(functionTraceGraphElement)
        obj.labelNewlySearchedNodes(functionTraceGraphElement)
        obj.labelTheClickedNodes(functionTraceGraphElement)
        obj.rememberY(functionTraceGraphElement)
        obj.adjustYAll()
        functionTraceGraphElement.querySelectorAll(".actor").forEach((element) => {
            element.addEventListener('click', function (e) {
                obj.handleClickNode(e.target)
            })
        })
        functionTraceGraphElement.querySelectorAll(".messageText").forEach((element) => {
            element.addEventListener('click', function (e) {
                obj.handleClickNode(e.target)
            })
        })
        if (curNodeId !== undefined && curNodeId !== null) {
            // 选择所有函数链的node
            const elementsWithClass = document.querySelectorAll('.node.default.clickable.flowchart-label');
            elementsWithClass.forEach(element => {
                // 找出当前的节点位置，然后调整滚动条
                if (element.id.includes(curNodeId)) {
                    if (element.getBoundingClientRect().top > 500) {
                        vue.$refs.scrollMenuRef.wrap.scrollTop = element.getBoundingClientRect().top - 500;
                    }
                }
            });
        }
    }
    obj.downloadGraph = function () {
        openLink("metrics://" + "SEQUENCE_DOWNLOAD")
        const svgElement = document.getElementById('functionTraceGraphDetail');
        const svgData = new XMLSerializer().serializeToString(svgElement);
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        const link = document.getElementById("graphDownloadStub")
        link.href = URL.createObjectURL(blob);
        console.log("link.href", link.href)
        let downloadName = "xcodemap_exported_image"
        if (vue.project.funcName !== null) {
            downloadName = "xcodemap_" + encodeURIComponent(vue.project.funcName);
        }
        downloadName = downloadName + ".svg";
        link.download = downloadName;  // 设置下载文件的名称
        // 触发点击下载
        link.click();
        openLink("download://" + downloadName + "/" + svgData)
        //svgElement.style.removeProperty("backgroundColor")
    }
    /*obj.doClickFunction = function (functionKey) {
        const nodeId = vue.graph.function2nodeMap.get(functionKey);
        if (nodeId !== undefined && nodeId !== null) {
            this.clickNode(nodeId);
        }
    }
    obj.clickNode = function (nodeId) {
        const functionKey = vue.graph.node2functionMap.get(nodeId);
        vue.fileTree.clickFunction(functionKey)
    }*/
    obj.findObjectChangeHistory = function (data) {
        console.log("Find object change history", data)
        const postData = {
            "processId": vue.project.processId,
            "objectId": data.id,
            "contextCallId": obj.currCallId
        };
        axios.post("./findObjectChangeHistory", postData, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => {
            console.log("./findObjectChangeHistory ", res, postData)
            
        }).catch(err => {
            console.log(err)
        })
    }
    // 仅获取 SQL 请求相关的 callId 列表，并更新计数
    obj.doGetInnerOutputRequestsIds = async function (currCallId) {
        try {
            obj.sqlRequestsCount = 0;
            obj.innerOutputRequestCallIds = [];
            obj.sqlRequestsLoadedForCallId = null;
            obj.functionCallInnerOutputRequestsTree.treeData = { rootNodes: [] };
            obj.sqlRequestsCollapsed = true;
            
            
            // 搜索SQL请求相关的调用（仅ID）
            const searchParams = {
                processId: vue.project.processId,
                threadId: vue.project.threadId,
                rootFunc: vue.project.rootFunc,
                currCallId: currCallId,
                namedSelector: {
                    type: "findInnerOutputCall",
                    name: "",
                    id: currCallId,
                    scope: "usage",
                }
            };
            
            const callIdsResponse = await this.searchCallIds(searchParams);
            console.log("SQL requests callIdsResponse (ids only)", searchParams, callIdsResponse);

            const callIds = (callIdsResponse && callIdsResponse.data && Array.isArray(callIdsResponse.data.funcCallIds))
                ? callIdsResponse.data.funcCallIds
                : [];

            // 保存ID与计数，并清空树数据，标记未加载详情
            obj.innerOutputRequestCallIds = callIds;
            obj.sqlRequestsCount = callIds.length;
            obj.functionCallInnerOutputRequestsTree.treeData = { rootNodes: [] };
            // 折叠面板，等待用户点击展开再加载详情
        } catch (error) {
            console.error('Error fetching SQL requests:', error);
        }
    }
    // 在需要时加载 SQL 请求详情（仅在首次展开时执行）
    obj.loadInnerOutputRequestDetailsIfNeeded = async function () {
        try {
            if (!obj.currCallId) return;
            if (obj.sqlRequestsLoadedForCallId === obj.currCallId) return;
            if (!obj.innerOutputRequestCallIds || obj.innerOutputRequestCallIds.length === 0) {
                // 没有可加载的ID
                obj.functionCallInnerOutputRequestsTree.treeData = { rootNodes: [] };
                obj.sqlRequestsLoadedForCallId = obj.currCallId; // 防止重复尝试
                return;
            }

            // 清空现有数据并开始加载详情
            obj.functionCallInnerOutputRequestsTree.treeData = { rootNodes: [] };

            for (let i = 0; i < obj.innerOutputRequestCallIds.length; i++) {
                const callId = obj.innerOutputRequestCallIds[i];
                const detailParams = {
                    processId: vue.project.processId,
                    threadId: vue.project.threadId,
                    callId: callId
                };
                const functionDetail = await this.getFunctionCallDetail(detailParams);

                if (functionDetail.data.headNode && functionDetail.data.treeData && functionDetail.data.treeData.rootNodes) {
                    if (!functionDetail.data.headNode.children) {
                        functionDetail.data.headNode.children = [];
                    }
                    const funcSelfNode = {
                        label: "跳转源码",
                        labelKey: "url",
                        labelValue: "./findFunc?callId=" + callId,
                        children: [],
                        alwaysShow: true,
                        leaf: true
                    };
                    functionDetail.data.headNode.children.push(funcSelfNode);
                    functionDetail.data.headNode.children.push(...functionDetail.data.treeData.rootNodes);
                    obj.functionCallInnerOutputRequestsTree.treeData.rootNodes.push(functionDetail.data.headNode);
                }
            }

            obj.sqlRequestsLoadedForCallId = obj.currCallId;
            console.log("SQL requests details loaded:", obj.functionCallInnerOutputRequestsTree.treeData.rootNodes.length);
        } catch (error) {
            console.error('Error loading SQL request details:', error);
        }
    }
    // 添加辅助函数来调用API
    obj.getFunctionCallDetail = async function(params) {
        const postData = {
            processId: params.processId,
            threadId: params.threadId,
            callId: params.callId
        };

        try {
            const response = await axios.post("./functionCallDetail", postData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    obj.searchCallIds = async function(params) {
        const postData = {
            processId: params.processId,
            threadId: params.threadId,
            rootFunc: params.rootFunc,
            currCallId: params.currCallId,
            namedSelector: params.namedSelector
        };

        try {
            const response = await axios.post("./search", postData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    obj.getObjectDetail = async function(params) {
        const postData = {
            processId: params.processId,
            threadId: params.threadId,
            callId: params.callId,
            labelKey: params.labelKey,
            maxVersion: params.maxVersion,
            uniqId: params.uniqId,
            depth: params.depth === undefined ? 1 : params.depth
        };

        try {
            const response = await axios.post("./objectDetail", postData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    return obj;
}
