import{i as A,u as U,r as u,v as T,z as D,j as i,o as n,l as e,n as x,q as s,t as g,m as d,C as L,D as P,_ as E,S as I,T as M}from"./style-ZGiRjKX1.js";import{u as $}from"./activation-DCKqwZPe.js";const F={class:"flex-1 overflow-y-auto overflow-x-hidden p-8"},R={class:"max-w-2xl mx-auto"},j={class:"text-center mb-8"},q={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6"},z={class:"space-y-6"},B={key:0,class:"space-y-4"},V={class:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600"},X={class:"flex items-center justify-between"},N={class:"text-sm font-mono break-all flex-1 mr-3 text-gray-900 dark:text-gray-100 font-medium"},O={key:0,class:"text-center"},W={key:1,class:"space-y-4"},G={class:"space-y-4"},H=["disabled"],J=["disabled"],K={key:2,class:"bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg"},Q={key:3,class:"bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg"},Y={key:0,class:"text-sm mt-2"},Z=A({__name:"Activating",setup(te){const v=U(),a=$(),c=u(""),r=u(""),p=u(!1),l=u(null),m=u(!1);T(()=>{v.initTheme(),f(),y()}),D(()=>{});const f=()=>{const t=new URLSearchParams(window.location.search).get("deviceCode");t&&(l.value=t,console.log("Device code found in URL parameters:",t))},y=async()=>{try{if(l.value)a.setDeviceCode(l.value),a.setStep("device_code_generated"),r.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(l.value)}`,p.value=!0,b();else{await a.generateDeviceCode();const o=a.getDeviceCode();o&&(r.value=`https://xcodemap.tech/maps/sequence/activated.html?deviceCode=${encodeURIComponent(o)}`,p.value=!0,b())}}catch(o){console.error("Failed to start activation process:",o)}},b=()=>{},_=async()=>{try{await navigator.clipboard.writeText(r.value),m.value=!0,setTimeout(()=>{m.value=!1},2e3),console.log("Activation link copied to clipboard")}catch(o){console.error("Failed to copy activation link:",o);const t=`clipboard://${r.value}`;console.log("Using clipboard protocol:",t),window.open(t,"_blank")}},k=()=>{r.value&&window.open(r.value,"_blank")},w=async()=>{if(c.value.trim())try{if(l.value){const o=`clipboard://xcm_fin_ack://${c.value.trim()}`;console.log("Using clipboard protocol:",o),window.open(o,"_blank"),a.setStep("activated")}else{const o=await a.finishActivationProcess(c.value.trim());if(console.log("Activation completed successfully, expired timestamp:",o),o){const t=new Date(o);console.log("License expires at:",t.toLocaleString())}}}catch(o){console.error("Failed to finish activation:",o)}},C=()=>{switch(a.getCurrentStep()){case"idle":return"准备开始激活流程...";case"generating_device_code":return"正在生成设备码...";case"device_code_generated":return"设备码生成成功，请获取激活码";case"finishing_activation":return"正在完成激活...";case"activated":return"激活成功！";case"error":return`激活失败: ${a.getError()}`;default:return"未知状态"}};return(o,t)=>(n(),i("div",{class:x(["h-screen w-full flex flex-col overflow-x-hidden",(s(v).theme==="dark","bg-[var(--bg-color)]")])},[e("div",F,[e("div",R,[e("div",j,[e("h1",{class:x(["text-3xl font-bold mb-4",s(v).theme==="dark"?"text-white":"text-gray-900"])}," XCodeMap 激活 ",2),e("p",{class:x(["text-lg",s(v).theme==="dark"?"text-gray-300":"text-gray-600"])},g(C()),3)]),e("div",q,[e("div",z,[r.value?(n(),i("div",B,[t[2]||(t[2]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 专属激活链接 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," 请访问此专属链接以获取激活码 ")],-1)),e("div",V,[e("div",X,[e("code",N,g(r.value),1)])]),e("div",{class:"flex justify-center space-x-4"},[e("button",{onClick:k,class:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-200"}," 访问链接 "),e("button",{onClick:_,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"}," 复制链接 ")]),m.value?(n(),i("div",O,t[1]||(t[1]=[e("p",{class:"text-sm text-green-600 dark:text-green-400 font-medium"}," ✓ 链接已复制到剪贴板 ",-1)]))):d("",!0),t[3]||(t[3]=e("div",{class:"text-center"},[e("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 如果无法直接访问，请复制链接到能联网访问的浏览器打开 ")],-1))])):d("",!0),p.value?(n(),i("div",W,[t[4]||(t[4]=e("div",{class:"text-center"},[e("h3",{class:"text-lg font-semibold mb-2 text-gray-900 dark:text-white"}," 输入激活码 "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 请输入从XCodeMap官网 https://xcodemap.tech 获得的激活码 ")],-1)),e("div",G,[L(e("textarea",{"onUpdate:modelValue":t[0]||(t[0]=S=>c.value=S),placeholder:"请输入激活码",rows:"4",class:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none",disabled:s(a).getCurrentStep()==="finishing_activation"},null,8,H),[[P,c.value]]),e("button",{onClick:w,disabled:!c.value.trim()||s(a).getCurrentStep()==="finishing_activation",class:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-lg"},g(s(a).getCurrentStep()==="finishing_activation"?"激活中...":"完成激活"),9,J)])])):d("",!0),s(a).hasError()?(n(),i("div",K,g(s(a).getError()),1)):d("",!0),s(a).isActivated()?(n(),i("div",Q,[t[5]||(t[5]=e("div",{class:"font-medium"},"激活成功！您现在可以使用 XCodeMap 了。",-1)),s(a).getExpiredTimestamp()?(n(),i("div",Y," 许可证过期时间："+g(new Date(s(a).getExpiredTimestamp()).toLocaleString()),1)):d("",!0)])):d("",!0)])])])])],2))}}),ee=E(Z,[["__scopeId","data-v-deb55133"]]),h=I(ee);h.use(M());h.mount("#app");
