:root {
    --bg-color: #2b2b2b;
    --text-color: #a9b7c5;
    --undercaret-bg-color: #323232;
    --identifier-undercaret-bg-color: #344134;
    --smart-step-bg-color: #824991;
}

[data-theme="dark"] {
    --bg-color: #2b2b2b;
    --text-color: #a9b7c5;
    --undercaret-bg-color: #323232;
    --identifier-undercaret-bg-color: #344134;
    --smart-step-bg-color: #824991;
}

[data-theme="dark-popup"] {
    --bg-color: #323232;
    --text-color: #a9b7c5;
    --undercaret-bg-color: #2b2b2b;
    --identifier-undercaret-bg-color: #344134;
    --smart-step-bg-color: #824991;
}
[data-theme="light"] {
    --bg-color: #ffffff;
    --text-color: #080808;
    --undercaret-bg-color: #fcfaed;
    --identifier-undercaret-bg-color: #edebfc;
    --smart-step-bg-color: #824991;
}
[data-theme="light-popup"] {
    --bg-color: #fcfaed;
    --text-color: #080808;
    --undercaret-bg-color: #ffffff;
    --identifier-undercaret-bg-color: #edebfc;
    --smart-step-bg-color: #824991;
}

body {
    font-family: "Times New Roman", Times, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}

html, body, #app, .el-container {
    /*background-color: #3d3f41;*/
    background-color: var(--bg-color);
    color: var(--text-color);
}
input::placeholder {
    color: var(--text-color);
    opacity: 1 !important; /* 默认 placeholder 可能会有透明度，确保颜色可见 */
}
.el-input__inner::placeholder {
    color: var(--text-color);
}

/*input:-webkit-autofill {
    background-color: transparent !important;
    color: red !important;
}*/


.el-tree {
    background: var(--bg-color);
    color: var(--text-color);
}
.el-tree-node:focus>.el-tree-node__content {
    background-color: var(--undercaret-bg-color);
}
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: var(--undercaret-bg-color);
}
.el-tree-node__content:hover {
    background-color: var(--undercaret-bg-color);
}
.el-tree-node__expand-icon {
    color: var(--text-color);
}
.el-tree-node__expand-icon > .is-leaf {
    color: transparent;
}


.el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: gray;
    color: var(--text-color);
}

.el-checkbox__input.is-checked+.el-checkbox__label {
    color: var(--text-color);
}

.function-display-config .el-checkbox {
    color: var(--text-color);
}

.left-tab-content-stacks-item-normal {
    color: gray;
    background-color: var(--undercaret-bg-color);
}

.left-tab-content-stacks-item-package {
    /*color: #bbbbbc;*/
    background-color: var(--identifier-undercaret-bg-color);
}

.left-tab-content-stacks-item-current {
    /*
    color: antiquewhite;
    */
    background-color: var(--smart-step-bg-color);
}

/* all tabs related*/
.graph-popup-content  .el-scrollbar {
    background-color: var(--undercaret-bg-color)
}
.search-tab-header >.el-tabs__header {
    background-color: var(--undercaret-bg-color)
}
.search-tab-header>.el-tabs__header .el-tabs__item.is-active {
    color: #409EFF;
    background-color: var(--identifier-undercaret-bg-color)
}

.search-result-item {
    color: gray;
}
.search-result-item:hover {
    color: var(--text-color);
    background-color: var(--identifier-undercaret-bg-color);

}

/* start el-tabs */
.el-tabs__item.is-active {
    background-color: var(--identifier-undercaret-bg-color);
    color: var(--text-color);
}

.el-tabs__item {
    color: var(--text-color);
}

.el-tabs {
    background-color: inherit;
}

.el-button:focus {
    background: inherit;
    border-color: inherit;
    color: var(--text-color);
}

.el-button:hover {
    background: inherit;
    border-color: inherit;
    color: #409EFF;
}

.node-scroll-button {
    background-color: var(--identifier-undercaret-bg-color);
}

.node-scroll-button > .el-dropdown {
    color: var(--text-color);
}

.node-frame {
    color: var(--text-color);
}

.node-frame.node-frame-active {
    color: red;
    background-color: var(--identifier-undercaret-bg-color);
}



.el-button {
    background-color: inherit;
    color: var(--text-color);
    border: 1px solid lightgray;
    border-radius: 10px;
}
.included-filter-label {
    color: var(--text-color);
    border-width: 1px;
    border-style: solid;
    border-radius: 50px;
    border-color: gray;
}


.el-tag {
    color: var(--text-color);
    background-color: inherit;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    border-color: gray;
}

.el-input__inner {
    background-color: var(--bg-color);
    color: var(--text-color);
    border-color: var(--undercaret-bg-color);
    border: 1px solid;
}

.el-input.is-disabled .el-input__inner {
    background-color: var(--bg-color);
    color: var(--text-color);
    border-color: var(--undercaret-bg-color);
}

.el-radio__input.is-checked .el-radio__inner {
    color: var(--text-color);
    background: gray;
}
.el-radio__inner {
    background-color: inherit;
}
.el-radio__input.is-checked+.el-radio__label {
    color: var(--text-color);
}
.el-radio {
    color: var(--text-color);
}

.controlBarMenu {
    background-color: var(--bg-color);
    color: var(--text-color);
}
.controlBarMenu:hover {
    color: #409EFF;
}

.searchBarMenu:hover {
    color: #409EFF;
    background-color: var(--identifier-undercaret-bg-color);
}
/* start filterBarMenu */
.filterBarMenu:hover {
    color: #409EFF;
}

.filterBarMenu {
    color: var(--text-color);
}

.custom-menu {
    background-color: inherit;
    border: 1px solid #ccc;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.menu-item-text {
    color: inherit;
}

.menu-item-text:hover {
    background-color: var(--identifier-undercaret-bg-color);
}

.included-filter-notes {
    color: #409EFF;
}

.hide-icon-left:hover {
    color: #409EFF;
}
.hide-icon-right:hover {
    color: #409EFF;
}

.el-header {
    color: #333;
}

/*.el-aside {
    color: #333;
}*/

.x-handle {
    background-color: gray;
}
.x-icon {
    height: 20px;
}

.hide-icon-right {
    background-color: antiquewhite;
}

.el-link.el-link--default {
    color: var(--text-color);
}



.jbcolor-bg-red {
    background-color: rgb(255, 0, 0) ;
}
.jbcolor-bg-blue {
    background-color: rgb(0, 0, 255);
}
.jbcolor-bg-pink {
    background-color: rgb(255, 175, 175);
}
.jbcolor-bg-orange {
    background-color: rgb(255, 200, 0);
}
.jbcolor-bg-yellow {
    background-color: rgb(255, 255, 0);
}
.jbcolor-bg-green {
    background-color: rgb(0, 255, 0);
}
.jbcolor-bg-magenta {
    background-color: rgb(255, 0, 255);
}
.jbcolor-bg-cyan {
    background-color: rgb(0, 255, 255);
}

.jbcolor-bg-red  .el-input__inner {
    background-color: rgb(255, 0, 0) ;
}
.jbcolor-bg-blue .el-input__inner {
    background-color: rgb(0, 0, 255);
}
.jbcolor-bg-pink .el-input__inner {
    background-color: rgb(255, 175, 175);
}
.jbcolor-bg-orange .el-input__inner {
    background-color: rgb(255, 200, 0);
}
.jbcolor-bg-yellow .el-input__inner {
    background-color: rgb(255, 255, 0);
}
.jbcolor-bg-green .el-input__inner {
    background-color: rgb(0, 255, 0);
}
.jbcolor-bg-magenta .el-input__inner {
    background-color: rgb(255, 0, 255);
}
.jbcolor-bg-cyan .el-input__inner {
    background-color: rgb(0, 255, 255);
}

.status-bar {
    background-color: var(--undercaret-bg-color);
    border-bottom: 1px solid var(--text-color);
    display: flex;
    align-items: center;
    padding: 0 20px;
}

.status-message {
    color: var(--text-color);
    font-size: 14px;
}

/* 高亮匹配的节点 */
.el-tree-node__label.highlighted {
    background-color: #ffeb3b !important;
    color: #000 !important;
    border-radius: 3px;
    padding: 2px 4px;
    margin: 1px 0;
    font-weight: bold;
}