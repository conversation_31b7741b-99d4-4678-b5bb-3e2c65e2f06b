<html>
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <title>使用手册</title>
    <style>
        /* cspell:disable-file */
        /* webkit printing magic: print all background colors */
        html {
            -webkit-print-color-adjust: exact;
        }

        * {
            box-sizing: border-box;
            -webkit-print-color-adjust: exact;
        }

        html,
        body {
            margin: 0;
            padding: 0;
            background-color: white;
        }

        @media only screen {
            body {
                margin: 2em auto;
                max-width: 900px;
                color: rgb(55, 53, 47);
            }
        }

        body {
            line-height: 1.5;
            /*white-space: pre-wrap;*/
        }

        a,
        a.visited {
            color: inherit;
            text-decoration: underline;
        }

        .pdf-relative-link-path {
            font-size: 80%;
            color: #444;
        }

        h1,
        h2,
        h3 {
            letter-spacing: -0.01em;
            line-height: 1.2;
            font-weight: 600;
            margin-bottom: 0;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-top: 0;
            margin-bottom: 0.75em;
        }

        h1 {
            font-size: 1.875rem;
            margin-top: 1.875rem;
        }

        h2 {
            font-size: 1.5rem;
            margin-top: 1.5rem;
        }

        h3 {
            font-size: 1.25rem;
            margin-top: 1.25rem;
        }

        .source {
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 1.5em;
            word-break: break-all;
        }

        .callout {
            border-radius: 3px;
            padding: 1rem;
        }

        figure {
            margin: 1.25em 0;
            page-break-inside: avoid;
        }

        figcaption {
            opacity: 0.5;
            font-size: 85%;
            margin-top: 0.5em;
        }

        mark {
            background-color: transparent;
        }

        .indented {
            padding-left: 1.5em;
        }

        hr {
            background: transparent;
            display: block;
            width: 100%;
            height: 1px;
            visibility: visible;
            border: none;
            border-bottom: 1px solid rgba(55, 53, 47, 0.09);
        }

        img {
            max-width: 100%;
        }

        @media only print {
            img {
                max-height: 100vh;
                object-fit: contain;
            }
        }

        @page {
            margin: 1in;
        }

        .collection-content {
            font-size: 0.875rem;
        }

        .column-list {
            display: flex;
            justify-content: space-between;
        }

        .column {
            padding: 0 1em;
        }

        .column:first-child {
            padding-left: 0;
        }

        .column:last-child {
            padding-right: 0;
        }

        .table_of_contents-item {
            display: block;
            font-size: 0.875rem;
            line-height: 1.3;
            padding: 0.125rem;
        }

        .table_of_contents-indent-1 {
            margin-left: 1.5rem;
        }

        .table_of_contents-indent-2 {
            margin-left: 3rem;
        }

        .table_of_contents-indent-3 {
            margin-left: 4.5rem;
        }

        .table_of_contents-link {
            text-decoration: none;
            opacity: 0.7;
            border-bottom: 1px solid rgba(55, 53, 47, 0.18);
        }

        table,
        th,
        td {
            border: 1px solid rgba(55, 53, 47, 0.09);
            border-collapse: collapse;
        }

        table {
            border-left: none;
            border-right: none;
        }

        th,
        td {
            font-weight: normal;
            padding: 0.25em 0.5em;
            line-height: 1.5;
            min-height: 1.5em;
            text-align: left;
        }

        th {
            color: rgba(55, 53, 47, 0.6);
        }

        ol,
        ul {
            margin: 0;
            margin-block-start: 0.6em;
            margin-block-end: 0.6em;
        }

        li > ol:first-child,
        li > ul:first-child {
            margin-block-start: 0.6em;
        }

        ul > li {
            list-style: disc;
        }

        ul.to-do-list {
            padding-inline-start: 0;
        }

        ul.to-do-list > li {
            list-style: none;
        }

        .to-do-children-checked {
            text-decoration: line-through;
            opacity: 0.375;
        }

        ul.toggle > li {
            list-style: none;
        }

        ul {
            padding-inline-start: 1.7em;
        }

        ul > li {
            padding-left: 0.1em;
        }

        ol {
            padding-inline-start: 1.6em;
        }

        ol > li {
            padding-left: 0.2em;
        }

        .mono ol {
            padding-inline-start: 2em;
        }

        .mono ol > li {
            text-indent: -0.4em;
        }

        .toggle {
            padding-inline-start: 0em;
            list-style-type: none;
        }

        /* Indent toggle children */
        .toggle > li > details {
            padding-left: 1.7em;
        }

        .toggle > li > details > summary {
            margin-left: -1.1em;
        }

        .selected-value {
            display: inline-block;
            padding: 0 0.5em;
            background: rgba(206, 205, 202, 0.5);
            border-radius: 3px;
            margin-right: 0.5em;
            margin-top: 0.3em;
            margin-bottom: 0.3em;
            white-space: nowrap;
        }

        .collection-title {
            display: inline-block;
            margin-right: 1em;
        }

        .page-description {
            margin-bottom: 2em;
        }

        .simple-table {
            margin-top: 1em;
            font-size: 0.875rem;
            empty-cells: show;
        }

        .simple-table td {
            height: 29px;
            min-width: 120px;
        }

        .simple-table th {
            height: 29px;
            min-width: 120px;
        }

        .simple-table-header-color {
            background: rgb(247, 246, 243);
            color: black;
        }

        .simple-table-header {
            font-weight: 500;
        }

        time {
            opacity: 0.5;
        }

        .icon {
            display: inline-block;
            max-width: 1.2em;
            max-height: 1.2em;
            text-decoration: none;
            vertical-align: text-bottom;
            margin-right: 0.5em;
        }

        img.icon {
            border-radius: 3px;
        }

        .user-icon {
            width: 1.5em;
            height: 1.5em;
            border-radius: 100%;
            margin-right: 0.5rem;
        }

        .user-icon-inner {
            font-size: 0.8em;
        }

        .text-icon {
            border: 1px solid #000;
            text-align: center;
        }

        .page-cover-image {
            display: block;
            object-fit: cover;
            width: 100%;
            max-height: 30vh;
        }

        .page-header-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .page-header-icon-with-cover {
            margin-top: -0.72em;
            margin-left: 0.07em;
        }

        .page-header-icon img {
            border-radius: 3px;
        }

        .link-to-page {
            margin: 1em 0;
            padding: 0;
            border: none;
            font-weight: 500;
        }

        p > .user {
            opacity: 0.5;
        }

        td > .user,
        td > time {
            white-space: nowrap;
        }

        input[type="checkbox"] {
            transform: scale(1.5);
            margin-right: 0.6em;
            vertical-align: middle;
        }

        p {
            margin-top: 0.5em;
            margin-bottom: 0.5em;
        }

        .image {
            border: none;
            margin: 1.5em 0;
            padding: 0;
            border-radius: 0;
            text-align: center;
        }

        .code,
        code {
            background: rgba(135, 131, 120, 0.15);
            border-radius: 3px;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
            tab-size: 2;
        }

        code {
            color: #eb5757;
        }

        .code {
            padding: 1.5em 1em;
        }

        .code-wrap {
            white-space: pre-wrap;
            word-break: break-all;
        }

        .code > code {
            background: none;
            padding: 0;
            font-size: 100%;
            color: inherit;
        }

        blockquote {
            font-size: 1.25em;
            margin: 1em 0;
            padding-left: 1em;
            border-left: 3px solid rgb(55, 53, 47);
        }

        .bookmark {
            text-decoration: none;
            max-height: 8em;
            padding: 0;
            display: flex;
            width: 100%;
            align-items: stretch;
        }

        .bookmark-title {
            font-size: 0.85em;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 1.75em;
            white-space: nowrap;
        }

        .bookmark-text {
            display: flex;
            flex-direction: column;
        }

        .bookmark-info {
            flex: 4 1 180px;
            padding: 12px 14px 14px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .bookmark-image {
            width: 33%;
            flex: 1 1 180px;
            display: block;
            position: relative;
            object-fit: cover;
            border-radius: 1px;
        }

        .bookmark-description {
            color: rgba(55, 53, 47, 0.6);
            font-size: 0.75em;
            overflow: hidden;
            max-height: 4.5em;
            word-break: break-word;
        }

        .bookmark-href {
            font-size: 0.75em;
            margin-top: 0.25em;
        }

        .sans {
            font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
        }

        .code {
            font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace;
        }

        .serif {
            font-family: Lyon-Text, Georgia, ui-serif, serif;
        }

        .mono {
            font-family: iawriter-mono, Nitti, Menlo, Courier, monospace;
        }

        .pdf .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP';
        }

        .pdf:lang(zh-CN) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC';
        }

        .pdf:lang(zh-TW) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC';
        }

        .pdf:lang(ko-KR) .sans {
            font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR';
        }

        .pdf .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP';
        }

        .pdf:lang(zh-CN) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC';
        }

        .pdf:lang(zh-TW) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC';
        }

        .pdf:lang(ko-KR) .code {
            font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR';
        }

        .pdf .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP';
        }

        .pdf:lang(zh-CN) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC';
        }

        .pdf:lang(zh-TW) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC';
        }

        .pdf:lang(ko-KR) .serif {
            font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR';
        }

        .pdf .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP';
        }

        .pdf:lang(zh-CN) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC';
        }

        .pdf:lang(zh-TW) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC';
        }

        .pdf:lang(ko-KR) .mono {
            font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR';
        }

        .highlight-default {
            color: rgba(55, 53, 47, 1);
        }

        .highlight-gray {
            color: rgba(120, 119, 116, 1);
            fill: rgba(120, 119, 116, 1);
        }

        .highlight-brown {
            color: rgba(159, 107, 83, 1);
            fill: rgba(159, 107, 83, 1);
        }

        .highlight-orange {
            color: rgba(217, 115, 13, 1);
            fill: rgba(217, 115, 13, 1);
        }

        .highlight-yellow {
            color: rgba(203, 145, 47, 1);
            fill: rgba(203, 145, 47, 1);
        }

        .highlight-teal {
            color: rgba(68, 131, 97, 1);
            fill: rgba(68, 131, 97, 1);
        }

        .highlight-blue {
            color: rgba(51, 126, 169, 1);
            fill: rgba(51, 126, 169, 1);
        }

        .highlight-purple {
            color: rgba(144, 101, 176, 1);
            fill: rgba(144, 101, 176, 1);
        }

        .highlight-pink {
            color: rgba(193, 76, 138, 1);
            fill: rgba(193, 76, 138, 1);
        }

        .highlight-red {
            color: rgba(212, 76, 71, 1);
            fill: rgba(212, 76, 71, 1);
        }

        .highlight-default_background {
            color: rgba(55, 53, 47, 1);
        }

        .highlight-gray_background {
            background: rgba(241, 241, 239, 1);
        }

        .highlight-brown_background {
            background: rgba(244, 238, 238, 1);
        }

        .highlight-orange_background {
            background: rgba(251, 236, 221, 1);
        }

        .highlight-yellow_background {
            background: rgba(251, 237, 214, 1);
        }

        .highlight-teal_background {
            background: rgba(237, 243, 236, 1);
        }

        .highlight-blue_background {
            background: rgba(231, 243, 248, 1);
        }

        .highlight-purple_background {
            background: rgba(244, 240, 247, 0.8);
        }

        .highlight-pink_background {
            background: rgba(249, 238, 243, 0.8);
        }

        .highlight-red_background {
            background: rgba(253, 235, 236, 1);
        }

        .block-color-default {
            color: inherit;
            fill: inherit;
        }

        .block-color-gray {
            color: rgba(120, 119, 116, 1);
            fill: rgba(120, 119, 116, 1);
        }

        .block-color-brown {
            color: rgba(159, 107, 83, 1);
            fill: rgba(159, 107, 83, 1);
        }

        .block-color-orange {
            color: rgba(217, 115, 13, 1);
            fill: rgba(217, 115, 13, 1);
        }

        .block-color-yellow {
            color: rgba(203, 145, 47, 1);
            fill: rgba(203, 145, 47, 1);
        }

        .block-color-teal {
            color: rgba(68, 131, 97, 1);
            fill: rgba(68, 131, 97, 1);
        }

        .block-color-blue {
            color: rgba(51, 126, 169, 1);
            fill: rgba(51, 126, 169, 1);
        }

        .block-color-purple {
            color: rgba(144, 101, 176, 1);
            fill: rgba(144, 101, 176, 1);
        }

        .block-color-pink {
            color: rgba(193, 76, 138, 1);
            fill: rgba(193, 76, 138, 1);
        }

        .block-color-red {
            color: rgba(212, 76, 71, 1);
            fill: rgba(212, 76, 71, 1);
        }

        .block-color-default_background {
            color: inherit;
            fill: inherit;
        }

        .block-color-gray_background {
            background: rgba(241, 241, 239, 1);
        }

        .block-color-brown_background {
            background: rgba(244, 238, 238, 1);
        }

        .block-color-orange_background {
            background: rgba(251, 236, 221, 1);
        }

        .block-color-yellow_background {
            background: rgba(251, 237, 214, 1);
        }

        .block-color-teal_background {
            background: rgba(237, 243, 236, 1);
        }

        .block-color-blue_background {
            background: rgba(231, 243, 248, 1);
        }

        .block-color-purple_background {
            background: rgba(244, 240, 247, 0.8);
        }

        .block-color-pink_background {
            background: rgba(249, 238, 243, 0.8);
        }

        .block-color-red_background {
            background: rgba(253, 235, 236, 1);
        }

        .select-value-color-uiBlue {
            background-color: rgba(35, 131, 226, .07);
        }

        .select-value-color-pink {
            background-color: rgba(245, 224, 233, 1);
        }

        .select-value-color-purple {
            background-color: rgba(232, 222, 238, 1);
        }

        .select-value-color-green {
            background-color: rgba(219, 237, 219, 1);
        }

        .select-value-color-gray {
            background-color: rgba(227, 226, 224, 1);
        }

        .select-value-color-transparentGray {
            background-color: rgba(227, 226, 224, 0);
        }

        .select-value-color-translucentGray {
            background-color: rgba(0, 0, 0, 0.06);
        }

        .select-value-color-orange {
            background-color: rgba(250, 222, 201, 1);
        }

        .select-value-color-brown {
            background-color: rgba(238, 224, 218, 1);
        }

        .select-value-color-red {
            background-color: rgba(255, 226, 221, 1);
        }

        .select-value-color-yellow {
            background-color: rgba(249, 228, 188, 1);
        }

        .select-value-color-blue {
            background-color: rgba(211, 229, 239, 1);
        }

        .select-value-color-pageGlass {
            background-color: undefined;
        }

        .select-value-color-washGlass {
            background-color: undefined;
        }

        .checkbox {
            display: inline-flex;
            vertical-align: text-bottom;
            width: 16;
            height: 16;
            background-size: 16px;
            margin-left: 2px;
            margin-right: 5px;
        }

        .checkbox-on {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
        }

        .checkbox-off {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
        }

    </style>
</head>
<body>
<article class="page sans" id="156b706e-3ea1-480f-9012-da113d26a492">
    <div class="page-body"><p class="" id="f7b0898c-1372-406b-9e67-41035f4c447e">XCodeMap
        是一个基于录制回放机制的代码分析引擎，它的使用流程包括两个基本步骤，录制和回放。</p>
        <h2 class="" id="1532aaae-f1c8-4d22-8afb-806d0fc7b77b">1. 录制</h2>
        <p class="" id="bc462de9-4dd8-4cfa-ba2e-b39b2772d70d">录制的基本原理是在启动原程序时插入一个 Java Agent
            探针。用户触发原程序功能（如发送 HTTP 请求）后，XCodeMap
            在后台透明地采集程序运行时数据。这种录制不会干扰原程序的功能，因此原程序启动后可以正常使用。</p>
        <h3 class="" id="d97be4b8-8358-42d7-bf17-638a449d0d1e">1.1 在代码 Main 方法处启动</h3>
        <p class="" id="20e6c69c-f4fb-4b41-90ec-e0e55365721c">找到 Main 方法后，点击左侧执行按钮旁的下拉列表。选择&quot;Debug
            xxx with XCodeMap&quot;选项，然后点击即可启动程序。</p>
        <figure class="image" id="3bfeacd0-605d-4255-ac18-bbdb9134bea8">
            <img src="../assets/article1/image.png"
                 style="width:1146px"/>
        </figure>
        <h3 class="" id="5258ea02-4021-4669-813b-52e01fecb738">1.2 在 Run Configuration 处启动</h3>
        <p class="" id="53529f7c-8d80-476b-8067-4f71fdd769c8">选择一个 Run configuration，选择 XCodeMap
            的图标，点击执行。</p>
        <figure class="image" id="0c319165-08e9-4074-899b-d8f114a4f110"><img
                src="../assets/article1/image-1.png"
                style="width:792px"/>
        </figure>
        <p class="" id="f2dc19f5-c2e9-422a-a314-91963c2610f3">
        </p>
        <p class="" id="0fc1dee8-8cf4-4229-9d34-99e1193a259d">原程序启动成功后，如果是 Web 程序，你可以使用 Postman 等工具发送
            HTTP 请求。XCodeMap
            会在后台自动录制程序的运行时数据，包括函数调用堆栈、参数和返回值。这些数据会被保存在本地，为后续的回放和分析提供基础。</p>
        <h2 class="" id="3fcfe739-e4a7-437f-8c0b-25a30cb48b3e">2. 回放</h2>
        <p class="" id="c8d42f80-9a64-431a-8368-e2bbe6371a87">XCodeMap
            在录制开始后会实时采集数据，并将原程序回放为一个序列图。通过这个序列图，你可以轻松地走读源码。值得注意的是，回放和采集并不冲突——你可以在回放某个请求时继续保持原程序运行，无需关闭它。</p>
        <h3 class="" id="ed93cb81-1bf9-4234-a6c3-c7e0de341d94">2.1 请求列表</h3>
        <p class="" id="26671b30-da5f-42c6-a69a-0e8a535e2c70">XCodeMap 按照请求的粒度组织序列图。你每触发原程序的一个功能，XCodeMap
            都会实时展示出这个功能对应的一个或多个请求。值得注意的是，Main 线程或者后台线程本身被当作一个特殊的请求。</p>
        <figure class="image" id="04503f75-17b8-4a5b-a3c9-16d875dcd3c7"><img
                src="../assets/article1/image-2.png"
                style="width:1190px"/>
        </figure>
        <p class="" id="02136205-38e6-4bf6-bde1-282ee6c5f59d">
            点击某个请求后，就可以看到原程序走过的序列图，顺着这个序列图我们就可以走读代码。</p>
        <h3 class="" id="09357a20-f9ef-42c6-b5d1-e28b07d3c6e3">2.1 走读代码</h3>
        <p>
            XCodeMap 会把程序走过的函数在源码处高亮出来，方便一眼看出程序的走向，免除分支抽象的困扰。
        </p>
        <figure class="image"><img
                src="../assets/article1/image-3-1.png"
                style="width:1716px"/>
        </figure>
        <p>
            XCodeMap 会把点击过的函数在序列图中显示出来，方便回顾总结，免除频繁Debug的困扰。
        </p>
        <figure class="image"><img
                src="../assets/article1/image-3-2.png"
                style="width:1716px"/>
        </figure>
        <p class="" id="826a4415-38dd-4046-95a3-34524757ba2c">XCodeMap 走读代码的基本操作：</p>
        <ul class="bulleted-list" id="0e0dfb9b-24ce-49d6-aaa5-352d50c36d5e">
            <li style="list-style-type:disc">直视源码，点击高亮关键字（免分支困扰）</li>
        </ul>
        <ul class="bulleted-list" id="a6af7b44-1e7b-42b0-9909-0eb18cfc2c53">
            <li style="list-style-type:disc">点击左侧【查看定义】，XCodeMap 会跳转到具体的实现处（免抽象困扰）</li>
        </ul>
        <ul class="bulleted-list" id="04056110-16fe-490a-b420-9062808df06a">
            <li style="list-style-type:disc">点击左侧【查看用法】，XCodeMap 会显示当前函数在当前行的所有调用（免循环困扰）</li>
        </ul>
        <ul class="bulleted-list" >
            <li style="list-style-type:disc">点击过的函数在序列图中，会显示出来，可以来回查看（免频繁Debug）</li>
        </ul>
        <p>
            XCodeMap 的两个额外技巧:
        </p>
        <ul class="bulleted-list" id="40c61da1-e6fa-458e-89a7-0ae857db3154">
            <li style="list-style-type:disc">右键序列图中的节点，可以查看更多功能</li>
        </ul>
        <ul class="bulleted-list" id="">
            <li style="list-style-type:disc">序列图右侧的竖长条导航栏，可以快速查看循环的函数</li>
        </ul>
        <p class="" id="c345013d-0ec9-43f6-93b8-24b928d5805c"> 用 XCodeMap 走读代码，与通常的走读习惯基本一致，
            不同的是，XCodeMap 把程序走过的代码高亮出来，免除分支抽象的困扰，把点击过的函数在序列图中显示出来，免除频繁Debug的困扰。</p>
        <h3 class="" id="80d7bf9e-41c7-4a56-be04-b5b465f2dfa8">2.2 上下文数据</h3>
        <p class="" id="88204483-4f1f-4da7-9339-3f57930a8cef">
            当纯看代码，难以理解时，可以借助上下文数据，来帮助你。序列图左侧会同步显示当前函数的参数，返回值，堆栈，借助这些数据，可以帮助你更好地理解程序。</p>
        <figure class="image" id="be74d881-c67d-4552-848b-a6b2b378b077"><img
                src="../assets/article1/image-4.png"
                style="width:1244px"/>
        </figure>
        <p class="" id="fb51a142-bc21-4751-bd66-1fa6aa813a69">
        </p>
        <h3 class="" id="1502489e-003e-4175-8a6b-d866488828c9">2.3 搜索</h3>
        <p class="" id="10a778db-d213-4797-b54a-e2e3bc4233b1">当你对代码有几分熟悉之后，可能不满足于一步步走读，你会想要快速跳到某个特定的代码位置。此时，你可以使用
            XCodeMap 的搜索功能，来快速抵达。</p>
        <figure class="image" id="2960e686-8d0d-4435-bcb5-3430082dd350"><img
                src="../assets/article1/image-5.png"
                style="width:1358px"/>
        </figure>
        <p class="" id="6d16b542-d099-462a-af96-654de184d3e7">
        </p>
        <h3 class="" id="fff016e7-f259-46c3-922f-664b5f7a3c87">2.4 追踪</h3>
        <p class="" id="72e91a47-506e-416e-95d2-aae68d579922">
            当你走读完代码的大致流程后，此时需要对某个类有一个较为全面的总结。你需要知道，这个类在哪创建，在哪使用，怎么使用等，而这些操作可能散落在代码的各个地方，通过走读没那么容易找全。</p>
        <p class="" id="f1351eba-b945-4472-85e1-fdc8ba994958">此时，你可以使用 XCodeMap
            的对象追踪能力，找出所有跟这个对象相关的操作。</p>
        <p class="" id="79d08552-7d7b-4061-8bb7-118712387585">具体操作办法，在某个函数的上下文数据处，找到类的对象，右键
            “trace object”，左侧序列图会标记出相关的函数。</p>
        <figure class="image" id="25636619-64f1-4f75-b93e-6d2ed15aa658"><img
                src="../assets/article1/image-6.png"
                style="width:708px"/>
        </figure>
        <p class="" id="97797736-0cca-4882-9e33-4855be9ccb5c">
            你还可以在中间栏，进一步筛选范围，比如选择“change”，找出对象的变更历史。</p>
        <figure class="image" id="24b2671f-d55c-4ec7-8e85-b1377049fac5"><img
                src="../assets/article1/image-7.png"
                style="width:1064px"/>
        </figure>
        <p class="" id="ac81376e-1745-420c-bebc-e5c703f74bf5">
        </p>
    </div>
</article>
<span class="sans" style="font-size:14px;padding-top:2em"></span></body>
</html>